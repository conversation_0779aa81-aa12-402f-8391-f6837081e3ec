const functions = require("@google-cloud/functions-framework");
const { BigQuery } = require("@google-cloud/bigquery");

const bigquery = new BigQuery();

exports.insert = async (req, res) => {
    try {
        const timestamp = new Date();
        const { key, ...data } = req.query;
        if (key) {
            await bigquery
                .dataset("autoqc")
                .table("user_response_log")
                .insert({ key, timestamp, data: JSON.stringify(data) });
            res.status(200).send("OK");
        }
        else {
            res.status(400).send("INVALID");
        }
    } catch(err) {
        console.error(err);
        res.status(500).send("ERROR");
    }
};