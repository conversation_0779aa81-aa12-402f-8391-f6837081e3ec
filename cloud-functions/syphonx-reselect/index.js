"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const functions = require("@google-cloud/functions-framework");
const bigquery_1 = require("@google-cloud/bigquery");
// const { BigQuery } = require("@google-cloud/bigquery");
// const { PublishOptions, PubSub } = require("@google-cloud/pubsub");
const pubsub_1 = require("@google-cloud/pubsub");
const bigquery = new bigquery_1.BigQuery({ projectId: "ps-bigdata" });
const pubsub = new pubsub_1.PubSub({ projectId: "ps-colossus" });
const MAX_MESSAGES = 100;
functions.http("syphonx-reselect", async (req, res) => {
    try {
        const { captureId } = req.query;
        const accountKey = req.query?.accountKey?.length ? decodeURIComponent(req.query.accountKey)?.replaceAll('"', '') : undefined;
        const sellerId = req.query?.sellerId ? Number(req.query?.sellerId) : undefined;
        if (!captureId?.length && !sellerId)
            throw new Error("captureId or sellerId is required for reselection. accountId is optional");
        await bigquery
            .dataset("syphonx")
            .table("reselect_log")
            .insert({
            timestamp: new Date(),
            capture_id: captureId,
            seller_id: sellerId,
            account_key: accountKey
        });
        if (captureId) {
            const topic = pubsub.topic("select");
            const message = { id: captureId, store: true };
            const data = Buffer.from(JSON.stringify(message));
            await topic.publishMessage({ data });
            res.status(200).send("Thank you. Please close this window.");
        }
        else if (sellerId) {
            const query = `
                SELECT capture_id
                FROM brand_monitor.product_page_captures
                WHERE
                DATE(capture_date)>=DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
                    AND seller_id=@sellerId
                    ${accountKey ? `AND account_key=@accountKey` : ""}
                QUALIFY ROW_NUMBER() OVER (PARTITION BY account_key, country_code, seller_id, sku ORDER BY COALESCE(select_date, capture_date) DESC) = 1
            `;
            const [job] = await bigquery.createQueryJob({ query, params: { accountKey, sellerId } });
            const [rows] = await job.getQueryResults() || [];
            if (rows?.length) {
                const publishOptions = {
                    batching: {
                        maxMessages: MAX_MESSAGES,
                        maxMilliseconds: 1000
                    }
                };
                const topic = pubsub.topic("select", publishOptions);
                while (rows?.length) {
                    await Promise.all(rows.splice(0, MAX_MESSAGES).map(async (row) => {
                        const message = { id: row.capture_id, store: true };
                        const data = Buffer.from(JSON.stringify(message));
                        await topic.publishMessage({ data });
                    }));
                }
            }
            res.status(200).send("Thank you. Please close this window.");
        }
        else {
            throw new Error("captureId or sellerId is required for reselection. accountId is optional");
        }
    }
    catch (err) {
        const error = `ERROR: ${err}`;
        res.status(500).send(error);
    }
});
