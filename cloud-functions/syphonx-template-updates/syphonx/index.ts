
import * as functions from "@google-cloud/functions-framework";

functions.cloudEvent("syphonx-template-updates", async (cloudEvent: any) => {
    try {
        const messageData = cloudEvent.data.message.data;
        const data = messageData ? Buffer.from(messageData, "base64").toString() : "";

        const { name, json, contract, metadata } = JSON.parse(data);
        const { url, timestamp } = JSON.parse(json);

        if (!/pricespider/.test(name)) {
            throw new Error(`Updates to ${name} will not be forwarded to ps-bigdata.`)
        }

        const body = {
            key: name,
            template: json ? JSON.parse(json) : {},
            contract: contract ? JSON.parse(contract) : {},
            metadata
        };

        await fetch("https://us-central1-ps-bigdata.cloudfunctions.net/pricespider-syphonx-template-log", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(timestamp ? { ...body, timestamp } : body)
        });

        if (!/pricespider\/sandbox\/|common\/|wtb\/|prowl\/|brand_monitor\//.test(name)) {
            throw new Error(`Updates to ${name} will not be forwarded to clerk.pricespider.com. Only pricespider/sandbox|common|wtb|prowl|brand_monitor scripts will be forwarded to clerk.pricespider.com.`)
        }

        // send updates to Conflux Redis API
        if (!url) {
            throw new Error(`url required to resolve domain. - ${name} will not be forwarded to the Clerk API.`);
        }

        if (!/\/(product_page|review_page|product_search|category_page).json$/.test(name)) {
            throw new Error(`Conflux can only handle product_page, review_page, search_page ("product_search"), and category_page crawling currently - ${name} will not be forwarded to the Clerk API.`);
        }

        const domain = (new URL(url.replace(/^{`|`}$/g, '').split('?')[0]))?.host;
        if (!domain) {
            throw new Error(`Error resolving domain from ${url}, will not sent to Clerk API.`);
        }

        await fetch("https://clerk.pricespider.com/syphonx/template", {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${process.env.CLERK_API_TOKEN}`,
                "User": "SyphonX",
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                contract,
                domain,
                path: name,
                template: json ? JSON.parse(json) : {},
            })
        });
        
        console.log(`Template ${name} sent to Clerk with a domain of ${domain}.`);
    } catch (e: any) {
        console.warn(e?.message);
    }
});
