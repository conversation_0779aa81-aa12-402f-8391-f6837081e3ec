
import * as functions from "@google-cloud/functions-framework";
import { BigQuery } from "@google-cloud/bigquery";
const bigquery = new BigQuery();

interface TemplateUpdateBody {
    timestamp?: string;
    key: string;
    template: object;
    contract?: object;
    metadata?: object;
}

functions.http("pricespider-syphonx-template-log", async (req: any, res: any) => {
    try {
        const data = req.body as TemplateUpdateBody;
        const { timestamp, key, template, contract, metadata } = data;

        if (!/pricespider/.test(key)) {
            throw new Error(`Updates to ${key} will not be forwarded to ps-bigdata.`);
        }

        if (!key || !template) {
            throw new Error("key and template are required");
        }

        const rowData = {
            key,
            template: template ? JSON.stringify(template) : null,
            contract: contract ? JSON.stringify(contract) : null,
            metadata: metadata ? JSON.stringify(metadata) : null
        };

        // const rows = [timestamp ? { ...rowData, timestamp } : rowData];
        const rows = [rowData];

        await bigquery.dataset("syphonx").table("template_log").insert(rows);
        res.sendStatus(200);
    }
    catch (e) {
        console.error(JSON.stringify(e));
        res.sendStatus(500);
    }
});


