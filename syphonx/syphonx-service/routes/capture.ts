import express from "express";
import { handleAsyncRequest, recapture, reselect } from "./lib.js";

const router = express.Router();

router.post("/reselect", (req, res) => handleAsyncRequest(req, res, async () => {
    const { captureId } = req.query as Record<string, string>;
    const accountKey = req.query?.accountKey ? decodeURIComponent(req.query.accountKey as string).replaceAll('"', '') : undefined;
    const sellerId = req.query?.sellerId ? Number(req.query?.sellerId) : undefined;
    await reselect({ captureId, accountKey, sellerId });
    res.status(200).send("Thank you. Please close this window.");
}));

router.post("/recapture", (req, res) => handleAsyncRequest(req, res, async () => {
    const { url, captureId } = req.query as Record<string, string>;
    await recapture({ captureId, url });
    res.status(200).send("Thank you. Please close this window.");
}));

export default router;