import express from "express";

import {
    handleRequest,
    handleAsyncRequest,
    postAuditResponse,
    serveAuditRequest,
    validateAuditRequest,
    HttpError
} from "./lib.js";

const router = express.Router();

let counter = 0;
router.get("/counter", (req, res) => handleRequest(req, res, () => {
    counter += 1;
    return { counter };
}));

router.get("/", (req, res) => handleAsyncRequest(req, res, async () => {
    const audit = await serveAuditRequest();
    return audit;
}));

router.post("/", (req, res) => handleAsyncRequest(req, res, async () => {
    const audit = validateAuditRequest(req.body);
    if (!audit)
        throw new HttpError(400, "Audit request invalid JSON data");
    await postAuditResponse(audit);
    return {
        id: audit.id,
        ok: true
    };
}));

export default router;
