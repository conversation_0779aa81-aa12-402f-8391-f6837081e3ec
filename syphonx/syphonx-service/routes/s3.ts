import express from "express";
import { handleAsyncRequest, s3 } from "./lib.js";

const router = express.Router();

router.get("/", (req, res) => handleAsyncRequest(req, res, async () => {
    try {
        const { capture_id, capture_date } = req.query as { capture_id: string; capture_date: string; };
        const s3UploadInformation = await s3.generateS3UploadInformation(capture_id, capture_date);
    
        return {
            bucket: s3UploadInformation.bucket,
            key: s3UploadInformation.key,
            url: s3UploadInformation.url
        };
    } catch (e) {
        return { bucket: null, key: null, url: null };
    }
}));

export default router;
