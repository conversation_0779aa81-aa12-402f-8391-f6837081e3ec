TODO:
On/off switch at the top (DONE)
Make perpetual requests (DONE)
Show status
Delay between serves to flip switch off
Option to pause after every completion
Prompt: Try Again, Blocked, Not Found, Other
  (if empty and no errors or only timeout error)
Screenshots



SELECT
  capture_status,
  COUNT(*)
FROM conflux.product_page_daily
WHERE app_name='brand_monitor'
GROUP BY 1
ORDER BY 1;
--> latest-valid: 81908, latest-invalid: 8578, never-valid: 3941, never-attempted: 3930

SELECT
  DATE_DIFF(last_attempt.capture_date, last_capture.capture_date, DAY),
  COUNT(*),
  ROUND(SAFE_DIVIDE(COUNT(*), (SELECT COUNT(*) FROM conflux.product_page_daily WHERE app_name='brand_monitor' AND capture_status='latest-invalid')), 3)
FROM conflux.product_page_daily
WHERE app_name='brand_monitor' AND capture_status='latest-invalid'
GROUP BY 1
ORDER BY 1;
--> 16%, 38%, 13%, 9%, 4%, 2%, 1%, ...

SELECT
  country_code,
  COUNT(*),
  ROUND(SAFE_DIVIDE(COUNT(*), (SELECT COUNT(*) FROM conflux.product_page_daily WHERE app_name='brand_monitor' AND capture_status='latest-invalid')), 3)
FROM conflux.product_page_daily
WHERE app_name='brand_monitor' AND capture_status='latest-invalid'
GROUP BY 1
ORDER BY 2 DESC;
--> US 81%, DE 5%, CA 3%, ...

SELECT
  seller_name,
  COUNT(*),
  ROUND(SAFE_DIVIDE(COUNT(*), (SELECT COUNT(*) FROM conflux.product_page_daily WHERE app_name='brand_monitor' AND capture_status='latest-invalid')), 3)
FROM conflux.product_page_daily
WHERE app_name='brand_monitor' AND capture_status='latest-invalid'
GROUP BY 1
ORDER BY 2 DESC;
--> Walmart 9%, NAPA 7%, Northern Tool & Equipment 6%

SELECT
  seller_name,
  COUNT(*),
  ROUND(SAFE_DIVIDE(COUNT(*), (SELECT COUNT(*) FROM conflux.product_page_daily WHERE app_name='brand_monitor' AND capture_status='never-valid')), 3)
FROM conflux.product_page_daily
WHERE app_name='brand_monitor' AND capture_status='never-valid'
GROUP BY 1
ORDER BY 2 DESC;
--> Amazon (DE) 16%, Bol.com (NL) 7%



SELECT
  page_id,
  capture_url,
  DATE_DIFF(last_attempt.capture_date, last_capture.capture_date, HOUR) AS capture_age,
  template_path
FROM conflux.product_page_daily
WHERE app_name='brand_monitor'
AND capture_status='latest-invalid'
AND seller_id=167
ORDER BY DATE_DIFF(last_attempt.capture_date, last_capture.capture_date, HOUR) DESC;
