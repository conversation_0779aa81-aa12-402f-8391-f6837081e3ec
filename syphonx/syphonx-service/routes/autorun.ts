import express from "express";

import {
    buildConfluxResultMessage,
    validateConfluxResultData,
    handleAsyncRequest,
    insertAutorunLog,
    loadGenericTemplate,
    loadTemplate,
    rabbit,
    HttpError,
    queryWorkstreams,
    insertSyphonxData,
    autorunDefault
} from "./lib.js";
import { processSearchUrls } from "../lib/url/url.js";
import { cache, getCacheKey, refreshCache, updateCache, QueueItem } from "../lib/cache/cache.js";

const router = express.Router();

const default_workstream = "default";
const default_pageType = "default";
const ConfluxRoutingKeys: { [key: string]: string } = {
    brand_monitor: `engine.hac.brand_monitor.results.product`,
    scout: `engine.hac.scout.results.product`,
    prowl: `engine.hac.scout.results.product`,
    wtb: `engine.hac.wtb.result`
};

type PageType = 'search' | 'product' | 'category' | 'category_product' | 'default';

function parsePageType(workstream_name: string): PageType {
    const normalizedName = workstream_name.toLowerCase().replace(/\s/g, "");

    if (normalizedName.includes('search')) {
        return 'search';
    } else if (normalizedName.includes('category_product')) {
        return 'category_product';
    } else if (normalizedName.includes('category')) {
        return 'category';
    } else if (normalizedName.includes('product')) {
        return 'product';
    }

    return 'default';
}

router.get("/health", (req, res) => handleAsyncRequest(req, res, async () => {
    return { ok: true };
}));

router.get("/workstreams", (req, res) => handleAsyncRequest(req, res, async () => {
    //const authorization = req.headers["authorization"];
    //if (!authorization)
    //throw new HttpError(401);

    const user = req.headers["x-username"] as string;
    if (!user)
        throw new HttpError(401);

    const workstreams = await queryWorkstreams();
    return workstreams;
}));

router.post("/invalidate-cache", (req, res) => handleAsyncRequest(req, res, async () => {
    const { workstream, pageType, include, exclude } = req.body;

    if (workstream && cache[workstream]) {
        cache[workstream].timestamp = 0; // Force refresh on next request
        console.info(`Cache invalidated for workstream: ${workstream}`);

        if (pageType) {
            await refreshCache(workstream, pageType, include, exclude);
            console.info(`Cache refreshed for workstream: ${workstream} and pageType: ${pageType}`);
        }
    }
    else {
        for (const key in cache) {
            cache[key].timestamp = 0;
        }
        console.info(`All caches invalidated`);
    }

    return { ok: true };
}));

router.get("/cache-status", (req, res) => handleAsyncRequest(req, res, async () => {
    const status: any = {};
    for (const key in cache) {
        status[key] = {
            itemCount: cache[key].buffer.length,
            lastUpdated: new Date(cache[key].timestamp).toISOString(),
            ageInMinutes: (Date.now() - cache[key].timestamp) / (60 * 1000),
            pendingItems: cache[key].buffer.filter(item => item.pending).length,
            availableItems: cache[key].buffer.filter(item => !item.posted && !item.pending).length,
        };
    }
    return status;
}));

router.get("/", (req, res) => handleAsyncRequest(req, res, async () => {
    //const authorization = req.headers["authorization"];
    //if (!authorization)
    //throw new HttpError(401);
    const user = req.headers["x-username"] as string;
    if (!user)
        throw new HttpError(401);


    const workstream = (req.query.workstream || default_workstream) as string;
    const workstream_name = (req.query.workstream_name || default_pageType) as string;

    const pageType = parsePageType(workstream_name);

    let include = typeof req.query.include === "string" ? req.query.include.split(",") : undefined;
    let exclude = typeof req.query.exclude === "string" ? req.query.exclude.split(",") : undefined;

    const cacheKey = getCacheKey(workstream, pageType);

    if (workstream === 'undefined' && workstream_name === 'undefined') {
        const result = await autorunDefault(include, exclude);
        if (!result) {
            return null;
        }

        const { item, workstream: itemWorkstream } = result;
        const sellerTemplate = await loadTemplate(item.template_path);
        const genericTemplate = await loadGenericTemplate();
        const template = { ...sellerTemplate, actions: [...sellerTemplate.actions, ...genericTemplate.actions] };

        item.pending = new Date().valueOf();
        item.user = user;
        await insertAutorunLog("serve", user, { id: item.page_id, url: item.capture_url });

        switch (item.page_type) {
            case 'search':
                return {
                    ...template,
                    id: item.page_id,
                    url: template?.url?.replace(/[{}`]/g, ''),
                    params: {
                        search: item.sf_search
                    },
                    workstream: itemWorkstream,
                };
            case 'category':
            case 'category_product':
                return {
                    ...template,
                    id: item.page_id,
                    url: item.capture_url,
                    workstream: itemWorkstream,
                };
            case 'product':
            default:
                return {
                    ...template,
                    id: item.page_id,
                    url: item.capture_url,
                    workstream: itemWorkstream,
                };
        }
    } else {
        await updateCache(workstream, pageType);
    }

    let rows = cache[cacheKey].buffer.filter(row => !row.posted && !row.pending);

    if (include) {
        rows = rows.filter(row => include?.includes(row.domain_name));
    } else if (exclude) {
        rows = rows.filter(row => !exclude?.includes(row.domain_name));
    }

    if (rows.length === 0) {
        return null;
    }

    const [row] = rows;

    // const confluxSelectors = await getSelectorConfig(REDIS_DOMAIN_WTB_CONFIG_KEY, `${row.domain_name.replace('.ca', '.com')}`)

    // let confluxTemplate;
    // if (confluxSelectors && pageType === "product")
    //      /* This line of code is fetching selector configuration data for a specific domain from a Redis
    //      database. */
    //      confluxTemplate = createConfluxTemplate(confluxSelectors)

    const sellerTemplate = await loadTemplate(row.template_path);
    const genericTemplate = await loadGenericTemplate();
    const template = { ...sellerTemplate, actions: [...sellerTemplate.actions, ...genericTemplate.actions] };

    // const confluxActions = confluxTemplate?.actions ? confluxTemplate.actions : "";
    // const productTemplate = {...confluxTemplate, actions: [...genericTemplate.actions, ...confluxActions]}


    row.pending = new Date().valueOf();
    row.user = user;
    await insertAutorunLog("serve", user, { id: row.page_id, url: row.capture_url });

    switch (pageType) {
        case 'search':
            return {
                ...template,
                id: row.page_id,
                url: template?.url?.replace(/[{}`]/g, ''),
                params: {
                    search: row.sf_search
                },
                workstream,
            };
        case 'category':
        case 'category_product':
            return {
                ...template,
                id: row.page_id,
                url: row.capture_url,
                workstream,
            };
        case 'product':
        default:
            return {
                ...template,
                id: row.page_id,
                url: row.capture_url,
                workstream,
            };
    }
}));

router.post("/", (req, res) => handleAsyncRequest(req, res, async () => {
    const user = req.headers["x-username"] as string;

    if (!req.body.data)
        throw new HttpError(400, "Missing data object.");

    if (!user)
        throw new HttpError(401);

    const { id } = req.body;
    if (!id)
        throw new HttpError(400);

    const workstream = (req.query.workstream || default_workstream) as string;
    const workstream_name = (req.query.workstream_name || default_pageType) as string;

    const pageType = parsePageType(workstream_name);

    let cacheKey: string | undefined;
    let row: QueueItem | undefined;

    if (workstream === 'undefined' && workstream_name === 'undefined') {
        cacheKey = Object.keys(cache).find(key =>
            cache[key]?.buffer?.some(item => item.page_id === id)
        );

        if (!cacheKey) {
            throw new HttpError(404, "Item not found in any cache");
        }

        row = cache[cacheKey].buffer.find(item => item.page_id === id);
    } else {
        cacheKey = getCacheKey(workstream, pageType);
        row = cache[cacheKey]?.buffer?.find(row => row.page_id === id);
    }

    if (!row)
        throw new HttpError(404);

    row.posted = new Date().valueOf();
    await insertAutorunLog("post", user, req.body);

    const sellerTemplate = await loadTemplate(row.template_path);
    const genericTemplate = await loadGenericTemplate();
    const template = { ...sellerTemplate, actions: [...sellerTemplate.actions, ...genericTemplate.actions] };

    if (row.page_type === 'product') {
        console.info(`Sending Conflux message...`);
        try {
            const message = buildConfluxResultMessage(row, req, template);
            const result = message?.result || message?.scrape_result;
            const valid = row.app_name && !!result && await validateConfluxResultData(row?.app_name, result, row.domain_name);

            if (valid) {
                const prowlMarketplace = ['scout', 'prowl'].find(item => item === row?.app_name) && (result.buybox || result.sellers);
                const routingKey = prowlMarketplace ? 'engine.hac.scout.results.marketplace' : ConfluxRoutingKeys[row.app_name];
                if (!routingKey) {
                    throw new Error(`problem resolving routingKey.`);
                }

                if (process.env.NODE_ENV === "development") {
                    console.info(`successful hac capture for page_id ${row.page_id} - publishing to Conflux.`);
                }
                const exchangeParams = {
                    url: process.env.PRICESPIDER_CONFLUX_RABBIT_URL!,
                    exchangeName: "external",
                    exchangeType: "topic",
                    routingKey,
                    buffer: Buffer.from(JSON.stringify(message))
                }
                
                console.info(`Conflux exchange params: ${exchangeParams.url} ${exchangeParams.routingKey}`)
                await rabbit.publishToExchange(exchangeParams);
            } else {
                throw new Error(`unsuccessful hac capture for page_id ${row.page_id} - hac capture did not pass the application result data validity check. Results will NOT be sent to Conflux.`);
            }
        } catch (error) {
            if (process.env.NODE_ENV === "development") {
                console.error(error);
            }
            insertAutorunLog("conflux-error", user, { id: row.page_id, url: row.capture_url, request: req.body, error: error });
            return { id, ok: false };
        } finally {
            insertAutorunLog("conflux-success", user, { id: row.page_id, url: row.capture_url, request: req.body });
        }
    } else {
        try {
            if (row.page_type === 'search') {
                row.capture_url = processSearchUrls(row, template?.url, String(row.sf_search))
                console.info(`Capture url processed for search_page...`);
            }
            console.info(`Writing to brand monitor...`)
            await insertSyphonxData(row, req.body.data);
        } catch (error) {
            insertAutorunLog("bm-error", user, { id: row.page_id, url: row.capture_url, request: req.body, error: error });
            throw new Error(`unsuccessful hac capture for page_id ${row.page_id} - Failed to insert brand monitor data.`);
        } finally {
            insertAutorunLog("bm-success", user, { id: row.page_id, url: row.capture_url, request: req.body });
        }
    }

    return { id, ok: true };
}));

export default router;

