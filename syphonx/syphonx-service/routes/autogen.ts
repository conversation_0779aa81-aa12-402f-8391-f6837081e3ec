import express from "express";
import { handleAsyncRequest, postAutogenRequest } from "./lib.js";

const router = express.Router();

router.get("/", (req, res) => handleAsyncRequest(req, res, async () => {
    return { hello: "autogen"};
}));

router.post("/", (req, res) => handleAsyncRequest(req, res, async () => {
    const obj = await postAutogenRequest(req.body);
    return obj;
}));

export default router;
