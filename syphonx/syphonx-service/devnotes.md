
## Setup
1. Clone repo
2. Copy .env and .env.yaml to root directory

.env has GOOGLE_APPLICATION_CREDENTIALS and OPEN_API_KEY
.env.yaml has OPEN_API_KEY

## Deploy
```
yarn build
yarn deploy
```

## Cloud Config
Calling [getSignedUrl](https://googleapis.dev/nodejs/storage/latest/Bucket.html#getSignedUrl) API from a cloud function requires the following additional configuration...
1. Enable [Identity and Access Management (IAM) API](https://console.cloud.google.com/apis/api/iam.googleapis.com/metrics)
2. Assign additional roles to **Default compute service account** from [IAM & Admin](https://console.cloud.google.com/iam-admin/iam) panel...
* `Service Account Token Creator`
* `Cloud Run Service Agent`

## References
* [Functions Framework for Node.js](https://github.com/GoogleCloudPlatform/functions-framework-nodejs)
* [Debug a HTTP function from your local machine](https://codelabs.developers.google.com/codelabs/local-development-with-cloud-functions)
* [Google Cloud Storage: Node.js Client](https://googleapis.dev/nodejs/storage/latest/Bucket.html)
