gcloud functions deploy syphonx-service \
    --gen2 \
    --entry-point app \
    --runtime nodejs20 \
    --trigger-http \
    --allow-unauthenticated \
    --memory 1GB \
    --min-instances 0 \
    --max-instances 1 \
    --region us-central1 \
    --project ps-bigdata \
    --set-secrets PRICESPIDER_SYPHONX_API_KEY=PRICESPIDER_SYPHONX_API_KEY:1 \
    --set-secrets OPENAI_API_KEY=OPENAI_API_KEY:2 \
    --set-secrets PRICESPIDER_CONFLUX_SCREENSHOT_S3_REGION=PRICESPIDER_CONFLUX_SCREENSHOT_S3_REGION:1 \
    --set-secrets PRICESPIDER_AWS_ACCESS_KEY_ID=PRICESPIDER_AWS_ACCESS_KEY_ID:1 \
    --set-secrets PRICESPIDER_AWS_SECRET_ACCESS_KEY=PRICESPIDER_AWS_SECRET_ACCESS_KEY:1 \
    --set-secrets PRICESPIDER_CONFLUX_SCREENSHOT_S3_BUCKET=PRICESPIDER_CONFLUX_SCREENSHOT_S3_BUCKET:1 \
    --set-secrets PRICESPIDER_CONFLUX_RABBIT_URL=PRICESPIDER_CONFLUX_RABBIT_URL:1
