{"name": "syphonx-service", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"tsc": "tsc -project tsconfig.json", "build": "tsc", "clean": "bash clean.sh", "start": "node --inspect node_modules/.bin/functions-framework --target=app --port=8081", "deploy": "bash deploy.sh"}, "keywords": [], "author": "", "dependencies": {"@aws-sdk/client-s3": "^3.609.0", "@aws-sdk/s3-request-presigner": "^3.609.0", "@google-cloud/bigquery": "^6.1.0", "@google-cloud/datastore": "^8.2.2", "@google-cloud/functions-framework": "^3.3.0", "@google-cloud/pubsub": "^4.0.6", "@google-cloud/storage": "^7.6.0", "amqplib": "^0.10.4", "async-parallel": "^1.2.3", "body-parser": "^1.20.1", "dotenv": "^16.0.3", "express": "^4.18.2", "morgan": "^1.10.0", "nanoid": "^5.0.7", "openai": "^3.2.1", "redis": "^4.7.0", "syphonx-lib": "^1.2.75", "typescript": "^4.9.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/amqplib": "^0.10.5", "@types/morgan": "^1.9.3", "@types/node": "^18.11.9", "@types/uuid": "^9.0.6"}}