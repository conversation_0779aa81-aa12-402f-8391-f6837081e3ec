import { createClient } from "redis";

const redisServer = {
    url: process.env.REDIS_URL || 'localhost:7000',
};

export const REDIS_DOMAIN_WTB_CONFIG_KEY = "config:domain:pricespider";

class RedisSelectors {
    // Note, this key was originally just WTB engine and paths. Engine should now be used from `engines:...`,
    // and these paths are likely to be shared with PROWL
    public rawData: string | undefined;
    public engine: string | undefined;
    public paths: any;

    constructor(redisData?: string) {
        if (!redisData) return;
        this.rawData = redisData;
        const redis = JSON.parse(redisData);
        // eslint-disable-next-line guard-for-in
        for (const key in redis) {
            const data = redis[key];
            switch (key) {
                case "engine":
                    this.engine = data;
                    break;
                case "paths":
                case "paths.old":
                    this.paths = data;
                    break;
                default:
                    break;
            }
        }
    }

    public formatForExport(): string {
        const self: any = { engine: this.engine };
        if (this.paths) {
            if (this.engine?.endsWith(".syphonx")) self["paths.old"] = this.paths;
            else self.paths = this.paths;
        }
        return JSON.stringify(self, null, 2);
    }
}

export async function getSelectorConfig(key: string, domain: string): Promise < any > {
    try {
        const client = createClient(redisServer);
        client.on("error", (err: any) => console.log("Redis:getHashKeyData error", err));
        await client.connect();
        const value = await client.hGet(key, domain);
        await client.disconnect();
        const selectorConfig = new RedisSelectors(value);
        return {
            domain,
            paths: selectorConfig.paths
        }
    } catch(e) {
        console.log(`REDIS:getHashKeyData[${key}:${domain}] exception`, e);
    }
}