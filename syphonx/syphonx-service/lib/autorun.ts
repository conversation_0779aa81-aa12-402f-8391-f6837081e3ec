import * as bigquery from "./bigquery.js";

export interface AutorunPage {
    domain_name: string;
    account_key: string;
    country_code: string;
    sku: string;
    page_id: string;
    page_type: string;
    capture_url: string;
    //capture_age: number;
    template_path: string;
    //n: number;
    seller_id: number;
    category_id: string;
    category_name: string;
    schedule: string;
    schedule_interval: string;
    week: string;
    task: string;

    sf_tags: string[] | null;
    sf_category: string | null;
    sf_search_group: string | null;
    sf_search: string | null;
    sf_group_name: string | null;
    sf_seller_id: number | null;

    cf_tags: string[] | null
    cf_category_id: string | null;
    cf_category_name: string | null;
    cf_schedule: string | null;
    cf_schedule_interval: string | null;
    cf_week: string | null;
    cf_task: string | null;
    cf_seller_id: number | null;

    page_number: number;
    targets: string;
    app_name: string;
    correlation_id: string;
    subscription_id: number;
    instruction_id: number;
    frequency?: number;
    PSMeta?: string;
    subscriptions?: string;
}

interface Workstream {
    workstream_id: string;
    workstream_name: string;
}


function pageTypeClause(pageType: string) {
    if (pageType === "default") {
        return `WHERE page_type IN ('search', 'product', 'category', 'category_product')`
    } else {
        return `WHERE page_type = '${pageType}'`
    }
}

export async function queryAutorunQueue(workstream: string, pageType: string, include?: string[], exclude?: string[]): Promise<AutorunPage[]> {
    console.log('Querying autorun queue for workstream', workstream, 'and page type', pageType);

    let query = `SELECT
            domain_name,
            page_type,
            app_name,
            account_key,
            country_code,
            page_id,
            capture_url,
            capture_age,
            template_path,
            
            product_fields.sku as sku,
            product_fields.correlation_id as correlation_id,
            product_fields.subscription_id as subscription_id,
            product_fields.instruction_id as instruction_id,
            product_fields.frequency as frequency,
            product_fields.PSMeta as PSMeta,
            product_fields.subscriptions as subscriptions,

            search_fields.tags as sf_tags,
            search_fields.name as sf_name,
            search_fields.category as sf_category,
            search_fields.search_group as sf_search_group,
            search_fields.search as sf_search,
            search_fields.group_name as sf_group_name,
            search_fields.group_id as sf_group_id,
            search_fields.seller_id as sf_seller_id,
            search_fields.search_phrase as sf_search_phrase,
            search_fields.schedule as sf_schedule,
            search_fields.search_key as sf_search_key,
            search_fields.schedule_interval as sf_schedule_interval,

            category_fields.category_id as cf_category_id,
            category_fields.category_name as cf_category_name,
            category_fields.schedule as cf_schedule,
            category_fields.schedule_interval as cf_schedule_interval,
            category_fields.week as cf_week,
            category_fields.task as cf_task,
            category_fields.seller_id as cf_seller_id,
            category_fields.page_number as cf_page_number,

            CASE
                WHEN page_type = '${pageType}' THEN search_fields.seller_id
                WHEN page_type = '${pageType}' THEN category_fields.seller_id
                ELSE NULL
            END as seller_id

        FROM syphonx.autorun_produce('${workstream}')
        ${pageTypeClause(pageType)}`


    if (include && include.length === 1) {
        query += ` AND domain_name = '${include[0]}' ORDER BY capture_age LIMIT 1000;`
    } else if (include && include.length > 1) {
        query += include && ` AND domain_name IN (${include.map(i => `'${i}'`).join(',')}) ORDER BY capture_age LIMIT 1000;`
    } else if (exclude && exclude.length === 1) {
        query += ` AND domain_name != '${exclude[0]}' ORDER BY capture_age LIMIT 1000;`
    } else if (exclude && exclude.length > 1) {
        query += exclude && ` AND domain_name NOT IN (${exclude.map(e => `'${e}'`).join(',')}) ORDER BY capture_age LIMIT 1000;`
    } else {
        query += ` LIMIT 1000;`
    }

    console.info(`Query text ${query}`);

    let rows = await bigquery.query<AutorunPage>(query);
    return rows;
}

export async function queryWorkstreams(): Promise<Workstream[]> {
    const query = `SELECT workstream_id, workstream_name FROM syphonx.autorun_workstreams WHERE active`
    const rows = await bigquery.query<Workstream>(query);
    console.log('Workstreams query', query);
    return rows;
}

export async function insertAutorunLog(key: string, user: string | undefined, data: {}) {
    await bigquery.insert("syphonx", "autorun_log", { key, user, timestamp: new Date(), data: JSON.stringify(data) });
}
