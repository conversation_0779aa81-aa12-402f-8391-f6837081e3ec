import * as amqp from "amqplib";

interface PublishParams {
    url: string;
    exchangeName: string;
    exchangeType: string;
    routingKey: string;
    buffer: Buffer;
}

export async function publishToExchange(params: PublishParams) {
    const { url, exchangeName, exchangeType, routingKey, buffer } = params;
    const connection = await amqp.connect(url);
    const channel = await connection.createChannel();
    await channel.assertExchange(exchangeName, exchangeType, { durable: true });
    channel.publish(exchangeName, routingKey, buffer);
}
