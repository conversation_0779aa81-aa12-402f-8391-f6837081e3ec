import { BigQuery } from "@google-cloud/bigquery";
import { PubSub } from "@google-cloud/pubsub";
import { InvalidArgError } from "./utilities.js";

const bigquery = new BigQuery();
const pubsub = new PubSub();

export interface ReselectOptions {
    captureId?: string;
    accountKey?: string;
    sellerId?: number;
}

const MAX_MESSAGES = 100;

// REVIEW: https://console.cloud.google.com/functions/details/us-central1/syphonx-recapture?env=gen2&project=ps-bigdata&tab=source
export async function reselect({ captureId, accountKey, sellerId }: ReselectOptions): Promise<void> {
    if (!captureId && !sellerId)
        throw new InvalidArgError("captureId or sellerId not specified");

    await bigquery
        .dataset("syphonx")
        .table("reselect_log")
        .insert({
            timestamp: new Date(),
            capture_id: captureId,
            seller_id: sellerId,
            account_key: accountKey
        });

    if (captureId) {
        const topic = pubsub.topic("select");
        const message = { id: captureId, store: true };
        const data = Buffer.from(JSON.stringify(message));
        await topic.publishMessage({ data });
    }
    else if (sellerId) {
        const query = `
            SELECT capture_id
            FROM brand_monitor.product_page_captures
            WHERE
            DATE(capture_date)>=DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
                AND seller_id=@sellerId
                ${accountKey ? `AND account_key=@accountKey` : ""}
            QUALIFY ROW_NUMBER() OVER (PARTITION BY account_key, country_code, seller_id, sku ORDER BY COALESCE(select_date, capture_date) DESC) = 1
        `;
        const [job] = await bigquery.createQueryJob({ query, params: { accountKey, sellerId } });
        const [rows] = await job.getQueryResults() || [];
        if (rows?.length) {
            const publishOptions = {
                batching: {
                    maxMessages: MAX_MESSAGES,
                    maxMilliseconds: 1000
                }
            };
            const topic = pubsub.topic("select", publishOptions);
            while (rows?.length) {
                await Promise.all(rows.splice(0, MAX_MESSAGES).map(async (row) => {
                    const message = { id: row.capture_id, store: true };
                    const data = Buffer.from(JSON.stringify(message));
                    await topic.publishMessage({ data });
                }));
            }
        }
    }
}