import { SyphonXApi, Template } from "syphonx-lib";

export async function loadTemplate(name: string): Promise<Template> {
    try {
        const api = new SyphonXApi(process.env.PRICESPIDER_SYPHONX_API_KEY, { url: process.env.SYPHONX_API_URL, appVersion: "pricespider" });
        const { template } = await api.loadTemplate(name);
    
        if (!template.timeout)
            template.timeout = 5;
    
        return template;
    } catch (e) {
        return { actions: [], timeout: 5 };
    }
}

export async function loadGenericTemplate(): Promise<Template> {
    const api = new SyphonXApi(process.env.PRICESPIDER_SYPHONX_API_KEY, { url: process.env.SYPHONX_API_URL, appVersion: "pricespider" });
    const { template } = await api.loadTemplate(`pricespider/generic/product_page.json`);

    if (!template.timeout)
        template.timeout = 5;
    
    return template;
}
