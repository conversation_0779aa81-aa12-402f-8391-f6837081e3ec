import { queryRequests, AuditRequest } from "./request.js";
import { insertAuditResponse, AuditResponse } from "./response.js";
import { datauriToBuffer } from "../datauri.js";
import { formatDate } from "../format.js";
import { writeStorageFile } from "../storage.js";

export { AuditRequest } from "./request.js";

interface AuditQueue {
    audits?: AuditRequest[];
    timestamp: number;
    ttl: number;
}

const seconds = 1000;
const minutes = 60 * seconds;
const queueTTL = 15 * minutes;
const emptyTTL = 10 * seconds;

const excludedSelectors = ["discontinued"];

const queue: AuditQueue = {
    timestamp: 0,
    ttl: 0
};

/**
 * Returns the next audit request to be served or null if the audit queue is empty.
 * @returns Audit request or null if no requests are available.
 */
export async function serveAuditRequest(): Promise<AuditRequest | null> {
    const elapsed = Date.now() - queue.timestamp;
    if (!queue.audits || elapsed > queue.ttl) {
        queue.audits = await queryRequests({ excludedSelectors });
        queue.timestamp = Date.now();
        queue.ttl = queue.audits.length > 0 ? queueTTL : emptyTTL;
    }
    return nextAuditRequest();
}

/**
 * Posts an audit response to the audit queue.
 * @param request The audit request response to post.
 * @returns A promise that resolves when the audit response has been posted.
 */
export async function postAuditResponse(request: AuditRequest): Promise<void> {
    const folder = `audits/${request.id}/${request.data.account_key}__${request.data.country_code}__${request.data.sku.replace(/\//g, "")}__${request.data.seller_id}`;

    if (request.html) {
        const url = await writeStorageFile(`${folder}/page.html`, request.html, "text/html");
        request.html = url;
    }

    for (const selector of request.selectors) {
        if (selector.screenshot) {
            const filename = `${folder}/${selector.name}.png`;
            const buffer = datauriToBuffer(selector.screenshot);
            const url = await writeStorageFile(filename, buffer, "image/png");
            selector.screenshot = url;
        }
    }

    const response: AuditResponse = {
        audit_id: request.data.audit_id,
        audit_date: formatDate(request.data.audit_date),
        page_id: request.data.page_id,
        audit_status: request.status || "ok",
        audit_url: request.url,
        audit_duration: request.served ? Date.now() - request.served : undefined,
        audit_comment: request.comment,
        audit_result: request.selectors.map(obj => {
            const {
                name: selector_name,
                selector: selector_path,
                status: selector_status,
                comment: selector_comment,
                html: selector_html,
                text: selector_text,
                ...metadata
            } = obj;
            return {
                selector_name,
                selector_path,
                selector_status,
                selector_comment,
                selector_html,
                selector_text,
                selector_metadata: JSON.stringify(metadata)
            };
        }),
        html: request.html,
        audited_by: "system",
        audited_at: new Date()
    };

    await insertAuditResponse(response);
    if (queue.audits) {
        const audit = queue.audits.find(audit => audit.id === request.id);
        if (audit)
            audit.posted = Date.now();
    }
}

/**
  * Determines whether an audit request is valid.
  * @param obj The audit request object.
  * @returns Returns the validated audit request or undefined if invalid.
 */
export function validateAuditRequest(obj: unknown): AuditRequest | undefined {
    if (typeof obj !== "object")
        return undefined;
    const request = obj as AuditRequest;
    if (!/^[A-Za-z0-9]{1,15}$/.test(request?.id!))
        return undefined;
    return request;
}

function nextAuditRequest(): AuditRequest | null {
    if (!queue.audits)
        return null;

    let audit = queue.audits.find(audit => !audit.served && !audit.posted);
    if (!audit)
        return null;

    audit = pivotAuditRequest(audit);
    audit.served = Date.now();
    return audit;
}

function pivotAuditRequest(audit: AuditRequest): AuditRequest {
    // todo: implement
    return audit;
}
