import * as bigquery from "../bigquery.js";

/**
 * Represents an audit record.
 */
export interface AuditRecord {
    /**
     * The unique identifier for the audit record.
     */
    audit_id: string;

    /**
     * The capture date that corresponds to the audit.
     */
    audit_date: Date;

    /**
     * The account key associated with the audit record.
     */
    account_key: string;

    /**
     * The country code associated with the audit record.
     */
    country_code: string;

    /**
     * The SKU associated with the audit record.
     */
    sku: string;

    /**
     * The name of the seller associated with the audit record.
     */
    seller_name: string;

    /**
     * The ID of the seller associated with the audit record.
     */
    seller_id: number;

    /**
     * The page ID associated with the audit record.
     */
    page_id: string;

    /**
     * The selector names associated with the audit record.
     */
    selectors: string;

    /**
     * The priority level associated with the audit record.
     */
    priority: number;

    /**
     * The time-to-live value associated with the audit record.
     */
    ttl: number;

    /**
     * The capture URL associated with the audit record.
     */
    capture_url: string;

    /**
     * The date and time when the audit record was requested.
     */
    requested_at: Date;

    /**
     * The name of the user who requested the audit record.
     */
    requested_by: string;

    /**
     * The date and time when the audit record was audited.
     */
    audited_at: Date;

    /**
     * The name of the user who audited the record.
     */
    audited_by: string;
}

/**
 * Queries the audit queue for records that have not been audited yet.
 * @returns A promise that resolves to an array of audit records.
 */
export async function queryAuditQueue(): Promise<AuditRecord[]> {
    const rows = await bigquery.query<AuditRecord>("SELECT * FROM syphonx.audit_request_queue WHERE audited_at IS NULL");
    return rows;
}
