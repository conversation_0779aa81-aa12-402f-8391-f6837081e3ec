import { queryAuditQueue, AuditRecord } from "./query.js";
import { schema } from "./schema.js";

export interface AuditRequest {
    id: string;
    name: string;
    url: string;
    selectors: AuditSelector[];
    status: string;
    comment?: string;
    html?: string;
    data: AuditRecord;
    served?: number;
    posted?: number;
}

export interface AuditSelector {
    name: string;
    type: string;
    repeated: boolean;
    selector?: string;
    status?: string;
    comment?: string;
    html?: string;
    text?: string;
    screenshot?: string;
    screenshot_rect?: DOMRect;
    selector_rect?: DOMRect[];
}

export interface QueryRequestOptions {
    excludedSelectors?: string[];
}

export async function queryRequests({ excludedSelectors = [] }: QueryRequestOptions = {}): Promise<AuditRequest[]> {
    const requests = await queryAuditQueue();
    return requests.map(request => ({
        id: request.audit_id,
        name: `${request.account_key} ${request.country_code} ${request.sku}`,
        url: request.capture_url,
        selectors: request.selectors.split(",")
            .filter(name => !excludedSelectors.includes(name))
            .map(name => ({ ...schema[name], name })),
        data: request
    } as AuditRequest));
}
