
## GET /audit
```mermaid
graph TD
A[GET /audit]
B["serveAuditRequest() <i>queue.ts</i>"]
C["queryRequests() <i>request.ts</i>"]
D["nextAuditRequest() <i>request.ts</i>"]
E["queryAuditQueue() <i>query.ts</i>"]
F["syphonx.audit_request_queue"]
G["AuditRequest <i>request.ts</i>"]

A --> B --> C --> E --> F
B --> D --> G
```

## POST /audit
```mermaid
graph TD
A[POST /audit]
B["postAuditResponse() <i>queue.ts</i>"]
C["writeStorageFile() <i>storage.ts</i>"]
D["insertAuditResponse() <i>response.ts</i>"]
E["audit_response_log"]

A --> B
B --> C
B --> D
D --> E
```

## Tables
- audit_request_queue
- audit_response_log

## Audit Record
name                      | type    | status | description
------------------------- | ------- | ------ | ---
id                        | string  | INPUT  | Unique identifier for page audit
name                      | string  | INPUT  | Name for page audit
url                       | string  | INPUT  | Page URL to audit
selectors                 | object  | -      | Selectors
selectors.name            | string  | INPUT  | Name of selector
selectors.type            | string  | INPUT  | Type of selector (string, number, boolean)
selectors.repeated        | boolean | INPUT  | Indicates if selector is repeated
selectors.selector        | string  | OUTPUT | CSS Selector of user selected page element
selectors.status          | string  | OUTPUT | User selected status (bad-page, not-found, skip, skip-all)
selectors.comment         | string  | OUTPUT | User comment
selectors.html            | string  | OUTPUT | HTML snippet of user selected page element
selectors.text            | string  | OUTPUT | Text of user selected page element
selectors.screenshot      | string  | OUTPUT | Screenshot of 
selectors.screenshot_rect | object  | OUTPUT | 
selectors.selector_rect   | object  | OUTPUT | 
status                    | string  | OUTPUT | Status output
comment                   | string  | OUTPUT | Comment output
html                      | string  | OUTPUT | HTML output
data                      | object  | -      | Raw query data
served                    | number  | -      | Timestamp of when served
posted                    | number  | -      | Timestamp of when posted
