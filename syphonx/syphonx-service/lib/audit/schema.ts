export const schema: Record<string, { type: string, repeated: boolean }> = {
    name: { type: "string", repeated: false },
    description: { type: "string", repeated: false },
    brand_name: { type: "string", repeated: false },
    price: { type: "number", repeated: false },
    model: { type: "string", repeated: false },
    vpn: { type: "string", repeated: false },
    sku: { type: "string", repeated: false },
    upc: { type: "string", repeated: false },
    in_stock: { type: "boolean", repeated: false },
    review_count: { type: "number", repeated: false },
    review_score: { type: "number", repeated: false },
    features: { type: "string", repeated: true },
    discontinued: { type: "boolean", repeated: false },
    specifications: { type: "boolean", repeated: false },
    list_price: { type: "number", repeated: false },
    was_price: { type: "number", repeated: false },
    free_shipping: { type: "boolean", repeated: false },
    sales_rank: { type: "number", repeated: false },    
    enhanced_content: { type: "boolean", repeated: false },
    promotional_offers: { type: "string", repeated: false },
    images: { type: "string", repeated: true },
    videos: { type: "string", repeated: true }
};
