import * as bigquery from "../bigquery.js";

export interface AuditResponse {
    audit_id: string;
    audit_date: string;
    page_id: string;
    audit_status: string;
    audit_url: string;
    audit_duration?: number;
    audit_comment?: string;
    audit_result: AuditResult[];
    html?: string;
    text?: string;
    audited_by: string;
    audited_at: Date;
}

export interface AuditResult {
    selector_name: string;
    selector_path?: string;
    selector_status?: string;
    selector_comment?: string;
    selector_html?: string;
    selector_text?: string;
    selector_metadata?: string;
}

export async function insertAuditResponse(response: AuditResponse): Promise<void> {
    await bigquery.insert("syphonx", "audit_response_log", response);
}
