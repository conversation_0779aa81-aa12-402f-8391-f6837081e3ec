import { BigQuery } from "@google-cloud/bigquery";

const bigquery = new BigQuery();

export async function query<T = any>(queryInput: string | { query: string; params?: Record<string, any> }): Promise<T[]> {
    try {
        let result;

        if (typeof queryInput === 'string') {
            result = await bigquery.query(queryInput);
        } else {
            result = await bigquery.query(queryInput);
        }

        const [rows] = result;

        if (!rows) return [];

        return rows.map((row: any) => formatRow(row));
    } catch (error) {
        console.error('Error executing query:', error);
        throw error;
    }
}

export async function insert(dataset: string, table: string, data: {}): Promise<void> {
    try {
        await bigquery
            .dataset(dataset)
            .table(table)
            .insert([data]);
    } catch (error) {
        console.error(JSON.stringify(error))
    }
}

function formatRow(obj: any): any {
    if (obj === null || typeof obj !== "object")
        return obj;
    if (Array.isArray(obj))
        return obj.map(formatRow);
    if (Object.keys(obj).length === 1 && typeof obj.value === "string" && /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}\.\d{3}Z)?$/.test(obj.value))
        return new Date(obj.value);
    const result: Record<string, any> = {};
    for (let key of Object.keys(obj))
        result[key] = formatRow(obj[key]);
    return result;
}
