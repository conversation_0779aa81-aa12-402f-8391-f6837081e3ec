
import { isJSDocSignature } from "typescript";
import * as bigquery from "../bigquery.js";
import { AutorunPage } from "../index.js";
import { fetchConfluxSelectorConfig } from "../ovomorph.js";

interface ConfluxValidityResult {
    valid_price: boolean;
    valid_availability: boolean;
    valid_availability_text: boolean;
    valid_product_name: boolean;
    valid_in_stock: boolean;
    valid_amstock: boolean;
    valid_data: boolean;
}

async function selectorRegex(domain_name: string) {
    try {
        const confluxSelectors = await fetchConfluxSelectorConfig(`${domain_name.replace('.ca', '.com')}`)
        const paths = confluxSelectors ? confluxSelectors?.paths : [];
        const clean: any[] = [];
        for (let p of paths) {
            if (p.clean && p.field_name) {
                clean.push({ field_name: p.field_name, regex: p.clean.replaceValues })
            }
        }
        return clean;
    } catch (error) {
        console.error('Error creating clean path regex:', error);
    }
}

function processDataWithRegex(data: any, cleanData: any) {
    cleanData.forEach((field: any) => {
        const { field_name, regex: regexes } = field;

        if (!data[field_name] || !regexes || !Array.isArray(regexes)) return;

        let currentValue = data[field_name];

        if (Array.isArray(currentValue)) {
            currentValue = currentValue.join(',');
        } else {
            currentValue = String(currentValue);
        }

        for (const regexObj of regexes) {
            if (!regexObj || !regexObj.regex) continue;

            try {
                const regex = new RegExp(regexObj.regex, 'gm');
                const match = regex.exec(currentValue);

                if (match) {
                    let result = match.length > 1 ? match[1] : match[0];
                    console.log(`RESULT PROCESS -- ${result}`)
                    data[field_name] = result;
                    break;
                }
            } catch (error) {
                console.error(`Error with regex for ${field_name}:`, error);
            }
        }
    });

    return data;
}
// validate Conflux PROWL marketplace messages...
export async function validateConfluxResultData(app_name: string, data: any, domain_name: string): Promise<boolean> {
    if (!app_name || !data) {
        return false;
    }

    try {
        // const cleanData = await selectorRegex(domain_name);
        // if (cleanData) {
        //     processDataWithRegex(data, cleanData);
        // }
        
        const sellerData = data?.sellers?.[0] || data;
        const queryObj = {
            query: `SELECT conflux.valid_conflux_data(@app_name, PARSE_JSON(@json_data)).*`,
            params: {
                app_name: app_name,
                json_data: JSON.stringify(sellerData)
            }
        };

        const rows = await bigquery.query<ConfluxValidityResult>(queryObj);

        if (rows.length === 0) {
            return false;
        }

        return rows[0]?.valid_data || false;
    } catch (error) {
        console.error('Error validating Conflux data:', error);
        return false;
    }
}


// todo: update this to reflect hac data with non-seo results
export function buildConfluxResultMessage(row: AutorunPage, req: any, template: object) {
    const capture_id = req.body.capture_id;
    const capture_date = req.body.capture_date;

    // elevate price and / or availability from seo result if present, when price and / or availability is not present in the result
    if (['scout', 'wtb'].indexOf(row.app_name) >= 0 && !req?.body?.data?.price && req?.body?.data?.seo?.value?.price?.value) {
        req.body.data.price = { value: String(req.body.data.seo.value?.price?.value) };
    }
    if (!req?.body?.data?.availability && req?.body?.data?.seo?.value?.availability?.value) {
        req.body.data.availability = { value: req.body.data.seo.value?.availability?.value };
    }

    const screenshot = req.body.screenshot;
    // const screenshot = {
    //     bucket: req.body.screenshot?.bucket,
    //     key: req.body.screenshot?.key,
    //     url: req.body.screenshot?.url
    // };

    const request_result = {
        http_response_status: 200, // get the real response code from the autorun...
        date_requested: capture_date,
        date_checked: capture_date,
        date_crawl_started: capture_date,
        url: row.capture_url,
        screenshot: screenshot.length === 1 ? screenshot[0] : screenshot
    };

    // const asset_locations = screenshot.length === 1 ? { screenshot: screenshot[0].url } : screenshot.reduce((a: { [key: string]: string }, b: { bucket: string; key: string; url: string; size: number; }, i: number) => { a[`screenshot${i}`] = b.url; return a; }, {});

    let asset_locations = { screenshot: "" }
    if (screenshot.length === 1) { // undefined can never be 1
        asset_locations = { screenshot: screenshot[0].url }
    }

    if (Array.isArray(screenshot))
        asset_locations = screenshot.reduce((a: { [key: string]: string }, b: { bucket: string; key: string; url: string; size: number; }, i: number) => { a[`screenshot${i}`] = b.url; return a; }, {})


    const message = {
        url: row.capture_url,
        uuid: capture_id,
        domain: row.domain_name,
        crawl_type: "normal",
        dates: {
            date_requested: capture_date,
            date_checked: capture_date,
            date_crawl_started: capture_date
        },
        request_result,
        request_config: { engine: "hac" },
        http_response_status: request_result.http_response_status,
        screenshot: screenshot.length === 1 ? screenshot[0] : screenshot,
        asset_locations,
        bullet: {
            meta: {
                identifier: "",
                schema: "",
                version: ""
            },
            tracing: {
                finished: capture_date,
                received: capture_date,
                started: capture_date
            }
        },
        director: {
            meta: {
                identifier: "director-01",
                schema: "v1.0.0",
                version: "v1.0.0"
            },
            tracing: {
                finished: capture_date,
                received: capture_date,
                started: capture_date
            }
        },
        capture_date,
        syphonx: {
            path: row.template_path,
            template
        },
        engine: "hac"
    } as any;

    if (row.app_name === 'wtb' && row.PSMeta) {
        message.PSMeta = JSON.parse(row.PSMeta);
        message.amqp_debug = 'reservoir.rosetta.spu';
        if (message.result?.seo) {
            delete message.result.seo;
        }
    } else {
        if (row.subscriptions) {
            message.subscriptions = JSON.parse(row.subscriptions);
        } else {
            message.subscriptions = [
                {
                    correlation_id: row.correlation_id,
                    frequency: row.frequency || 1440,
                    namespace: row.app_name === "prowl" ? "scout" : row.app_name,
                    routing_key: "product", // need 'marketplace' support 
                    subscription_id: row.subscription_id
                }
            ];
        }
        message.instruction_id = String(row.instruction_id);
    }

    const routing_key = row.subscriptions ? JSON.parse(row.subscriptions)?.[0]?.routing_key : "product";
    if (routing_key === "marketplace" && row.app_name === "prowl") {
        message.scrape_result = {
            buybox: req.body?.data?.buybox?.value.map((item: any) => { return Object.keys(item).reduce((a: any, b: any) => { a[b] = item[b]?.value || null; return a; }, {}) }),
            sellers: req.body?.data?.sellers?.value.map((item: any) => { return Object.keys(item).reduce((a: any, b: any) => { a[b] = item[b]?.value || null; return a; }, {}) })
        };
    } else {
        message.result = Object.keys(req.body.data).reduce((a: any, b: any) => { a[b] = req.body.data[b]?.value || null; return a; }, {});
    }
    return message;
}