
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

export interface S3Response {
    bucket: string;
    key: string;
    url: string;
}

const client = new S3Client({
    region: process.env.PRICESPIDER_CONFLUX_SCREENSHOT_S3_REGION!,
    credentials: {
        accessKeyId: process.env.PRICESPIDER_AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.PRICESPIDER_AWS_SECRET_ACCESS_KEY!
    }
});

export async function generateS3UploadInformation(capture_id: string, capture_date: string): Promise<S3Response> {
    const year = (new Date(capture_date)).getFullYear();
    const dayOfYear = Math.floor(((new Date(capture_date).valueOf()) - new Date(new Date(capture_date).getFullYear(), 0, 0).valueOf()) / 86400000);
    const key = `scout/micro/${year}/${dayOfYear}/${capture_id}.png`;
    const bucket = process.env.PRICESPIDER_CONFLUX_SCREENSHOT_S3_BUCKET!;

    const command = new PutObjectCommand({ Bucket: bucket, Key: key });
    const url = await getSignedUrl(client, command, { expiresIn: 60 });
    return { bucket, key, url };
}