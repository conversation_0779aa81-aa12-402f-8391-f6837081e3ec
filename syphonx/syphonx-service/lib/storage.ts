import { Storage } from "@google-cloud/storage";

const storage = new Storage();

const bucket = "ps-syphonx";

const minutes = 60 * 1000;
const expiration = 15 * minutes;

export async function getSignedUrl(filename: string): Promise<string> {
    const file = storage.bucket("ps-syphonx").file(filename);
    const [url] = await file.getSignedUrl({
        version: "v4",
        action: "write",
        expires: Date.now() + expiration
    });
    return url;
}

export type ContentType = "text/plain" | "text/html" | "image/png";

export async function writeStorageFile(filename: string, data: string | Buffer, contentType?: ContentType): Promise<string> {
    const file = storage.bucket(bucket).file(filename);
    await file.save(data, { contentType });
    return `https://storage.googleapis.com/ps-syphonx/${filename}`;
}
