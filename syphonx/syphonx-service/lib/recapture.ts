import { BigQuery } from "@google-cloud/bigquery";
import { Datastore } from "@google-cloud/datastore";
import { PubSub } from "@google-cloud/pubsub";
import { tryParse<PERSON>son, InvalidArgError, NotFoundError, UnexpectedError } from "./utilities.js";
import * as uuid from "uuid";

const bigquery = new BigQuery();
const datastore = new Datastore();
const pubsub = new PubSub();

export interface RecaptureOptions {
    captureId: string;
    url?: string;
}

// REVIEW: https://console.cloud.google.com/functions/details/us-central1/syphonx-recapture?env=gen2&project=ps-bigdata&tab=source
export async function recapture({ captureId, url }: RecaptureOptions): Promise<void> {
    const info = await loadCaptureInfo(captureId);    
    const script = await loadScript(info.script, captureId);
    const mergedTags = {
        ...(script.tags || {}),
        ...(info.tags || {}),
        // targets is required to get results into the specified dataset.table... (why?)
        targets: script?.targets?.length ? [script.targets[0].name] : ['brand-monitor'] // needed to pass 'shouldInsertTarget' in Venom... corresponds to scriptJson.name
    };

    const tags = Object.keys(mergedTags).reduce((a, b) => { a[b.replace(/[A-Z]/g, c => `_${c.toLowerCase()}`)] = mergedTags[b]; return a; }, {} as Record<string, string>);
    if (!tags?.product_id || !tags?.seller_id)
        throw new Error(`tags do not hold product_id and or seller_id for originalCaptureId ${captureId}... cannot recapture ${captureId}`);

    const recaptureId = uuid.v4();

    await bigquery
        .dataset("syphonx")
        .table("recapture_log")
        .insert({
            timestamp: new Date(),
            capture_id: recaptureId,
            original_capture_id: captureId,
            script: info.script,
            tags: JSON.stringify(tags),
            url: script.url || url // from datastore
        });

    const topic = pubsub.topic("capture");
    const message = {
        id: recaptureId,
        store: true,
        script: info.script,
        tags,
        url: script.url || url,
        // targets: scriptJson.targets // get this working!!! // needs target.name?
    };

    const data = Buffer.from(JSON.stringify(message));
    await topic.publishMessage({ data });
}

function validateDatastoreObject(name: string, data: unknown, properties: string[]): void {
    if (!(data instanceof Array) || data.length < 1 || !data[0]) {
        throw new NotFoundError(`"${name}" not found in script library`);
    }
    if (typeof data[0] !== "object" || data[0] === null) {
        throw new InvalidArgError(`"${name}" invalid content in script`);
    }
    for (const p of properties) {
        if (p) {
            if (typeof data[0][p] !== "string") {
                throw new InvalidArgError(`"${name}" invalid property ${p}`);
            }
        }
    }
}

async function loadCaptureInfo(captureId: string) {
    const key = datastore.key(["captures", captureId]);
    const result = await datastore.get(key);
    const [info] = result;
    if (!info.script)
        throw new UnexpectedError(`could not resolve script from captureId ${captureId}`);
    return info;
}

async function loadScript(name: string, captureId: string): Promise<Record<string, any>> {
    const data = await datastore.get(datastore.key(["scripts", name]));
    validateDatastoreObject(name, data, ["content"]);
    const { content } = data[0];
    const result = tryParseJson(content);
    if (result === undefined)
        throw new UnexpectedError(`Invalid JSON data in script ${name} for capture ${captureId}`);
    return result;
}
