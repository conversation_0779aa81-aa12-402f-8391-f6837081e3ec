import * as bigquery from "../bigquery.js";
import { v4 as uuidv4 } from "uuid";

interface CategoryConfig {
    seller_id: number | null;
    category_id: string | null;
    category_name: string | null;
}


interface SearchProduct {
    vpn: string;
    name: string;
    brand_name: string;
    product_url: string;
    image_url: string[];
    review_score: number;
    review_count: number;
    price: number;
    was_price: number | null;
    in_stock: boolean;
    sponsored: boolean;
    page: number;
}

interface CategoryProduct {
    vpn: string;
    name: string;
    product_url: string;
    image_url: string[];
    review_score: number | null;
    review_count: number | null;
    price: number;
    best_seller: boolean;
    sponsored: boolean;
    free_shipping: boolean;
    options: boolean;
    addon_item: boolean;
    coupon_savings: number | null;
    add_to_cart: boolean;
    checkout_savings: number | null;
    subscribe_save: boolean;
    page: number;
    addtolist_badge: boolean;
    member_only: boolean;
    rebates: boolean;
}

interface SyphonxData {
    sponsored_by: {
        nodes: string[];
        key: string;
        value: string[];
    };
    search_result_count: {
        nodes: string[];
        key: string;
        value: string[];
    };
    brands: {
        nodes: string[];
        key: string;
        value: string[];
    };
    product_count: {
        nodes: string[];
        key: string;
        value: number;
    };
    page_count: {
        nodes: string[];
        key: string;
        value: number;
    };
    category_name: {
        nodes: string[];
        key: string;
        value: string;
    };
    products: {
        nodes: string[];
        key: string;
        value: Array<any>;
    };
    search_term: {
        nodes: string[];
        key: string;
        value: string;
    };
    mpn: {
        nodes: string[];
        key: string;
        value: string;
    };
    vpn: {
        nodes: string[];
        key: string;
        value: string;
    };
    name: {
        nodes: string[];
        key: string;
        value: string;
    };
}

interface MetaData {
    name: string;
    domain_name: string;
    page_type: string;
    app_name: string;
    account_key: string;
    country_code: string;
    seller_id: number;
    sku: string | null;
    page_id: string;
    capture_url: string;
    capture_age: string | null;
    template_path: string;
    correlation_id: string | null;
    subscription_id: string | null;
    instruction_id: string | null;

    frequency: string | null;
    PSMeta: string | null;
    pending: number;
    posted: number;

    cf_seller_id: number;
    cf_category_id: string;
    cf_category_name: string | null;
    cf_user: string;
    cf_schedule_interval: string | null;
    cf_schedule: string | null;
    cf_task: string | null;
    cf_tags: string | null;
    cf_proxy: string | null;
    cf_page_number: number | null;

    targets: string[] | null;
    sf_name: string | null;
    sf_seller_id: number | null;
    sf_proxy: string | null;
    sf_zip_code: number | null;
    sf_group_id: number | null;
    sf_search_key: string | null;
    sf_group_name: string | null;
    sf_search_group: string | null;
    sf_search_phrase: string | null;
    sf_search_category: string | null;
    sf_search: string | null;
    sf_category: string | null;

    sf_tags: string | null;
    sf_sizzler: boolean | null;

}

interface CaptureData {
    capture_id: string;
    capture_date: string;
    select_date: string | null;
    capture_url: string;
    data: string;
    tags: string;
    account_key: string;
    country_code: string;
    seller_id: number;
    page_number: number;
    week: string;
}



/**
 * Gets the start of the week (Monday) for a given date,
 * with an option to go back a specified number of weeks
 * 
 * @param date The reference date
 * @param weeksBack Number of weeks to go back (default: 0)
 * @returns ISO date string (YYYY-MM-DD) for the start of the week
 */
function getStartOfWeek(date: Date, weeksBack: number = 0): string {
    const d = new Date(date);
    d.setUTCHours(0, 0, 0, 0);

    const day = d.getUTCDay();

    const diff = d.getUTCDate() - day + (day === 0 ? -6 : 1);
    d.setUTCDate(diff);

    if (weeksBack > 0) {
        d.setUTCDate(d.getUTCDate() - (weeksBack * 7));
    }

    return d.toISOString().slice(0, 10);
}

function transformSearchProduct(product: any): SearchProduct {
    // Ensure image_url is always an array
    const imageUrls = product?.image_url?.value ?
        (Array.isArray(product.image_url.value) ? product.image_url.value : [product.image_url.value]) :
        [];

    return {
        vpn: product?.vpn?.value,
        name: product?.name?.value,
        brand_name: product?.brand_name?.value,
        product_url: product?.product_url?.value,
        image_url: imageUrls,
        review_score: product?.review_score?.value ? Number(product.review_score.value) : 0,
        review_count: product?.review_count?.value ? Number(product.review_count.value) : 0,
        price: product?.price?.value ? Number(product.price.value) : 0,
        was_price: product?.was_price?.value ? Number(product.was_price.value) : null,
        in_stock: product?.in_stock?.value !== undefined ? Boolean(product.in_stock.value) : true,
        sponsored: product?.sponsored?.value ? Boolean(product.sponsored.value) : false,
        page: product?.page?.value ? Number(product.page.value) : 1
    };
}

function transformCategoryProduct(product: any): CategoryProduct {
    if (!product) {
        throw new Error('CategoryProduct data is null or undefined');
    }
    return {
        vpn: product?.vpn?.value,
        name: product?.name?.value,
        product_url: product?.product_url?.value,
        image_url: product?.image_url?.value,
        review_score: product?.review_score?.value,
        review_count: product?.review_count?.value,
        price: product?.price?.value,
        best_seller: product?.best_seller?.value,
        sponsored: product?.sponsored?.value,
        free_shipping: product?.free_shipping?.value,
        options: product?.options?.value,
        addon_item: product?.addon_item?.value,
        coupon_savings: product?.coupon_savings?.value,
        add_to_cart: product?.add_to_cart?.value,
        checkout_savings: product?.checkout_savings?.value,
        subscribe_save: product?.subscribe_save?.value,
        page: product?.page?.value,
        addtolist_badge: product?.addtolist_badge?.value || null,
        rebates: product?.rebates?.value || null,
        member_only: product?.member_only?.value || null,
    };
}

function transformProducts(products: any[], isSearch: boolean): (CategoryProduct | SearchProduct)[] {
    try {
        if (!Array.isArray(products)) {
            console.error('Products is not an array:', products);
            return [];
        }

        return products
            .map((p: any): CategoryProduct | SearchProduct | null => {
                try {
                    return isSearch ? transformSearchProduct(p) : transformCategoryProduct(p);
                } catch (productError) {
                    console.error('Error transforming individual product:', productError);
                    return null;
                }
            })
            .filter((p): p is CategoryProduct | SearchProduct => p !== null);
    } catch (error) {
        console.error('Error in transformProducts:', error);
        return [];
    }
}

function createCategoryItem(syphonxData: SyphonxData): string {
    const brands = syphonxData?.brands?.value || [];
    const productCount = syphonxData?.product_count?.value || 0;
    const pageCount = syphonxData?.page_count?.value || 1;
    const categoryName = syphonxData?.category_name?.value;
    const products = syphonxData?.products?.value || [];
    const result = {
        brands,
        video_buying_guides: null,
        category_name: categoryName,
        product_count: productCount,
        page_count: pageCount,
        products: transformProducts(products, false)
    };

    return JSON.stringify(result);
}

function createSearchItem(syphonxData: SyphonxData): string {
    const brands = syphonxData?.brands?.value || [];
    const pageCount = syphonxData?.page_count?.value || 1;
    const searchTerm = syphonxData?.search_term?.value;
    const products = syphonxData?.products?.value || [];
    const search_result_count = products.length || 0;
    const sponsored_by = syphonxData?.sponsored_by?.value || null;

    const result = {
        search_result_count,
        sponsored_by,
        brands,
        search_term: searchTerm,
        page_count: pageCount,
        products: transformProducts(products, true)
    };

    return JSON.stringify(result);
}

/** Workaround for missing category names */
async function fixMissingCategoryData(metaData: MetaData): Promise<string | null> {
    try {
        if (!metaData.cf_category_id)
            return null;

        const [row] = await bigquery.query<CategoryConfig>(
            `SELECT category_name FROM brand_monitor.category_config WHERE category_id = ${metaData.cf_category_id} LIMIT 1;`
        );

        return row?.category_name;
    } catch (error) {
        throw new Error(`Failed to get category name: ${error}`);

    }
}

/** Creates Category Data for inserting into BigQuery capture table */
async function createBigQueryCategoryData(syphonxData: SyphonxData, metaData: MetaData): Promise<CaptureData> {
    try {
        if (!metaData.cf_category_name) {
            console.info(`Missing category name for ${metaData.cf_category_id}, fetching`)
            const category_name = await fixMissingCategoryData(metaData);
            metaData.cf_category_name = category_name;
        }

        const evaluateSellerIds = metaData.seller_id ? Number(metaData.seller_id) : metaData.sf_seller_id ? Number(metaData.sf_seller_id) : metaData.cf_seller_id ? Number(metaData.cf_seller_id) : 0

        if (!metaData.cf_page_number) {
            throw new Error(`Data missing for proper insert, page_number: ${metaData.cf_page_number} is undefined`);
        }

        return {
            capture_id: uuidv4(),
            capture_date: new Date().toISOString().replace('T', ' ').replace(/\.\d+Z$/, ' UTC'),
            select_date: null,
            capture_url: metaData?.capture_url,
            data: createCategoryItem(syphonxData),
            tags: JSON.stringify({
                name: "category-page",
                task: metaData?.cf_task,
                week: getStartOfWeek(new Date()),
                proxy: "hac",
                library: "syphonx-hac",
                targets: metaData?.targets || ['category-page'],
                schedule: "hac",
                seller_id: evaluateSellerIds,
                account_key: metaData?.account_key,
                category_id: metaData?.cf_category_id,
                page_number: Number(metaData?.cf_page_number),
                country_code: metaData?.country_code || "US",
                category_name: metaData?.cf_category_name,
                schedule_interval: "hac",
            }),
            account_key: metaData?.account_key,
            country_code: metaData?.country_code || "US",
            seller_id: evaluateSellerIds,
            page_number: 1,
            week: getStartOfWeek(new Date())
        };
    } catch (error) {
        throw new Error(`Failed to convert source data: ${error}`);
    }
}

/** Creates search page data in the correct format for insertion into the search_page_captures table */
async function createBigQuerySearchData(syphonxData: SyphonxData, metaData: MetaData) {
    const evaluateSellerIds = metaData.seller_id ? Number(metaData.seller_id) : metaData.sf_seller_id ? Number(metaData.sf_seller_id) : metaData.cf_seller_id ? Number(metaData.cf_seller_id) : 0
    let sfTags = [];
    if (metaData?.sf_tags) {
        try {
            sfTags = JSON.parse(`[${metaData.sf_tags}]`);
        } catch (e) {
            console.error(e)
            sfTags = metaData?.sf_tags
                .replace(/\\"/g, '')
                .replace(/"/g, '')
                .split(',')
                .map(tag => tag.trim());
        }
    }

    return {
        capture_id: uuidv4(),
        capture_date: new Date().toISOString().replace('T', ' ').replace(/\.\d+Z$/, ' UTC'),
        select_date: null,
        capture_url: metaData.capture_url,
        data: createSearchItem(syphonxData),
        tags: JSON.stringify({
            name: metaData?.sf_name,
            account_key: metaData?.account_key,
            targets: ["search-page"],
            library: "syphonx-hac",
            country_code: metaData?.country_code,
            zip_code: metaData?.sf_zip_code,
            group_id: metaData?.sf_group_id,
            seller_id: evaluateSellerIds,
            search_key: metaData?.sf_search_key,
            group_name: metaData?.sf_group_name,
            search_group: metaData?.sf_search_group,
            search_phrase: metaData?.sf_search_phrase,
            search_category: metaData?.sf_search_category,
            search: metaData?.sf_search,
            category: metaData?.sf_name,
            tags: sfTags || [""],
            schedule: "hac",
            schedule_interval: "hac",
            sizzler: metaData?.sf_sizzler || false,
            proxy: metaData?.sf_proxy,
            geoLocation: null,
            pages: 1
        }),
        account_key: metaData?.account_key,
        country_code: metaData?.country_code,
        seller_id: metaData?.seller_id,
    }
}

/** Creates category product page data in the correct format for insertion into the category_product_page_captures table */
async function createBigQueryCategoryProductData(syphonxData: SyphonxData, metaData: MetaData): Promise<any> {
    try {
        const evaluateSellerIds = metaData.seller_id ? Number(metaData.seller_id) : metaData.sf_seller_id ? Number(metaData.sf_seller_id) : metaData.cf_seller_id ? Number(metaData.cf_seller_id) : 0;

        // Extract product data from syphonxData
        const productData = {
            mpn: syphonxData?.mpn?.value || null,
            vpn: syphonxData?.vpn?.value || null,
            name: syphonxData?.name?.value || null
        };

        return {
            capture_id: uuidv4(),
            capture_date: new Date().toISOString().replace('T', ' ').replace(/\.\d+Z$/, ' UTC'),
            select_date: null,
            capture_url: metaData?.capture_url,
            data: JSON.stringify(productData),
            tags: JSON.stringify({
                name: "category-product-page",
                task: "syphonx-hac",
                proxy: "syphonx-hac",
                source: "wtb",
                library: "puppeteer",
                schedule: "category-product-page",
                seller_id: evaluateSellerIds,
                account_key: metaData?.account_key,
                country_code: metaData?.country_code || "US",
                category_name: metaData?.cf_category_name,
                schedule_interval: metaData?.cf_schedule_interval || "thursday_3p"
            }),
            account_key: metaData?.account_key,
            country_code: metaData?.country_code || "US",
            seller_id: evaluateSellerIds,
        };
    } catch (error) {
        throw new Error(`Failed to convert source data: ${error}`);
    }
}

async function processSyphonxDataForBigQuery(
    syphonxData: SyphonxData,
    metaData: MetaData
): Promise<any> {
    try {
        switch (metaData.page_type) {
            case 'category':
                console.info(`Creating category data...`)
                const bqCategory = await createBigQueryCategoryData(syphonxData, metaData);
                return bqCategory;
            case 'search':
                console.info(`Creating search data...`)
                const bqSearch = await createBigQuerySearchData(syphonxData, metaData);
                return bqSearch;
            case 'category_product':
                console.info(`Creating category product data...`)
                const bqCategoryProduct = await createBigQueryCategoryProductData(syphonxData, metaData);
                return bqCategoryProduct;
            default:
                console.info(`No data created...`)
        }
    } catch (error) {
        console.error('Error processing data:', error);
        throw error;
    }
}

export const insertSyphonxData = async (row: any, data: any) => {
    try {
        const formatted_data = await processSyphonxDataForBigQuery(data, row);
        
        let bqTables = "";
        switch (row.page_type) {
            case 'category':
                bqTables = "category_page_captures"
                break;
            case 'search':
                bqTables = "search_page_captures"
                break;
            case 'category_product':
                bqTables = "category_product_page_captures"
                break;
            default:
                throw new Error(`Invalid page type: ${row.page_type}`);
        }
        await bigquery.insert("brand_monitor", bqTables, formatted_data);
    } catch (error) {
        throw new Error(`Failed to convert source data: ${error}`);
    }
}