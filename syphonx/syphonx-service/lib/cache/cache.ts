import {
    insertAutorunLog,
    queryAutorunQueue,
    AutorunPage,
} from '../autorun.js'

const seconds = 1000;
const minutes = 60 * seconds;
const ttl = 180 * minutes // Refetch after 1 hour

export interface QueueItem extends AutorunPage {
    pending?: number;
    user?: string;
    posted?: number;
}

export interface Cache {
    buffer: QueueItem[];
    timestamp: number;
}

export const cache = {
    default: {
        buffer: [],
        timestamp: 0,
    }
} as { [workstream: string]: Cache };

function applyPending(buffer: QueueItem[], pending: QueueItem[]) {
    for (const obj of pending) {
        const row = buffer.find(row => row.page_id === obj.page_id);
        if (row) {
            row.pending = obj.pending;
            row.user = obj.user;
        }
        else {
            buffer.push(obj);
        }
    }
}

export async function purgeExpired(cacheKey: string) {
    const now = new Date().valueOf();

    if (!cache[cacheKey]) return;

    const expired = cache[cacheKey].buffer.filter(row =>
        !row.posted && row.pending && now - row.pending >= 5 * minutes
    );

    for (const row of expired) {
        row.pending = undefined;
        row.user = undefined;
        await insertAutorunLog("timeout", row.user, row);
    }
}

export async function refreshCache(cacheKey: string, pageType: string, include?: string[], exclude?: string[]) {
    const workstream = cacheKey.split(':')[0];
    const pending = cache[cacheKey].buffer.filter(row => row.pending);
    cache[cacheKey].buffer = await queryAutorunQueue(workstream, pageType, include, exclude) as QueueItem[];

    applyPending(cache[cacheKey].buffer, pending);

    cache[cacheKey].timestamp = new Date().valueOf();
    console.log(`Refresh Cache timestamp: ${cache[cacheKey].timestamp}`)
}

// Create a composite cache key that includes filter parameters
export const getCacheKey = (workstream: string, pageType: string) => {
    return `${workstream}:${pageType}`;
};

export async function updateCache(workstream: string, pageType: string, include?: string[], exclude?: string[]) {
    console.info(`Update Cache...`);
    const cacheKey = getCacheKey(workstream, pageType);

    if (!cache[cacheKey]) {
        cache[cacheKey] = { buffer: [], timestamp: 0 };
        console.info(`No cache entry found, creating new entry for key: ${cacheKey}`);
    }

    await purgeExpired(cacheKey);

    const availableItems = cache[cacheKey].buffer.filter(row => !row.posted && !row.pending).length;

    const now = new Date().valueOf();

    // make sure this is a positive number
    if (Math.abs(now - cache[cacheKey].timestamp) > ttl) {
        console.info(`Cache refresh needed for key ${cacheKey}: availableItems=${availableItems}, age=${(now - cache[cacheKey].timestamp) / 60000} minutes`);
        await refreshCache(cacheKey, pageType, include, exclude);
    }

    if (cache[cacheKey].buffer.length === 0) {
        console.info(`Cache refresh completed for key ${cacheKey} but buffer is still empty`);
    }
}