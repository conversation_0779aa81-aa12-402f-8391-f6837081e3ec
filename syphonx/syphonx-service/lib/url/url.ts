// todo: generalize this, perhaps keep a record of known search params or regex
export function processSearchUrls(row: any, url?: string, searchParams?: string) {
    if (url) {
        row.capture_url = url?.replace(/[{}`]/g, '');
        if (row.capture_url.includes('$params.search')) {
            console.info(`params search detected`)
            row.capture_url = encodeURI(row.capture_url.replace('$params.search', String(searchParams)))
        }
        if (row.capture_url.includes('<search_phrase>')) {
            console.info(`search_phrase detected`)
            row.capture_url = encodeURI(row.capture_url.replace('<search_phrase>', String(searchParams)))
        }
    }
    return row.capture_url
}

export function processUserInput(input?: string[]) {
    if (input && input.length) {
        input = input.map((i) => {
            i = i.replace(/\s/g, '')
            if (!i.includes("www.")) {
                i = `www.${i}`;
            }
            return i;
        });
    }
    return input;
}