import { OpenA<PERSON>pi, ChatCompletionRequestMessage, Configuration } from "openai";

export type ResponseStatus = "stop" | "length" | "content_filter" | null;

export interface ChatParams {
    prompt?: string;
    max_tokens?: number;
    messages?: ChatCompletionRequestMessage[];
    model?: string;
    temperature?: number;
}

export interface ChatResult {
    messages: ChatCompletionRequestMessage[];
    usage: number;
    status: ResponseStatus;
}

export async function chat({ prompt, messages, max_tokens, temperature, model = "gpt-3.5-turbo" }: ChatParams): Promise<ChatResult> {
    if (prompt)
        messages = [{
            role: "user",
            content: prompt
        }];
    if (!messages)
        throw new Error("Prompt not specified");

    const openai = new OpenAIApi(new Configuration({ apiKey: process.env.OPENAI_API_KEY }));
    const { data } = await openai.createChatCompletion({
        model,
        messages,
        max_tokens,
        temperature
    });

    const [choice] = data.choices;
    if (choice.message) {
        messages = messages.slice(0);
        messages.push(choice.message);
    }

    return {
        messages,
        status: choice.finish_reason as ResponseStatus,
        usage: data.usage?.total_tokens || 0
    };
}
