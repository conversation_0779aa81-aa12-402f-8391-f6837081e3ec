import * as http from "http";
import * as express from "express";

export class HttpError extends Error {
    status: number;
    constructor(status: number, message?: string) {
        super(message ?? http.STATUS_CODES[status] ?? "HTTP Error");
        this.status = status;
    }
}

export class NotFoundError extends HttpError {
    constructor(message: string) {
        super(404, message);
    }
}

export class InvalidArgError extends HttpError {
    constructor(message: string) {
        super(400, message);
    }
}

export class UnexpectedError extends HttpError {
    constructor(message: string) {
        super(409, message);
    }
}

export function cors(res: express.Response): void {
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "GET");
    res.set("Access-Control-Allow-Headers", "*");
}

export function handleRequest(req: express.Request, res: express.Response, handler: (req: express.Request, res: express.Response) => unknown) {
    let data: unknown;
    try {
        data = handler(req, res);
    }
    catch (err) {
        if (err instanceof HttpError) {
            res.status(err.status).send(err.message);
        }
        else {
            res.sendStatus(500);
            console.error(err);
        }
    }

    if (data !== undefined) {
        cors(res);
        if (req.query.pretty === undefined) {
            res.jsonp(data);
        }
        else {
            res.header("Content-Type", "application/json");
            res.send(JSON.stringify(data, null, 2));
        }
    }
}

export async function handleAsyncRequest(req: express.Request, res: express.Response, handler: (req: express.Request, res: express.Response) => Promise<unknown>) {
    let data: unknown;
    try {
        data = await handler(req, res);
    }
    catch (err) {
        if (err instanceof HttpError) {
            res.status(err.status).send(err.message);
            return;
        }
        else {
            res.sendStatus(500);
            console.error(err);
            return;
        }
    }

    if (data !== undefined) {
        cors(res);
        if (req.query.pretty === undefined) {
            res.jsonp(data);
        }
        else {
            res.header("Content-Type", "application/json");
            res.send(JSON.stringify(data, null, 2));
        }
    }
}

export function parseDomain(url: string): string {
    try {
        const hostname = new URL(url).hostname;
        return hostname.replace(/^www\./, "");
    }
    catch (err) {
        return "";
    }
}

export function parseTopLevelDomainName(domain_name: string): string {
    const [name] = domain_name.split(".");
    return name;
}

export function regexpExtract(text: string, pattern: RegExp): string {
    if (text) {
        const result = text.match(pattern);
        if (result)
            return result[1];
    }
    return "";
}

export function tryParseJson<T = Record<string, unknown>>(text: string): T | undefined {
    try {
        return JSON.parse(text);
    }
    catch {
        return undefined;
    }
}
