/**
 * Represents a regex-based value replacement rule
 */
interface ReplaceValue {
    regex: string;
    replaceValue?: string; // Optional since some entries don't have it
}

/**
 * Represents cleaning configurations for selectors
 */
interface CleanConfig {
    replaceValues: ReplaceValue[];
}

/**
 * Represents a single field's selector configuration
 */
interface SelectorPath {
    field_name: string;
    selectors: string[];
    clean?: CleanConfig;
}

/**
 * Represents the complete selector configuration for a domain
 */
interface ConfluxConfig {
    domain: string;
    paths: SelectorPath[];
}

/**
 * Fetches the selector configuration for a given domain
 * @param domain - The domain to fetch configuration for
 * @returns A Promise resolving to the selector configuration or null if there was an error
 */
export async function fetchConfluxSelectorConfig(domain: string): Promise<ConfluxConfig | null> {
    try {
        const response = await fetch(`https://ovo.pricespy.com/api/v1/retailer-admin/shared/selectors?domain=${domain}`);

        if (!response.ok) {
            throw new Error(`Failed to fetch selector config: ${response.status} ${response.statusText}`);
        }

        const config: ConfluxConfig = await response.json();
        return config;
    } catch (error) {
        console.error('Error fetching selector config:', error);
        return null;
    }
}