import { queryWorkstreams } from "../autorun.js";
import { updateCache, cache, getCacheKey } from "../cache/cache.js";

export async function autorunDefault(include?: string[], exclude?: string[]) {
    const workstreams = await queryWorkstreams();

    for (const workstream of workstreams) {
        const cacheKey = getCacheKey(workstream.workstream_id, "default");

        await updateCache(workstream.workstream_id, "default");

        let availableItems = cache[cacheKey]?.buffer.filter(row => !row.posted && !row.pending) || [];

        if (include) {
            availableItems = availableItems.filter(row => include.includes(row.domain_name));
        } else if (exclude) {
            availableItems = availableItems.filter(row => !exclude.includes(row.domain_name));
        }

        if (availableItems.length > 0) {
            return {
                item: availableItems[0],
                workstream: workstream.workstream_id
            };
        }
    }

    return null;
}