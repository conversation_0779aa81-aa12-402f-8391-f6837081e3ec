import { PubSub } from "@google-cloud/pubsub";
import * as bigquery from "./bigquery.js";
import { parseDomain, parseTopLevelDomainName, uniqueId, HttpError } from "./index.js";

const pubsub = new PubSub();

export interface AutogenRequest {
    autogen_id: string;
    url: string;
    domain_name: string;
    country_code: string;
    template_path: string;
    selector_profile: string;
    autogen_profile: string;
    overwrite: boolean;
    priority: number;
    requested_at: Date;
}

export async function postAutogenRequest(request: Partial<AutogenRequest>): Promise<AutogenRequest> {
    if (!(typeof request.url === "string" && request.url.startsWith("https://")))
        throw new HttpError(400, "url is invalid");
    if (request.country_code !== undefined && !/^[a-z]{2}$/i.test(request.country_code))
        throw new HttpError(400, "country_code is invalid");
    if (request.priority !== undefined && !isNaN(request.priority) && request.priority >= 0)
        throw new HttpError(400, "priority is invalid");

    const selector_profile = request.selector_profile || "common-product-page";
    const country_code = request.country_code?.toUpperCase() || "US";
    const domain_name = parseDomain(request.url);
    const template_name = parseTopLevelDomainName(domain_name);
    const template_path = `pricespider/autogen/${selector_profile.replaceAll("-", "_")}/${country_code.toLowerCase()}/${template_name}.json`;

    const row: AutogenRequest = {
        autogen_id: uniqueId(),
        url: request.url,
        domain_name,
        country_code,
        template_path,
        selector_profile,
        autogen_profile: request.autogen_profile || "default",
        overwrite: request.overwrite || false,
        priority: request.priority || 3,
        requested_at: new Date()
    };

    await bigquery.insert("syphonx", "autogen_requests", row);
    const data = Buffer.from(JSON.stringify(row));
    await pubsub.topic("syphonx-autogen").publishMessage({ data });

    return row;
}
