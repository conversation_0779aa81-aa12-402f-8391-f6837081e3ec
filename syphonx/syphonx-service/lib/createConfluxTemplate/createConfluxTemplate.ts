import { Template, Select, SelectQuery } from "syphonx-lib";

interface ReplaceValue {
    regex: string;
    replaceValue?: string;
}

interface CleanConfig {
    replaceValues: ReplaceValue[];
}

interface SelectorPath {
    field_name: string;
    selectors: string[];
    clean?: CleanConfig;
}

interface ConfluxConfig {
    domain: string;
    paths: SelectorPath[];
}

/**
 * Builds a SelectQuery from an XPath selector
 */
function buildQueryItemFromXPath(xpath: string, regexPattern?: string): SelectQuery {
    const queryItem: SelectQuery = [xpath];
    
    if (regexPattern) {
        queryItem.push(['extract', `/${regexPattern}/`]);

    }

    return queryItem;
}

/**
 * Determines the type of a field based on its name
 */
function determineType(fieldName: string): "string" | "number" | "boolean" {
    let type: "string" | "number" | "boolean" = "string";

    const numberFields = [
        'price', 'review_score', 'review_count', 'rating', 'review_rating',
        'quantity', 'stock'
    ];

    const booleanFields = [
        'in_stock', 'available', 'is_available', 'enhanced_content', 'specifications'
    ];

    const isBooleanField = booleanFields.some(field =>
        fieldName === field || fieldName.includes(field)
    );

    if (isBooleanField) {
        type = "boolean";
    }

    return type;
}

/**
 * Creates a SyphonX template from a Conflux configuration
 */
function createConfluxTemplate(conflux: ConfluxConfig): Template {
    if (!conflux || !Array.isArray(conflux.paths) || conflux.paths.length === 0) {
        return { actions: [] };
    }

    const selectItems: Select[] = [];
    let regexPattern;

    for (const path of conflux.paths) {
        if (path.field_name && Array.isArray(path.selectors) && path.selectors.length > 0) {

            // if (path.clean && Array.isArray(path.clean.replaceValues)) {
            //     regexPattern = path.clean.replaceValues[0].regex
            // }
            
            
            const queryItems: SelectQuery[] = [];

            for (const xpath of path.selectors) {
                if (xpath && xpath.trim()) {
                    const queryItem = buildQueryItemFromXPath(xpath, regexPattern);
                    queryItems.push(queryItem);
                }
            }

            if (queryItems.length > 0) {
                const selectItem: Select = {
                    name: path.field_name,
                    query: queryItems,
                    repeated: true,
                    all: true,
                    removeNulls: true,
                    limit: queryItems.length,
                    type: determineType(path.field_name)
                };

                selectItems.push(selectItem);
            }
        }
    }

    const template: Template = {
        actions: [
            {
                select: selectItems
            }
        ],
        debug: true
    };

    if (conflux.domain) {
        template.params = {
            domain: conflux.domain
        };
    }

    return template;
}

export default createConfluxTemplate;