import * as dotenv from "dotenv";
dotenv.config();

import express from "express";
import bodyParser from "body-parser";
import morgan from "morgan";
import * as routes from "./routes/index.js";

const app = express();
app.use(express.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(morgan("dev"));

app.use("/audit", routes.audit);
app.use("/autogen", routes.autogen);
app.use("/autorun", routes.autorun);
app.use("/capture", routes.capture);
app.use("/s3", routes.s3);

export { app }; // cloud function entry point
