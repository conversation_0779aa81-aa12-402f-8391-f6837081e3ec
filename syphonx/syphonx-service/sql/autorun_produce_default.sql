WITH
recent_captures AS (
  SELECT DISTINCT LAX_STRING(data.id) AS page_id
  FROM syphonx.autorun_log
  WHERE key='post'
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
),

workstream AS (
  SELECT *
  FROM syphonx.autorun_workstreams
  WHERE workstream_id = _workstream_id
),

product_pages AS (
  SELECT
    NET.HOST(capture_url) AS domain_name,
    'product' as page_type,
    cppd.app_name,
    cppd.account_key,
    cppd.country_code,
    page_id,
    capture_url,
    DATE_DIFF(last_attempt.capture_date, last_capture.capture_date, HOUR) AS capture_age,
    CASE
      WHEN w.app_name="brand_monitor" THEN cppd.template_path
      ELSE CONCAT("pricespider/common/sellers/", LOWER(cppd.country_code), "/", 
           REGEXP_REPLACE(NET.REG_DOMAIN(cppd.capture_url), r'\..*$', ''), "/product_page.json")
    END AS template_path,
    STRUCT<
      sku STRING,
      correlation_id STRING,
      subscription_id INT64,
      instruction_id INT64,
      frequency INT64,
      PSMeta JSON,
      subscriptions JSON
    >(
      sku,
      LAX_STRING(last_attempt.metadata.correlation_id),
      LAX_INT64(last_attempt.metadata.subscription_id),
      LAX_INT64(last_attempt.metadata.instruction_id),
      LAX_INT64(last_attempt.metadata.frequency),
      cppd.last_attempt.metadata.PSMeta,
      cppd.last_attempt.metadata.subscriptions
    ) AS product_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      name STRING,
      category STRING,
      search_group STRING,
      search_phrase STRING,
      schedule STRING,
      schedule_interval STRING,
      search_key STRING,
      search STRING,
      search_category STRING,
      group_name STRING,
      seller_id INT64,
      page_number INT64,
      group_id INT64
    >) AS search_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      category_id STRING,
      category_name STRING,
      schedule STRING,
      schedule_interval STRING,
      week STRING,
      task STRING,
      seller_id INT64,
      page_number INT64
    >) AS category_fields
  FROM conflux.product_page_daily AS cppd,
       workstream as w
  LEFT JOIN recent_captures USING (page_id)
  WHERE cppd.app_name = w.app_name
    AND (
      w.account_key IS NULL 
      OR LENGTH(w.account_key) = 0 
      OR EXISTS (
        SELECT 1
        FROM UNNEST(SPLIT(w.account_key, ',')) account_part
        WHERE cppd.account_key = TRIM(account_part)
      )
    )
    AND (
      w.country_code IS NULL 
      OR LENGTH(w.country_code) = 0 
      OR EXISTS (
        SELECT 1
        FROM UNNEST(SPLIT(w.country_code, ',')) country_part
        WHERE cppd.country_code = TRIM(country_part)
      )
    )
    AND (
      w.seller_id IS NULL 
      OR LENGTH(w.seller_id) = 0 
      OR cppd.seller_id IN (
        SELECT CAST(TRIM(seller_part) AS INT64)
        FROM UNNEST(SPLIT(w.seller_id, ',')) seller_part
      )
    )
    AND recent_captures.page_id IS NULL
    AND (
      (
        cppd.app_name IN ('brand_monitor', 'prowl')
        AND LAX_STRING(last_attempt.metadata.correlation_id) IS NOT NULL
        AND LAX_INT64(last_attempt.metadata.subscription_id) IS NOT NULL
        AND LAX_INT64(last_attempt.metadata.instruction_id) IS NOT NULL
      )
      OR
      (
        cppd.app_name IN ('wtb')
        AND last_attempt.metadata.PSMeta IS NOT NULL
      )
    )
)

SELECT * FROM product_pages
)