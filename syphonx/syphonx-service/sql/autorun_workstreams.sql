CREATE OR REPLACE VIEW syphonx.autorun_workstreams
AS
SELECT
  LAX_STRING(data.workstream_id) AS workstream_id,
  LAX_STRING(data.workstream_name) AS workstream_name,
  LAX_STRING(data.app_name) AS app_name,
  LAX_STRING(data.account_key) AS account_key,
  LAX_STRING(data.country_code) AS country_code,
  LAX_STRING(data.seller_id) AS seller_id,
  LAX_BOOL(data.active) AS active
FROM syphonx.autorun_log
WHERE key='workstream'
QUALIFY ROW_NUMBER() OVER (PARTITION BY LAX_STRING(data.workstream_id) ORDER BY timestamp DESC)=1
ORDER BY LAX_STRING(data.workstream_id)='default' DESC, LAX_STRING(data.workstream_name);