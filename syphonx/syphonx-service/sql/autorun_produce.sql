CREATE OR R<PERSON>LACE TABLE FUNCTION `ps-bigdata.syphonx.autorun_produce`(_workstream_id STRING) AS (
WITH
recent_captures AS (
  SELECT DISTINCT LAX_STRING(data.id) AS page_id
  FROM syphonx.autorun_log
  WHERE key='post'
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
),

workstream AS (
  SELECT *
  FROM syphonx.autorun_workstreams
  WHERE workstream_id = _workstream_id
),

product_pages AS (
  SELECT
    NET.HOST(capture_url) AS domain_name,
    'product' as page_type,
    cppd.app_name,
    cppd.account_key,
    cppd.country_code,
    page_id,
    capture_url,
    DATE_DIFF(last_attempt.capture_date, last_capture.capture_date, HOUR) AS capture_age,
    CASE
      WHEN w.app_name="brand_monitor" THEN cppd.template_path
      ELSE CONCAT("pricespider/common/sellers/", LOWER(cppd.country_code), "/", 
           REGEXP_REPLACE(NET.REG_DOMAIN(cppd.capture_url), r'\..*$', ''), "/product_page.json")
    END AS template_path,
    STRUCT<
      sku STRING,
      correlation_id STRING,
      subscription_id INT64,
      instruction_id INT64,
      frequency INT64,
      PSMeta JSON,
      subscriptions JSON
    >(
      sku,
      LAX_STRING(last_attempt.metadata.correlation_id),
      LAX_INT64(last_attempt.metadata.subscription_id),
      LAX_INT64(last_attempt.metadata.instruction_id),
      LAX_INT64(last_attempt.metadata.frequency),
      cppd.last_attempt.metadata.PSMeta,
      cppd.last_attempt.metadata.subscriptions
    ) AS product_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      name STRING,
      category STRING,
      search_group STRING,
      search_phrase STRING,
      schedule STRING,
      schedule_interval STRING,
      search_key STRING,
      search STRING,
      search_category STRING,
      group_name STRING,
      seller_id INT64,
      page_number INT64,
      group_id INT64
    >) AS search_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      category_id STRING,
      category_name STRING,
      schedule STRING,
      schedule_interval STRING,
      week STRING,
      task STRING,
      seller_id INT64,
      page_number INT64
    >) AS category_fields
  FROM conflux.product_page_daily AS cppd,
       workstream as w
  LEFT JOIN recent_captures USING (page_id)
  WHERE capture_status IN ('never-valid', 'latest-invalid')
    AND last_attempt.capture_date>=TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
    AND (w.app_name IS NULL OR LENGTH(w.app_name)=0 OR cppd.app_name LIKE CONCAT('%', w.app_name, '%'))
  AND (
    w.account_key IS NULL 
    OR LENGTH(w.account_key)=0 
    OR EXISTS (
      SELECT 1
      FROM UNNEST(SPLIT(w.account_key, ',')) account_part
      WHERE cppd.account_key LIKE CONCAT('%', TRIM(account_part), '%')
    )
  )
  AND (
    w.country_code IS NULL 
    OR LENGTH(w.country_code)=0 
    OR EXISTS (
      SELECT 1
      FROM UNNEST(SPLIT(w.country_code, ',')) country_code_part
      WHERE cppd.country_code LIKE CONCAT('%', TRIM(country_code_part), '%')
    )
  )
  AND (
    w.seller_id IS NULL 
    OR LENGTH(w.seller_id)=0 
    OR cppd.seller_id IN (
      SELECT CAST(TRIM(seller_id_part) AS INT64)
      FROM UNNEST(SPLIT(w.seller_id, ',')) seller_id_part
    )
  )
    AND recent_captures.page_id IS NULL
    AND (
      (
        cppd.app_name IN ('brand_monitor', 'prowl')
        AND LAX_STRING(last_attempt.metadata.correlation_id) IS NOT NULL
        AND LAX_INT64(last_attempt.metadata.subscription_id) IS NOT NULL
        AND LAX_INT64(last_attempt.metadata.instruction_id) IS NOT NULL
      )
      OR
      (
        cppd.app_name IN ('wtb')
        AND last_attempt.metadata.PSMeta IS NOT NULL
      )
    )
),

general_product_pages AS (
  SELECT
    NET.HOST(capture_url) AS domain_name,
    'product' as page_type,
    cppd.app_name,
    cppd.account_key,
    cppd.country_code,
    page_id,
    capture_url,
    DATE_DIFF(last_attempt.capture_date, last_capture.capture_date, HOUR) AS capture_age,
    CASE
      WHEN w.app_name="brand_monitor" THEN cppd.template_path
      ELSE CONCAT("pricespider/common/sellers/", LOWER(cppd.country_code), "/", 
           REGEXP_REPLACE(NET.REG_DOMAIN(cppd.capture_url), r'\..*$', ''), "/product_page.json")
    END AS template_path,
    STRUCT<
      sku STRING,
      correlation_id STRING,
      subscription_id INT64,
      instruction_id INT64,
      frequency INT64,
      PSMeta JSON,
      subscriptions JSON
    >(
      sku,
      LAX_STRING(last_attempt.metadata.correlation_id),
      LAX_INT64(last_attempt.metadata.subscription_id),
      LAX_INT64(last_attempt.metadata.instruction_id),
      LAX_INT64(last_attempt.metadata.frequency),
      cppd.last_attempt.metadata.PSMeta,
      cppd.last_attempt.metadata.subscriptions
    ) AS product_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      name STRING,
      category STRING,
      search_group STRING,
      search_phrase STRING,
      schedule STRING,
      schedule_interval STRING,
      search_key STRING,
      search STRING,
      search_category STRING,
      group_name STRING,
      seller_id INT64,
      page_number INT64,
      group_id INT64
    >) AS search_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      category_id STRING,
      category_name STRING,
      schedule STRING,
      schedule_interval STRING,
      week STRING,
      task STRING,
      seller_id INT64,
      page_number INT64
    >) AS category_fields
  FROM conflux.product_page_daily AS cppd,
       workstream as w
  LEFT JOIN recent_captures USING (page_id)
  WHERE cppd.app_name = w.app_name
    AND (
      w.account_key IS NULL 
      OR LENGTH(w.account_key) = 0 
      OR EXISTS (
        SELECT 1
        FROM UNNEST(SPLIT(w.account_key, ',')) account_part
        WHERE cppd.account_key = TRIM(account_part)
      )
    )
    AND (
      w.country_code IS NULL 
      OR LENGTH(w.country_code) = 0 
      OR EXISTS (
        SELECT 1
        FROM UNNEST(SPLIT(w.country_code, ',')) country_part
        WHERE cppd.country_code = TRIM(country_part)
      )
    )
    AND (
      w.seller_id IS NULL 
      OR LENGTH(w.seller_id) = 0 
      OR cppd.seller_id IN (
        SELECT CAST(TRIM(seller_part) AS INT64)
        FROM UNNEST(SPLIT(w.seller_id, ',')) seller_part
      )
    )
    AND recent_captures.page_id IS NULL
    AND (
      (
        cppd.app_name IN ('brand_monitor', 'prowl')
        AND LAX_STRING(last_attempt.metadata.correlation_id) IS NOT NULL
        AND LAX_INT64(last_attempt.metadata.subscription_id) IS NOT NULL
        AND LAX_INT64(last_attempt.metadata.instruction_id) IS NOT NULL
      )
      OR
      (
        cppd.app_name IN ('wtb')
        AND last_attempt.metadata.PSMeta IS NOT NULL
      )
    )
),

search_page_failures AS (
  SELECT 
    NET.HOST(b.domain) AS domain_name,
    'search' as page_type,
    'brand_monitor' AS app_name,
    JSON_VALUE(tags, '$.account_key') AS account_key,
    JSON_VALUE(tags, '$.country_code') AS country_code,
    CONCAT(
        JSON_VALUE(tags, '$.account_key'),
        JSON_VALUE(tags, '$.country_code'), '_',
        'search',
        NET.HOST(b.domain),
        '_',
        GENERATE_UUID()
    ) as page_id,
    b.domain as capture_url,
    CAST(NULL AS INT64) as capture_age,
    CONCAT("pricespider/brand_monitor/sellers/",
        LOWER(JSON_VALUE(tags, '$.country_code')), "/",
        REGEXP_REPLACE(REGEXP_REPLACE(NET.HOST(b.domain), r'^www\.', ''), r'\..*$', ''),
        "/product_search.json") AS template_path,
    CAST(NULL AS STRUCT<
      sku STRING,
      correlation_id STRING,
      subscription_id INT64,
      instruction_id INT64,
      frequency INT64,
      PSMeta JSON,
      subscriptions JSON
    >) AS product_fields,
    STRUCT<
      tags STRING,
      category STRING,
      name STRING,
      search_group STRING,
      search_phrase STRING,
      schedule STRING,
      schedule_interval STRING,
      search_key STRING,
      search STRING,
      search_category STRING,
      group_name STRING,
      seller_id INT64,
      page_number INT64,
      group_id INT64
    >(
      ARRAY_TO_STRING(JSON_EXTRACT_ARRAY(tags, '$.tags'), ','),
      JSON_VALUE(tags, '$.category'),
      JSON_VALUE(tags, '$.name'),
      JSON_VALUE(tags, '$.search_group'),
      JSON_VALUE(tags, '$.search_phrase'),
      JSON_VALUE(tags, '$.schedule'),
      JSON_VALUE(tags, '$.schedule_interval'),
      JSON_VALUE(tags, '$.search_key'),
      JSON_VALUE(tags, '$.search'),
      JSON_VALUE(tags, '$.search_category'),
      JSON_VALUE(tags, '$.group_name'),
      CAST(JSON_VALUE(tags, '$.seller_id') AS INT64),
      CAST(JSON_VALUE(tags, '$.pages') AS INT64),
      CAST(JSON_VALUE(tags, '$.group_id') AS INT64)
    ) AS search_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      category_id STRING,
      category_name STRING,
      schedule STRING,
      schedule_interval STRING,
      week STRING,
      task STRING,
      seller_id INT64,
      page_number INT64
    >) AS category_fields
  FROM brand_monitor.schedule_search_page_failures as b,
       workstream as w 
  WHERE CAST(JSON_VALUE(tags, '$.seller_id') AS INT64) in (191, 2, 167)
  AND EXISTS (
    SELECT 1
    FROM UNNEST(SPLIT(w.account_key, ',')) account
    WHERE JSON_VALUE(tags, '$.account_key') LIKE CONCAT('%', TRIM(account), '%')
  )
),

search_page_schedule AS (
  SELECT 
    NET.HOST(b.domain) AS domain_name,
    'search' as page_type,
    'brand_monitor' AS app_name,
    JSON_VALUE(tags, '$.account_key') AS account_key,
    JSON_VALUE(tags, '$.country_code') AS country_code,
    CONCAT(
        JSON_VALUE(tags, '$.account_key'),
        JSON_VALUE(tags, '$.country_code'), '_',
        'search',
        NET.HOST(b.domain),
        '_',
        GENERATE_UUID()
    ) as page_id,
    b.domain as capture_url,
    CAST(NULL AS INT64) as capture_age,
    CONCAT("pricespider/brand_monitor/sellers/",
        LOWER(JSON_VALUE(tags, '$.country_code')), "/",
        REGEXP_REPLACE(REGEXP_REPLACE(NET.HOST(b.domain), r'^www\.', ''), r'\..*$', ''),
        "/product_search.json") AS template_path,
    CAST(NULL AS STRUCT<
      sku STRING,
      correlation_id STRING,
      subscription_id INT64,
      instruction_id INT64,
      frequency INT64,
      PSMeta JSON,
      subscriptions JSON
    >) AS product_fields,
    STRUCT<
      tags STRING,
      category STRING,
      name STRING,
      search_group STRING,
      search_phrase STRING,
      schedule STRING,
      schedule_interval STRING,
      search_key STRING,
      search STRING,
      search_category STRING,
      group_name STRING,
      seller_id INT64,
      page_number INT64,
      group_id INT64
    >(
      ARRAY_TO_STRING(JSON_EXTRACT_ARRAY(tags, '$.tags'), ','),
      JSON_VALUE(tags, '$.category'),
      JSON_VALUE(tags, '$.name'),
      JSON_VALUE(tags, '$.search_group'),
      JSON_VALUE(tags, '$.search_phrase'),
      JSON_VALUE(tags, '$.schedule'),
      JSON_VALUE(tags, '$.schedule_interval'),
      JSON_VALUE(tags, '$.search_key'),
      JSON_VALUE(tags, '$.search'),
      JSON_VALUE(tags, '$.search_category'),
      JSON_VALUE(tags, '$.group_name'),
      CAST(JSON_VALUE(tags, '$.seller_id') AS INT64),
      CAST(JSON_VALUE(tags, '$.pages') AS INT64),
      CAST(JSON_VALUE(tags, '$.group_id') AS INT64)
    ) AS search_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      category_id STRING,
      category_name STRING,
      schedule STRING,
      schedule_interval STRING,
      week STRING,
      task STRING,
      seller_id INT64,
      page_number INT64
    >) AS category_fields
  FROM brand_monitor.search_page_schedule as b,
       workstream as w 
  WHERE EXISTS (
    SELECT 1
    FROM UNNEST(SPLIT(w.account_key, ',')) account
    WHERE JSON_VALUE(tags, '$.account_key') LIKE CONCAT('%', TRIM(account), '%')
  )
),

category_pages AS (
  SELECT 
    NET.HOST(b.url) AS domain_name,
    'category' as page_type,
    'brand_monitor' AS app_name,
    JSON_VALUE(tags, '$.account_key') AS account_key,
    JSON_VALUE(tags, '$.country_code') AS country_code,
    CONCAT(
        JSON_VALUE(tags, '$.account_key'),
        JSON_VALUE(tags, '$.country_code'), '_',
        'category',
        NET.HOST(b.url),
        '_',
        GENERATE_UUID()
    ) as page_id,
    b.url as capture_url,
    CAST(NULL AS INT64) as capture_age,
    CONCAT("pricespider/brand_monitor/sellers/",
        LOWER(JSON_VALUE(tags, '$.country_code')), "/",
        REGEXP_REPLACE(REGEXP_REPLACE(NET.HOST(b.url), r'^www\.', ''), r'\..*$', ''),
        "/category_page.json") AS template_path,
    CAST(NULL AS STRUCT<
      sku STRING,
      correlation_id STRING,
      subscription_id INT64,
      instruction_id INT64,
      frequency INT64,
      PSMeta JSON,
      subscriptions JSON
    >) AS product_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      category STRING,
      name STRING,
      search_group STRING,
      search_phrase STRING,
      schedule STRING,
      schedule_interval STRING,
      search_key STRING,
      search STRING,
      search_category STRING,
      group_name STRING,
      seller_id INT64,
      page_number INT64,
      group_id INT64
    >) AS search_fields,
    STRUCT<
      tags STRING,
      category_id STRING,
      category_name STRING,
      schedule STRING,
      schedule_interval STRING,
      week STRING,
      task STRING,
      seller_id INT64,
      page_number INT64
    >(
      tags,
      JSON_VALUE(tags, '$.category_id'),
      JSON_VALUE(tags, '$.category_name'),
      JSON_VALUE(tags, '$.schedule'),
      JSON_VALUE(tags, '$.schedule_interval'),
      JSON_VALUE(tags, '$.week'),
      JSON_VALUE(tags, '$.task'),
      CAST(JSON_VALUE(tags, '$.seller_id') AS INT64),
      CAST(JSON_VALUE(tags, '$.page_number') AS INT64)
    ) AS category_fields
  FROM brand_monitor.schedule_category_page_failures as b,
       workstream as w
  WHERE CAST(JSON_VALUE(tags, '$.seller_id') AS INT64) in (191)
  AND DATE(JSON_VALUE(tags, '$.week')) >= DATE_TRUNC(DATE_SUB(CURRENT_DATE('America/Los_Angeles'), INTERVAL 7 DAY), WEEK(MONDAY))  
  AND EXISTS (
    SELECT 1
    FROM UNNEST(SPLIT(w.account_key, ',')) account
    WHERE JSON_VALUE(tags, '$.account_key') LIKE CONCAT('%', TRIM(account), '%')
  )
),

-- first page for category crawls
category_pages_one AS (
  SELECT 
    NET.HOST(b.url) AS domain_name,
    'category' as page_type,
    'brand_monitor' AS app_name,
    JSON_VALUE(tags, '$.account_key') AS account_key,
    JSON_VALUE(tags, '$.country_code') AS country_code,
    CONCAT(
        JSON_VALUE(tags, '$.account_key'),
        JSON_VALUE(tags, '$.country_code'), '_',
        'category',
        NET.HOST(b.url),
        '_',
        GENERATE_UUID()
    ) as page_id,
    b.url as capture_url,
    CAST(NULL AS INT64) as capture_age,
    CONCAT("pricespider/brand_monitor/sellers/",
        LOWER(JSON_VALUE(tags, '$.country_code')), "/",
        REGEXP_REPLACE(REGEXP_REPLACE(NET.HOST(b.url), r'^www\.', ''), r'\..*$', ''),
        "/category_page.json") AS template_path,
    CAST(NULL AS STRUCT<
      sku STRING,
      correlation_id STRING,
      subscription_id INT64,
      instruction_id INT64,
      frequency INT64,
      PSMeta JSON,
      subscriptions JSON
    >) AS product_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      category STRING,
      name STRING,
      search_group STRING,
      search_phrase STRING,
      schedule STRING,
      schedule_interval STRING,
      search_key STRING,
      search STRING,
      search_category STRING,
      group_name STRING,
      seller_id INT64,
      page_number INT64,
      group_id INT64
    >) AS search_fields,
    STRUCT<
      tags STRING,
      category_id STRING,
      category_name STRING,
      schedule STRING,
      schedule_interval STRING,
      week STRING,
      task STRING,
      seller_id INT64,
      page_number INT64
    >(
      tags,
      JSON_VALUE(tags, '$.category_id'),
      JSON_VALUE(tags, '$.category_name'),
      JSON_VALUE(tags, '$.schedule'),
      JSON_VALUE(tags, '$.schedule_interval'),
      JSON_VALUE(tags, '$.week'),
      JSON_VALUE(tags, '$.task'),
      CAST(JSON_VALUE(tags, '$.seller_id') AS INT64),
      CAST(JSON_VALUE(tags, '$.page_number') AS INT64)
    ) AS category_fields
  FROM brand_monitor.schedule_category_page_one_failures as b,
       workstream as w
  WHERE CAST(JSON_VALUE(tags, '$.seller_id') AS INT64) in (191)
  AND DATE(JSON_VALUE(tags, '$.week')) >= DATE_TRUNC(DATE_SUB(CURRENT_DATE('America/Los_Angeles'), INTERVAL 7 DAY), WEEK(MONDAY))  
  AND EXISTS (
    SELECT 1
    FROM UNNEST(SPLIT(w.account_key, ',')) account
    WHERE JSON_VALUE(tags, '$.account_key') LIKE CONCAT('%', TRIM(account), '%')
  )
),

-- category product pages
category_product_page AS (
  SELECT 
    NET.HOST(b.url) AS domain_name,
    'category_product' as page_type,
    'brand_monitor' AS app_name,
    JSON_VALUE(tags, '$.account_key') AS account_key,
    JSON_VALUE(tags, '$.country_code') AS country_code,
    CONCAT(
        JSON_VALUE(tags, '$.account_key'),
        JSON_VALUE(tags, '$.country_code'), '_',
        'category',
        NET.HOST(b.url),
        '_',
        GENERATE_UUID()
    ) as page_id,
    b.url as capture_url,
    CAST(NULL AS INT64) as capture_age,
    CONCAT("pricespider/brand_monitor/sellers/",
        LOWER(JSON_VALUE(tags, '$.country_code')), "/",
        REGEXP_REPLACE(REGEXP_REPLACE(NET.HOST(url), r'^www\.', ''), r'\..*$', ''),
        "/category_product_page.json") AS template_path,
    CAST(NULL AS STRUCT<
      sku STRING,
      correlation_id STRING,
      subscription_id INT64,
      instruction_id INT64,
      frequency INT64,
      PSMeta JSON,
      subscriptions JSON
    >) AS product_fields,
    CAST(NULL AS STRUCT<
      tags STRING,
      category STRING,
      name STRING,
      search_group STRING,
      search_phrase STRING,
      schedule STRING,
      schedule_interval STRING,
      search_key STRING,
      search STRING,
      search_category STRING,
      group_name STRING,
      seller_id INT64,
      page_number INT64,
      group_id INT64
    >) AS search_fields,
    STRUCT<
      tags STRING,
      category_id STRING,
      category_name STRING,
      schedule STRING,
      schedule_interval STRING,
      week STRING,
      task STRING,
      seller_id INT64,
      page_number INT64
    >(
      tags,
      JSON_VALUE(tags, '$.category_id'),
      JSON_VALUE(tags, '$.category_name'),
      JSON_VALUE(tags, '$.schedule'),
      JSON_VALUE(tags, '$.schedule_interval'),
      JSON_VALUE(tags, '$.week'),
      JSON_VALUE(tags, '$.task'),
      CAST(JSON_VALUE(tags, '$.seller_id') AS INT64),
      CAST(JSON_VALUE(tags, '$.page_number') AS INT64)
    ) AS category_fields
  FROM brand_monitor.schedule_category_product_page_failures as b,
       workstream as w
  WHERE CAST(JSON_VALUE(tags, '$.seller_id') AS INT64) in (191, 2, 167)
  AND EXISTS (
    SELECT 1
    FROM UNNEST(SPLIT(w.account_key, ',')) account
    WHERE JSON_VALUE(tags, '$.account_key') LIKE CONCAT('%', TRIM(account), '%')
  )
)

  SELECT * FROM product_pages
  UNION ALL
  SELECT * FROM search_page_failures
  UNION ALL
  SELECT * FROM search_page_schedule
  UNION ALL
  SELECT * FROM category_pages
  UNION ALL
  SELECT * FROM category_pages_one
  UNION ALL
  SELECT * FROM category_product_page
  UNION ALL
  SELECT * FROM general_product_pages
);