WITH recent_captures AS (
  SELECT DISTINCT LAX_STRING(data.id) AS page_id
  FROM syphonx.autorun_log
  WHERE key='post'
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
)
SELECT
  app_name,
  IF(NOT ENDS_WITH(seller_name, CONCAT('(', country_code, ')')), CONCAT(seller_name, ' (', country_code, ')'), seller_name) AS seller_name,
  ANY_VALUE(NET.REG_DOMAIN(capture_url)) AS domain_name,
  COUNTIF(recent_captures.page_id IS NOT NULL) AS recent_count,
  COUNTIF(capture_status='latest-invalid') AS latest_invalid_count,
  COUNTIF(capture_status='latest-valid') AS latest_valid_count,
  COUNTIF(capture_status='never-valid') AS never_valid_count,
  COUNTIF(capture_status='never-attempted') AS never_attempted_count,
  COUNT(*) AS total_count
FROM conflux.product_page_daily
LEFT JOIN recent_captures USING (page_id)
GROUP BY 1, 2
HAVING latest_invalid_count > 0
ORDER BY latest_invalid_count DESC, domain_name;