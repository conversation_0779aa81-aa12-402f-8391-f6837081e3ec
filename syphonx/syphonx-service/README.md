# Setup
1. Clone this repo
2. Obtain `.env` and `.env.yaml` files and place them in the `syphonx-pricespider-service` directory.

# Build
1. `yarn build` to build or hit **ctrl+shift+b** in VSCode

# Debug
1. `yarn start` to start functions framework
2. Select **Attach** in *Run and Debug*
3. Hit F5 in VSCode to attach the debugger
4. Set a breakpoint in a route
5. `curl http://localhost:8081/` to test

# Deploy
`yarn deploy` to deploy
