# SyphonX Service - AutoRun Documentation

## Overview

The AutoRun service is a critical component of the syphonx-service backend infrastructure, designed to facilitate human-assisted crawling when automated crawling encounters significant blocking. It works in conjunction with the syphonx-chrome extension and integrates with BigQuery for data retrieval and processing.

## Architecture

### Core Components

1. **Express Router Service**
   - Handles HTTP endpoints for health checks, workstream management, and data processing
   - Implements caching mechanism for optimizing data retrieval
   - Manages authentication through username headers

2. **Cache Management**
   - Implements an in-memory cache system with TTL (Time To Live)
   - Default TTL: 5 minutes
   - Supports multiple workstreams with independent caches

3. **Queue Management**
   - Handles pending items and posted items
   - Implements automatic expiration of pending items
   - Maintains state across multiple users and sessions

### API Endpoints

[Link Text](./filename.md#section-name)

#### 1. Health Check
```
GET /health
Response: { ok: true }
```

#### 2. Workstreams
```
GET /workstreams
Authentication: Required (x-username header)
Response: List of available workstreams
```

#### 3. Queue Management
```
GET /
Authentication: Required (x-username header)
Query Parameters:
  - workstream (optional, defaults to "default")
  - account_key
  - include (comma-separated domains to include)
  - exclude (comma-separated domains to exclude)
Response: Template with capture URL and page ID
```

#### 4. Result Submission
```
POST /
Authentication: Required (x-username header)
Body: Requires 'id' field
Response: { id, ok: true/false }
```

## Data Flow

1. **Initial Request**
   - Client requests data through the GET endpoint
   - System checks cache for available items
   - If cache is expired, refreshes from BigQuery
   - Returns next available item with template

2. **Processing**
   - Item marked as pending with user assignment
   - Timeout after 5 minutes if not processed
   - Uses both seller-specific and generic templates

3. **Result Submission**
   - Client submits processed data
   - System validates results
   - If valid, prepares for Conflux message queue
   - Marks item as posted

## Cache Management

### Cache Structure
```typescript
interface Cache {
    buffer: QueueItem[];
    timestamp: number;
}
```

### Cache Operations

1. **Refresh**
   - Triggered when cache expires (5 minutes)
   - Preserves pending items during refresh
   - Updates timestamp

2. **Purge**
   - Removes expired pending items
   - Logs timeout events
   - Clears user assignments

## Configuration

### Environment Variables
- NODE_ENV: Affects logging and debugging
- PRICESPIDER_CONFLUX_RABBIT_URL: RabbitMQ connection string

### Routing Keys
Configured for different applications:
- brand_monitor: `engine.hac.brand_monitor.results.product`
- scout: `engine.hac.prowl.results.product`
- wtb: `engine.hac.wtb.result`

## Error Handling

1. **Authentication**
   - 401 for missing username
   - Validates through x-username header

2. **Data Validation**
   - Validates results before queue submission
   - Logs validation failures in development
   - Returns error status for invalid submissions

## Best Practices

1. **Cache Management**
   - Regular purging of expired items
   - Maintaining pending state across refreshes
   - TTL-based cache invalidation

2. **Error Handling**
   - Proper HTTP status codes
   - Detailed error logging in development
   - Validation before queue submission

3. **Security**
   - Authentication required for all endpoints
   - Workstream isolation
   - User tracking for operations

## Limitations and Considerations

1. **Cache Limitations**
   - In-memory cache may not persist across service restarts
   - 5-minute TTL may need adjustment based on load

2. **Pending Items**
   - 5-minute timeout for pending items
   - No automatic retry mechanism
   - Manual intervention required for failed items

## Integration Points

1. **BigQuery**
   - Data source for queue items
   - Uses SQL templates for data retrieval
   - Workstream-based filtering

2. **RabbitMQ**
   - Result publication endpoint
   - Topic-based routing
   - External exchange integration

## Monitoring and Logging

1. **Event Logging**
   - Timeout events
   - Service operations
   - Validation failures

2. **Health Checks**
   - Endpoint for service health
   - Cache status monitoring
   - Queue status tracking