import { Template } from "syphonx-lib";
import { Capture, Selector } from "./query-autofix.js";
import { verifySelector } from "./verify-selector.js";
import chalk from "chalk";

interface FieldCapture extends Capture {
    field: string;
}

export interface VerifyAutofixOptions {
    template: Template;
    updatedTemplate: Template;
    fields: Selector[];
    max?: number;
    online?: boolean;
    headless?: boolean;
}

export interface VerifyAutofixResult {
    scanned: number;
    verified: number;
    unverified: number;
    skipped: number;
}

export async function verifyAutofix({ template, updatedTemplate, fields, max = Infinity, online = false, headless }: VerifyAutofixOptions): Promise<VerifyAutofixResult> {
    const captures: FieldCapture[] = [];
    for (const { selector_name: field, ...obj } of fields)
        for (const capture of obj.captures)
            if (captures.length < max)
                captures.push({ ...capture, field });
    
    const counters = {
        scanned: 0,
        verified: 0,
        unverified: 0,
        skipped: 0
    };
    
    for (const capture of captures) {
        counters.scanned += 1;
        const line = `[${captures.indexOf(capture) + 1}/${captures.length}] ${capture.capture_url} ${!online ? `OFFLINE {${capture.capture_id}}` : "ONLINE"} ${capture.field} ${capture.last_valid_capture_days}d`;
    
        const result1 = await verifySelector({
            template,
            key: capture.field,
            capture_url: capture.capture_url,
            capture_id: !online ? capture.capture_id : undefined,
            headless
        });
        if (result1.skipped) {
            counters.skipped += 1;
            console.log(`${line} ${chalk.cyan("SKIPPED")}`);
            continue;
        }
    
        const result2 = await verifySelector({
            template: updatedTemplate,
            key: capture.field,
            capture_url: capture.capture_url,
            capture_id: !online ? capture.capture_id : undefined
        });
        if (result2.skipped) {
            console.log(`${line} ${chalk.green("VERIFIED")}`);
            counters.verified += 1;
        }
        else {
            console.log(`${line} ${chalk.yellow("UNVERIFIED")}`);
            counters.unverified += 1
        }
    }

    return counters;
}
