move sql into dbt!!

pending mode should show # days outstanding for all items
main mode
    only show items with no status and not DISABLED
    min threshold of hits
    show all flag
    tallys

pending
    tallys

status mode to show status of ALL items

view selected value in verify mode and compare to original value

split commands into seperate programs (call the main one "ready" mode)
use parseargs module
debug a fix that has more than just a selector: NFM (US), The Good Guys (AU)
consider logging the number of examples for each selector

DONE
add number of hits to commit log
