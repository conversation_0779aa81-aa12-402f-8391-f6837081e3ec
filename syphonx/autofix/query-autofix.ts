import { BigQuery } from "@google-cloud/bigquery";

const bigquery = new BigQuery();

export interface Row {
    seller_name: string;
    seller_id: number;
    country_code: string;
    template_type: string;
    template_path: string;
    template_enabled: boolean;
    status: string;
    selectors: Selector[];
}

export interface Selector {
    selector_name: string;
    selector: string;
    confidence: number;
    pending: boolean;
    captures: Capture[];
}

export interface Capture {
    capture_id: string;
    capture_url: string;
    last_valid_capture_id: string;
    last_valid_capture_days: number;
}

export async function queryAutofixCandidates(): Promise<Row[]> {
    const [rows] = await bigquery.query(`SELECT * FROM syphonx.autofix_candidates`) as [Row[]];
    return rows;
}

export interface AutofixPending {
    seller_name: string;
    seller_id: number;
    country_code: string;
    template_type: string;
    template_path: string;
    template_enabled: boolean;
    selector_name: string;
    status: string;
    url_count: number;
    commit_id: string;
    created_at: Date;
    updated_at: Date;
    confirmed: boolean;
    confirmed_days: number;
}

export async function queryAutofixPending(): Promise<AutofixPending[]> {
    const [rows] = await bigquery.query(`SELECT * FROM syphonx.autofix_pending_commits`) as [AutofixPending[]];
    return rows;
}

export async function confirmAutofixPending() {
    const rows = await queryAutofixPending();
    const data = rows
        .filter(row => row.confirmed)
        .map(row => ({
            commit_id: row.commit_id,
            selector_name: row.selector_name,
            status: "confirmed",
            updated_at: new Date()
        }));
    if (data.length > 0)
        await bigquery.dataset("syphonx").table("autofix_commit_status_log").insert(data);
    return data.length;
}