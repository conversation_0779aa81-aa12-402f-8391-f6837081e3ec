import * as dotenv from "dotenv";
import * as syphonx from "./syphonx.js";
import { BigQuery } from "@google-cloud/bigquery";
import { queryAutofixCandidates, queryAutofixPending, confirmAutofixPending, Selector, Row } from "./query-autofix.js";
import { updateTemplate } from "./update-template.js";
import { verifyAutofix } from "./verify-autofix.js";
import { uid } from "./lib/index.js";
import chalk from "chalk";

dotenv.config();

const bigquery = new BigQuery();

if (process.argv[2] === "--help") {
    console.log(`Usage: node . [list|verify|commit|reject|pending|confirm] [#] [--max] [--key] [--online] [--show]`);
    process.exit(0);
}

const params = {
    command: process.argv[2] || "",
    online: !!process.argv.includes("--online"),
    show: !!process.argv.includes("--show"),
    max: process.argv.includes("--max") ? parseInt(process.argv[process.argv.indexOf("--max") + 1]) : Infinity,
    field: process.argv.includes("--field") ? process.argv[process.argv.indexOf("--field") + 1] : undefined
};

const command = process.argv[2] || "";
if (!["", "list", "verify", "commit", "reject", "pending", "confirm"].includes(command)) {
    console.log(chalk.red("Invalid command"));
    process.exit();
}

const comment = command === "reject" ? process.argv[4] : undefined;
if (command === "reject" && !comment) {
    console.log(chalk.red("Please specify a reject reason in double-quotes"));
    process.exit();
}

if (command === "pending") {
    const rows = await queryAutofixPending();
    for (const row of rows)
        console.log(`${row.template_path} PENDING -> ${row.confirmed ? `${chalk.green("CONFIRMED")} ${row.confirmed_days}d` : row.template_enabled ? chalk.yellow("UNCONFIRMED") : chalk.magenta("DISABLED")} (${row.url_count} urls)`);
    process.exit();
}

if (command === "confirm") {
    const n = await confirmAutofixPending();
    console.log(`${n} confirmed`);
    process.exit();
}

console.log("Querying autofixes...");
const rows = await queryAutofixCandidates();

if (!command) {
    for (const row of rows)
        console.log(`${rows.indexOf(row) + 1}. ${formatRow(row)}`);
    process.exit();    
}

const n = parseInt(process.argv[3]);
if (isNaN(n) || n < 1 || n > rows.length) {
    console.log(`Select a number from 1 to ${rows.length}`);
    process.exit();
}

const i = n - 1;
const row = rows[i];
const {
    seller_name,
    seller_id,
    country_code,
    template_type,
    template_path,
    status
} = row;

const fields = row.selectors.filter(field => params.field ? field.selector_name === params.field : true);
const updates = fields.map(field => [field.selector_name, field.selector, field.captures.length] as [string, string, number]);

const { template, metadata } = await syphonx.loadTemplate(template_path);
const { template: updated_template, diffs, errors: update_errors } = updateTemplate(template, updates);
if (update_errors.length > 0)
    console.log(chalk.yellow(update_errors.join("\n")));

console.log(template_path);
console.log(`Last modiifed: ${metadata.modifiedAt}`);
console.log(`Seller: ${seller_name} id=${seller_id} [${template_type}]`);
console.log(`${updates.length} updates...`);
for (const update of updates) {
    const [field, selector, hits] = update;    
    console.log(`${field} (${hits})`);
    console.log(`  ${selector}`);
    const i = updates.indexOf(update);
    const diff = diffs[i];
    if (diff) {
        console.log(`  - ${diff.before}`);
        console.log(`  + ${diff.after}`);
    }
}

if (command === "list") {
    for (const { selector_name: field, ...obj } of fields)
        for (const capture of obj.captures)
            console.log(capture.capture_url);
}
else if (command === "verify") {
    if (["pending", "rejected"].includes(status)) {
        console.log(chalk.yellow(`WARNING: Template already has status ${status.toUpperCase()}`));
        process.exit();
    }
    const result = await verifyAutofix({
        template,
        updatedTemplate: updated_template,
        fields,
        max: params.max,
        online: params.online,
        headless: !params.show
    });
    console.log(`${result.verified} verified, ${result.unverified} unverified, ${result.skipped} skipped, ${result.scanned} total`);
}
else if (command === "commit") {
    if (["pending", "rejected"].includes(status)) {
        console.log(chalk.red(`ERROR: Cannot commit a template with status ${status.toUpperCase()}`));
        process.exit();
    }
    if (diffs.length > 0) {
        await commit();
        await syphonx.saveTemplate(template_path, updated_template);
        console.log(chalk.green(`${template_path} committed`));
    }
    else {
        console.log(chalk.yellow(`${template_path} not updated, no diffs`));
    }
}
else if (command === "reject") {
    if (["pending", "rejected"].includes(status)) {
        console.log(chalk.red(`ERROR: Cannot reject a template with status ${status.toUpperCase()}`));
        process.exit();
    }    
    await reject(comment);
    console.log(chalk.yellow(`${template_path} autofix rejected`));
}

process.exit();

function formatRow(row: Row) {
    return `${row.seller_name} ${formatFields(row.selectors)} ${formatStatus(row.status)} ${!row.template_enabled ? chalk.magenta("DISABLED") : ""}`;
}

function formatFields(fields: Selector[]) {
    return `${fields.map(field => `[${field.selector_name}${field.pending ? chalk.cyan("*") : ""}: ${field.captures.length} ${field.confidence} ${formatDayRange(field)}]`).join(" ")}`;
}

function formatDayRange(field: Selector) {
    const min = Math.min(...field.captures.map(capture => capture.last_valid_capture_days));
    const max = Math.max(...field.captures.map(capture => capture.last_valid_capture_days));
    return min === max ? `${min}d` : `${min}-${max}d`;
}

function formatStatus(status: string) {
    if (status === "pending")
        return chalk.cyan("PENDING");
    else if (status === "confirmed")
        return chalk.green("CONFIRMED");
    else if (status === "rejected")
        return chalk.red("REJECTED");
    else if (status)
        return status.toUpperCase();
    else
        return "";
}

async function commit(comment?: string) {
    const commit_id = uid();
    await bigquery.dataset("syphonx").table("autofix_commits").insert({
        commit_id,
        seller_name,
        seller_id,
        country_code,
        template_type,
        template_path,
        timestamp: new Date(),
        commit: diffs,
        comment
    });
}

async function reject(comment?: string) {
    const commit_id = uid();
    await bigquery.dataset("syphonx").table("autofix_rejects").insert({
        commit_id,
        seller_name,
        seller_id,
        country_code,
        template_type,
        template_path,
        timestamp: new Date(),
        commit: diffs,
        comment
    });
}
