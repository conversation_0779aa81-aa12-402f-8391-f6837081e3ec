import { Template, Select } from "syphonx-lib";
import { isEmpty } from "./lib/index.js";
import * as syphonx from "./syphonx.js";
import * as venom from "./venom.js";

export interface VerifySelectorOptions {
    template: Template;
    capture_url: string;
    capture_id?: string;
    key: string;
    headless?: boolean;
}

export interface VerifySelectorResult {
    skipped: boolean;
    value?: unknown;
    nodes?: string[];
    select?: Select;
}

export async function verifySelector({ template, key, capture_url, capture_id, headless }: VerifySelectorOptions): Promise<VerifySelectorResult> {
    const select = syphonx.findSelectAction(template, key);
    const url = capture_url;
    const html = capture_id ? await venom.loadCaptureHtml(capture_id) : undefined;
    const result = await syphonx.extract({ template, url, html, unwrap: false, headless });
    const obj = result.data[key];
    const value = obj?.value;

    return {
        skipped: !isEmpty(value),
        value: obj?.value,
        nodes: obj?.nodes,
        select
    };
}
