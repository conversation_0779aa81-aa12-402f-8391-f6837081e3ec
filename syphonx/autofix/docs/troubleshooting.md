

## view autofixes by day
```sql
SELECT
  date,
  COUNT(*)
FROM brand_monitor_data_quality.seller_anomalies
WHERE solution='product_page'
AND detector_type='missing-value'
AND JSON_EXTRACT_SCALAR(recommendation.`new-selector`) IS NOT NULL
GROUP BY 1
ORDER BY 1 DESC;
```

## check for pending dupes
```sql
SELECT seller_name, seller_id, COUNT(*)
FROM syphonx.autofix_log
WHERE status='pending'
GROUP BY 1, 2
HAVING COUNT(*)>1
ORDER BY 1 DESC;
```


## dedupe pending log
```sql
CREATE OR REPLACE TABLE syphonx.autofix_log
AS
SELECT * EXCEPT(dupe)
FROM syphonx.autofix_log
QUALIFY IF(status='pending', ROW_NUMBER() OVER (PARTITION BY template_path, ARRAY_TO_STRING(ARRAY(SELECT DISTINCT key FROM UNNEST(diffs) ORDER BY 1), ', ') ORDER BY created_at) = 1, TRUE);
```