export function clone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
}

export function isEmpty(obj: unknown): boolean {
    if (obj === null || obj === undefined)
        return true;
    else if (typeof obj === "string" || Array.isArray(obj))
        return obj.length === 0;
    else if (typeof obj === "object")
        return Object.keys(obj as object).length === 0;
    else
        return false;
}
