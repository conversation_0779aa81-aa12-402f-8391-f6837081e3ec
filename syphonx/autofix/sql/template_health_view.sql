CREATE OR REPLACE VIEW syphonx.template_health_view
AS
WITH
r1 AS (
  SELECT
    seller_name,
    seller_id,
    REGEXP_EXTRACT(seller_name, r'\(([A-Z]{2})\)$') AS country_code,
    account_key,
    STRING(anomaly_details.sku) AS sku,
    STRING(anomaly_details.capture_url) AS capture_url,
    STRING(anomaly_details.capture_id) AS capture_id,
    STRING(anomaly_details.last_valid_capture_id) AS last_valid_capture_id,
    DATE_DIFF(DATE(STRING(anomaly_details.capture_date)), DATE(STRING(anomaly_details.last_valid_capture_date)), DAY) AS last_valid_days,
    ARRAY_CONCAT_AGG(field_names) AS fields,
    NET.REG_DOMAIN(ANY_VALUE(STRING(anomaly_details.capture_url))) AS domain,
    REGEXP_EXTRACT(NET.REG_DOMAIN(ANY_VALUE(STRING(anomaly_details.capture_url))), r'^([^.]+)') AS subdomain
  FROM brand_monitor_data_quality.anomalies
  WHERE date=DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
  AND detector_type='missing-value'
  AND ARRAY_LENGTH(field_names)>0
  GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9
),
r2 AS (
  SELECT
    seller_name,
    seller_id,
    country_code,
    ANY_VALUE(domain) AS domain,
    ANY_VALUE(subdomain) AS subdomain,
    ARRAY_CONCAT_AGG(fields) AS fields,
    ARRAY_AGG(
      STRUCT(
        account_key,
        sku,
        capture_url,
        capture_id,
        last_valid_capture_id,
        last_valid_days,
        (ARRAY(SELECT DISTINCT field FROM UNNEST(fields) AS field ORDER BY field)) AS fields
      )
      ORDER BY account_key, sku
    ) AS skus
  FROM r1
  GROUP BY 1, 2, 3
)
SELECT
  *
  EXCEPT(skus)
  REPLACE(ARRAY(SELECT DISTINCT * FROM UNNEST(fields) ORDER BY 1) AS fields),
  ARRAY(SELECT DISTINCT account_key FROM UNNEST(skus) ORDER BY 1) AS accounts,
  (SELECT COUNT (DISTINCT account_key) FROM UNNEST(skus)) AS account_count,
  (SELECT COUNT (DISTINCT value) FROM UNNEST(fields) AS value) AS field_count,
  ARRAY_LENGTH(skus) AS sku_count,
  (SELECT MIN(last_valid_days) FROM UNNEST(skus)) AS last_valid_days_min,
  (SELECT MAX(last_valid_days) FROM UNNEST(skus)) AS last_valid_days_max,
  skus
FROM r2
ORDER BY 1;