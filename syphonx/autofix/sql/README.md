

Some recommendations are null and some have errors in them...
```
SELECT *
FROM brand_monitor_data_quality.seller_anomalies
WHERE date=DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
AND STRING(recommendation.`parsedef-path`) IS NULL
ORDER BY seller_name, solution;
```

## Questions
1. What % of url's have been healed by autoheal?
2. What % of anomalies detected have been autohealed?


## Brand Monitor Autoheal Workstreams
1. Yield
    a. schema.org
    b. gpt/html extraction
    c. page classifier
2. Metrics
3. Commit Process Workflow

# PROWL Ops Data QA Dashboard
1. 


breakdown for urls with outstanding anomalies
    by country
    by selectors