CREATE OR R<PERSON>LACE TABLE syphonx.autofix_metrics
AS
WITH
seller_config AS (
  SELECT
    seller_id,
    product_library='syphonx' AS product_page_enabled,
    search_library='syphonx' AS search_page_enabled,
    category_library='syphonx' AS category_page_enabled,
    review_library='syphonx' AS review_page_enabled,
    question_library='syphonx' AS question_page_enabled,
  FROM brand_monitor.seller_config
  WHERE active
),
autofix_commits AS (
  SELECT
    seller_id,
    selector_name,
    url_count,
    DATE(created_at) AS date,
    status='confirmed' AS confirmed
  FROM syphonx.autofix_commit_status
  QUALIFY ROW_NUMBER() OVER (PARTITION BY seller_id, selector_name ORDER BY created_at DESC) = 1
),
sellers_flat AS (
  SELECT
    date,
    seller_name,
    seller_id,
    country_code,
    REGEXP_EXTRACT(STRING(report.field_names), r'^([^,]+)') AS selector_name,
    recommendation.`new-selector` IS NOT NULL AS fix_available,
    INT64(report.affected_products) AS fix_count,
    product_page_enabled AS seller_enabled,
    INT64(report.total_products) AS match_count
  FROM brand_monitor_data_quality.seller_anomalies
  LEFT JOIN seller_config USING(seller_id)
  WHERE solution='product_page'
  AND date>'2023-08-15'
),
sellers_commits_flat AS (
  SELECT *, fix_available AND fix_count>0 AND seller_enabled AS fixable
  FROM sellers_flat
  LEFT JOIN autofix_commits USING (seller_id, selector_name, date)
),
sellers_grouped AS (
  SELECT
    date,
    seller_name,
    seller_id,
    country_code,
    ARRAY_AGG(
      STRUCT(
        selector_name,
        fix_available,
        fix_count,
        fixable,
        confirmed
      )
      ORDER BY selector_name
    ) AS autofixes,
    ANY_VALUE(seller_enabled) AS seller_enabled,
    ANY_VALUE(match_count) AS match_count
  FROM sellers_commits_flat
  GROUP BY 1, 2, 3, 4
)
SELECT * EXCEPT(autofixes),
  (SELECT COUNT(*) FROM UNNEST(autofixes) WHERE fixable) AS selector_count,
  (SELECT SUM(fix_count) FROM UNNEST(autofixes) WHERE fixable) AS fixable_count,
  (SELECT SUM(fix_count) FROM UNNEST(autofixes) WHERE fixable AND confirmed) AS confirmed_count
FROM sellers_grouped
ORDER BY seller_name, date DESC;