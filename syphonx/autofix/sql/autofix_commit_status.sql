CREATE OR REPLACE VIEW syphonx.autofix_commit_status
AS
SELECT
  commit_id,
  seller_name,
  seller_id,
  country_code,
  template_type,
  template_path,
  selector_name,
  IFNULL(status, 'pending') AS status,
  IFNULL(brand_monitor.product_page_enabled, FALSE) AS seller_enabled,
  url_count,
  timestamp AS created_at,
  updated_at
FROM syphonx.autofix_commits, UNNEST(commit)
LEFT JOIN syphonx.autofix_commit_status_log USING (commit_id, selector_name)
LEFT JOIN syphonx.seller_config USING(seller_id)
QUALIFY ROW_NUMBER() OVER (PARTITION BY template_path, selector_name ORDER BY COALESCE(updated_at, created_at) DESC) = 1;