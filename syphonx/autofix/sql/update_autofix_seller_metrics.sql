CREATE OR REPLACE PROCEDURE syphonx.update_autofix_seller_metrics()
BEGIN

DECLARE max_lookback INT64 DEFAULT 7;

CREATE TEMP FUNCTION hasValue(json STRING, fieldsList STRING)
RETURNS BOOL
LANGUAGE js AS """
  const obj = JSON.parse(json);
  return fieldsList
    .split(',')
    .map(field => {
      const val = obj[field];
      switch (typeof val) {
        case 'string':
          return val.trim() !== '';

        case 'number':
          return val !== 0;

        case 'object':
          return val?.length > 0;
        
        default:
          return false;
      }
    })
    .some(hasValue => hasValue);
""";

CREATE OR REPLACE TABLE temp.autofix_seller_metrics
AS
WITH
-- collect date ranges for which we have anomalies
dates AS (
  SELECT DISTINCT date
  FROM `ps-bigdata.brand_monitor_data_quality.seller_anomalies` AS sa
  WHERE sa.date >= '2023-08-01'

  -- DEBUG DATE
  -- WHERE sa.date = '2023-09-07'
),

-- from most granular level (date,seller,field,sku), determine if there was a registered anomaly within last "max_lookback" days
-- this way we can carry an anomaly over no-capture dates
anomalies AS (
  SELECT
    d.date,
    seller_id,
    ARRAY_TO_STRING(field_names, ',') AS field_names,
    JSON_EXTRACT_SCALAR(a.anomaly_details.sku) AS sku,
    MAX(a.date) AS anomaly_date
  FROM dates AS d
    JOIN `ps-bigdata.brand_monitor_data_quality.anomalies` AS a
      ON a.date BETWEEN DATE_SUB(d.date, INTERVAL max_lookback DAY) AND d.date
  WHERE detector_type = 'missing-value'
    AND solution = 'product_page'
  GROUP BY 1, 2, 3, 4
),

-- filter out anomalies where anomaly_date is less then capture_date - that means there was a new capture which did not generate an anomaly therefore we can assume it was fixed (one way or another)
-- this leaves outstanding anomalies
captures AS (
  SELECT 
    a.date,
    a.seller_id,
    a.field_names,
    a.sku,
    a.anomaly_date,
    MAX(DATE(TIMESTAMP_TRUNC(pac.capture_date, DAY))) AS capture_date
  FROM anomalies AS a
    LEFT OUTER JOIN `ps-bigdata.brand_monitor.product_page_captures` AS pac
      ON capture_date BETWEEN TIMESTAMP(a.anomaly_date) AND TIMESTAMP(a.date)
      AND pac.seller_id = a.seller_id
      AND pac.sku = a.sku
      AND hasValue(pac.data, a.field_names)
  GROUP BY 1,2,3,4,5
  HAVING MAX(DATE(TIMESTAMP_TRUNC(pac.capture_date, DAY))) IS NULL          -- no new capture 
    OR MAX(DATE(TIMESTAMP_TRUNC(pac.capture_date, DAY))) = a.anomaly_date   -- capture generated an anomaly
)
SELECT
  c.date,
  COUNT(DISTINCT seller_id) AS affected_sellers,
  COUNT(DISTINCT CONCAT(seller_id, ':', sku)) AS affected_urls
FROM captures AS c
GROUP BY 1
ORDER BY 1 DESC;

CREATE OR REPLACE TABLE temp.autofix_commit_status
AS
SELECT * FROM syphonx.autofix_commit_status;

END;