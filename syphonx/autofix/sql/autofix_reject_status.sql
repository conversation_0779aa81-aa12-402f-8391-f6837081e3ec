CREATE OR REPLACE VIEW syphonx.autofix_reject_status
AS
SELECT
  commit_id,
  seller_name,
  seller_id,
  country_code,
  template_type,
  template_path,
  selector_name,
  'rejected' AS status,
  IFNULL(brand_monitor.product_page_enabled, FALSE) AS seller_enabled,
  url_count,
  timestamp AS created_at,
  CAST(NULL AS TIMESTAMP) AS updated_at
FROM syphonx.autofix_rejects, UNNEST(commit)
LEFT JOIN syphonx.seller_config USING(seller_id)
QUALIFY ROW_NUMBER() OVER (PARTITION BY template_path, selector_name ORDER BY created_at DESC) = 1;