CREATE OR REPLACE VIEW syphonx.autofix_pending_commits
AS
WITH
last_autofix AS (
  SELECT
    STRING(recommendation.`parsedef-path`) AS template_path,
    STRING(recommendation.`parsedef-action`.name) AS selector_name,
    date AS last_autofix_date
  FROM brand_monitor_data_quality.seller_anomalies
  WHERE STRING(recommendation.`parsedef-path`) IS NOT NULL
  AND STRING(recommendation.`parsedef-action`.name) IS NOT NULL
  QUALIFY ROW_NUMBER() OVER (PARTITION BY template_path, selector_name ORDER BY date DESC) = 1
),
seller_config AS (
  SELECT
    seller_id,
    product_library='syphonx' AS product_page_enabled,
    search_library='syphonx' AS search_page_enabled,
    category_library='syphonx' AS category_page_enabled,
    review_library='syphonx' AS review_page_enabled,
    question_library='syphonx' AS question_page_enabled,
  FROM brand_monitor.seller_config
  WHERE active
),
pending_commits AS (
  SELECT
    *,
    IFNULL(
      CASE
        WHEN template_type='brand_monitor/product_page' THEN product_page_enabled
        WHEN template_type='brand_monitor/search_page' THEN search_page_enabled
        WHEN template_type='brand_monitor/category_page' THEN category_page_enabled
        WHEN template_type='brand_monitor/review_page' THEN review_page_enabled
        WHEN template_type='brand_monitor/question_page' THEN question_page_enabled
      END,
      FALSE
    ) AS template_enabled,
    (SELECT MAX(last_autofix_date) AS current_date FROM last_autofix) AS current_date
  FROM syphonx.autofix_commit_status
  LEFT JOIN last_autofix USING(template_path, selector_name)
  LEFT JOIN seller_config USING(seller_id)
  WHERE status='pending'
)
SELECT
  seller_name,
  seller_id,
  country_code,
  template_type,
  template_path,
  template_enabled,
  selector_name,
  status,
  url_count,
  commit_id,
  created_at,
  updated_at,
  --last_autofix_date,
  --current_date,
  IF(template_enabled, current_date > last_autofix_date AND DATE(created_at) <= last_autofix_date, FALSE) AS confirmed, --if last_autofix_date is less than the current_date then it is confirmed
  IF(template_enabled AND current_date > last_autofix_date AND DATE(created_at) <= last_autofix_date, DATE_DIFF(last_autofix_date, DATE(created_at), DAY), NULL) AS confirmed_days
FROM pending_commits
ORDER BY seller_name, template_type, selector_name;