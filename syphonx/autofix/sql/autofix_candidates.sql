CREATE OR <PERSON><PERSON>LACE VIEW syphonx.autofix_candidates
AS
WITH
template_status AS (
  SELECT
    template_path,
    ANY_VALUE(status) AS status,
    ARRAY_AGG(selector_name ORDER BY selector_name) AS pending_selectors --todo: confusing to have rejected fields in here
  FROM syphonx.autofix_status
  WHERE status IN ('pending', 'rejected')
  --AND created_at>DATE_SUB(CURRENT_DATE(), INTERVAL 10 DAY) --todo: later
  GROUP BY 1
),
seller_config AS (
  SELECT
    seller_id,
    product_library='syphonx' AS product_page_enabled,
    search_library='syphonx' AS search_page_enabled,
    category_library='syphonx' AS category_page_enabled,
    review_library='syphonx' AS review_page_enabled,
    question_library='syphonx' AS question_page_enabled,
  FROM brand_monitor.seller_config
  WHERE active
)
SELECT
  seller_name,
  seller_id,
  country_code,
  CONCAT('brand_monitor/', solution) AS template_type,
  STRING(ANY_VALUE(recommendation.`parsedef-path`)) AS template_path,
  IFNULL(
    CASE
      WHEN ANY_VALUE(solution)='product_page' THEN ANY_VALUE(product_page_enabled)
      WHEN ANY_VALUE(solution)='search_page' THEN ANY_VALUE(search_page_enabled)
      WHEN ANY_VALUE(solution)='category_page' THEN ANY_VALUE(category_page_enabled)
      WHEN ANY_VALUE(solution)='review_page' THEN ANY_VALUE(review_page_enabled)
      WHEN ANY_VALUE(solution)='question_page' THEN ANY_VALUE(question_page_enabled)
    END,
    FALSE
  ) AS template_enabled,
  ANY_VALUE(status) AS status,
  ARRAY_AGG(
    STRUCT(
      STRING(recommendation.`parsedef-action`.name) AS selector_name,
      STRING(recommendation.`new-selector`) AS selector,
      ROUND(FLOAT64(recommendation.`confidence`), 4) AS confidence,
      (SELECT COUNT(*) FROM UNNEST(pending_selectors) AS selector_name WHERE selector_name = STRING(recommendation.`parsedef-action`.name)) > 0 AS pending,
      ARRAY((
        SELECT AS STRUCT
          STRING(obj.capture_url) AS capture_url,
          STRING(obj.capture_id) AS capture_id,
          STRING(obj.last_valid_capture_id) AS last_valid_capture_id,
          DATE_DIFF(DATE(STRING(obj.capture_date)), DATE(STRING(obj.last_valid_capture_date)), DAY) AS last_valid_capture_days
        FROM UNNEST(JSON_EXTRACT_ARRAY(report.examples)) AS obj
      )) AS captures
    )
  ) AS selectors
FROM brand_monitor_data_quality.seller_anomalies
LEFT JOIN template_status ON template_path=STRING(recommendation.`parsedef-path`)
LEFT JOIN seller_config USING(seller_id)
WHERE date=DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
AND solution='product_page'
AND detector_type='missing-value'
AND recommendation IS NOT NULL
AND JSON_EXTRACT_SCALAR(recommendation.`new-selector`) IS NOT NULL
GROUP BY 1, 2, 3, 4
ORDER BY seller_name, template_type;