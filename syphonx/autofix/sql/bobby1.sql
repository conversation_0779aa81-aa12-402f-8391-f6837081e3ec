WITH 
  -- Start with history of anomalies, stats aggregated on the daily
  -- Downstream stages gather specific aspects/facets for each date. This way we break down a complicated time-series analititcs into more managable subqueries. Also avoids cross-fascet contaminations where UNNEST/JOINS from one aggregation fascet affects another if implemented in a single SELECT statement  
  stage_1 AS (
    SELECT  
      sa.date,
      COUNT(*) AS seller_field_anomalies,
      SUM(CAST(JSON_EXTRACT_SCALAR(sa.report.anomalies) AS INT64)) AS affected_urls,
      COUNTIF(JSON_EXTRACT_SCALAR(sa.recommendation.`new-selector`) IS NOT NULL) AS autofix_recommendations,
      SUM(CASE WHEN JSON_EXTRACT_SCALAR(sa.recommendation.`new-selector`) IS NOT NULL THEN CAST(JSON_EXTRACT_SCALAR(sa.report.anomalies) AS INT64) ELSE 0 END) AS autofix_recomendations_affected_urls,
      ARRAY_AGG(DISTINCT sa.seller_id) AS seller_ids
    FROM `ps-bigdata.brand_monitor_data_quality.seller_anomalies` AS sa
    WHERE sa.solution = 'product_page'
    GROUP BY sa.date

    -- DEBUG date - not processed yet so we dont have records but there are autofix confirmations we want to show
    UNION ALL

    SELECT 
      DATE('2023-09-05') AS date,
      0 AS seller_field_anomalies,
      0 AS affected_urls,
      0 AS autofix_recommendations,
      0 AS autofix_recomendations_affected_urls,
      [] AS seller_ids
  ),

  -- gather seller stats - specifically how many are SyphonX and therefore autofixable
  stage_2 AS (
    SELECT 
      s.date, 
      s.seller_field_anomalies, 
      s.affected_urls, 
      s.autofix_recommendations, 
      s.autofix_recomendations_affected_urls,
      ANY_VALUE(s.seller_ids) AS seller_ids,
      ARRAY_LENGTH(s.seller_ids) AS affected_sellers,
      COUNT(DISTINCT sc.seller_id) AS syphonx_sellers
    FROM stage_1 AS s
    LEFT OUTER JOIN `ps-bigdata.brand_monitor.seller_config` AS sc
      ON sc.seller_id IN UNNEST(s.seller_ids)
      AND sc.active
      AND sc.product_library = 'syphonx'
    GROUP BY 
      s.date, 
      s.seller_field_anomalies, 
      s.affected_urls, 
      s.autofix_recommendations, 
      s.autofix_recomendations_affected_urls,
      ARRAY_LENGTH(s.seller_ids)
  ),

  -- gather commit and confirms stats
  stage_3 AS (
    SELECT 
      s.date,
      s.seller_field_anomalies,
      s.affected_sellers,
      s.affected_urls,
      s.autofix_recommendations, 
      s.autofix_recomendations_affected_urls,
      CASE WHEN s.affected_sellers > 0 THEN s.syphonx_sellers / s.affected_sellers ELSE 0 END AS syphonx_ratio,
      COUNT(DISTINCT CONCAT(ac.commit_id, ac.field)) AS autofix_commits,
      COUNT(DISTINCT CONCAT(acsl.commit_id, acsl.field)) AS autofix_confirms,
      ARRAY_AGG(STRUCT(acsl.commit_id, acsl.field)) AS autofix_confirms_commit_ids
    FROM stage_2 AS s 
    LEFT OUTER JOIN (
      SELECT
        ac.commit_id,
        ac.seller_id,
        commit.field,        
        ac.timestamp
      FROM `syphonx.autofix_commits` AS ac, 
        UNNEST(ac.commit) AS commit) AS ac
      ON DATE(ac.timestamp) = s.date    
    LEFT OUTER JOIN `syphonx.autofix_commit_status_log` AS acsl
      ON DATE(acsl.updated_at) = s.date
      AND acsl.status = 'confirmed'
    GROUP BY s.date,
      s.seller_field_anomalies,
      s.affected_sellers,
      s.affected_urls,
      s.autofix_recommendations, 
      s.autofix_recomendations_affected_urls,
      CASE WHEN s.affected_sellers > 0 THEN s.syphonx_sellers / s.affected_sellers ELSE 0 END
  ),

  -- gather confirms' urls stats - Intermitten stage to find anomaly's date of a confirmed autofix. This is strictly needed because syphonx.autofix_commits does not reference the date of the anomaly's selector recommendation it commited
  stage_4 AS (
    SELECT 
      s.date,
      s.seller_field_anomalies,
      s.affected_sellers,
      s.affected_urls,
      s.autofix_recommendations, 
      s.autofix_recomendations_affected_urls,
      s.syphonx_ratio,
      s.autofix_commits,
      s.autofix_confirms,
      ac.seller_id,
      ac.field,
      MAX(sa.date) AS anomaly_date
    FROM stage_3 AS s,
      UNNEST(s.autofix_confirms_commit_ids) AS confirm_id
    LEFT OUTER JOIN (
        SELECT
          ac.commit_id,
          ac.seller_id,
          commit.field,
          ac.timestamp
        FROM `syphonx.autofix_commits` AS ac,
          UNNEST(ac.commit) AS commit) AS ac
      ON ac.commit_id = confirm_id.commit_id
      AND ac.field = confirm_id.field
    LEFT OUTER JOIN `ps-bigdata.brand_monitor_data_quality.seller_anomalies` AS sa
      ON sa.date <= DATE(ac.timestamp)
      AND sa.solution = 'product_page'
      AND sa.seller_id = ac.seller_id
      AND ac.field IN UNNEST(SPLIT(JSON_EXTRACT_SCALAR(sa.report.field_names), ','))
    GROUP BY s.date,
      s.seller_field_anomalies,
      s.affected_sellers,
      s.affected_urls,
      s.autofix_recommendations, 
      s.autofix_recomendations_affected_urls,
      s.syphonx_ratio,
      s.autofix_commits,
      s.autofix_confirms,
      ac.seller_id,
      ac.field
  ),

  -- gather confirms' urls stats - aka how many urls were fixed
  stage_5 AS (
    SELECT 
      s.date,
      s.seller_field_anomalies,
      s.affected_sellers,
      s.affected_urls,
      s.autofix_recommendations, 
      s.autofix_recomendations_affected_urls,
      s.syphonx_ratio,
      s.autofix_commits,
      s.autofix_confirms,
      SUM(CAST(JSON_VALUE(sa.report.anomalies) AS INT64)) AS autofix_confirms_affected_urls
    FROM stage_4 AS s
    LEFT OUTER JOIN `ps-bigdata.brand_monitor_data_quality.seller_anomalies` AS sa
      ON sa.date = s.anomaly_date
      AND sa.solution = 'product_page'
      AND sa.seller_id = s.seller_id
      AND s.field IN UNNEST(SPLIT(JSON_EXTRACT_SCALAR(sa.report.field_names), ','))
    GROUP BY s.date,
      s.seller_field_anomalies,
      s.affected_sellers,
      s.affected_urls,
      s.autofix_recommendations, 
      s.autofix_recomendations_affected_urls,
      s.syphonx_ratio,
      s.autofix_commits,
      s.autofix_confirms
  )

SELECT *
FROM stage_5
ORDER BY date ASC