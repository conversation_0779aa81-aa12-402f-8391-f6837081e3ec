--REWORK #3
CREATE OR REPLACE TABLE syphonx.autofix_commits
AS
WITH
fixes AS (
  SELECT
    date,
    --seller_name,
    seller_id,
    --country_code,
    REGEXP_EXTRACT(STRING(report.field_names), r'^([^,]+)') AS selector_name,
    recommendation.`new-selector` IS NOT NULL AS fix_available,
    INT64(report.affected_products) AS fix_count,
    INT64(report.total_products) AS match_count
  FROM brand_monitor_data_quality.seller_anomalies
  WHERE solution='product_page'
  QUALIFY ROW_NUMBER() OVER (PARTITION BY date, seller_id ORDER BY INT64(report.affected_products) DESC) = 1
),
commits AS (
  SELECT
    commit_id,
    seller_name,
    seller_id,
    country_code,
    template_type,
    template_path,
    timestamp,
    comment,
    field AS selector_name, --rename
    before,
    after,
    hits, --rename
    DATE(timestamp) AS date
  FROM syphonx.autofix_commits, UNNEST(commit)
),
commits_with_fix_counts AS (
  SELECT *
  FROM commits
  LEFT JOIN fixes USING(date, seller_id, selector_name)
)
SELECT
  commit_id,
  ANY_VALUE(seller_name) AS seller_name,
  ANY_VALUE(seller_id) AS seller_id,
  ANY_VALUE(country_code) AS country_code,
  ANY_VALUE(template_type) AS template_type,
  ANY_VALUE(template_path) AS template_path,
  ANY_VALUE(timestamp) AS timestamp,
  ARRAY_AGG(
    STRUCT(
      selector_name, --renamed from field
      before,
      after,
      COALESCE(fix_count, hits) AS url_count --renamed from hits
    )
  ) AS commit,
  ANY_VALUE(comment) AS comment,
FROM commits_with_fix_counts
GROUP BY 1;


--REWORK #2
CREATE OR REPLACE TABLE syphonx.autofix_commits
AS
SELECT
  autofix_id,
  seller_name,
  seller_id,
  country_code,
  template_type,
  template_path,
  IF(accepted, 'accepted', 'rejected') AS status,
  timestamp,
  ARRAY(SELECT AS STRUCT key AS field, before, after FROM UNNEST(diffs)) AS commit,
  comment
FROM syphonx.autofix_commit_log;


--REWORK #1
CREATE OR REPLACE TABLE syphonx.autofix_commits
AS
SELECT
  LEFT(TO_HEX(MD5(template_path)), 15) AS commit_id,
  seller_name,
  seller_id,
  country_code,
  template_type,
  template_path,
  timestamp,
  commit,
  comment
FROM syphonx.autofix_commits
WHERE status='accepted';