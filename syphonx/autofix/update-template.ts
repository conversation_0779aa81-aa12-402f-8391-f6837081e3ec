import { clone } from "./lib/index.js";
import * as syphonx from "./syphonx.js";
import { Template } from "syphonx-lib";

export interface UpdateTemplateResult {
    template: Template;
    diffs: Diff[];
    errors: string[];
}

export interface Diff {
    selector_name: string;
    before: string;
    after: string;
    url_count: number;
}

export function updateTemplate(template: Template, updates: Array<[string, string, number]>): UpdateTemplateResult {
    template = clone(template);
    const result: UpdateTemplateResult = {
        template,
        diffs: [],
        errors: []
    };
    for (const [field, selector, hits] of updates) {
        const select = syphonx.findSelectAction(template, field);
        if (select?.query) {
            for (const q of select.query) {
                if (q[0] === selector) {
                    result.errors.push(`select action for "${field}" already has selector "${selector}"`);
                    return result;
                }
            }
            const before = JSON.stringify(select.query);
            const query = clone(select.query[0]);
            query[0] = selector;
            select.query.push(query);
            const after = JSON.stringify(select.query);
            result.diffs.push({ selector_name: field, before, after, url_count: hits });
        }
        else {
            result.errors.push(`select action for "${field}" not found`);
        }
    }
    return result;
}
