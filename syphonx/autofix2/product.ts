import { promises as fs } from "fs";

export interface ProductReport {
    template: string;
    key: string;
    recommendation: Recommendation;
    report: Report;
}

export interface Recommendation {
    selector: string;
    confidence: number;
    notes: string;
}

export interface Report {
    examples: Example[];
}

export interface Example {
    capture_id: string;
    capture_url: string;
    //last_valid_capture_id: string;
    //sku: string;
    //product_id: number;
    //product_name: string;
}

export async function loadProductReport(name: string): Promise<ProductReport> {
    const data = await fs.readFile(name, "utf8");
    return JSON.parse(data) as ProductReport;
}
