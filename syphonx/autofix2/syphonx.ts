import * as playwright from "playwright";
import * as syphonx from "syphonx-lib";

import {
    SyphonXApi,
    ExtractResult,
    ExtractState,
    LoadTemplateResult,
    Select,
    Template,
    invokeAsyncMethod
} from "syphonx-lib";

export interface RunOptions {
    template: Template | string;
    url: string;
    html?: string;
}

export function findSelector(template: Template, name: string): Select | undefined {
    const actions = template.actions
        .filter(action => action.hasOwnProperty("select"))
        .map(action => (action as { select: Select[] }).select);
    for (const action of actions)
        for (const select of action)
        if (select.name === name)
            return select;
}

export async function loadTemplate(name: string): Promise<LoadTemplateResult> {
    const api = new SyphonXApi(process.env.SYPHONX_API_KEY);
    const result = await api.loadTemplate(name);
    return result;
}

export async function saveTemplate(name: string, template: Template): Promise<void> {
    const api = new SyphonXApi(process.env.SYPHONX_API_KEY);
    const data = JSON.stringify(template, null, 2); // todo: pretty
    await api.write(name, data);
}

export async function extract({ template, html, url }: RunOptions): Promise<ExtractResult> {
    const api = new SyphonXApi(process.env.SYPHONX_API_KEY);
    const script = new Function("state", `return ${syphonx.script}(state)`) as (state: ExtractState) => ExtractState;
    const browser = await playwright.chromium.launch();
    const page = await browser.newPage();

    const result = await api.run({
        template,
        url,
        html,
        unwrap: true,
        onExtract: async state => {
            const result = await page.evaluate<ExtractState, ExtractState>(script, state);
            return result;
        },
        onGoback: async ({ timeout, waitUntil }) => {
            const response = await page.goBack({ timeout, waitUntil });
            const status = response?.status();
            return { status };
        },
        onHtml: async () => {
            const html = await page.evaluate(() => document.querySelector("*")!.outerHTML);
            return html;
        },
        onLocator: async ({ frame, selector, method, params }) => {
            let locator = undefined as playwright.Locator | undefined;
            if (frame)
                locator = await page.frameLocator(frame).locator(selector);
            else
                locator = await page.locator(selector);
            const result = await invokeAsyncMethod(locator, method, params);
            return result;
        },
        onNavigate: async ({ url, timeout, waitUntil }) => {
            const response = await page.goto(url, { timeout, waitUntil });
            const status = response?.status();
            return { status };
        },
        onReload: async ({ timeout, waitUntil }) => {
            const response = await page.reload({ timeout, waitUntil });
            const status = response?.status();
            return { status };
        },
        onScreenshot: async ({ selector, fullPage, ...options }) => {
            const path = `./screenshots/${new Date().toLocaleString("en-US", { hour12: false }).replace(/:/g, "-").replace(/\//g, "-").replace(/,/g, "")}.png`;
            let clip: { x: number, y: number, height: number, width: number } | undefined = undefined;
            if (selector)
                clip = await page.evaluate(() => document.querySelector(selector)?.getBoundingClientRect());
            await page.screenshot({ ...options, path, clip, fullPage });
        },
        onYield: async ({ timeout, waitUntil }) => {
            await page.waitForLoadState(waitUntil, { timeout });
        }
    });

    return result;
}
