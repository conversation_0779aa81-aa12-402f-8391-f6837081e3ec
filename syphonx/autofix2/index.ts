import * as dotenv from "dotenv";
import { BigQuery } from "@google-cloud/bigquery";

dotenv.config();

const bigquery = new BigQuery();

interface TemplateHealth {
    seller_name: string;
    seller_id: number;
    country_code: string;
    domain: string;
    subdomain: string;
    fields: string[];
    accounts: string[];
    account_count: number;
    field_count: number;
    sku_count: number;
    last_valid_days_min: number;
    last_valid_days_max: number;
    skus: TemplateHealthSku[];
}

interface TemplateHealthSku {
    account_key: string;
    sku: string;
    capture_url: string;
    capture_id: string;
    last_valid_capture_id: string;
    last_valid_days: number;
    fields: string[];
}

const [rows] = await bigquery.query(`SELECT seller_name FROM syphonx.template_health ORDER BY 1`) as [TemplateHealth[]];
for (const row of rows) {
    console.log(row.seller_name);
}