import * as dotenv from "dotenv";
import * as syphonx from "./syphonx.js";
import * as venom from "./venom.js";
import { loadProductReport } from "./product.js";

dotenv.config();

if (process.argv[2] === "--help") {
    console.log(`Usage: node ${process.argv[1]} [--online] [--max <number>] [--save]`);
    process.exit(0);
}

const online = !!process.argv.includes("--online");
const max = process.argv.includes("--max") ? parseInt(process.argv[process.argv.indexOf("--max") + 1]) : undefined;
const save = process.argv.includes("--save");

const product = await loadProductReport("./product1.json");
const { template } = await syphonx.loadTemplate(product.template);
const select = syphonx.findSelector(template, product.key);
const selector1 = select?.query ? select?.query[0][0] : undefined;
const selector2 = product.recommendation.selector;

const template2 = JSON.parse(JSON.stringify(template));
const select2 = syphonx.findSelector(template2, product.key);
select2!.query![0][0] = selector2;

console.log(`template: ${product.template}`);
console.log(product.recommendation.notes);
console.log(`field: ${product.key}`);
console.log(`current selector: ${selector1}`);
console.log(`fixed selector: ${selector2}`);
console.log(`confidence: ${product.recommendation.confidence.toFixed(2)}`)

const examples = product.report.examples.slice(0, max);
const m = examples.length;
const result = {
    scanned: 0,
    fixed: 0,
    unfixed: 0,
    skipped: 0
};
console.log(`scanning ${m} pages ${online ? "ONLINE" : "OFFLINE"}...`);
for (const example of examples) {
    const n = product.report.examples.indexOf(example) + 1;
    const url = example.capture_url;
    const html = !online ? await venom.loadCaptureHtml(example.capture_id) : undefined;

    const result1 = await syphonx.extract({ template, url, html });
    const data1 = result1.data[product.key];

    if (!data1) {
        const result2 = await syphonx.extract({ template: template2, url, html });
        const data2 = result2.data[product.key];
        const fixed = !!data2;
        console.log(`[${n}/${m}] ${url} ${fixed ? "FIXED" : "NOT FIXED"}`);
        if (fixed)
            result.fixed += 1;
        else
            result.unfixed += 1;
    }
    else {
        console.log(`[${n}/${m}] ${url} SKIPPED`);
        result.skipped += 1;
    }
    result.scanned += 1;
}

console.log(`${result.fixed} fixed, ${result.unfixed} unfixed, ${result.skipped} skipped, ${result.scanned} total`);
if (save) {
    await syphonx.saveTemplate(product.template, template2);
    console.log(`template ${product.template} saved`);
}
else {
    console.log(`template ${product.template} not saved`);
}

process.exit();
