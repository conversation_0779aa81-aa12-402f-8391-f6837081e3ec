import * as dotenv from "dotenv";
dotenv.config();

import * as syphonx from "syphonx-lib";
import chalk from "chalk";
import { flattenTemplateSelect } from "syphonx-lib";

import {
    classifyLivePage,
    classifyScreenshotUrl,
    createRemoteLogStream,
    autocloseAnalyze,
    closeLogStream,
    diffDate,
    formatJson,
    generateTimestamp,
    glob,
    hookConsoleLog,
    locateContent,
    mapPageClassificationToAuditCode,
    openPage,
    parseArgs,
    queryAudits,
    queryAuditBlackList,
    queryTargetSelectorRegressions,
    renderJsonValueToString,
    renderScreenshotToStorageFile,
    sleep,
    summarize,
    truncate,
    tryLoadTemplate,
    tryParseJson,
    unhookConsoleLog,
    validateSelectorResult,
    waitForKeypress,
    AuditRecordCreator,
    AuditRecord,
    ClassifyLivePageResult,
    LLMModel,
    QueryTargetSelectorRegressionsResult,
    Timer,
    WaitFor
}
from "./lib/index.js";

interface SellerStatus {
    audited: number;
    confirmed: number;
    strikes: number;
}

const args = parseArgs({
    required: {
        0: "brand_monitor, wtb, prowl",
        1: "selector name target, one only (default=all)"
    },
    optional: {
        country: "country-code filter, one only",
        seller: "seller-id filter, one only",
        sku: "sku filter regex",
        capture_days: "include recent captures from the specified number of days (default=none)",
        audit_days: "include recent audit status from the specified number of days (default=30)",
        threshold: "include domains that have at least this many regressions (default=10)",
        limit: "include at most this number of regressions for each domain, ignored if all specified (default=10)",
        confirmed: "number of confirmed regression before skipping to the next domain (default=0)",
        strikes: "max number of strikes before skipping to the next domain (default=3)",
        pause: "pause for user input after each page load",
        snooze: "amount of time in seconds to snooze after loading a live page (default=2) (ignored if pause option is used)",
        countries: "preview countries",
        screenshots: "include screenshot verification",
        headless: "hide browser window (default=false)",
        waitfor: "specify load, domcontentloaded, or networkidle (default=none)",
        summary: "brief summary rollup by seller without doing any work",
        preview: "detailed page level preview without doing any work",
        force: "force audit by not excluding already audited sellers",
        fresh: "include fresh regressions since last audit",
        diagnose: "dumps detailed diagnostic data, implicitly sets preview and force options",
        test: "run but don't record any audit results",
        verbose: "verbose log output"
    },
    validate: params => {
        if (!["brand_monitor", "wtb", "prowl"].includes(params[0]))
            return "specify brand_monitor, wtb, or prowl only";
        if (params[1].includes(","))
            return "only one target selector allowed"
    }
});

const app_name = args[0];
const target_selector_name = args[1];
const model = args.model as LLMModel || "gpt-4o";
const country_code = args.country;
const seller_id = parseInt(args.seller) || undefined;
const sku = args.sku;
const capture_age_days = parseInt(args.capture_days) || 14;
const snooze = args.snooze ? parseInt(args.snooze) : 4;
const threshold = args.threshold ? parseInt(args.threshold) : 10;
const limit = args.limit ? parseInt(args.limit) : 10;
const fatal_errors = ["ERR_PROXY_CONNECTION_FAILED"];

const diagnose = !!args.diagnose;
const preview = !!args.preview || diagnose;
const force = !!args.force || diagnose || !!args.seller;
const test = !!args.test;

const options = {
    headless: !!args.headless,
    timeout: 10000,
    waitfor: (args.waitfor as WaitFor | undefined) || (!args.snooze && !args.pause ? "load" : undefined)
};

if (args.verbose)
    process.env.VERBOSE = "1";

type EvaluateArg = { select: syphonx.Select[] };
type EvaluateResult = syphonx.ExtractState;
type PageFunction = (arg: EvaluateArg) => Promise<EvaluateResult>;
const script = new Function("obj", `return ${syphonx.script}(obj)`) as PageFunction;

const limits = {
    confirmed: parseInt(args.confirmed) || 3,
    strikes: parseInt(args.strikes) || 3,
    classifications: parseInt(args.classifications) || 100
};

const default_status: SellerStatus = {
    audited: 0,
    confirmed: 0,
    strikes: 0
};

const timer = new Timer();
const rows = await queryTargetSelectorRegressions({ app_name, selector_name: target_selector_name, country_code, seller_id, sku, capture_age_days, diagnose });
const countries = Array.from(new Set(rows.map(row => row.country_code))).sort();
const seller_keys = Array.from(new Set(rows.map(row => row.seller_name))).sort();
const seller_status = seller_keys.reduce((result, key) => ({ ...result, [key]: { ...default_status } }), {} as Record<string, SellerStatus>);
console.log(chalk.gray(`${rows.length} regressions, ${seller_keys.length} sellers, ${countries.length} countries (${(timer.elapsed() / 1000).toFixed(1)} seconds)`));

const seller_map: Record<string, QueryTargetSelectorRegressionsResult[]> = {};
for (const row of rows) {
    if (!seller_map[row.seller_name])
        seller_map[row.seller_name] = [];
    seller_map[row.seller_name].push(row);
}

timer.reset();
const audits = await queryAudits({
    app_name,
    page_type: "product-page",
    selector_name: target_selector_name,
    seller_id: parseInt(args.seller) || undefined,
    days: parseInt(args.audit_days) || 30
});
console.log(chalk.gray(`${audits.length} existing audit records found (${(timer.elapsed() / 1000).toFixed(1)} seconds)`));

const audit_map: Record<string, AuditRecord[]> = {};
for (const audit of audits) {
    const hit = rows.find(row => row.page_id === audit.page_id);
    if (hit) {
        const key = hit.seller_name;
        if (!audit_map[key])
            audit_map[key] = [];
        audit_map[key].push(audit);
    }
}

const excluded_seller_keys = new Set<string>();
const counters = {
    black: 0,
    threshold: 0,
    audited: 0,
    fresh: 0,
    total: 0
};

if (!force) {
    console.log();
    const blacklist = await queryAuditBlackList();
    for (const key of Object.keys(seller_map))
        if (glob(blacklist, key)) {
            const [{ seller_id }] = seller_map[key];
            console.log(chalk.gray(`${chalk.strikethrough(`${key} ${seller_id}`)}: ${seller_map[key].length} (BLACK LISTED)`));
            excluded_seller_keys.add(key);
            counters.black += 1;
            counters.total += 1;
        }

    for (const key of Object.keys(seller_map))
        if (seller_map[key].length < threshold && !excluded_seller_keys.has(key)) {
            const [{ seller_id }] = seller_map[key];
            console.log(chalk.gray(`${chalk.strikethrough(`${key} ${seller_id}`)}: ${seller_map[key].length} regressions (REGRESSIONS BELOW THRESHOLD)`));
            excluded_seller_keys.add(key);
            counters.threshold += 1;
            counters.total += 1;
        }

    for (const key of Object.keys(seller_map))
        if (audit_map[key] && !excluded_seller_keys.has(key)) {
            const [last_audit] = audit_map[key];
            const [{ seller_id }] = seller_map[key];
            const recent_regressions = seller_map[key].filter(row => row.last_capture && last_audit?.audited_at ? row.last_capture.capture_date > last_audit.audited_at : false);
            if (recent_regressions.length >= threshold && args.fresh) {
                console.log(chalk.gray.white.italic(`${key} ${seller_id}: ${recent_regressions.length}/${seller_map[key].length} regressions since last audit ${diffDate(last_audit.audited_at)} ago (FRESH REGRESSIONS)`));
            }
            else {
                if (recent_regressions.length >= threshold)
                    console.log(chalk.white.italic(`${chalk.strikethrough(`${key} ${seller_id}`)}: ${recent_regressions.length}/${seller_map[key].length} regressions since last audit ${diffDate(last_audit.audited_at)} ago${!force ? " (FRESH REGRESSIONS)" : ""}`));
                else
                    console.log(chalk.gray(`${chalk.strikethrough(`${key} ${seller_id}`)}: ${recent_regressions.length}/${seller_map[key].length} regressions since last audit ${diffDate(last_audit.audited_at)} ago${!force ? " (ALREADY AUDITED)" : ""}`));
                excluded_seller_keys.add(key);
            }
            counters.audited += 1;
            counters.total += 1;
            if (recent_regressions.length >= threshold)
                counters.fresh += 1;
        }
}

const keys = seller_keys.filter(key => !excluded_seller_keys.has(key));

if (keys.length === 0) {
    console.log();
    console.log(chalk.gray("0 sellers queued"));
    if (excluded_seller_keys.size > 0)
        console.log(chalk.gray("hint: use --seller option to override a skipped seller, or --force to override all skipped sellers"));
    if (counters.fresh > 0)
        console.log(chalk.gray("hint: use --fresh option to include fresh regressions since last audit"));    
    process.exit(0);
}

console.log();
for (const key of keys)
    console.log(chalk.white(`${key}: queued (${seller_map[key].length} regressions)`));

if (args.countries) {
    console.log();
    for (const country of countries)
        console.log(chalk.gray(`${country} ${new Set(rows.filter(row => row.country_code === country).map(row => row.seller_name)).size} sellers, ${rows.filter(row => row.country_code === country).length} regressions`));
}

console.log();
console.log(chalk.gray(`${keys.length} sellers queued, ${counters.total} skipped (${counters.black} blacklisted, ${counters.threshold} regressions below threshold, ${counters.audited} already audited, ${counters.fresh} fresh since last audit)`));
if (excluded_seller_keys.size > 0)
    console.log(chalk.gray("hint: use --seller option to override a skipped seller, or --force to override all skipped sellers"));

if (counters.fresh > 0)
    console.log(chalk.gray("hint: use --fresh option to include fresh regressions since last audit"));

if (args.summary)
    process.exit(0);

if (preview) {
    for (const key of keys) {
        const rows = seller_map[key];
        const [{
            template_path,
            template_last_modified_date,
            template_last_selectors_changed,
            template_revision_count,
            seller_id
        }] = rows;

        const [last_page_audit] = audit_map[key] ?? [];
        const last_audited_at = last_page_audit?.audited_at;
        const last_audit_code = last_page_audit?.audit_code;

        console.log();
        console.log(chalk.underline(`${key} ${seller_id} [${keys.indexOf(key) + 1}/${keys.length}]`));
        console.log(chalk.gray(`template-path: ${template_path}`));
        console.log(chalk.gray(`last-modified: ${diffDate(template_last_modified_date)} ago`));
        console.log(chalk.gray(`last-selectors-changed: ${template_last_selectors_changed}`));
        console.log(chalk.gray(`revisions: ${template_revision_count}`));
        console.log(chalk.gray(`${rows.length} regressions on "${target_selector_name}"`));
        console.log(chalk.gray(`latest: ${diffDate(new Date(Math.max(...rows.map(row => row.last_capture.capture_date.getTime()))))} ago`));
        console.log(chalk.gray(`earliest: ${diffDate(new Date(Math.min(...rows.map(row => row.last_capture.capture_date.getTime()))))} ago`));
        if (last_audited_at)
            console.log(chalk.gray(`last audited: ${diffDate(last_audited_at)} ago (${last_audit_code})`));
        console.log();

        for (const row of rows.slice(0, limit)) {
            const page_audit = audit_map[key]?.find(audit => audit.page_id === row.page_id);
            console.log(`${row.page_id} (${diffDate(row.last_capture.capture_date)} ago) ${chalk.gray(row.capture_url)}`);
            if (args.diagnose) {
                const { last_attempt, last_capture } = row;
                console.log(chalk.gray(`last-capture: ${last_capture.capture_id} ${last_capture.capture_date} (${diffDate(last_capture.capture_date)} ago)`));
                console.log(`${chalk.gray("last-capture-screenshot:")} ${last_capture.screenshot_url ? `${chalk.gray(last_capture.screenshot_url)} ${!last_capture.screenshot_class ? chalk.gray("UNCLASSIFIED") : last_capture.screenshot_class !== "PRODUCT-PAGE" ? chalk.yellow(last_capture.screenshot_class.toUpperCase()) : chalk.gray(last_capture.screenshot_class.toUpperCase())}` : chalk.gray("NONE")}`);
                if (last_capture.capture_id !== last_attempt.capture_id)
                    console.log(chalk.gray(`last-attempt: ${last_attempt.capture_id} ${last_attempt.capture_date} (${diffDate(last_attempt.capture_date)} ago)`));
                else
                    console.log(chalk.gray(`last-attempt: (same)`));
                console.log(chalk.gray(`last-attempt-status: ${last_attempt.status}`));
                console.log(`${chalk.gray("last-attempt-screenshot:")} ${last_attempt.screenshot_url ? `${chalk.gray(last_attempt.screenshot_url)} ${!last_attempt.screenshot_class ? chalk.gray("UNCLASSIFIED") : last_attempt.screenshot_class !== "PRODUCT-PAGE" ? chalk.yellow(last_attempt.screenshot_class.toUpperCase()) : chalk.gray(last_attempt.screenshot_class.toUpperCase())}` : chalk.gray("NONE")}`);
                if (page_audit)
                    console.log(chalk.gray(`last-audit: ${page_audit.audit_code} (${diffDate(page_audit.audited_at)} ago, ${last_capture.capture_date > page_audit.audited_at! ? chalk.italic("AFTER last-capture") : chalk.italic("BEFORE last-capture")})`));
                console.log(chalk.gray(`${last_attempt.error_count} errors`));
                if (last_attempt.errors.length > 0)
                    console.log(chalk.red(last_attempt.errors.map(text => text.replaceAll("\n", " ").replace(/\s{2,}/g, " ")).join("\n")));

                console.log(chalk.gray(`${row.selectors.length} selectors`));
                for (const { selector_name, selector_json_value, regression, reference } of row.selectors) {
                    const json = !regression ? selector_json_value : reference?.selector_json_value;
                    let value = json ? renderJsonValueToString(json).replaceAll("\n", json.startsWith(`[`) ? ", " : " ") : undefined;
                    if (value !== undefined) {
                        const obj = tryParseJson(json);
                        if (Array.isArray(obj))
                            value = `[${obj.length}] ${value}`;
                        value = truncate(value, 200, true);
                    }
                    if (!regression)
                        console.log(chalk.gray(`${selector_name === target_selector_name ? ">": " "} ${selector_name}: ${value !== undefined ? value : chalk.italic("(null)")}`));
                    else
                        console.log(chalk.yellow(`${selector_name === target_selector_name ? ">": " "} ${selector_name}: ${value} ${chalk.italic(`(${diffDate(reference?.capture_date)} prior)`)}`));
                }

                console.log(chalk.blueBright(formatJson(last_capture.data)));
                console.log();
                }
        }            
        if (rows.length > limit)
            console.log(chalk.gray(`(+${rows.length - limit} more)`));

        if (audit_map[key]) {
            const [last_audit] = audit_map[key];
            const last_audit_time = diffDate(last_audit.audited_at);
            console.log(chalk.cyan.italic(`${audit_map[key].length} audit records (${last_audit_time} ago)`));
        }
    }
    console.log();
    console.log(`${keys.length} sellers queued`);
    process.exit(0);
}

for (const key of keys) {
    const rows = seller_map[key];
    const status = seller_status[key];

    const [{
        page_id,
        seller_name,
        seller_id,
        account_key,
        country_code,
        sku,
        template_path,
        template_last_selectors_changed,
        template_last_modified_date,
        template_revision_count,
        target_selector,
        capture_url: audit_url
    }] = rows;

    const audit_record = new AuditRecordCreator({
        app_name,
        selector_name: target_selector.selector_name,
        page_type: "product-page",
        page_id,
        seller_name,
        seller_id,
        account_key,
        country_code,
        sku,
        audit_url
    });

    console.log();
    console.log(chalk.underline(`${seller_name} ${seller_id} [${keys.indexOf(key) + 1}/${keys.length}]`));

    const result = await tryLoadTemplate(template_path);
    if (!result?.template) {
        console.log(chalk.red(`unable to load template ${template_path}${result.error ? ` ${JSON.stringify(result.error)}`: ""}`));
        if (!test)
            await audit_record.save("template-load-error", result.error ? JSON.stringify(result.error) : undefined);
        continue; // skip to next seller
    }

    const template = result?.template;
    const select = flattenTemplateSelect(template.actions, [target_selector.selector_name]);
    console.log(chalk.gray(`template-path: ${template_path} (${diffDate(template_last_modified_date)} ago) ${template_last_selectors_changed}`));
    console.log(chalk.gray(`revisions: ${template_revision_count}`));
    console.log(chalk.gray(`${rows.length} regressions`));
    console.log(chalk.gray(`${target_selector.selector_name}: ${select[0]?.query ? truncate(JSON.stringify(select[0].query), 200, true) : "(none)"}`));
    if (select.length === 0) {
        const message = `No selector for "${target_selector.selector_name}" in template ${template_path}`;
        console.log(chalk.yellow(message));
        audit_record.audit_url = rows[0]?.capture_url;
        if (!test)
            await audit_record.save("selector-not-found", message);
        continue; // skip to next seller
    }

    const [last_page_audit] = audit_map[seller_name] ?? [];
    const last_audited_at = last_page_audit?.audited_at;
    for (const row of rows) {
        const {
            page_id,
            capture_url,
            last_capture,
            last_attempt,
            target_selector,
            account_key,
            country_code,
            sku
        } = row;

        const page_audit = audit_map[seller_name]?.find(audit => audit.page_id === row.page_id);
        if (page_audit && !force) {
            console.log();
            console.log(`SKIPPED ${seller_name} ${page_id} ${page_audit.audit_code} [${rows.indexOf(row) + 1}/${rows.length}]`);
            continue; // skip to next url
        }

        if (last_audited_at && last_capture.capture_date <= last_audited_at && !force) {
            console.log();
            console.log(`SKIPPED ${seller_name} ${page_id} captured BEFORE last audit ${diffDate(last_audited_at)} ago [${rows.indexOf(row) + 1}/${rows.length}]`);
            continue; // skip to next url
        }

        status.audited += 1;
        audit_record.reset();
        audit_record.page_id = page_id;
        audit_record.audit_url = capture_url;
        audit_record.screenshot_url = undefined;
        audit_record.account_key = account_key;
        audit_record.country_code = country_code;
        audit_record.sku = sku;
        const timestamp = generateTimestamp("milliseconds");
        const storagePath = `audits/${timestamp}`;

        if (status.audited > limit) {
            console.log(chalk.gray(`${limit} audit limit exceeded, skipping to next seller`));
            break; // skip to next seller
        }

        console.log();
        console.log(`${seller_name} ${page_id} [${rows.indexOf(row) + 1}/${rows.length}]`);
        console.log(chalk.gray(`capture-id: ${last_capture.capture_id}`));
        console.log(chalk.gray(`capture-date: ${last_capture.capture_date} (${diffDate(last_capture.capture_date)} ago)`));
        console.log(chalk.gray(`capture-url: ${capture_url}`));
        console.log(chalk.gray(`capture-type: (${last_capture.screenshot_class || "none"}) ${last_capture.screenshot_url}`));
        if (last_audited_at && last_capture.capture_date > last_audited_at)
            console.log(chalk.gray.italic(`captured AFTER last audit ${diffDate(last_audited_at)} ago`));
        else if (last_audited_at && last_capture.capture_date <= last_audited_at)
            console.log(chalk.gray.italic(`captured BEFORE last audit ${diffDate(last_audited_at)} ago`));
        
        let screenshot_result: ClassifyLivePageResult | undefined = undefined;
        if (args.screenshots) {
            if (!last_attempt.screenshot_url) {
                console.log(chalk.gray(`no screenshot`));
                continue; // skip to next url
            }
    
            console.log(chalk.gray(`verifying screenshot classification...`));
            const classify_report_url = `${storagePath}/classify-screenshot.html`;
            screenshot_result = await classifyScreenshotUrl({
                url: last_attempt.screenshot_url,
                context: row,
                model,
                report_path: classify_report_url
            });
            if (screenshot_result?.analyze)
                audit_record.analyze.push(...screenshot_result.analyze);
            if (!screenshot_result.ok) {
                status.strikes += 1;
                console.log(chalk.yellow(`screenshot verification failed: ${screenshot_result.message}`));
                console.log(chalk.gray(`report: ${classify_report_url}`));
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (!test)
                    await audit_record.save("screenshot-verification-error", screenshot_result.message);
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }
            console.log(chalk.gray(`page-classification: (${screenshot_result.page_class}) ${screenshot_result.report_url}${screenshot_result.cached ? " (cached)": ""}`));
            if (screenshot_result.page_class !== last_attempt.screenshot_class)
                console.log(chalk.yellow(`warning: LLM result (${screenshot_result.page_class}) does not match ML result (${last_attempt.screenshot_class})`));
            console.log(chalk.gray(`report: ${classify_report_url}`));
    
            // todo: consider whether to trust bulk screenshot classification to skip the page
            /*
            if (screenshot_result.classification !== "product-page") {
                status.strikes += 1;
                status.skipped += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}`));
                continue; // skip to next rl
            }
            */
        }
    
        console.log(chalk.gray(`opening page... (waitfor=${options.waitfor || "none"})`));
        const { page, ok: page_ok, error: page_error } = await openPage({ url: row.capture_url, ...options });
    
        if (!page || !page_ok) {
            console.log(chalk.red(`\u2717 failed to load page`));
            console.log(chalk.gray(page_error));
            if (fatal_errors.some(key => page_error?.includes(key))) {
                console.log(chalk.gray("fatal error"));
                process.exit(0);
            }
            if (!test)
                await audit_record.save("page-load-error", page_error);
            status.strikes += 1;
            console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
            if (status.strikes >= limits.strikes)
                break; // skip to next seller
            else
                continue; // skip to next row
        }
    
        if (args.pause) {
            console.log("press any key to continue...");
            await waitForKeypress();
        }
        else if (snooze) {
            console.log(chalk.gray(`snoozing for ${snooze} seconds...`));
            await sleep(snooze * 1000);
        }

        audit_record.screenshot_url = await renderScreenshotToStorageFile(`${storagePath}/screenshot.png`, page);
        const log_file = `${storagePath}/index.html`;
        const { stream, url: report_url } = createRemoteLogStream(log_file, { title: "AUDIT", subtitle: `${page_id} at ${seller_name}` });
        hookConsoleLog();

        try {
            console.log(chalk.gray(`checking selector against live page...`));
            const select_result = await page.evaluate<EvaluateResult, EvaluateArg>(script, { select });
            const value = select_result?.data[target_selector.selector_name]?.value;
            if (value) {
                const { validation_code, validation_message } = validateSelectorResult(target_selector.selector_name, value);
                if (validation_code !== "ok") {
                    console.log(chalk.cyan(`\u2717 selector for "${target_selector.selector_name}" returned an invalid value ${chalk.gray(`[${validation_code}] ${validation_message}`)}`), chalk.gray.italic(`-> ${truncate(JSON.stringify(value), 200, true)}`));
                    audit_record.validation_code = validation_code;
                    if (!test)
                        await audit_record.save("invalid-selector-result", validation_message);
                    break; // skip to next seller
                }
                else {
                    status.strikes += 1;
                    console.log(chalk.cyan(`\u2717 not a regression, selector for "${target_selector.selector_name}" returned a value`), chalk.gray(`-> ${truncate(JSON.stringify(value), 200, true)}`));
                    if (!test)
                        await audit_record.save("selector-not-broken", `Not a regression, selector for "${target_selector.selector_name}" returned a value -> ${truncate(JSON.stringify(value), 200, true)}`);
                    console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                    if (status.strikes >= limits.strikes)
                        break; // skip to next seller
                    else
                        continue; // skip to next row
    
                }
            }
            else {
                console.log(chalk.gray(`confirmed selector for "${target_selector.selector_name}" did not return a value`));
            }

            console.log(chalk.gray(`classifying live page...`));
            let page_result = await classifyLivePage({
                page,
                context: row,
                stream
            });
            if (page_result?.analyze)
                audit_record.analyze.push(...page_result.analyze);
            const explain = page_result.explain ? `${page_result.explain} [${page_result.page_class}]` : `[${page_result.page_class}]`;

            if (page_result.page_class === "MODAL" || page_result.page_data?.popup_dialog) {
                //process.stdout.write(chalk.cyan("popup dialog detected, please close it and press any key to continue... "));
                //playAudio("notify");
                //await waitForKeypress();

                console.log(chalk.gray(`popup dialog detected, closing...`));
                const dialog_result = await autocloseAnalyze({ page, stream });
                console.log(chalk.gray(`${dialog_result.labels.length} dialog(s) closed...`));
                console.log(chalk.gray.italic(dialog_result.explain));
                if (dialog_result?.analyze)
                    audit_record.analyze.push(...dialog_result.analyze);
    
                console.log(chalk.gray(`re-classifying live page...`));
                page_result = await classifyLivePage({
                    page,
                    context: row,
                    stream
                });
                if (page_result?.analyze)
                    audit_record.analyze.push(...page_result.analyze);
            }

            if (!page_result.ok || !page_result.page_class) {
                status.strikes += 1;
                console.log(chalk.yellow("page classification failed"));
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (!test)
                    await audit_record.save("page-classification-error", page_result.message);
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            console.log(chalk.gray(`page-classification: (${page_result.page_class}) ${page_result.report_url}`));
            console.log(chalk.italic.gray(page_result.explain));
            if (page_result.page_data?.out_of_stock)
                console.log(chalk.bold.italic.gray("out-of-stock status detected"));
            if (page_result.page_data?.discontinued)
                console.log(chalk.bold.italic.gray("discontinued status detected"));
            if (page_result.page_data?.local_only)
                console.log(chalk.bold.italic.gray("local only status detected"));
            if (page_result.page_data?.hidden_price)
                console.log(chalk.bold.italic.gray("hidden price detected"));
            if (page_result.page_data?.no_price)
                console.log(chalk.bold.italic.gray("no price found"));
            if (page_result.page_data?.no_reviews)
                console.log(chalk.bold.italic.gray("no reviews found"));
        
            if (page_result.page_class !== "PRODUCT-PAGE") {
                const audit_code = mapPageClassificationToAuditCode(page_result.page_class);
                console.log(chalk.cyan(`\u2717 ${audit_code}`));
                if (!test)
                    await audit_record.save(audit_code, explain);
                status.strikes += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            if (page_result.page_data?.discontinued) {
                console.log(chalk.cyan(`\u2717 product is discontinued`));
                if (!test)
                    await audit_record.save("product-discontinued", explain);
                status.strikes += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            if (page_result.page_data?.out_of_stock && ["price", "review_count", "review_score"].includes(target_selector.selector_name)) {
                console.log(chalk.cyan(`\u2717 skipping out-of-stock product for "${target_selector.selector_name}" selector`));
                if (!test)
                    await audit_record.save("out-of-stock", explain);
                status.strikes += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            if (page_result.page_data?.no_price && target_selector.selector_name === "price") {
                console.log(chalk.cyan(`\u2717 no price found`));
                if (!test)
                    await audit_record.save("no-price", explain);
                status.strikes += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            if (page_result.page_data?.no_reviews && ["review_score", "review_count"].includes(target_selector.selector_name)) {
                console.log(chalk.cyan(`\u2717 no reviews`));
                if (!test)
                    await audit_record.save("no-reviews", explain);
                status.strikes += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            if (page_result.page_data?.local_only && ["in_stock", "stock_status"].includes(target_selector.selector_name)) {
                console.log(chalk.cyan(`\u2717 local onlyl`));
                if (!test)
                    await audit_record.save("local-only", explain);
                status.strikes += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            if (page_result.page_data?.hidden_price && target_selector.selector_name === "price") {
                console.log(chalk.cyan(`\u2717 price is hidden`));
                if (!test)
                    await audit_record.save("hidden-price", explain);
                status.strikes += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            const locate_result = await locateContent({
                page,
                /*
                context: {
                    domain_name: row.domain_name,
                    selectors: [{
                        selector_name: target_selector.selector_name,
                        selector_json_value: target_selector.selector_json_value,
                        regression: true
                    }]
                },
                targets: [target_selector.selector_name],
                */
                selector_profile: "brand-monitor-product-page",
                domain_name: row.domain_name,
                stream
            });
            audit_record.analyze.push(...locate_result.analyze.map(obj => ({ ...obj, report_url })));
            if (!locate_result.ok || !locate_result.targets) {
                if (!test)
                    await audit_record.save("unexpected-error", explain);
                status.strikes += 1;
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
                else
                    continue; // skip to next row
            }

            const target = locate_result.targets[target_selector.selector_name];
            if (target && target.labels && target.labels.length > 0) {
                status.confirmed += 1;
                console.log(chalk.green("\u2713 regression confirmed"));
                //todo: consider which fields require stock to be confirmed
                if (!test)
                    await audit_record.save("regression-confirmed", locate_result.message);
                break; // skip to next seller
            }
            else {
                status.strikes += 1;
                console.log(chalk.cyan(`\u2717 content not located for ${target_selector.selector_name}`));
                console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
                if (!test)
                    await audit_record.save("content-not-located", locate_result.message);
                if (status.strikes >= limits.strikes)
                    break; // skip to next seller
            }
        }
        catch (err) {
            status.strikes += 1;
            const message = err instanceof Error ? err.message : JSON.stringify(err);
            console.log(chalk.yellow(`ERROR: ${message}`));
            console.log(chalk.red(`STRIKE ${status.strikes}/${limits.strikes}${status.strikes >= limits.strikes ? " OUT" : ""}`));
            if (!test)
                await audit_record.save("unexpected-error", message);
            if (status.strikes >= limits.strikes)
                break; // skip to next seller
        }
        finally {
            await closeLogStream(stream, { analyze: audit_record.analyze, console: true });
            unhookConsoleLog();
            await page.close();
            const { summary } = summarize(audit_record.analyze);
            console.log(chalk.gray(summary));
            console.log(chalk.gray(`REPORT: ${report_url}`));
        }
    }
}

let audited = 0;
let confirmed = 0;
for (const key of seller_keys)
    if (!excluded_seller_keys.has(key)) {
        const status = seller_status[key];
        audited += status.audited;
        confirmed += status.confirmed;
    }
console.log();
console.log(chalk.gray(`${keys.length} sellers, ${audited} audits, ${confirmed} regressions confirmed`));

process.exit(0);
