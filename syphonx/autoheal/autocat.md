# autocat process

## New Product Update Process

1. Create pending products in Snowflake
```sql
CALL WTB.PRODUCT_CATEGORY.generate_product_embeddings((
    SELECT ARRAY_AGG(DISTINCT product_id)
    FROM WTB.PRODUCT_CATEGORY.product_category_base
    WHERE category_id IS NULL
    AND country_code='US'
))
```

2. Run `node autocat-batch` to create a batch from pending products
3. Run `node batch` to monitor batch progress
4. Run `node autocat-complete` to process batch completions
5. Run `CALL WTB.PRODUCT_CATEGORY.update_product_category()` in Snowflake to update product_category table

## Links
- [HOWTO queue pending products for autocategorization](https://github.com/PriceSpider-NeuIntel/bigdata-dbt/blob/master/wtb/models/SOURCE/clientdb_product_category/documentation/howtos/general.md)
- [Trace Batch Completions](bigdata-dbt/wtb/models/SOURCE/client_db_product_category/documentation/howtos/trace-batch.md)
