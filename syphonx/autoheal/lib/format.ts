import { table } from "table";

export function formatJson(value: unknown): string {
    try {
        if (!value)
            return "";
        else if (typeof value === "string")
            return JSON.stringify(JSON.parse(value), null, 2);
        else
            return JSON.stringify(value, null, 2);
    }
    catch (err) {
        return "";
    }
}

export function formatLineNums(text: string, linenums: number[]): string {
    return text
        .split("\n")
        .map((line, index) => `${linenums.includes(index + 1) ? ">" : " "} ${String(index + 1).padStart(3, "0")} ${line}`)
        .join("\n");
}

export function formatPercent(numerator: number, denominator: number): string {
    if (numerator < denominator)
        return `${Math.min(100 * numerator / denominator, 99).toFixed(0)}%`;
    else
        return "100%";
}

function formatRowValue(value: unknown): string {
    if (typeof value === "string")
        return value.replace(/[\u0001-\u0006\u0008\u0009\u000B-\u001A]/g, ""); // invalid control character (found examples in PROWL... comes from https://www.npmjs.com/package/table)
    else if (typeof value === "number")
        return String(value);
    else if (typeof value === "boolean")
        return String(value);
    else if (value instanceof Date)
        return value.toISOString();
    else
        return JSON.stringify(value);
}

export function formatTemplate(template: string, values: Record<string, string>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (placeholder, key) => (key in values ? values[key] : placeholder));
}

export function formatTable(rows: unknown[]): string {
    if (rows.length > 0)
        return table([
            Object.keys(rows[0] as Record<string, unknown>),
            ...(rows as Record<string, unknown>[]).map(row =>
                Object.keys(row).map(key => formatRowValue(row[key])))
        ], { singleLine: true })
    else
        return "(empty)";
}
