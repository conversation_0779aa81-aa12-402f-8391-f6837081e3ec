import Jimp from "jimp";

const SAMPLE_SIZE = 32;
const LOW_SIZE = 8;

const SQRT = initSQRT(SAMPLE_SIZE);
const COS = initCOS(SAMPLE_SIZE);

function initSQRT(N: number): number[] {
    const c = new Array(N).fill(0);
    for (let i = 1; i < N; i++) {
        c[i] = 1;
    }
    c[0] = 1 / Math.sqrt(2.0);
    return c;
}

function initCOS(N: number): number[][] {
    const cosines: number[][] = new Array(N);
    for (let k = 0; k < N; k++) {
        cosines[k] = new Array(N);
        for (let n = 0; n < N; n++) {
            cosines[k][n] = Math.cos(((2 * k + 1) / (2.0 * N)) * n * Math.PI);
        }
    }
    return cosines;
}

function applyDCT(f: number[][], size: number): number[][] {
    const N = size;
    const F: number[][] = new Array(N);

    for (let u = 0; u < N; u++) {
        F[u] = new Array(N).fill(0);
        for (let v = 0; v < N; v++) {
            let sum = 0;
            for (let i = 0; i < N; i++) {
                for (let j = 0; j < N; j++) {
                    sum += COS[i][u] * COS[j][v] * f[i][j];
                }
            }
            sum *= (SQRT[u] * SQRT[v]) / 4;
            F[u][v] = sum;
        }
    }

    return F;
}

export async function phash(buffer: Buffer): Promise<string> {
    const image = await Jimp.read(buffer);
    image.resize(SAMPLE_SIZE, SAMPLE_SIZE).greyscale();

    const s: number[][] = new Array(SAMPLE_SIZE);
    for (let x = 0; x < SAMPLE_SIZE; x++) {
        s[x] = new Array(SAMPLE_SIZE);
        for (let y = 0; y < SAMPLE_SIZE; y++) {
            const pixel = Jimp.intToRGBA(image.getPixelColor(x, y)).r; // Only take the red channel since the image is greyscale
            s[x][y] = pixel;
        }
    }

    const dct = applyDCT(s, SAMPLE_SIZE);

    let totalSum = 0;
    for (let x = 0; x < LOW_SIZE; x++) {
        for (let y = 0; y < LOW_SIZE; y++) {
            totalSum += dct[x + 1][y + 1];
        }
    }

    const avg = totalSum / (LOW_SIZE * LOW_SIZE);

    let hash = "";
    for (let x = 0; x < LOW_SIZE; x++) {
        for (let y = 0; y < LOW_SIZE; y++) {
            hash += dct[x + 1][y + 1] > avg ? "1" : "0";
        }
    }

    return hash;
}
