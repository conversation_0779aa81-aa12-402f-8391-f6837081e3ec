import { PNG } from "pngjs";
import { phash } from "./phash-jimp.js";

export async function createPhash(buffer: Buffer): Promise<string> {
    const p = await phash(buffer);
    const h = phash2hex(p);
    return h;
}

export function hammingDistance(hash1: string, hash2: string): number {
    const a = BigInt("0x" + hash1).toString(2).padStart(64, "0");
    const b = BigInt("0x" + hash2).toString(2).padStart(64, "0");
    let n = 0;
    for (let i = 0; i < 64; i++)
        if (a[i] !== b[i])
            n += 1;
    return n;
}

export async function renderImageFingerprint(phash: string): Promise<string> {
    const a = phash2bitmap(phash);
    const size = 24;
    const n = size / 8;

    const png = new PNG({ width: size, height: size });
    for (let y = 0; y < size; y++) {
        for (let x = 0; x < size; x++) {
            const k = (size * y + x) << 2;
            if (x % n !== n) {
                const i = Math.floor(y / n);
                const j = Math.floor(x / n);
                const bit = a[i][j];
                png.data[k + 0] = bit === 1 ? 0 : 255;
                png.data[k + 1] = bit === 1 ? 0 : 255;
                png.data[k + 2] = bit === 1 ? 0 : 255;
                png.data[k + 3] = 255;
            }
        }
    }

    const datauri = await png2datauri(png);
    return datauri;
}

function png2datauri(png: PNG): Promise<string> {
    return new Promise<string>((resolve, reject) => {
        const chunks: Buffer[] = [];
        png.pack()
           .on("data", chunk => chunks.push(chunk))
           .on("end", () => {
               const buffer = Buffer.concat(chunks);
               const datauri = `data:image/png;base64,${buffer.toString("base64")}`;
               resolve(datauri);
           })
           .on("error", reject);
    });
}

function phash2hex(binary_value: string): string {
    return parseInt(binary_value.slice(0, 32), 2).toString(16).padStart(8, "0") + parseInt(binary_value.slice(32), 2).toString(16).padStart(8, "0");
}

function phash2bitmap(hex_value: string): number[][] {
    const a = [];
    const b = BigInt("0x" + hex_value).toString(2).padStart(64, "0"); // convert hex string to binary string
    for (let i = 0; i < 8; i++) {
        const row = [];
        for (let j = 0; j < 8; j++)
            row.push(parseInt(b[i * 8 + j], 10));
        a.push(row);
    }
    return a;
}
