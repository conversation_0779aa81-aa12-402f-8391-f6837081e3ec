import sharp from "sharp";
import <PERSON><PERSON> from "jimp";
import chalk from "chalk";
import { tryPageScreenshot, Page } from "../index.js";

export type Image = string | Buffer;

export interface CreateImageUriOptions {
    url?: string;
    buffer?: Buffer;
}

const white = { r: 255, g: 255, b: 255, alpha: 1 };
const gray = { r: 196, g: 196, b: 196, alpha: 1 };

export async function createImageUri({ url, buffer }: CreateImageUriOptions): Promise<string> {
    if (!url && !buffer)
        throw new Error("url or buffer not specified");
    const image = await Jimp.read((url || buffer) as any);
    const datauri = await image.getBase64Async(Jimp.MIME_PNG);
    return datauri;
}

export interface ThumbnailImageOptions {
    page?: Page;
    url?: string;
    buffer?: Buffer;
    size: number;
}

export async function createThumbnailImage({ page, buffer, url, size }: ThumbnailImageOptions): Promise<string> {
    if (process.env.VERBOSE)
        console.log(chalk.gray("Creating thumbnail image..."));
    if (page)
        buffer = await tryPageScreenshot(page);

    if (!url && !buffer)
        return "";

    let image = await Jimp.read((url || buffer) as any);
    if (image.getWidth() > size)
        image = image.resize(size, Jimp.AUTO); // scale horizontally

    if (image.getHeight() > size)
        image = image.crop(0, 0, size, size); // crop vertically

    const datauri = await image.getBase64Async(Jimp.MIME_PNG);
    return datauri;
}

export interface DownloadImageOptions {
    width?: number;
    height?: number;
}

export interface DownloadCropImageResult {
    ok: boolean;
    datauri?: string;
    width?: number;
    height?: number;
    error?: string;
    timeout?: boolean;
}

export async function downloadClipImage(url: string, { width, height }: DownloadImageOptions = {}): Promise<DownloadCropImageResult> {
    try {
        let image = await Jimp.read(url);
        if (width && height)
            image = image.crop(0, 0, Math.min(image.getWidth(), width), Math.min(image.getHeight(), height));
        const png = await image.getBufferAsync(Jimp.MIME_PNG);
        const datauri = `data:image/png;base64,${png.toString("base64")}`;
        return {
            datauri,
            width: image.getWidth(),
            height: image.getHeight(),
            ok: true
        };
    } catch (err) {
        return {
            error: err instanceof Error ? err.message : JSON.stringify(err),
            timeout: err instanceof Error && err.name === "TimeoutError",
            ok: false
        };
    }
}

export async function loadImage(url: string): Promise<string> {
    const image = await Jimp.read(url);
    const mime = image.getMIME();
    const png = await image.getBufferAsync(mime);
    const datauri = `data:${mime};base64,${png.toString("base64")}`;
    //const png = await image.getBufferAsync(Jimp.MIME_PNG);
    //const datauri = `data:image/png;base64,${png.toString("base64")}`;
    return datauri;
}

export interface ThumbnailImage extends Partial<sharp.OutputInfo> {
    buffer?: Buffer;
    status?: number;
    error?: string;
    ok: boolean;
}

export async function labelImage(image: Image, label: string): Promise<Buffer> {
    const composite = sharp(image)
        .composite([
            {
                input: {
                    text: {
                        text: `<span size="xx-small" background="white"><span foreground="white">.</span><span foreground="black">${label}</span><span foreground="white">.</span></span>`,
                        rgba: true,
                        dpi: 150
                    },
                },
                left: 0,
                top: 0
            }
        ])
        .png();
    return composite.toBuffer();
}

export interface CreateImageStripOptions {
    images: Image[];
    columns?: number;
    thumbnail_size?: number;
    padding?: number;
}

export async function createImageStrip({ images, columns = 0, thumbnail_size = 150, padding = 8 }: CreateImageStripOptions): Promise<Buffer> {
    // Determine layout dimensions
    const column_count = columns > 0 ? columns : images.length;
    const row_count = Math.ceil(images.length / column_count);

    const width = column_count * (thumbnail_size + padding) + padding;
    const height = row_count * (thumbnail_size + padding) + padding;

    // Create the base image canvas with calculated dimensions
    const image = sharp({
        create: {
            width,
            height,
            channels: 4,
            background: gray
        }
    });

    // Calculate each image's position
    const input = images.map((image, i) => ({
        input: image,
        left: (i % column_count) * (thumbnail_size + padding) + padding,
        top: Math.floor(i / column_count) * (thumbnail_size + padding) + padding
    }));

    // Composite images onto the canvas
    const output = await image.composite(input);
    const buffer = await output.png().toBuffer();
    return buffer;
}

export interface DownloadThumbnailImageOptions {
    url: string;
    thumbnail_size?: number;
    background_threshold?: number;
}

export async function downloadThumbnailImage({ url, thumbnail_size = 100, background_threshold = 32 }: DownloadThumbnailImageOptions): Promise<ThumbnailImage> {
    try {
        const response = await fetch(url);
        if (!response.ok)
            return {
                ok: false,
                status: response.status, error: response.statusText
            };

        const buffer = await response.arrayBuffer();
        const result = await sharp(Buffer.from(buffer))
            .trim({
                threshold: background_threshold,
                lineArt: false
            })
            .resize({
                width: thumbnail_size,
                height: thumbnail_size,
                background: white,
                fit: "contain"
            })
            .png()
            .toBuffer({ resolveWithObject: true });

        return {
            ...result.info,
            buffer: result.data,
            status: response.status,
            ok: true
        };
    }
    catch (err) {
        return {
            ok: false,
            error: err instanceof Error ? err.message : JSON.stringify(err)
        };
    }
}
