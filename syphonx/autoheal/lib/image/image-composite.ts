import Jim<PERSON> from "jimp";

// Define the interface for the function options
export interface CompositeOptions {
    width?: number; // Desired width of the overlay image
    height?: number; // Desired height of the overlay image
    right: number; // Pixels from the right edge of image1
    bottom: number; // Pixels from the bottom edge of image1
    border?: number;
}

export async function compositeImage(image1: string | Buffer, image2: string | Buffer, { width, height, right, bottom, border }: CompositeOptions): Promise<Buffer> {
    const baseImage = await Jimp.read(image1 as any);
    let overlayImage = await Jimp.read(image2 as any);

    // Resize the overlay image as specified
    if (width || height)
        overlayImage.resize(width || Jimp.AUTO, height || Jimp.AUTO);


    // Add a gray border around the overlay image
    if (border) {
        const borderedOverlay = await new Jimp(overlayImage.bitmap.width + 2 * border, overlayImage.bitmap.height + 2 * border, 0xCCCCCCFF);
        borderedOverlay.composite(overlayImage, border, border); // Place the overlay image inside the border
        overlayImage = borderedOverlay;
    }

    // Calculate the position for the overlay
    const x = baseImage.bitmap.width - overlayImage.bitmap.width - right;
    const y = baseImage.bitmap.height - overlayImage.bitmap.height - bottom;

    // Composite the overlay image onto the base image at calculated position
    baseImage.composite(overlayImage, x, y, {
        mode: Jimp.BLEND_SOURCE_OVER,
        opacitySource: 1, // Fully opaque
        opacityDest: 1 // Base image also fully visible
    });

    // Get the final image as a buffer
    return baseImage.getBufferAsync(Jimp.MIME_PNG);
}