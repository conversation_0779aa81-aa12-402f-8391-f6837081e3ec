import Jimp from "jimp";
import quantize, { RgbPixel } from "quantize";

export type RGB = RgbPixel;

/**
 * Find the dominant colors for an image by quantizing the image to a color palette of fixed size.
 * @param buffer The image to quantize.
 * @param k The number of colors in the output palette.
 * @returns An array of RGB colors that represents the palette.
 */
export async function quantizeImage(buffer: Buffer, k = 4): Promise<RGB[]> {
    const image = await Jimp.read(buffer);
    const rgb = toRGB(image);
    const result = quantize(rgb, k);
    if (!result)
        throw new Error(`Failed to quantize image`);
    return result.palette();
}

/**
 * Calculates the total difference in hue between corresponding colors from two color palettes.
 * @param palette1 The first color palette.
 * @param palette2 The second color palette.
 * @returns The total hue difference.
 */
export function colorDistance(palette1: RgbPixel[], palette2: RgbPixel[]): number {
    let result = 0;
    const n = Math.min(palette1.length, palette2.length);

    for (let i = 0; i < n; i++) {
        const hue1 = toHue(palette1[i]);
        const hue2 = toHue(palette2[i]);
        const delta = Math.abs(hue1 - hue2);
        const diff = Math.min(delta, 360 - delta);
        result += diff;
    }

    return result;
}
function toRGB(image: Jimp): RGB[] {
    const result: RGB[] = [];
    const a = image.bitmap.data;
    for (let i = 0; i < a.length; i += 4)
        result.push([a[i], a[i + 1], a[i + 2]]);
    return result;
}

function toHue(rgb: RgbPixel): number {
    const [r, g, b] = rgb.map((v) => v / 255); // Normalize RGB values to [0, 1]
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const delta = max - min;

    let hue = 0;
    if (delta === 0)
        hue = 0; // Hue is undefined when delta is 0, default to 0
    else if (max === r)
        hue = ((g - b) / delta) % 6;
    else if (max === g)
        hue = (b - r) / delta + 2;
    else if (max === b)
        hue = (r - g) / delta + 4;

    hue *= 60;
    if (hue < 0) hue += 360;

    return hue;
}
