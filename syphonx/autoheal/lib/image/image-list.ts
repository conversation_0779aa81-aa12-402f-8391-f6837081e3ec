import {
    createImageStrip,
    downloadThumbnailImage,
    labelImage
} from "./image.js";

import {
    colorDistance,
    quantizeImage,
    RGB
} from "./image-quantizer.js";

import {
    createPhash,
    hammingDistance
} from "./phash.js";

import {
    resolveUrl
} from "../url.js"

export interface ImageListImage {
    url: string;
    href: string;
    index: number;
    phash: string;
    palette: RGB[];
    group: number;
    buffer: Buffer;
}
export interface ImageListError {
    url: string;
    href: string;
    error?: string;
}

export interface ImageListOptions {
    thumbnail_size?: number;
    hamming_tolerance?: number;
    color_tolerance?: number;
    background_threshold?: number;
    padding?: number;
    columns?: number;
}

export interface ImageListCreateImageStripOptions {
    label?: boolean;
    max_images?: number;
}

/**
 * Manages a list of images from different pages with the ability to identify duplicate images.
 */
export class ImageList {
    images: ImageListImage[];
    groups: Record<number, ImageListImage[]>;
    keycodes: Record<string, number>;
    errors: ImageListError[];
    thumbnail_size: number;
    hamming_tolerance: number;
    color_tolerance: number;
    background_threshold: number;
    padding: number;
    columns: number;

    constructor({
        thumbnail_size = 150,
        hamming_tolerance = 12,
        color_tolerance = 400,
        background_threshold = 32,
        padding = 8,
        columns = 5
    }: ImageListOptions = {}) {
        this.images = [];
        this.groups = {};
        this.keycodes = {};
        this.errors = [];
        this.thumbnail_size = thumbnail_size;
        this.hamming_tolerance = hamming_tolerance;
        this.color_tolerance = color_tolerance;
        this.background_threshold = background_threshold;
        this.padding = padding;
        this.columns = columns;
    }

    /**
     * Adds an image to the image list.
     * @param url The url of the image.
     * @param href The url of the page that references the image.
     */
    async add(url: string, href: string): Promise<void> {
        url = resolveUrl(url, href);
        const index = this.images.filter(image => image.href === href).length;
        const image = this.images.find(image => image.url === url);
        if (!image) {
            const result = await downloadThumbnailImage({
                url,
                thumbnail_size: this.thumbnail_size,
                background_threshold: this.background_threshold
            });
            if (result.buffer) {
                const phash = await createPhash(result.buffer);
                const palette = await quantizeImage(result.buffer, 4);
                if (hammingDistance(phash, "0000000000000000") > 1 && hammingDistance(phash, "ffffffffffffffff") > 1)
                    this.images.push({
                        url,
                        href,
                        index,
                        phash,
                        palette,
                        buffer: result.buffer,
                        group: -1
                    });
                else
                    this.errors.push({
                        url,
                        href,
                        error: "BLANK"
                    });    

            }
            else {
                this.errors.push({
                    url,
                    href,
                    error: result.error
                });
            }
        }
        else {
            this.images.push({
                url,
                href,
                index,
                phash: image.phash,
                palette: image.palette,
                buffer: image.buffer,
                group: -1
            });
        }
    }

    /**
     * Uses image hasing to dedup the images by seperating images into groups.
     * Assigns a group number to each similar image.
     */
    dedup() {
        this.groups = {};
        let counter = -1;
        this.images.forEach(image => image.group = -1);
        for (const image of this.images) {
            if (image.group === -1) {
                image.group = ++counter;
                for (const reference of this.images.filter(obj => obj.group === -1)) {    
                    const hamming_distance = hammingDistance(image.phash, reference.phash);
                    const color_distance = colorDistance(image.palette, reference.palette);
                    if (hamming_distance <= this.hamming_tolerance && color_distance <= this.color_tolerance)
                        reference.group = image.group;
                }
            }
        }
        for (let key = 0; key <= counter; ++key)
            this.groups[key] = this.images.filter(image => image.group === key);
        this.codeify();
    }

    private codeify() {
        this.keycodes = {};
        const list = Object.entries(this.groups).sort((a, b) => b[1].length - a[1].length).slice(0, 26); // sort by descending size, max of 26
        let i = 0;
        for (const item of list) {
            const key = String.fromCharCode(65 + i);
            this.keycodes[key] = parseInt(item[0]);
            i += 1;
        }
    }

    /**
     * Creates an image strip of the set of de-duplicated images.
     * Optionally, each image is labelled A-Z where A corresponds to group 0, B to 1, and so on.
     * @returns A datauri of the generated image strip.
     */
    async createImageStrip({ label = true, max_images = 26 }: ImageListCreateImageStripOptions = {}): Promise<Buffer | undefined> {
        if (this.images.length > 0) {
            this.dedup();

            const images: Buffer[] = [];
            const keys = Object.keys(this.keycodes).slice(0, max_images);
            for (const key of keys) {
                const group = this.keycodes[key];
                const [{ buffer }] = this.groups[group];
                const image = label ? await labelImage(buffer, key) : buffer;
                images.push(image);
            }
    
            const buffer = await createImageStrip({
                images,
                thumbnail_size: this.thumbnail_size,
                padding: this.padding,
                columns: 5
            });
            return buffer;
        }
    }
}
