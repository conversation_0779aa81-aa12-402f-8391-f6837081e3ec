import { createVirtualBrowser, <PERSON> } from "./virtual-browser/index.js";
import { collapseWhitespace, sleep } from "./utilities.js";
import { renderScreenshot } from "./render-screenshot.js";
import { addLabels } from "./scripts/labels.js";

export interface RenderHtmlOptions {
    html: string;
    file?: string;
    labels?: boolean;
    labelStyle?: string;
    fullPage?: boolean;
    timeout?: number;
    maxWidth?: number;
    maxHeight?: number;
    snooze?: number;
    visibleOnly?: boolean;
    show?: boolean;
    shared?: boolean;
    autoclose?: boolean;
}

export interface RenderHtmlResult {
    html?: string;
    datauri?: string;
    page?: Page;
    width?: number;
    height?: number;
    error?: string;
}

export async function renderHtml({ fullPage, timeout = 10000, snooze, maxWidth, maxHeight, file, labelStyle, visibleOnly, show, shared, autoclose = true, ...options }: RenderHtmlOptions): Promise<RenderHtmlResult> {
    const browser = await createVirtualBrowser({ headless: !show, shared });
    const page = await browser.newPage();

    try {
        await page.goto("data:text/html,");
        await page.setContent(options.html);
        await page.waitForLoadState("load", { timeout });

        if (fullPage) {
            await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
            await sleep(500);
            await page.evaluate(() => window.scrollTo(0, 0));
            await sleep(500);
        }

        if (snooze)
            await sleep(snooze);

        if (options.labels)
            await page.evaluate(addLabels, labelStyle);

        const html = visibleOnly ? await extractVisibleContent(page) : await page.content();
        const screenshot = await renderScreenshot(page, { file, maxWidth, maxHeight, fullPage });
        return { html, page, ...screenshot };
    }
    catch (err) {
        return { error: err instanceof Error ? err.message : JSON.stringify(err) };
    }
    finally {
        if (!show && autoclose)
            await page.close();
    }
}

async function extractVisibleContent(page: Page): Promise<string> {
    let html = await page.evaluate(() => {
        return walk(document.body);

        function attr(element: Element) {
            if (!element.attributes || element.attributes.length === 0) return '';
            const parts = [];
            for (let i = 0; i < element.attributes.length; i++) {
                const { name, value } = element.attributes[i];
                parts.push(`${name}="${value}"`);
            }
            return parts.length > 0 ? ' ' + parts.join(' ') : '';
        }

        function visible(element: Element) {
            if (!(element instanceof HTMLElement)) return false;
            const style = window.getComputedStyle(element);
            if (style.display === "none") return false;
            if (style.visibility !== "visible") return false;
            if (parseFloat(style.opacity) < 0.1) return false;
            if (element.offsetWidth === 0 || element.offsetHeight === 0) return false;
            return true;
        }

        function walk(node: Node) {
            if (node.nodeType === Node.TEXT_NODE)
                return node.textContent + "\n";
            else if (node.nodeType !== Node.ELEMENT_NODE)
                return "";

            const element = node as Element;
            const tagName = element.tagName.toLowerCase();
            if (["script", "noscript", "style"].includes(tagName))
                return "";

            if (!visible(element))
                return "";


            let childContent = '';
            for (const child of node.childNodes)
                childContent += walk(child);

            const attrs = attr(element);
            return `<${tagName}${attrs}>${childContent}</${tagName}>`;
        }
    });
    html = collapseWhitespace(html || "");
    html = `<html>\n${html}\n</html>`;
    return html;
}