import Jimp from "jimp";
import { Page } from "./index.js";

export interface RenderScreenshotOptions {
    /** Crops width of unscaled image if specified maximum is exceeded. */
    maxWidth?: number;
    /** Crops height of unscaled image if specified maximum is exceeded. */
    maxHeight?: number;
    /** Deteremines whether to render full page or viewport before cropping. */
    fullPage?: boolean;
    /** A scale factor by which to reduce the image (default=1). */
    scale?: number;
    /** Name of output path to write image. */
    file?: string;
}

export interface RenderScreenshotResult {
    datauri: string;
    width: number;
    height: number;
    scale: number;
}

export async function renderScreenshot(page: Page, { file, maxWidth, maxHeight, fullPage, scale }: RenderScreenshotOptions = {}): Promise<RenderScreenshotResult> {
    if (maxWidth || scale) {
        const buffer = await page.screenshot({
            fullPage,
            clip: {
                width: maxWidth ?? 2048,
                height: maxHeight ?? 1024,
                x: 0,
                y: 0,
                scale: 1
            }
        });
        let image = await Jimp.read(buffer);
        image = image.autocrop(0.02);
        
        if (maxWidth && image.getWidth() > maxWidth)
            image = image.resize(maxWidth, Jimp.AUTO);
        if (!fullPage)
            maxHeight = image.getHeight();
        if (maxHeight && image.getHeight() > maxHeight)
            image = image.crop(0, 0, image.getWidth(), maxHeight);
        if (scale)
            image = image.scale(scale);
        if (file)
            await image.writeAsync(file);
        const datauri = await image.getBase64Async(Jimp.MIME_PNG);
        const dimensions = {
            datauri,
            width: image.getWidth(),
            height: image.getHeight(),
            scale: maxWidth && image.getWidth() > maxWidth ? image.getWidth() / maxWidth : scale || 1
        };

        return dimensions 
    }
    else {
        const buffer = await page.screenshot({ path: file, fullPage });
        const datauri = `data:image/png;base64,${buffer.toString("base64")}`;
        const image = await Jimp.read(buffer);
        return {
            datauri,
            width: image.getWidth(),
            height: image.getHeight(),
            scale: 1
        };
    }
}
