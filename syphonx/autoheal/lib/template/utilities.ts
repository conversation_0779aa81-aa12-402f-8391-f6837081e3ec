import {
    ExtractResult,
    LoadTemplateResult,
    Run<PERSON><PERSON><PERSON>,
    SyphonXApi,
    Template
} from "syphonx-lib";

import * as syphonx from "syphonx-lib";

export type ActionType = "select"; // todo: remove on next syphonx-core update

let api: SyphonXApi | undefined = undefined;
let genericTemplate: Template | undefined = undefined;

export function createSyphonXApi(): SyphonXApi {
    return new SyphonXApi(process.env.SYPHONX_API_KEY, { url: process.env.SYPHONX_API_URL, appVersion: "autoheal" });
}

export async function loadTemplate(name: string): Promise<LoadTemplateResult> {
    if (!api)
        api = createSyphonXApi();
    const result = await api.loadTemplate(name);
    return result;
}

export async function loadTemplates(names: string[]): Promise<Record<string, Template>> {
    if (!api)
        api = createSyphonXApi();
    const result: Record<string, Template> = {};
    for (const name of names) {
        const { template } = await tryLoadTemplate(name);
        if (template)
            result[name] = template;
    }
    return result;
}

export interface RunTemplateOptions {
    url?: string;
    html?: string;
}

export async function runTemplate(name: string | Template, options: Partial<RunOptions>): Promise<ExtractResult> {
    if (!api)
        api = createSyphonXApi();
    const { template } = typeof name === "string" ? await api.loadTemplate(name) : { template: name };
    const result = await api.run({ template, unwrap: true, ...options });
    return result;
}

export async function runGenericProductPageTemplate(html: string): Promise<Partial<ExtractResult>> {
    if (!api)
        api = createSyphonXApi();
    if (!genericTemplate) {
        const { template } = await api.loadTemplate("/pricespider/generic/product_page.json");
        genericTemplate = template;
    }
    const result = await api.run({ template: genericTemplate, unwrap: true, html });
    return result;    
}

export function selectHtml(html: string, selects: syphonx.Select[]) {
    const result = syphonx.select(selects, html, { unwrap: true });
    return result;
}

export async function saveTemplate(name: string, template: Template): Promise<void> {
    if (!api)
        api = createSyphonXApi();
    const data = JSON.stringify(template, null, 2); // todo: pretty
    await api.write(name, data);
}

export async function tryLoadTemplate(name: string): Promise<Partial<LoadTemplateResult> & { ok: boolean, error?: any }> {
    try {
        const result = await loadTemplate(name);
        return { ...result, ok: true };
    }
    catch (err) {
        return { ok: false, error: err }
    }
}

export function templateNameFromDomainName(domain_name: string, dir: "matching"): string {
    const [name] = domain_name.split(".");
    return `/pricespider/${dir}/${name}.json`;
}

export type TemplateDir = "matching";
const existing_templates: Record<string, Template> = {};
const nonexistsing_templates = new Set<string>;
async function tryLoadDomainTemplate(domain_name: string, dir: TemplateDir): Promise<Template | undefined> {
    const key = `${domain_name}/${dir}`;
    if (existing_templates[key])
        return existing_templates[key];
    else if (nonexistsing_templates.has(key))
        return undefined;
    
    const template_name = templateNameFromDomainName(domain_name, dir);
    const { template } = await tryLoadTemplate(template_name);
    if (template) {
        existing_templates[key] = template;
        return template;
    }
    else {
        nonexistsing_templates.add(key);
    }
    return undefined;
}

export async function runDomainProductPageTemplate(domain_name: string, html: string): Promise<Partial<ExtractResult>> {
    if (!api)
        api = createSyphonXApi();
    const template = await tryLoadDomainTemplate(domain_name, "matching");
    if (!template)
        return { ok: false };
    const result = await api.run({ template, unwrap: true, html });
    return result;
}
