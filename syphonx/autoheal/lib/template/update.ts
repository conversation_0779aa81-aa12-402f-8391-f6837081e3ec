import { findSelect, findLastSelectGroup, parse<PERSON><PERSON><PERSON><PERSON>, Select, SelectQ<PERSON>y, Template } from "syphonx-lib";
import { clone } from "../utilities.js";
import { AuditCommitSelector } from "../query/audit-commit.js";
import { SelectorUpdate } from "../commit.js";

const repeated_selectors = ["features", "images", "videos"];

export interface UpdateTemplateResult {
    template: Template;
    messages: string[];
    commits: AuditCommitSelector[];
    ok: boolean;
}

export function updateTemplate(template: Template, updates: SelectorUpdate[]): UpdateTemplateResult {
    const result: UpdateTemplateResult = {
        template: clone(template),
        messages: [],
        commits: [],
        ok: false
    };

    for (const update of updates) {
        let selects = findSelect(result.template.actions, update.selector_name);
        if (selects.length > 1)
            result.messages.push(`${update.selector_name}: multiple select actions found with same name`);
    
        const [select] = selects;
        if (select) {
            // if there's already an existing query in the template...
            if (select.query) {
                if (!update.selector.startsWith("$")) {
                    // check if selector is already in the query...
                    let stop = false;
                    for (const query of select.query) {
                        if (query[0].includes(update.selector)) {
                            result.messages.push(`${update.selector_name}: already has selector "${update.selector}"`);
                            stop = true;
                            break;
                        }
                    }
                    if (stop)
                        continue; // go on to next update
                }

                const before = JSON.stringify(select.query);
                if (!update.selector.startsWith("$")) {
                    const query: SelectQuery = [update.selector];
                    select.query.unshift(query); // prepend
                    // if first query is a simple selector-only query, then prepend another query
                    //if (select.query[0].length === 1) {
                    //const query = clone(select.query[0]);
                    //query[0] = update.selector;
                    //select.query.unshift(query); // prepend
                    //select.query.push(query); // append
                    //}
                    // otherwise if the query has additional steps then add to the selector using the comma operator so as to include the extra steps
                    //else {
                    //select.query[0][0] = `${select.query[0][0]}, ${update.selector}`;
                    //}
                }
                else {
                    const query = parseJQuery(update.selector);
                    if (query) {
                        select.query.unshift(query); // prepend
                    }
                    else {
                        result.messages.push(`${update.selector_name}: failed to parse jquery ${update.selector}`);
                        continue;
                    }
                }
                const after = JSON.stringify(select.query);
                if (before !== after) {
                    result.messages.push(`${update.selector_name}: added selector`);
                    result.commits.push({ selector_name: update.selector_name, before, after });
                }
                else {
                    result.messages.push(`${update.selector_name}: unable to add selector`);
                }
            }
            // otherwise if no existing query in the template...
            else {
                select.query = [[update.selector]];
                result.commits.push({ selector_name: update.selector_name, after: JSON.stringify(select.query) });
                result.messages.push(`${update.selector_name}: replaced query`);
            }
            result.ok = true;
        }
        else {
            const group = findLastSelectGroup(result.template.actions);
            if (group) {
                const select: Select = {
                    name: update.selector_name,
                    query: [[update.selector]],
                    repeated: repeated_selectors.includes(update.selector_name)
                };
                group.push(select);
                result.commits.push({ selector_name: update.selector_name, after: JSON.stringify(select.query) });
                result.messages.push(`${update.selector_name}: selector added`);
                result.ok = true;
            }
            else
                result.messages.push(`${update.selector_name}: select action not found`);
        }
    }

    return result;
}
