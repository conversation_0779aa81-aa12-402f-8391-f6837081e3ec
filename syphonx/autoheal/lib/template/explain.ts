import * as syphonx from "syphonx-lib";
import { findSelect } from "syphonx-lib";

import {
    autogenProfile,
    formatLineNums,
    renderJinjaTemplateFile,
    sliceHtml,
    LLMChat
} from "../index.js";

export async function explainRegression(html: string, selector_name: string, template: syphonx.Template, generated_selector: string, new_html: string) {
    const model = autogenProfile().locateContent.model;
    const [select] = findSelect(template.actions, selector_name);
    if (select.query) {
        const [[original_selector]] = select.query;
        const result = syphonx.select([select], html);
        if (result.data && result.data[selector_name]) {
            const { nodes } = result.data[selector_name];
            const slice = sliceHtml(html, { selector: nodes.join(", ") });
            if (slice) {
                const prompt = renderJinjaTemplateFile("explain-regression.md", {
                    original_selector,
                    original_html: formatLineNums(slice.html, slice.linenums),
                    new_selector: generated_selector,
                    new_html
                });
                const chat = new LLMChat(model);
                const explanation = chat.prompt(prompt);
                if (explanation)
                    console.log(explanation);
            }
        }
        else {
            console.log(`${selector_name}: cannot explain regression`);
        }
    }
}
