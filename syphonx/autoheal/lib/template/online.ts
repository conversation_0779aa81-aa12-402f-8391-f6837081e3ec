import * as playwright from "playwright";
import * as syphonx from "syphonx-lib";
import chalk from "chalk";

import {
    ExtractResult,
    ExtractState,
    Template,
    invokeAsyncMethod
}
from "syphonx-lib";

import {
    createSyphonXApi,
    formatJson,
    sleep,
    waitForKeypress
}
from "../index.js";

export interface OnlineOptions {
    template: Template | string;
    url: string;
    html?: string;
    unwrap?: boolean;
    headless?: boolean;
    pause?: boolean;
    snooze?: number;
}

export async function online({ template, html, url, unwrap, headless, pause, snooze }: OnlineOptions): Promise<ExtractResult> {
    const api = createSyphonXApi();
    const script = new Function("state", `return ${syphonx.script}(state)`) as (state: ExtractState) => ExtractState;
    const browser = await playwright.chromium.launch({ headless });
    const page = await browser.newPage();

    const result = await api.run({
        template,
        url,
        html,
        unwrap,
        onExtract: async state => {
            console.log(chalk.gray(`running template...`));
            const result = await page.evaluate<ExtractState, ExtractState>(script, state);
            console.log(chalk.blueBright(formatJson(result.data)));
            if (pause) {
                console.log("press any key to continue...");
                await waitForKeypress();
            }
            if (result.errors)
                console.log(chalk.red(result.errors.map(err => `${err.code}: ${err.message}`).join("\n")));
            return result;
        },
        onGoback: async ({ timeout, waitUntil }) => {
            console.log(chalk.gray(`goback...`));
            const response = await page.goBack({ timeout, waitUntil });
            const status = response?.status();
            console.log(chalk.gray(`goback complete, status=${status}`));
            return { status };
        },
        onHtml: async () => {
            console.log(chalk.gray(`extracting html...`));
            const html = await page.evaluate(() => document.querySelector("*")!.outerHTML);
            console.log(chalk.gray(`html extraction complete`));
            return html;
        },
        onLocator: async ({ frame, selector, method, params }) => {
            console.log(chalk.gray(`running locator "${selector}"...`));
            let locator = undefined as playwright.Locator | undefined;
            if (frame)
                locator = await page.frameLocator(frame).locator(selector);
            else
                locator = await page.locator(selector);
            const result = await invokeAsyncMethod(locator, method, params);
            console.log(chalk.gray(`locator complete`));
            return result;
        },
        onNavigate: async ({ url, timeout, waitUntil }) => {
            console.log(chalk.gray(`navigating to ${url}... waitUntil=${waitUntil}, timeout=${timeout}`));
            let status: number | undefined = undefined;
            try {
                const response = await page.goto(url, { timeout, waitUntil });
                status = response?.status();
                console.log(chalk.gray(`navigation complete, status=${status}`));
            }
            catch (err) {
                console.log(chalk.red(`navigation error ${err}`));
            }
            if (pause) {
                console.log("press any key to continue...");
                await waitForKeypress();
            }
            else if (snooze && status) {
                console.log(chalk.gray(`snoozing for ${snooze} seconds ...`));
                await sleep(snooze * 1000);
            }
            return { status };
        },
        onReload: async ({ timeout, waitUntil }) => {
            console.log(chalk.gray(`reloading...`));
            const response = await page.reload({ timeout, waitUntil });
            const status = response?.status();
            console.log(chalk.gray(`reload complete, status=${status}`));
            return { status };
        },
        onScreenshot: async ({ selector, fullPage, ...options }) => {
            console.log(chalk.gray(`taking screenshot...`));
            const path = `./screenshots/${new Date().toLocaleString("en-US", { hour12: false }).replace(/:/g, "-").replace(/\//g, "-").replace(/,/g, "")}.png`;
            let clip: { x: number, y: number, height: number, width: number } | undefined = undefined;
            if (selector)
                clip = await page.evaluate(() => document.querySelector(selector)?.getBoundingClientRect());
            await page.screenshot({ ...options, path, clip, fullPage });
            console.log(chalk.gray(`screenshot written, path=${path}`));
        },
        onYield: async ({ timeout, waitUntil }) => {
            console.log(chalk.gray(`yielding...`));
            await page.waitForLoadState(waitUntil, { timeout });
            console.log(chalk.gray(`yield complete`));
        }
    });

    return result;
}
