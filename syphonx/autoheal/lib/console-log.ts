import * as util from "util";

const ref = console.log;
let buffer: Array<[number, string]> = [];

export interface DumpConsoleLogOptions {
    timestamp?: Intl.DateTimeFormat
}

export function dumpConsoleLog(options: DumpConsoleLogOptions = {}): string {
    const whitespace = " ".repeat(options.timestamp?.format(Date.now()).length || 0);
    return buffer.map(formatter).join("\n").trim();

    function formatter([timestamp, text]: [number, string]): string {
        text = text.replace(/\x1B[[(?);]{0,2}(;?\d)*[@-~]/g, ''); // remove non-printable characters
        if (text.length === 0)
            return "";
        else if (options.timestamp) {
            if (text.includes("\n"))
                text = indentHangingLines(text);
            return `${options.timestamp.format(timestamp)} ${text}`;
        }
        else {
            return text;
        }
    }

    function indentHangingLines(text: string): string {
        const lines = text.split("\n");
        return `${lines[0]}\n${lines.slice(1).map(line => line.trim().length > 0 ? `${whitespace} ${line}` : "").join("\n")}`;
    }
}

export function hookConsoleLog(): void {
    if (console.log === log)
        console.error("warning: unexpected console log hook");
    console.log = log;
}

export function resetConsoleLog(): void {
    buffer = [];
}

export function unhookConsoleLog(): void {
    if (console.log === ref)
        console.error("warning: unexpected console log unhook");
    console.log = ref;
    resetConsoleLog();
}

function log(...args: any[]): void {
    const text = util.format(...args);
    if (text || buffer.length > 0)
        buffer.push([Date.now(), text]);
    ref(...args);
}
