import { llm, LLMModel, LLMModelOptions, LLMMessage, LLMResult } from "./llm.js";
import { loadImages } from "./image.js";
import { extractJson } from "./json.js";
import chalk from "chalk";

export interface LLMChatOptions extends LLMModelOptions {
    model: LLMModel;
    host?: string;
    max_tokens?: number;
    max_retries?: number;
    apiKey?: string;
}

export interface LLMUsage {
    model: LLMModel;
    input_tokens: number;
    output_tokens: number;
    cached_tokens: number;
    cost: number;
    host?: string;
}

export class LLMError extends Error {
    constructor(message: string, response?: string) {
        super(message);
        this.response = response;
    }
    response?: string;
}

export class LLMChat {
    model: LLMModel;
    host?: string;
    tokens = 0;
    input_tokens = 0;
    output_tokens = 0;
    cached_tokens = 0;
    cost = 0;
    turns = 0;
    elapsed = 0;
    max_tokens = 4096;
    max_retries = 3;
    messages: LLMMessage[] = [];
    usage: LLMUsage[] = [];
    apiKey?: string;

    constructor(params: LLMModel | LLMChatOptions) {
        if (typeof params === "string") {
            this.model = params;
        }
        else if (typeof params === "object" && params !== null) {
            this.model = params.model;
            this.host = params.host;
            if (params.max_retries)
                this.max_retries = params.max_retries;
            if (params.max_tokens)
                this.max_tokens = params.max_tokens;
            if (params.apiKey)
                this.apiKey = params.apiKey;
        }
        else {
            throw new Error("LLMChat model not specified");
        }
    }

    reset(): void {
        this.tokens = 0;
        this.input_tokens = 0;
        this.output_tokens = 0;
        this.cached_tokens = 0;
        this.cost = 0;
        this.turns = 0;
        this.elapsed = 0;
        this.messages = [];
        this.usage = [];
    }

    clone(): LLMChat {
        const chat = new LLMChat(this.model);
        Object.assign(chat, {
            ...this,
            messages: this.messages.slice(0),
            usage: this.usage.slice(0)    
        });
        return chat;
    }

    async prompt(input: string, options?: LLMModelOptions): Promise<string> {
        const message: LLMMessage = {
            role: "user",
            content: [{
                type: "text",
                value: input
            }],
            text: input
        };

        if (options?.images && options.images.length > 0) {
            const images = await loadImages(options.images);
            for (const datauri of images)
                message.content.push({
                    type: "image",
                    value: datauri
                });
        }
        
        this.messages.push(message);
        const t0 = Date.now();
        const response = await retryLLM(
            this.model,
            this.messages,
            {
                ...options,
                max_tokens: this.max_tokens,
                apiKey: this.apiKey
            },
            this.host,
            this.max_retries
        );
        this.elapsed += Date.now() - t0;
        this.messages = response.messages;
        this.turns += 1;
        this.tokens += response.input_tokens + response.output_tokens;
        this.input_tokens += response.input_tokens;
        this.output_tokens += response.output_tokens;
        this.cost += response.cost;
        this.usage.push({
            model: this.model,
            host: response.host,
            input_tokens: response.input_tokens,
            output_tokens: response.output_tokens,
            cached_tokens: response.cached_tokens,
            cost: response.cost
        });

        if (!response.stop_ok)
            throw new Error(`Model "${this.model}" returned unexpected LLM stop reason "${response.stop_reason}"`);

        return response.output;
    }

    async json<T = unknown>(input: string, options?: LLMModelOptions): Promise<[T | undefined, string]> {
        const output = await this.prompt(input, { ...options, json: true });
        const result = extractJson(output);
        if (result.length === 0)
            return [undefined, output];
        const [{ obj }] = result;
        return [obj as T, output];
    }
}

const cooldown_intervals = [5, 10, 30]; // seconds
async function retryLLM(model: LLMModel, messages: LLMMessage[], options: LLMModelOptions | undefined, host: string | undefined, max_retries: number): Promise<LLMResult> {
    let retry = 0;
    while (true) {
        try {
            const response = await llm(model, messages, options, host);
            return response;
        }
        catch (err) {
            if (typeof err === "object" && err !== null && (err as any).status === 429) {
                if (++retry <= max_retries) {
                    const cooldown_seconds = retry <= cooldown_intervals.length ? cooldown_intervals[retry - 1] : cooldown_intervals.at(-1)!;
                    if (process.env.VERBOSE)
                        console.log(chalk.italic.yellow(`${model} rate limit hit, ${cooldown_seconds} second cooldown...`));
                    await sleep(cooldown_seconds * 1000);
                    continue;
                }
            }
            throw err;
        }
    }
}

function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}
