import Anthropic from "@anthropic-ai/sdk";
import { <PERSON><PERSON><PERSON><PERSON>, LLMMessage, LLMModelOptions, LLMResult } from "./llm.js";

export type AnthropicAIModel =
    "claude-3-haiku" |
    "claude-3-haiku-20240307" |
    "claude-3-5-sonnet" |
    "claude-3-5-sonnet-20240620" |
    "claude-3-opus" |
    "claude-3-opus-20240229"

export const models = [
    "claude-3-haiku",
    "claude-3-haiku-20240307",
    "claude-3-5-sonnet",
    "claude-3-5-sonnet-20240620",
    "claude-3-opus",
    "claude-3-opus-20240229"
];

const alias_map: Record<string, AnthropicAIModel> = {
    "claude-3-haiku": "claude-3-haiku-20240307",
    "claude-3-5-sonnet": "claude-3-5-sonnet-20240620",
    "claude-3-opus": "claude-3-opus-20240229"
};

// https://www.anthropic.com/api-bk
const price_map: Record<string, [number, number]> = {
    "claude-3-haiku-20240307": [0.25, 1.25],
    "claude-3-5-sonnet-20240620": [3, 15],
    "claude-3-opus-20240229": [15, 75]
};

export async function llm(model: AnthropicAIModel, messages: LLMMessage[], { apiKey = process.env.ANTHROPIC_API_KEY, temperature, max_tokens = 4096 }: LLMModelOptions): Promise<LLMResult> {
    if (!apiKey)
        throw new Error("ANTHROPIC_API_KEY not found");
    const anthropic = new Anthropic({ apiKey });

    if (alias_map[model])
        model = alias_map[model];

    const response = await anthropic.messages.create({
        model,
        temperature,
        max_tokens,
        messages: messages.map(renderMessage)
    });

    const pricing = price_map[model];
    const input_tokens = response.usage.input_tokens;
    const output_tokens = response.usage.output_tokens;
    const cost = input_tokens * pricing[0] / 1000000 + output_tokens * pricing[1] / 1000000;
    const stop_reason = response.stop_reason;

    return {
        host: "anthropic",
        output: (response.content.at(-1) as { text: string }).text,
        messages,
        response,
        model,
        input_tokens,
        output_tokens,
        cached_tokens: 0,
        cost,
        stop_reason: stop_reason || "(none)",
        stop_ok: stop_reason === "end_turn" || stop_reason === undefined
    };
}

function renderMessage(message: LLMMessage) {
    return {
        role: message.role,
        content: message.content.map(renderContent)
    } as Anthropic.MessageParam
}

function renderContent(content: LLMContent) {
    if (content.type === "text")
        return {
            type: "text",
            text: content.value
        } as Anthropic.TextBlockParam
    else if (content.type === "image")
        return {
            type: "image",
            source: {
                type: "base64",
                media_type: contentTypeFromDatauri(content.value),
                data: content.value.split(",")[1]
            }
        } as Anthropic.ImageBlockParam
    else
        throw new Error("LLMContent type is invalid");
}

type ImageType = "image/jpeg" | "image/png" | "image/gif" | "image/webp";
function contentTypeFromDatauri(datauri: string): ImageType {
    const [, match] = datauri.match(/^data:([^;]+);/) || [];
    return match as ImageType;
}
