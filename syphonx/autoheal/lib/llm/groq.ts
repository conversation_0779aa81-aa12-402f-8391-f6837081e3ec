import Groq from "groq-sdk";
import { <PERSON><PERSON><PERSON><PERSON>, LLMMessage, LLMModelOptions, LLMResult } from "./llm.js";

export type GroqAIModel =

    // production models
    "gemma2-9b-it" |
    "llama-3.3-70b-versatile" |
    "llama-3.1-8b-instant" |
    "llama-guard-3-8b" |
    "llama3-70b-8192" |
    "mixtral-8x7b-32768" |
    "llama3-8b-8192" |
    "gemma-7b-it" |

    // preview models
    "deepseek-r1-distill-llama-70b-specdec" |
    "deepseek-r1-distill-llama-70b" |
    "lama-3.3-70b-specdec" |
    "llama-3.2-1b-preview" |
    "llama-3.2-3b-preview" |
    "llama-3.2-11b-vision-preview" |
    "llama-3.2-90b-vision-preview";


// https://console.groq.com/docs/models
export const models = [

    // production models
    "gemma2-9b-it",
    "llama-3.3-70b-versatile",
    "llama-3.1-8b-instant",
    "llama-guard-3-8b",
    "llama3-70b-8192",
    "mixtral-8x7b-32768",
    "llama3-8b-8192",
    "gemma-7b-it",

    // preview models
    "deepseek-r1-distill-llama-70b-specdec",
    "deepseek-r1-distill-llama-70b",
    "lama-3.3-70b-specdec",
    "llama-3.2-1b-preview",
    "llama-3.2-3b-preview",
    "llama-3.2-11b-vision-preview",
    "llama-3.2-90b-vision-preview"
];

// https://groq.com/pricing/
const price_map = {

    // production models
    "gemma2-9b-it": [0.20, 0.20],
    "llama-3.3-70b-versatile": [0.59, 0.79],
    "llama-3.1-8b-instant": [0.05, 0.08],
    "llama-guard-3-8b": [0.20, 0.20],
    "llama3-70b-8192": [0.59, 0.70],
    "mixtral-8x7b-32768": [0.24, 0.24],
    "llama3-8b-8192": [0.05, 0.08],
    "gemma-7b-it": [0.07, 0.07],

    // preview models
    "deepseek-r1-distill-llama-70b-specdec": [0.75, 0.99],
    "deepseek-r1-distill-llama-70b": [0.75, 0.99],
    "lama-3.3-70b-specdec": [0.59, 0.99],
    "llama-3.2-1b-preview": [0, 0],
    "llama-3.2-3b-preview": [0, 0],
    "llama-3.2-11b-vision-preview": [0.18, 0.18],
    "llama-3.2-90b-vision-preview": [0.90, 0.90]
};

export async function llm(model: GroqAIModel, messages: LLMMessage[], { apiKey = process.env.GROQ_API_KEY, temperature, max_tokens = 4096 }: LLMModelOptions): Promise<LLMResult> {
    if (!apiKey)
        throw new Error("GROQ_API_KEY not found");

    const groq = new Groq({ apiKey, dangerouslyAllowBrowser: true });
      
    const response = await groq.chat.completions.create({
        messages: messages.map(renderMessage),
        model,
        temperature,
        max_tokens
    });

    const pricing = price_map[model];
    const input_tokens = response.usage?.prompt_tokens || 0;
    const output_tokens = response.usage?.completion_tokens || 0;
    const cost = input_tokens * pricing[0] / 1000000 + output_tokens * pricing[1] / 1000000;
    const finish_reason = response.choices.at(-1)?.finish_reason;

    return {
        host: "groq",
        output: response.choices.at(-1)?.message.content!,
        messages,
        response,
        model,
        input_tokens,
        output_tokens,
        cached_tokens: 0,
        cost,
        stop_reason: finish_reason || "(none)",
        stop_ok: finish_reason === "stop" || finish_reason === undefined
    };
}

function renderMessage(message: LLMMessage): Groq.Chat.ChatCompletionMessageParam {
    if (message.content.length === 1 && message.content[0].type === "text")
        return {
            role: message.role,
            content: message.content[0].value
        };
    else
        return {
            role: message.role,
            content: message.content.map(renderContent) as any
        };
}

function renderContent(content: LLMContent) {
    if (content.type === "text")
        return {
            type: "text",
            text: content.value
        };
    else if (content.type === "image")
        return {
            type: "image_url",
            image_url: {
                url: content.value,
            }
        };
    else
        throw new Error("LLMContent type is invalid");
}
