import { GenerateContentRequest, GoogleGenerativeAI } from "@google/generative-ai";
const defaultModel = "gemini-2.0-flash" as const;

import { LLMContent, LLMMessage, LLMModelOptions, LLMResult } from "./llm.js";

export type GoogleAIModel =

    // production models
    "gemini-2.0-flash" |
    "gemini-2.0-flash-lite" |

    // preview models
    "gemini-2.0-flash-exp" |
    "gemini-2.0-flash-thinking-exp-01-21" |
    "gemini-2.0-pro-exp-02-05" |
    "gemini-2.5-pro-exp-03-25";

export const models = [

    // production models
    "gemini-2.0-flash",
    "gemini-2.0-flash-lite",

    // preview models
    "gemini-2.0-flash-exp",
    "gemini-2.0-flash-thinking-exp-01-21",
    "gemini-2.0-pro-exp-02-05",
    "gemini-2.5-pro-exp-03-25"
];

// https://aistudio.google.com/
// https://cloud.google.com/vertex-ai/generative-ai/pricing
const price_map: Record<string, number[]> = {

    // production models
    "gemini-2.0-flash": [0.1, 0.4], // all
    "gemini-2.0-flash-lite": [0.075, 0.3], // all

    // preview models
    "gemini-2.0-flash-exp": [0, 0, 0, 0],
    "gemini-2.0-flash-thinking-exp-01-21": [0, 0, 0, 0],
    "gemini-2.0-pro-exp-02-05": [0, 0, 0, 0],
    "gemini-2.5-pro-exp-03-25": [0, 0, 0, 0]
};

export async function llm(model: GoogleAIModel, messages: LLMMessage[], { apiKey = process.env.GOOGLE_GENAI_KEY, temperature, max_tokens = 4096 }: LLMModelOptions): Promise<LLMResult> {
    if (!apiKey)
        throw new Error("GOOGLE_GENAI_KEY not found");

    const genai = new GoogleGenerativeAI(apiKey);
    const request = {
        contents: messages.map(renderMessage),
        generationConfig: {
            temperature,
            maxOutputTokens: max_tokens
        }
    } as GenerateContentRequest;

    const result = await genai.getGenerativeModel({ model }).generateContent(request);
    const response = await result.response;
    const output = response.text();

    const pricing = price_map[model] || price_map[defaultModel];
    const input_tokens = result.response.usageMetadata?.promptTokenCount || 0;
    const output_tokens = result.response.usageMetadata?.candidatesTokenCount || 0;
    const cost = (input_tokens / 1000000) * pricing[0] + (output_tokens / 1000000) * pricing[1];
    const stop_reason = response.candidates ? response.candidates[0].finishReason : undefined;

    return {
        host: "google",
        output,
        messages,
        response,
        model,
        input_tokens,
        output_tokens,
        cached_tokens: 0,
        cost,
        stop_reason,
        stop_ok: stop_reason === "STOP" || stop_reason === undefined
    };
}

function renderMessage(message: LLMMessage) {
    return {
        role: renderRole(message.role),
        parts: message.content.map(renderContent)
    }
}

function renderRole(role: string) {
    if (role === "user")
        return "user";
    else if (role === "assistant")
        return "model"
    else
        throw new Error("LLMMessage role is invalid");
}

function renderContent(content: LLMContent) {
    if (content.type === "text") {
        return { text: content.value };
    }
    else if (content.type === "image") {
        const [scheme, data] = content.value.split(",");
        const mimeType = scheme.slice(5, scheme.indexOf(";"));
        return { inlineData: { mimeType, data } };
    }
    else {
        throw new Error("LLMContent type is invalid");
    }
}
