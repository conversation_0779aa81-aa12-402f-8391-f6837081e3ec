import { expect } from "chai";
import { extractJson } from "./json.js";

describe("extractJson", () => {
    it("empty string has expected result", () => expect(extractJson("")).eql([]));
    it("blank string has expected result", () => expect(extractJson("   ")).eql([]));
    it("null has expected result", () => expect(extractJson(null as unknown as string)).eql([]));
    it("undefined has expected result", () => expect(extractJson(undefined as unknown as string)).eql([]));
    
    it("t1 has expected value", () => expect(extractJson("Hello world!")).eql([]));
    it("t2 has expected value", () => expect(extractJson("Hello {sailor}! Nothing happens here.")).eql([]));

    it("unwrapped-1 has expected result", () => expect(extractJson(`{"a":1}`)).eql([{ start: 0, end: 7, obj: { a: 1 } }]));
    it("unwrapped-2 string has expected result", () => expect(extractJson(`   { "a": 1  }   `)).eql([{ start: 0, end: 17, obj: { a: 1 } }]));    
    it("unwrapped-3 has expected result", () => expect(extractJson(`{"a":1} {"b":2}`)).eql([{ start: 0, end: 7, obj: { a: 1 } }, { start: 8, end: 15, obj: { b: 2 }}]));

    it("unwrapped-4 has expected result", () => expect(extractJson(`[{"a":1}]`)).eql([{ start: 0, end: 9, obj: [{ a: 1 }] }]));
    it("unwrapped-5 has expected result", () => expect(extractJson(`[1,2,3]`)).eql([{ start: 0, end: 7, obj: [1, 2, 3] }]));
    it("unwrapped-6 has expected result", () => expect(extractJson(`["alpha", "beta", "gamma"]`)).eql([{ start: 0, end: 26, obj: ["alpha", "beta", "gamma"] }]));

    it("unwrapped-7 has expected result", () => expect(extractJson(`
{
    "alpha": "phi",
    "beta": "Lorum \\"ipsum\\"",
    "gamma": null
}`
    )).eql([{ start: 0, end: 73, obj: { alpha: "phi", beta: `Lorum "ipsum"`, gamma: null } }]));

    it("wrapped-1 has expected result", () => expect(extractJson(`
\`\`\`json
{
    "alpha": "phi",
    "beta": "Lorum \\"ipsum\\"",
    "gamma": null
}
\`\`\``
    )).eql([{ start: 8, end: 82, obj: { alpha: "phi", beta: `Lorum "ipsum"`, gamma: null } }]));


    it("wrapped-2 has expected result", () => expect(extractJson(`
Here is the data...

\`\`\`json
{
    "alpha": "phi",
    "beta": "Lorum \\"ipsum\\"",
    "gamma": null
}
\`\`\`

That's about it.`
    )).eql([{ start: 29, end: 103, obj: { alpha: "phi", beta: `Lorum "ipsum"`, gamma: null } }]));


    it("wrapped-3 has expected result", () => expect(extractJson(`
Here's some data...

\`\`\`json
{
    "alpha": "phi",
    "beta": "Lorum \\"ipsum\\"",
    "gamma": null
}
\`\`\`

Here's some more data...

\`\`\`json
[
    "one",
    "two",
    "three"
]
\`\`\`

That's about it.`
    )).eql([
        { start: 29, end: 103, obj: { alpha: "phi", beta: `Lorum "ipsum"`, gamma: null } },
        { start: 141, end: 180, obj: ["one", "two", "three"] }
    ]));

    it("inline-1 has expected result", () => expect(extractJson(`
Here is the data...

{
    "alpha": "phi",
    "beta": "Lorum \\"ipsum\\"",
    "gamma": null
}

That's about it.`
            )).eql([{ start: 22, end: 94, obj: { alpha: "phi", beta: `Lorum "ipsum"`, gamma: null } }]));
});
