import * as anthropic from "./anthropic.js";
import * as google from "./google.js";
import * as groq from "./groq.js";
import * as openai from "./openai.js";

export type LLMModel =
    anthropic.AnthropicAIModel |
    google.GoogleAIModel |
    groq.GroqAIModel |
    openai.OpenAIModel;

export const llm_models = [
    ...openai.models,
    ...google.models,
    ...anthropic.models,
    ...groq.models
];

export interface LLMOptions {
    model: LLMModel;
    prompt?: string;
    template?: string;
    context?: object;
    obj?: unknown;
    /** images can be a file, url, or datauri */
    images?: string[];
    temperature?: number;
    max_tokens?: number;
    json?: boolean;
}

export interface LLMModelOptions {
    json?: boolean;
    temperature?: number;
    max_tokens?: number;
    images?: string[];
    apiKey?: string;
};

export type LLMRole = "user" | "assistant";
export interface LLMMessage {
    role: LLMRole;
    content: LLMContent[];
    text: string;
}

export type LLMContentType = "text" | "image";
export interface LLMContent {
    type: LLMContentType;
    value: string;
}

export interface LLMResult {
    output: string;
    messages: LLMMessage[];
    response: unknown;
    model: LLMModel;
    host: string;
    input_tokens: number;
    output_tokens: number;
    cached_tokens: number;
    cost: number;
    stop_reason: string | undefined;
    stop_ok: boolean;
}

export async function llm(model: LLMModel, messages: LLMMessage[], options: LLMModelOptions = {}, host?: string): Promise<LLMResult> {
    model = model.toLowerCase() as LLMModel;
    let result: LLMResult;
    if (openai.models.includes(model) && (!host || host === "openai"))
        result = await openai.llm(model as openai.OpenAIModel, messages, options);
    else if (google.models.includes(model) && (!host || host === "google"))
        result = await google.llm(model as google.GoogleAIModel, messages, options);
    else if (anthropic.models.includes(model) && (!host || host === "anthropic"))
        result = await anthropic.llm(model as anthropic.AnthropicAIModel, messages, options);
    else if (groq.models.includes(model) && (!host || host === "groq"))
        result = await groq.llm(model as groq.GroqAIModel, messages, options);
    else
        throw new Error(`Invalid LLM model specified: "${model}"`);

    // ensure all messages have a text representation...
    messages = messages.map(message => ({
        role: message.role,
        content: message.content,
        text: message.text || message.content.find(content => content.type === "text")?.value || ""
    }));

    // append the last output as an assistant response
    messages.push({
        role: "assistant",
        content: [{
            type: "text",
            value: result.output
        }],
        text: result.output
    });
    
    return { ...result, messages };
}
