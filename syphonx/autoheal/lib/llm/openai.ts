import OpenAI from "openai";
import { <PERSON><PERSON>ontent, LLMMessage, LLMModelOptions, LLMResult } from "./llm.js";

// https://platform.openai.com/docs/models/
export type OpenAIModel =
    "o1" |
    "o3-mini" |
    "gpt-4" |
    "gpt-4o" |
    "gpt-4o-mini" |
    "gpt-4-turbo" |
    "gpt-4-turbo-2024-04-09" |
    "gpt-4-1106-preview" |
    "gpt-4-0125-preview" |
    "gpt-4-turbo-preview" |
    "gpt-4-vision-preview" |
    "gpt-4-32k" |
    "gpt-3.5-turbo" |
    "gpt-3.5-turbo-1106";

export const models = [
    "o1",
    "o3-mini",
    "gpt-4",
    "gpt-4o",
    "gpt-4o-mini",
    "gpt-4-turbo",
    "gpt-4-turbo-2024-04-09",
    "gpt-4-1106-preview",
    "gpt-4-0125-preview",
    "gpt-4-turbo-preview",
    "gpt-4-vision-preview",
    "gpt-4-32k",
    "gpt-3.5-turbo",
    "gpt-3.5-turbo-1106"
];

// https://openai.com/api/pricing/
const price_map = {
    "o1": [15, 60],
    "o3-mini": [1.1, 4.4],
    "gpt-4": [30, 60],
    "gpt-4o": [5, 15],
    "gpt-4o-mini": [0.15, 0.60],
    "gpt-4-turbo": [10, 30],
    "gpt-4-turbo-2024-04-09": [30, 60],
    "gpt-4-1106-preview": [30, 30],
    "gpt-4-0125-preview": [30, 30],
    "gpt-4-turbo-preview": [30, 30],
    "gpt-4-vision-preview": [10, 30],
    "gpt-4-32k": [6, 12],
    "gpt-3.5-turbo": [1, 2],
    "gpt-3.5-turbo-1106": [1, 2]
};

// note: vision costs are translated to tokens, so no additional cost calculations are required
// - low costs 85 tokens flat
// - high costs 170 tokens per 512x512 tile on top of the flat 85 token base
// - images over 2048x2048 are resized to 2048x2048 mainting aspect ratio
// https://platform.openai.com/docs/guides/vision/calculating-costs

export async function llm(model: OpenAIModel, messages: LLMMessage[], { apiKey = process.env.OPENAI_API_KEY, temperature, max_tokens = 4096, json }: LLMModelOptions): Promise<LLMResult> {
    if (!apiKey)
        throw new Error("OPENAI_API_KEY not found");

    const openai = new OpenAI({ apiKey, dangerouslyAllowBrowser: true });
    const response = await openai.chat.completions.create({
        model,
        messages: messages.map(renderMessage),
        max_tokens: model.startsWith("gpt-") ? max_tokens : undefined,
        max_completion_tokens: !model.startsWith("gpt-") ? max_tokens : undefined,
        temperature,
        // 2/3/25 specifying json_object wasn't working for returning arrays of objects, it was only returning the first object in the array
        //response_format: json ? { type: "json_object" } : undefined
    });

    const pricing = price_map[model];
    const input_tokens = response.usage?.prompt_tokens || 0;
    const output_tokens = response.usage?.completion_tokens || 0;
    const cached_tokens = response.usage?.prompt_tokens_details?.cached_tokens || 0;
    const cost = response.usage ? input_tokens * pricing[0] / 1000000 + output_tokens * pricing[1] / 1000000 : 0;
    const [choice] = response.choices;
    const finish_reason = choice.finish_reason;

    return {
        host: "openai",
        output: choice.message.content || "",
        messages,
        response,
        model,
        input_tokens,
        output_tokens,
        cached_tokens,
        cost,
        stop_reason: finish_reason || "(none)",
        stop_ok: finish_reason === "stop" || finish_reason === undefined
    }
}

function renderMessage(message: LLMMessage) {
    return {
        role: message.role,
        content: message.content.map(renderContent)
    } as OpenAI.ChatCompletionMessageParam;
}

function renderContent(content: LLMContent) {
    if (content.type === "text")
        return {
            type: "text",
            text: content.value
        } as OpenAI.ChatCompletionContentPartText
    else if (content.type === "image")
        return {
            type: "image_url",
            image_url: {
                url: content.value,
                detail: "auto"
            }
        } as OpenAI.ChatCompletionContentPartImage
    else
        throw new Error("LLMContent type is invalid");
}
