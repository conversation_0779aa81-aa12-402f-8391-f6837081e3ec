import JSON5 from "json5";

export interface ExtractJson {
    start: number;
    end: number;
    obj: unknown;
}

export function extractJson(text: string): ExtractJson[] {
    if (typeof text === "string") {
        const r1 = extractJsonUnwrapped(text);
        if (r1)
            return r1;
        const r2 = extractJsonWrapped(text, "```json", "```");
        if (r2)
            return r2;
        const r3 = extractJsonWrapped(text, "```", "```");
        if (r3)
            return r3;
        const r4 = extractJsonInline(text);
        if (r4)
            return r4;
    }
    return [];
}

function extractJsonUnwrapped(text: string): ExtractJson[] | undefined {
    if (text.trim().startsWith("{") && text.trim().endsWith("}")) {
        const obj = tryParseJson(text);
        if (obj !== undefined)
            return [{ start: 0, end: text.length, obj }];
    }
    if (text.trim().startsWith("[") && text.trim().endsWith("]")) {
        const obj = tryParseJson(text);
        if (obj !== undefined)
            return [{ start: 0, end: text.length, obj }];
    }
}

function extractJsonWrapped(text: string, prefix: string, suffix: string): ExtractJson[] | undefined {
    const result = [];
    let i = text.indexOf(prefix);
    let j = text.indexOf(suffix, i + 1);
    while (i >= 0 && j > i) {
        const substr = text.slice(i + prefix.length, j);
        const obj = tryParseJson(substr);
        if (obj !== undefined)
            result.push({ start: i + prefix.length, end: j, obj });
        i = text.indexOf(prefix, j + 1);
        j = text.indexOf(suffix, i + 1);
    }
    if (result.length > 0)
        return result;
}

function extractJsonInline(text: string): ExtractJson[] | undefined {
    return extractJsonInlineX(text, "{}") || extractJsonInlineX(text, "[]");
}

function extractJsonInlineX(text: string, a: "{}" | "[]"): ExtractJson[] | undefined {
    const result = [];
    const i = text.indexOf(a[0]);
    const j = text.lastIndexOf(a[1]);
    const substr = text.slice(i, j + 1);
    const obj = tryParseJson(substr);
    if (obj !== undefined)
        result.push({ start: i, end: j + 1, obj });
    return result;
}

function tryParseJson(text: string) {
    try { return JSON5.parse(text); }
    catch {}
}
