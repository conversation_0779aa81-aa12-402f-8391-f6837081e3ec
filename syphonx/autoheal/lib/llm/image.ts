import { downloadClipImage } from "../index.js";

export async function loadImages(files: string[]) {
    const images = [];
    for (let file of files) {
        if (file.startsWith("data:image")) {
            images.push(file);
        }
        else {
            const { datauri } = await downloadClipImage(file);
            if (datauri)
                images.push(datauri);
            else
                throw new Error(`Failed to download image "${file}"`);
        }
    }
    return images;
}
