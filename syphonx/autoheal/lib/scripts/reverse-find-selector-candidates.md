Below is a step-by-step explanation of what the function is doing. At a high level, **`reverseFindSelectorCandidates`** is meant to climb up the DOM tree from a given target element, collecting candidate CSS selectors that uniquely identify that target in the entire document.

---

## Overview

1. **Inputs**  
   - **`selector`**: A normal CSS selector that identifies the single target element.
   - **`nthOfTypeRunLimit`**: Limits how many consecutive occurrences of `:nth-of-type` (or `:first-of-type`) can appear in the final selector.

2. **Outputs**  
   - **Returns** an array of potential selectors, each of which uniquely identifies that element in the entire document.

---

## How the Function Works

1. **Get the Target Element**  
   ```ts
   const targetElement = document.querySelector(selector);
   ```
   - The function first looks up the element based on the `selector` provided.  
   - If no element is found, it immediately returns an empty array.

2. **Initialization**  
   ```ts
   const closed: string[] = [];
   let open: string[] = [];
   let element: Element | null = targetElement;
   ```
   - **`closed`**: A list of selectors that have already been proven to be unique (only match the target element).  
   - **`open`**: A list of selectors currently being explored but not yet proven unique.  
   - **`element`** starts as the target element and will climb up the DOM tree.

3. **Climb Up the DOM Tree**  
   ```ts
   while (element && element.tagName !== "BODY") {
       const next = new Set<string>();
       // ...
       element = element.parentElement; // move up
   }
   ```
   - This loop ascends from the target element toward the `BODY`.  
   - In each iteration, it looks at `element` and tries to build new selector parts for it.  

4. **Building Selector Parts**  
   Inside each loop iteration, the code looks at various attributes that might identify the current `element`:

   1. **Element Tag**  
      ```ts
      const tag = element.tagName.toLowerCase();
      append(tag);
      ```
      - Always includes the lowercase tag name, e.g., `"div"`, `"span"`, etc.

   2. **ID Attribute**  
      ```ts
      const id = element.getAttribute("id");
      if (id && /^[A-Za-z]+[\w\-\:\.]*$/.test(id)) {
          append(`#${id}`);
      }
      ```
      - If the element has an `id` and it matches the RegEx for valid IDs, `#id` gets appended as an option.

   3. **Classes**  
      ```ts
      const classes = element.getAttribute("class") ?
          element.getAttribute("class")!.split(" ")
              .filter(name => /^-?[_a-z]+[_a-z0-9-]*$/i.test(name))
          : [];
      classes.forEach(name => append(`.${name}`));
      ```
      - Splits the `class` attribute and checks each class name against a RegEx (valid CSS class name).  
      - Each valid class name becomes an option like `.myClass`.

   4. **Other Attributes**  
      ```ts
      const attributes = Array.from(element.attributes)
          .filter(attr => !["id", "class", "style", "src", "href", "title", "lang"].includes(attr.name))
          .filter(attr => !attr.value.includes("\n") && attr.value.length <= 40);
      attributes.forEach(attr => 
          append(`[${attr.name}${attr.value ? `='${attr.value.replace(/'/g, "\\'")}'` : ""}]`)
      );
      ```
      - Looks at all attributes other than typical ones like `id`, `class`, etc.  
      - Skips any attribute values that are too long or contain newlines.  
      - Builds `[attrName='attrValue']` for each valid attribute.

   Each time it has a potential piece of a selector (tag, ID, class, or attribute), it calls `append(...)`.

5. **The `append` Function**  
   ```ts
   function append(target: string): void {
       const paths = open.length > 0 ? open : [""]; 
       for (const path of paths) {
           const selector = path ? `${target} > ${path}` : target;
           const elements = Array.from(document.querySelectorAll(selector));
           
           if (elements.length === 1 && elements[0].isEqualNode(targetElement)) {
               // unique match => move to 'closed'
               closed.push(selector);
               const i = open.indexOf(path);
               open.splice(i, 1);
           }
           else if (element?.parentElement && /^[a-z]+$/.test(target)) {
               // If the target is just a tag, try adding :nth-of-type or :first-of-type
               const i = Array.from(element.parentElement.children)
                   .filter(child => child.tagName.toLowerCase() === target)
                   .findIndex(child => child.isEqualNode(element));
               if (i === 0)
                   next.add(`${target}:first-of-type${path ? ` > ${path}` : ""}`);
               else if (i > 0)
                   next.add(`${target}:nth-of-type(${i + 1})${path ? ` > ${path}` : ""}`);
           } 
           else if (element?.parentElement) {
               // For non-tag-based selectors (e.g. .class, #id), see if it alone matches uniquely within the parent
               const elements = element.parentElement.querySelectorAll(selector);
               if (elements.length === 1 && elements[0].isEqualNode(targetElement)) {
                   next.add(selector);
               }
           }
       }
   }
   ```
   - **`paths`**: If there are already open selectors being explored, it extends them; otherwise, it starts fresh with `[""]`.  
   - **Constructing a candidate**: It prepends the new piece (`target`) to existing paths (`path`) with a `>` combinator, creating something like `div > .myClass > ...` or `#someId > ...`.  
   - **Check if unique**: `document.querySelectorAll(selector)` is used to see if the selector matches exactly one element (the target). If it **is** unique, we push it to **`closed`**.  
   - **Handling Tag Selectors**: If the `target` is just a tag (e.g. `"div"`) and not unique by itself, the code tries adding `:first-of-type` or `:nth-of-type(...)` to narrow it down among sibling elements of the same tag.  
   - **For non-tag-based** parts (e.g. `.class`, `#id`, `[attr=val]`), it checks if the new piece plus the path is unique inside the parent; if so, it’s added to `next`.

   Eventually, after trying all possible combinations for the current element, the function sets `open = Array.from(next)`, meaning the next iteration will keep building from the newly discovered partial selectors.

6. **Reducing Repetitive `:nth-of-type`**  
   After the main loop ends, we have some set of unique selectors in **`closed`** and partially built ones in **`open`**:
   ```ts
   const selectors = Array.from(new Set([...closed, ...open]));
   ```
   Then there’s a pass that attempts to simplify or remove overly complex selectors:
   ```ts
   for (let i = selectors.length - 1; i >= 0; --i) {
       const selector1 = selectors[i];
       if (/ > [a-z]+:(?:first-of-type|nth-of-type\(\d+\)) > /.test(selector1)) {
           const elements1 = document.querySelectorAll(selector1);
           const selector2 = selector1.replace(
               /( > [a-z]+:(?:first-of-type|nth-of-type\(\d+\)))+ > /g,
               " "
           );
           const elements2 = document.querySelectorAll(selector2);
           if (elements1.length === elements2.length && elements1[0].isEqualNode(elements2[0])) {
               // shorter version matches exactly => replace
               selectors.splice(i, 1, selector2);
           } 
           else if (selectors.length > 1) {
               const hits = Array.from(selector1.matchAll(/( > [a-z]+:(?:first-of-type|nth-of-type\(\d+\)))/g));
               if (hits.length - 1 > nthOfTypeRunLimit)
                   selectors.splice(i, 1); 
           }
       }
   }
   ```
   - It detects patterns like `" > div:nth-of-type(2) > span:first-of-type > ..."` and tries removing extra `:nth-of-type` parts if the shorter version still matches the same elements.  
   - If removing them still matches the target uniquely, the shorter version is used.  
   - If there are too many consecutive `nth-of-type` segments (exceeding `nthOfTypeRunLimit`), it removes that selector altogether.

7. **Sorting the Selectors**  
   Finally, it sorts the resulting set of unique selectors:
   ```ts
   result.push(
       ...Array.from(new Set(selectors))
       .sort((a, b) =>
           (a.match(/:nth-/g) || []).length - (b.match(/:nth-/g) || []).length ||
           (a.match(/>/g) || []).length - (b.match(/>/g) || []).length ||
           a.length - b.length ||
           a.localeCompare(b)
       )
   );
   ```
   The sorting criteria:
   1. Fewer occurrences of `:nth-` first.  
   2. Fewer child combinators (`>`).  
   3. Shorter overall selector length.  
   4. Alphabetical order if all else is equal.

8. **Return the Result**  
   The final **`result`** array is returned. Each item in `result` should be a selector that uniquely identifies the original target element.

---

## Summary

- **Core Idea**: Start from the target element, build potential identifying pieces (tag, ID, class, attribute), and check if they uniquely match the element in the entire document.  
- **If Not Unique**: Use more specific refinements (`:first-of-type`, `:nth-of-type`) and ascend up the DOM (adding parent combinators `>`).  
- **Store Unique Selectors** in `closed`, continue exploring partial matches in `open`.  
- **Perform Cleanups** to remove redundant `:nth-of-type` usage or overly complex selectors.  
- **Sort** the final unique selectors by how specific or how short they are.  

That’s how `reverseFindSelectorCandidates` systematically finds every possible unique selector for a given element, then filters and sorts them.