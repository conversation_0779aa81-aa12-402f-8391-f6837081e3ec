import { jsdomFromFile } from "./jsdom.js";
import { sliceHtml } from "./slice.js";
import { fileURLToPath } from "url";

const file = fileURLToPath(new URL("./a.html", import.meta.url));
jsdomFromFile(file);

const maxLength = 10000;
const options = {
    selector: "[__target__]",
    parents: <PERSON>,
    children: <PERSON>,
    siblings: 10,
    uncles: 6,
    nephews: 6,
    anchor: 3
};

let result = sliceHtml(options);
if (!result || result.html.length <= maxLength)
    process.exit(0);

while (result.html.length > maxLength && options.uncles > 0) {
    options.uncles -= 1;
    result = sliceHtml(options)!;
}
options.uncles += 1;
result = sliceHtml(options)!;

while (result.html.length > maxLength && options.nephews > 0) {
    options.nephews -= 1;
    result = sliceHtml(options)!;
}

while (result.html.length > maxLength && options.siblings > 2) {
    options.siblings -= 1;
    result = sliceHtml(options)!;
}

/*
const options = {
    selector: "[__target__]",
    parents: <PERSON>,
    children: <PERSON>,
    siblings: 6,
    uncles: 6,
    nephews: 6,
    anchor: 3
};

while (options.siblings > -1) {
    const result = sliceHtml(options);
    console.log(options.uncles, result?.html.length);
    options.siblings -= 1;
}

uncles
6 13001
5 10614
4 8805
3 1359
2 1359
1 1174
0 1174

nephews
6 13001
6 11102
6 9844
6 8136
6 7053
6 4479
6 2252
*/
