/**
 * Finds the image element most closely related to the element with a given label and returns
 * an updated label if found.
 * 
 * The search proceeds in the following order:
 * 1. If the labeled element is itself an <img>, return its classes.
 * 2. Otherwise, search for all <img> elements within the labeled element’s children.
 * 3. If none are found, search the labeled element’s siblings.
 * 4. If still none are found, move up one level and search the parent’s siblings.
 * The “largest” image (based on client area) is returned at each step.
 *
 * @param {number} label - The initial target label number.
 * @param {number} up - Number of parents to climb up the tree.
 * @returns {number} Returns either the adjusted label number found or the original label number.
 */
export function findClosestImageByLabel({ label, up = 2, minWidth = 1, minHeight = 1 }: { label: number, up?: number, minWidth?: number, minHeight?: number }): number {
    let element = document.querySelector(`[__label__='${label}']`);
    if (!element)
        return label;

    if (element instanceof HTMLImageElement)
        return Number(element.getAttribute("__label__")) || label;

    const images = Array.from(element.getElementsByTagName("img"));
    if (images.length > 0) {
        const image = findLargestElement(images);
        if (image)
            return Number(image.getAttribute("__label__")) || label;
    }

    while (up-- >= 0) {
        const siblings = collectSiblings(element);
        const sibling_images = collectImages(siblings);
        const image = findLargestElement(sibling_images);
        if (image)
            return Number(image.getAttribute("__label__")) || label;
        else if (element.parentElement)
            element = element.parentElement;
        else
            break;
    }

    return label;

    function collectImages(elements: Element[]): HTMLImageElement[] {
        const images = [];
        for (const element of elements)
            images.push(...element.getElementsByTagName("img"));
        return images;
    }

    function collectSiblings(element: Element): Element[] {
        let siblings = [];
        let right = element.nextElementSibling;
        let left = element.previousElementSibling;
        while (right || left) {
            if (right) {
                siblings.push(right);
                right = right.nextElementSibling;
            }
            if (left) {
                siblings.push(left);
                left = left.previousElementSibling;
            }
        }
        return siblings;
    }

    function findLargestElement(elements: Element[]): Element | undefined {
        const [largest] = elements
            .filter(element => element.clientWidth >= minWidth && element.clientHeight >= minHeight) // minimum image width x height to qualify
            .map(element => ({ element, size: element.clientWidth * element.clientHeight }))
            .sort((a, b) => b.size - a.size); // sort hi-to-low
        return largest?.element;
    }
}