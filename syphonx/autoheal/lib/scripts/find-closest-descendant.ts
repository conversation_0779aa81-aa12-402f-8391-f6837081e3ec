interface Hit {
    element: Element;
    depth: number;
}

export function findClosestDescendantContains(element: Element, text: string, threshold: number): Element[] {
    while (!element.textContent?.includes(text) && element.parentElement)
        element = element.parentElement;

    if (!element)
        return [];

    const hits: Hit[] = [];
    findDescendants(element, text, 0);

    // Helper function to recursively find descendants containing the text
    function findDescendants(element: Element, text: string, depth: number): void {
        depth += 1;
        for (const child of element.children) {
            if (threshold < 1) {
                const score = matchText(child.textContent!, text);
                if (score >= threshold) {
                    hits.push({ element: child, depth });
                    findDescendants(child, text, depth);
                }
            }
            else {
                if (child.textContent?.includes(text)) {
                    hits.push({ element: child, depth });
                    findDescendants(child, text, depth);
                }
            }
        }
    }

    function matchText(source: string, target: string): number {
        source = collapseWhitespace(source);
        target = collapseWhitespace(target);
        const max = maxCommonSubstringLength(source, target);
        return max / target.length;
    }

    function collapseWhitespace(text: string): string {
        return text
            .replace(/\r\n/g, "\n") // Reformat windows crlf to just a newline
            .replace(/\n(?:[\t ]*\n)+/g, "\n\n") // Collapse lines that only contain whitespace between newlines
            .replace(/^\s+|\s+$/g, '') // Trim leading and trailing whitespace
            .replace(/\n{3,}/g, "\n\n") // Collapse 3+ newlines to two newlines
            .trim();
    }

    function maxCommonSubstringLength(str1: string, str2: string): number {
        if (!str1 || !str2)
            return 0;

        const len1 = str1.length;
        const len2 = str2.length;
    
        // Initialize a 2D array to store lengths of longest common suffixes
        const dp: number[][] = Array.from({ length: len1 + 1 }, () => Array(len2 + 1).fill(0));
        let maxLength = 0;
    
        // Build the dp array
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                if (str1[i - 1] === str2[j - 1]) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                    maxLength = Math.max(maxLength, dp[i][j]);
                }
            }
        }
    
        return maxLength;
    }

    // Sort hits by depth in descending order
    hits.sort((a, b) => b.depth - a.depth);
    
    // Filter out nodes that are not leaf nodes
    const elements = hits.map(hit => hit.element);
    const result = elements.filter(element =>  
        !elements.some(other => other.parentElement === element)); // ...a leaf node in this context does not have any children also in hits

    return result;
}
