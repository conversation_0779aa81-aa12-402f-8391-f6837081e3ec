import * as fs from "fs";
import * as playwright from "playwright";
import { JSD<PERSON> } from "jsdom";

export function jsdom(html: string): JSDOM {
    html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script\s*>/gi, "");
    html = html.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style\s*>/gi, "");
    const jsdom = new JSDOM(html);
    const { window } = jsdom as unknown as Window & typeof globalThis;
    global.window = window;
    global.document = window.document;
    return jsdom;
}

export function jsdomFromFile(file: string | URL): void {
    const html = fs.readFileSync(file, "utf-8");
    jsdom(html);
}

export async function jsdomFromUrl(url: string): Promise<void> {
    const browser = await playwright.chromium.launch({ headless: true });
    const page = await browser.newPage();
    await page.goto(url);
    const html = await page.content();
    jsdom(html);
}
