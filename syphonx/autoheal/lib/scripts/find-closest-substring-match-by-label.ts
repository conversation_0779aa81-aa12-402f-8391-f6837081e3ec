interface SearchConfig {
    label: number;    // the label of the starting (target) element
    text: string;     // the text to match
    degrees?: number; // maximum degrees of separation (default: 20)
}

interface Candidate {
    label: number;
    score: number;
    degree: number;
}

interface NodeWithDegree {
    element: Element;
    degree: number;
}

export function findClosestSubstringMatchByLabel({
    label,
    text,
    degrees = 10,
}: SearchConfig): number {
    // Find the target element by its __label__ attribute.
    const targetElement = document.querySelector(`[__label__="${label}"]`);
    if (!targetElement)
        return label;

    const queue: NodeWithDegree[] = [{ element: targetElement, degree: 0 }];
    const visited = new Set<Element>([targetElement]);

    // Our best candidate so far.
    let bestCandidate: Candidate | undefined = undefined;

    // Helper to update the best candidate based on the element's text score.
    function updateCandidate(el: Element, currentDegree: number) {
        // Only consider elements that have a __label__ attribute.
        const labelStr = el.getAttribute("__label__");
        if (!labelStr) return;
        const candidateLabel = Number(labelStr);
        const elText = (el.textContent || "").trim();
        // Compute a score (0 to 1) based on the maximal common substring match.
        const score = subTextMatch(elText, text);
        // If there’s a tie, prefer the one that is closer in the DOM (lower degree).
        if (
            !bestCandidate ||
            score > bestCandidate.score ||
            (score === bestCandidate.score && currentDegree < bestCandidate.degree)
        ) {
            bestCandidate = { label: candidateLabel, score, degree: currentDegree };
        }
    }

    // Begin the BFS.
    while (queue.length > 0) {
        const { element, degree } = queue.shift()!;
        if (degree > degrees) continue;

        if (element.hasAttribute("__label__")) {
            updateCandidate(element, degree);
        }

        if (degree === degrees) continue;

        // Get the neighbors (parent, siblings, and children).
        const neighbors = getNeighborsSorted(element, targetElement);
        for (const neighbor of neighbors) {
            if (!visited.has(neighbor)) {
                visited.add(neighbor);
                queue.push({ element: neighbor, degree: degree + 1 });
            }
        }
    }

    return bestCandidate ? (bestCandidate as Candidate).label : label;

    // Helper: Returns neighbor elements sorted relative to the starting element.
    function getNeighborsSorted(
        element: Element,
        startingElement: Element
    ): Element[] {
        const neighbors = new Set<Element>();

        const parent = element.parentElement;
        if (parent) {
            neighbors.add(parent);
            for (const child of Array.from(parent.children)) {
                if (child !== element) {
                    neighbors.add(child);
                }
            }
        }
        for (const child of Array.from(element.children)) {
            neighbors.add(child);
        }

        // Sort based on document order relative to startingElement.
        return Array.from(neighbors).sort((a, b) => {
            const aPos = startingElement.compareDocumentPosition(a);
            const bPos = startingElement.compareDocumentPosition(b);
            const aBefore = !!(aPos & Node.DOCUMENT_POSITION_PRECEDING);
            const bBefore = !!(bPos & Node.DOCUMENT_POSITION_PRECEDING);
            if (aBefore && !bBefore) return -1;
            if (!aBefore && bBefore) return 1;
            return 0;
        });
    }

    // ------------------------------
    // Matching functions using maximal common substring.
    // ------------------------------

    /**
     * Computes the length of the longest common substring between two strings.
     */
    function maxCommonSubstringLength(str1: string, str2: string): number {
        if (typeof str1 !== "string" || typeof str2 !== "string")
            return 0;

        const len1 = str1.length;
        const len2 = str2.length;
        // Create a 2D array to store lengths of longest common suffixes.
        const dp: number[][] = Array.from({ length: len1 + 1 }, () =>
            Array(len2 + 1).fill(0)
        );
        let maxLength = 0;

        // Build the dp array.
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                if (str1[i - 1] === str2[j - 1]) {
                    if (!Array.isArray(dp[i - 1]))
                        continue; // prevent edge case error
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                    maxLength = Math.max(maxLength, dp[i][j]);
                }
            }
        }

        return maxLength;
    }

    /**
     * A helper to normalize text. (Adjust this as needed for your use case.)
     */
    function formatText(s: string): string {
        return s.trim().toLowerCase();
    }

    /**
     * Computes the match score between the source and target texts.
     * The score is the maximal common substring length normalized by the target length.
     */
    function subTextMatch(source: string, target: string): number {
        source = formatText(source);
        target = formatText(target);
        const max = maxCommonSubstringLength(source, target);
        return max / target.length;
    }
}
