interface SearchConfig {
    label: number;   // the label of the starting (target) element
    text: string;    // the text to match
    degrees?: number; // maximum degrees of separation (default: 3)
}

interface Candidate {
    label: number;
    distance: number;
    degree: number;
}

interface NodeWithDegree {
    element: Element;
    degree: number;
}

export function findClosestTextMatchByLabel({ label, text, degrees = 10 }: SearchConfig): number {
    // Find the target element by its __label__ attribute.
    const targetElement = document.querySelector(`[__label__="${label}"]`);
    if (!targetElement)
        return label;

    const queue: NodeWithDegree[] = [{ element: targetElement, degree: 0 }];
    const visited = new Set<Element>([targetElement]);

    // Our best candidate so far.
    let bestCandidate: Candidate | undefined = undefined;

    // Helper to update the best candidate based on the element's text distance.
    function updateCandidate(el: Element, currentDegree: number) {
        // Only consider elements that have a __label__ attribute.
        const labelStr = el.getAttribute("__label__");
        if (!labelStr) return;
        const candidateLabel = Number(labelStr);
        const elText = (el.textContent || "").trim();
        const distance = levenshtein(elText, text);
        // Use the distance; if there’s a tie, prefer the one that is closer in the DOM.
        if (
            !bestCandidate ||
            distance < bestCandidate.distance ||
            (distance === bestCandidate.distance && currentDegree < bestCandidate.degree)
        ) {
            bestCandidate = { label: candidateLabel, distance, degree: currentDegree };
        }
    }

    // Begin the BFS.
    while (queue.length > 0) {
        const { element, degree } = queue.shift()!;
        if (degree > degrees) continue;

        // If this element has a __label__, evaluate it as a candidate.
        if (element.hasAttribute("__label__")) {
            updateCandidate(element, degree);
        }

        // Do not add neighbors if we are already at the max degree.
        if (degree === degrees) continue;

        // Get the neighbors (parent, siblings, and children).
        const neighbors = getNeighborsSorted(element, targetElement);
        for (const neighbor of neighbors) {
            if (!visited.has(neighbor)) {
                visited.add(neighbor);
                queue.push({ element: neighbor, degree: degree + 1 });
            }
        }
    }

    return bestCandidate ? (bestCandidate as Candidate).label : label;


    // Helper: Returns neighbor elements sorted relative to the starting element.
    function getNeighborsSorted(element: Element, startingElement: Element): Element[] {
        const neighbors = new Set<Element>();

        const parent = element.parentElement;
        if (parent) {
            neighbors.add(parent);
            for (const child of Array.from(parent.children)) {
                if (child !== element) {
                    neighbors.add(child);
                }
            }
        }
        for (const child of Array.from(element.children)) {
            neighbors.add(child);
        }

        // Convert to array and sort based on document order relative to startingElement.
        return Array.from(neighbors).sort((a, b) => {
            // We want elements that come before startingElement to come first.
            const aPos = startingElement.compareDocumentPosition(a);
            const bPos = startingElement.compareDocumentPosition(b);

            const aBefore = !!(aPos & Node.DOCUMENT_POSITION_PRECEDING);
            const bBefore = !!(bPos & Node.DOCUMENT_POSITION_PRECEDING);

            if (aBefore && !bBefore) return -1;
            if (!aBefore && bBefore) return 1;
            return 0;
        });
    }

    /**
     * Computes the Levenshtein edit distance between two strings.
     */
    function levenshtein(a: string, b: string): number {
        const m = a.length;
        const n = b.length;

        // If one string is empty, the distance is the length of the other.
        if (m === 0) return n;
        if (n === 0) return m;

        // Create a 2D array.
        const dp: number[][] = [];
        for (let i = 0; i <= m; i++) {
            dp[i] = [i];
        }
        for (let j = 0; j <= n; j++) {
            dp[0][j] = j;
        }

        // Fill in the rest of the matrix.
        for (let i = 1; i <= m; i++) {
            for (let j = 1; j <= n; j++) {
                const cost = a[i - 1] === b[j - 1] ? 0 : 1;
                dp[i][j] = Math.min(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                );
            }
        }
        return dp[m][n];
    }
}
