export function findClosestHtmlSubstringMatch({ label, text, up = 3 }: { label: number, text: string, up?: number }): number {
    let element = document.querySelector(`[__label__="${label}"]`);
    while (--up >= 0) {
        if (!element)
            return label;
        const n = Number(element.getAttribute("__label__"));
        if (element.outerHTML.includes(text) && n > 0)
            return n;
        element = element?.parentElement;
    }
    return label;
}
