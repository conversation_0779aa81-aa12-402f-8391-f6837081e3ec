// @ts-nocheck

/**
 * Add a label number to every visible elements on the page.
 *
 * @description
 * Adds a __label__ attribute to every element in the body, assigning a unique sequence number to each element (1, 2, 3, etc.).
 * Also adds a CSS rule to display the sequence number for each visible element after the corresponding content.
 * Running a second time will incrementally add labels to unlabelled elements, continuing the number sequence from the last assigned number.
 *
 * @param {string} css Optional CSS style to apply to the label.
 * @returns {number} The total count of nodes that were labeled during the traversal.
 */
export function addLabels(css = "font-weight: normal; text-decoration: none; color: black;") {
    let n = lastLabelNumber();
    traverseNodes(document.body);
    addStyleSheet();
    return n;

    function addStyleSheet() {
        if (!document.getElementById('__label__')) {
            const style = document.createElement('style');
            document.head.appendChild(style);
            style.id = '__label__';
            setTimeout(() => {
                style.sheet.insertRule(`[__label__][__show__]:not(img):not(input[type="button"])::after { content: '[' attr(__label__) ']'; margin-left: 4px; ${css} }`);
                style.sheet.insertRule(`img[__label__][__show__] { position: relative; display: inline-block; outline: 1px dotted gray; outline-offset: -2px; }`);
                style.sheet.insertRule(`.img-label { position: absolute; top: 2px; left: 2px; width: auto !important; height: auto !important; z-index: 1; white-space: nowrap; font-size: 12px; color: black; background-color: rgba(255, 255, 255, 0.7); ${css} }`);
            }, 0);
        }
    }

    function isVisible(node) {
        if (node.nodeType === Node.ELEMENT_NODE) {
            const style = getComputedStyle(node);
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
        }
        return true; // assume text nodes are visible
    }

    function lastLabelNumber() {
        const labels = Array.from(document.querySelectorAll('[__label__]'));
        return labels.length > 0 ? Math.max(...labels.map(element => parseInt(element.getAttribute('__label__')))) : 0;
    }

    function show(node) {
        const tagName = node.tagName.toUpperCase();
        const isFormElement = ['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA'].includes(tagName);
        const hasDirectTextContent = Array.from(node.childNodes).some(child => child.nodeType === Node.TEXT_NODE && child.nodeValue.trim().length > 0);            
        const isContainerOnly = Array.from(node.children).length > 0 && !hasDirectTextContent;
        const hasValue = node instanceof HTMLInputElement && node.value !== '';
        const hasPlaceholder = node instanceof HTMLInputElement && node.placeholder !== '';
        const isWrapper = node.className === '__wrapper__';

        const isButton = (
            node.tagName === 'BUTTON' || 
            (node.tagName === 'INPUT' && node.type === 'button') ||
            node.getAttribute('role') === 'button'
        );

        const isLeaf = (
            ['IMG', 'SVG', 'IFRAME'].includes(tagName) ||
            isFormElement ||
            hasDirectTextContent ||
            hasValue ||
            hasPlaceholder ||
            isButton
        );
        
        return isVisible(node)
            && !isWrapper
            && !isContainerOnly
            && isLeaf;
    }

    function traverseNodes(node) {
        if (node.nodeType === Node.ELEMENT_NODE) {
            node.setAttribute('__label__', ++n);
            if (show(node))
                node.setAttribute('__show__', '');
            if (node.tagName === 'IMG') {
                const span = document.createElement('span');
                span.className = 'img-label';
                span.textContent = `[${n}]`;
                node.parentNode.insertBefore(span, node.nextSibling);
            }
            else if (node.tagName === 'INPUT' && node.type === 'button') {
                node.value = `${node.value} [${n}]`; // add label decoration to input buttons
            }
        }
        node.childNodes.forEach(traverseNodes);
    }
}

/**
 * Removes all labels from the page.
 */
export function removeLabels() {
    document.getElementById('__label__')?.remove(); // remove style element with id of __label__
    for (const element of document.getElementsByClassName('img-label'))
        element.remove(); // remove img-label spans
    for (const element of document.querySelectorAll(`input[type='button'][__label__]`) as HTMLInputElement[])
        element.value = element.value.replace(/( \[\d+\])/, ''); // remove label within input[type=button] value
    for (const element of document.querySelectorAll('[__label__]')) {
        element.removeAttribute('__label__'); // remove __label__ attributes
        element.removeAttribute('__show__'); // remove __show__ attributes
    }
}
