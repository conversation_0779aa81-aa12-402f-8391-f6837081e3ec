export interface SliceHtmlOptions {
    /**
     * A CSS selector that targets the element for the HTML slice.
     */
    selector: string;
    /**
     * The number of parent elements to include in the slice. (0=no parents, 1=parents, 2=grandparents, etc.)
     */
    parents?: number;
    /**
     * Depth of child elements to include in the slice. (0=no children, 1=children, 2=great grandchildren, etc.)
     */
    children?: number;
    /**
     * The number of direct sibling elements to include in the slice.
     */
    siblings?: number;
    /**
     * The number of uncle levels to include in the slice. (0=no uncles, 1=uncles, 2=great uncles, etc.)
     * 
     * NOTE: The number of parents must be greater than or equal to the number of uncles.
     */
    uncles?: number;
    /**
     * The number of nephew levels to include in the slice. (0=no nephews, 1=nephews, 2=grand nephews, etc.)
     * 
     * NOTE: The number of siblings must be greater than or equal to the number of nephews.
     */
    nephews?: number;
    /**
     * Max string size before truncation.
     */
    truncate?: number;
    /**
     * Determines the parent level at which to drop an anchor in the slice.
     * 
     * NOTE: The number of parents must be greater than or equal to the anchor level otherwise no anchors will be produced.
     */
    anchor?: number;
}

export interface SliceHtmlResult {
    /*
     * The HTML slice.
     */
    html: string;

    /*
     * The line numbers of the selected target elements.
     */
    linenums: number[];

    targets: string[];
    anchors: string[];
}

/**
 * Slices the HTML document to include the specified element and its ancestors and descendants.
 */
export function sliceHtml({
    selector,
    parents = 6,
    children = 3,
    siblings = 4,
    uncles = 0,
    nephews = 0,
    anchor = 1,
    truncate = 100
}: SliceHtmlOptions): SliceHtmlResult | undefined {
    const lines: string[] = [];
    const linenums: number[] = [];
    const targets: string[] = [];
    const anchors = new Set<string>();
    const elements = mark();

    render(document.documentElement);
    unmark();

    if (lines.length > 0) {
        const html = lines.join("\n");
        return { html, linenums, targets, anchors: Array.from(anchors).sort() };
    }

    function mark(): Element[] {
        const elements = Array.from(document.querySelectorAll(selector));
        for (const element of elements) {
            element.setAttribute("marked", "");
            if (anchor === 0)
                anchors.add(pathToElement(element));
            if (siblings > 0)
                traverseLateral(element, siblings);
            if (parents > 0)
                traverseUp(element, parents - 1, anchor);
            if (children > 0)
                traverseDown(element, children - 1);
        }
        return elements;
    }

    function unmark(): void {
        document.querySelectorAll("[marked]").forEach(element => element.removeAttribute("marked"));
    }

    function traverseUp(element: Element, level = Infinity, anchor = 0): void {
        if (element.parentElement) {
            element.parentElement.setAttribute("marked", "");
            if (--anchor === 0)
                anchors.add(pathToElement(element.parentElement));
            if (--uncles >= 0)
                traverseLateral(element.parentElement);
            if (--level >= 0)
                traverseUp(element.parentElement, level, anchor);
        }
    }
    
    function traverseDown(element: Element, level = Infinity): void {
        for (const child of element.children) {
            child.setAttribute("marked", "");
            if (--level >= 0)
                traverseDown(child, level);
        }
    }

    function traverseLateral(element: Element, limit = Infinity) {
        if (element.parentElement) {
            const children = Array.from(element.parentElement.children);
            let i = children.indexOf(element);
            let j = i;
            let n = 0;
            while (n < limit && (i > 0 || j < children.length - 1)) {
                if (i > 0) {
                    const lateral = children[--i];
                    lateral.setAttribute("marked", "");
                    n += 1;
                    if (nephews > 0)
                        traverseDown(lateral, nephews - 1);
                }
                if (j < children.length - 1 && n < limit) {
                    const lateral = children[++j];
                    lateral.setAttribute("marked", "");
                    n += 1;
                    if (nephews > 0)
                        traverseDown(lateral, nephews - 1);
                }
            }
        }
    }

    function render(element: Element, level = 0): void {
        const tag = element.tagName.toLowerCase();
        const exclude = ["noscript", "script", "style", "svg"];
        const container = !["area", "base", "br", "col", "embed", "hr", "img", "input", "link", "meta", "param", "source", "track", "wbr"].includes(tag);
        const attributes = renderAttributes(element.attributes);
        const marked = element.hasAttribute("marked");
        //const text = element.textContent ? trunc(element.textContent.trim().replace(/\s+/gm, " "), truncate) : "";

        if (marked && elements.includes(element)) {
            linenums.push(lines.length + 1);
            targets.push(pathToElement(element));
        }

        if (!container && marked) {
            lines.push(`${" ".repeat(level * 2)}<${tag}${attributes}>`);
        }
        else if (element.children.length > 0) {
            if (marked)
                lines.push(`${" ".repeat(level * 2)}<${tag}${attributes}>`);
            const i = lines.length;
            for (const node of element.childNodes) {
                if (node.nodeType === 1 /*Node.ELEMENT_NODE*/) {
                    const child = node as Element;
                    if (!exclude.includes(child.tagName.toLowerCase()))
                        render(child, marked ? level + 1 : level);    
                }
                else if (node.nodeType === 3 /*Node.TEXT_NODE*/ && marked) {
                    const text = node.textContent?.trim().replace(/\s+/gm, " ");
                    if (text)
                        lines.push(`${" ".repeat((level + 1) * 2)}${text}`);
                }
            }
            if (marked) {
                if (lines.length === i)
                    lines[lines.length - 1] += `</${tag}>`;
                else
                    lines.push(`${" ".repeat(level * 2)}</${tag}>`);
            }
        }
        else if (marked) {
            const text = element.textContent ? trunc(element.textContent.trim().replace(/\s+/gm, " "), truncate) : "";
            lines.push(`${" ".repeat(level * 2)}<${tag}${attributes}>${text}</${tag}>`);
        }

        function renderAttributes(attributes: NamedNodeMap): string {
            const text = Array.from(attributes)
                .filter(attr => attr.value && attr.name !== "marked")
                .map(attr => `${attr.name}="${!["id", "class"].includes(attr.name) && !attr.name.startsWith("__") ? trunc(attr.value.replace(/"/g,'\"'), truncate) : attr.value.replace(/"/g,'\"')}"`)
                .join(" ");
            return text ? " " + text : "";
        }

        function trunc(text: string, max: number): string {
            if (text.length <= max)
                return text;
            const m = Math.floor(max / 2);
            const ix = text.slice(0, m).lastIndexOf(" ");
            const i = ix > (0.7 * m) ? Math.min(ix, m) : m;
            const jx = text.slice(-m).indexOf(" " + 1);
            const j = jx > (0.7 * m) ? Math.min(m - jx, m) : m;
            return `${text.slice(0, i).trim()} ... ${text.slice(-j).trim()}`;
        }
    }

    function pathToElement(element: Element | null): string {
        const path = [];
        while (element) {
            const tag = element.tagName?.toLowerCase();
            if (!element.parentElement) {
                path.push(tag);
                break;
            }

            if (tag === "body") {
                path.push(tag);
            }
            else {
                const id = element.id || "";
                const [className] = element.className?.split(" ") || [];
                const a = Array.from(element.parentElement.children);
                const n = a.indexOf(element) + 1;
        
                const uniqueId = /^[A-Za-z0-9_-]+$/.test(id) ? document.querySelectorAll(`#${id}`).length === 1 : false;
                const uniqueClassName = tag && /^[A-Za-z0-9_-]+$/.test(className) ? document.querySelectorAll(`${tag}.${className}`).length === 1 : false;
                const onlyTag = element.parentElement.querySelectorAll(tag).length === 1;
                const onlyClassName = tag && /^[A-Za-z0-9_-]+$/.test(className) ? element.parentElement.querySelectorAll(`${tag}.${className}`).length === 1 : false;
        
                if (uniqueId)
                    path.push(`#${id}`);
                else if (uniqueClassName)
                    path.push(`${tag}.${className}`);
                else if (onlyTag)
                    path.push(tag);
                else if (onlyClassName)
                    path.push(`${tag}.${className}`);
                else
                    path.push(`${tag}:nth-child(${n})`);
                    //path.push(n === 1 ? `${tag}:first-child` : n === a.length ? `${tag}:last-child` : `${tag}:nth-child(${n})`);

                if (uniqueId || uniqueClassName)
                    break;
            }

            element = element.parentElement;
        }

        return path.reverse().join(" > ");
    }
}
