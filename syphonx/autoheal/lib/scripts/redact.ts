export function redact() {
    // redact elements containing text
    const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, null);
    let node;
    while (node = walker.nextNode()) {
        if (node.nodeValue?.trim() && node.parentNode) {
            const span = document.createElement('span');
            span.style.setProperty('color', '#000', 'important');
            span.style.setProperty('background-color', '#000', 'important');
            node.parentNode.insertBefore(span, node);
            span.appendChild(node);
        }
    }

    // redact images and iframes
    document.querySelectorAll('img, iframe, svg, button, input[type=button]').forEach(element => {
        const style = window.getComputedStyle(element);
        if (style.display !== 'none') {
            const div = document.createElement('div');
            div.style.display = style.display !== 'inline' ? style.display : 'inline-block';
            div.style.width = style.width;
            div.style.height = style.height;
            div.style.backgroundColor = '#000';
            div.style.borderColor = '#000';
            div.style.margin = style.margin;
            div.style.padding = style.padding;
            div.style.border = style.border;
            div.className = `redact-${element.tagName}`;
            element.parentNode?.replaceChild(div, element);
        }
    });

    // redact placeholder text within input and textarea elements
    document.querySelectorAll('input, textarea').forEach(element => element.setAttribute('placeholder', ''));

    // redact elements with a background-image style
    document.querySelectorAll('*').forEach(element => {
        const style = window.getComputedStyle(element);
        if (style.backgroundImage.includes('url(') && element instanceof HTMLElement) {
            element.style.setProperty('background-image', 'none', 'important');
            element.style.setProperty('background-color', '#000', 'important');
        }
    });

    // remove vertical scrollbars
    document.documentElement.style.overflow = 'hidden';
    document.body.style.overflow = 'hidden';
}
