export function reverseFindSinglePathToElement(selector: string): string {
    const path = [];
    let element = document.querySelector(selector);
    while (element) {
        const parent = element.parentElement;
        const tag = element.tagName.toLowerCase();
        const id = element.id || "";
        const className = element.className.split(" ")[0] || "";
        
        // If no parent, use element itself as starting point
        if (!parent) {
            if (id && /^[A-Za-z0-9_-]+$/.test(id))
                path.push(`#${id}`);
            else if (tag && className && /^[A-Za-z0-9_-]+$/.test(className))
                path.push(`${tag}.${className}`);
            else
                path.push(tag);
            break;
        }
    
        const children = Array.from(parent.children);
        const n = children.indexOf(element) + 1;
    
        const uniqueId = /^[A-Za-z0-9_-]+$/.test(id) ? 
            document.querySelectorAll(`#${id}`).length === 1 : 
            false;
        const uniqueClassName = tag && /^[A-Za-z0-9_-]+$/.test(className) ? 
            document.querySelectorAll(`${tag}.${className}`).length === 1 : 
            false;
        const onlyTag = parent.getElementsByTagName(tag).length === 1;
        const onlyClassName = tag && /^[A-Za-z0-9_-]+$/.test(className) ? 
            parent.querySelectorAll(`${tag}.${className}`).length === 1 : 
            false;
    
        if (uniqueId)
            path.push(`#${id}`);
        else if (uniqueClassName)
            path.push(`${tag}.${className}`);
        else if (onlyTag)
            path.push(tag);
        else if (onlyClassName)
            path.push(`${tag}.${className}`);
        else
            path.push(`${tag}:nth-child(${n})`);
    
        if (uniqueId || uniqueClassName)
            break;

        element = element.parentElement;
    }

    return path.reverse().join(" > ");
}