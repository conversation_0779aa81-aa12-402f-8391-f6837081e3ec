export function reverseFindFullPathToElement(selector: string): string {
    // Find the target element using the provided selector
    let element = document.querySelector(selector);
    if (!element)
        return '';

    // Traverse up the DOM tree until we reach the html element
    const path = [];
    while (element && element.nodeName.toLowerCase() !== 'html') {
        const tagName = element.nodeName.toLowerCase();
        let segment = tagName;

        // Check if we need nth-child
        if (element.parentElement) {
            const siblings = Array.from(element.parentElement.children);
            const i = siblings.indexOf(element); // 0-based index

            // Only add nth-child if there are multiple siblings
            if (siblings.length > 1)
                segment += `:nth-child(${i + 1})`; // CSS is 1-based
        }

        path.unshift(segment);
        element = element.parentElement;
    }

    // Add 'html' at the beginning if we found it
    if (element?.nodeName.toLowerCase() === 'html')
        path.unshift('html');

    // Join all parts with ' > '
    return path.join(' > ');
}