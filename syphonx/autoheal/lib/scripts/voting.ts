$(document).ready(function() {
    const user = Cookies.get("user");
    if (user)
        $("#user").val(user);

    $("#user").on("keydown", function (event) {
        if (event.which === 13) {
            event.preventDefault(); // Prevent form submission if necessary
            Cookies.set("user", $(this).val() as string, { expires: 1000, path: "/" });
        }
    });

    $("#user").on("blur", function () {
        Cookies.set("user", $(this).val() as string, { expires: 1000, path: "/" })
    });
});

$("a[data-params]").on("click", function (event) {
    event.preventDefault();
    const user = Cookies.get("user");
    if (!user) {
        $("#user").focus();
        return;
    }

    const $comment = $(this).closest(":has(input[type=text])").find("input[type=text]");
    const comment = ($comment.val() as string)?.trim();
    const params = $(this).data("params");
    const url = `https://us-central1-ps-bigdata.cloudfunctions.net/omega/${params}&href=${encodeURIComponent(window.location.href)}&user=${encodeURIComponent(user)}${comment ? `&comment=${encodeURIComponent(comment)}` : ""}`;

    $.get(url);

    const icon = $(this).find("i");
    if (icon.hasClass("fa-thumbs-o-up"))
        icon.removeClass("fa-thumbs-o-up").addClass("fa-thumbs-up");
    else if (icon.hasClass("fa-thumbs-o-down"))
        icon.removeClass("fa-thumbs-o-down").addClass("fa-thumbs-down");

    if (comment)
        $comment.prop("readonly", true);
    else
        $comment.hide();
});