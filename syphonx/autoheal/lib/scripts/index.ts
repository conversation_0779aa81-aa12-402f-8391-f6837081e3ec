import { jsdom } from "./jsdom.js";
export { jsdom } from "./jsdom.js";

import { collapseWhitespace } from "../utilities.js";

import {
    sliceHtml as sliceHtmlScript,
    SliceHtmlOptions,
    SliceHtmlResult
}
from "./slice.js";

export {
    SliceHtmlOptions,
    SliceHtmlResult
}
from "./slice.js";

export function sliceHtml(html: string, options: SliceHtmlOptions): SliceHtmlResult | undefined {
    jsdom(html);
    return sliceHtmlScript(options);
}

export function rebaseHtml(html: string, href: string): string {
    removeBaseHtml(html);
    const i = html.indexOf("<head>") + 6;
    const left = html.slice(0, i);
    const mid = `\n<base href="${href}">\n`;
    const right = html.slice(i);
    return `${left}${mid}${right}`;
}

export function removeBaseHtml(html: string): string {
    return html.replace(/\s*<base [^>]+>/, "");
}

/**
 * Sanitizes an HTML string by removing unnesessary tags including `<script>`, `<style>`, and `<svg>` tags,
 * as well as HTML comments. It also collapses extraneous whitespace within the HTML.
 *
 * @param {string} html - The HTML string to be sanitized.
 * @returns {string} The sanitized HTML string with specific tags and comments removed, and with whitespace collapsed.
 */
export function sanitizeHtml(html: string, removeLabels = false): string {
    html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script\s*>/gi, ""); // remove script tags
    html = html.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style\s*>/gi, ""); // remove style tags
    html = html.replace(/<svg\b[^<]*(?:(?!<\/svg>)<[^<]*)*<\/svg\s*>/gi, ""); // remove svg tags
    html = html.replace(/<!--[\s\S]*?-->/g, ""); // remove HTML comments
    html = html.replace(/([a-z-]+)\s*=\s*["']data:[^"']+["']/gi, `$1="data:,"`); // remove datauri references in img tags or other elements
    if (removeLabels)
        html = html.replace(/\s+__label__="\d+"/g, "").replace(/\s+__show__/g, "");
    html = collapseWhitespace(html);
    return html;
}
