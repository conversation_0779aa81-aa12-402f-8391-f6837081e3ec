export interface FindContainedElementsResult {
    contained: string[];
    uncontained: string[];
}

/**
 * Query the set of elements identified by the given CSS selector and return those elements
 * that are contained within the specified root and those that are not.
 * @param selector A CSS selector identifying the elements to be evaluated.
 * @param root_selector A CSS selector identifying the root of the container.
 * @returns A list of CSS selectors identifying the elements that are contained and uncontained.
 */
export function findContainedElements(selector: string, root_selector: string): FindContainedElementsResult {
    const result: FindContainedElementsResult = {
        contained: [],
        uncontained: []
    };
    const root = document.querySelector(root_selector);
    if (root) {
        const elements = document.querySelectorAll(selector);
        for (const element of elements) {
            const path = pathToElement(element);
            if (root.contains(element))
                result.contained.push(path);
            else
                result.uncontained.push(path);
        }
    
    }
    return result;

    function pathToElement(element: Element | null): string {
        const path = [];
        while (element) {
            const tag = element.tagName?.toLowerCase();
            if (!element.parentElement) {
                path.push(tag);
                break;
            }

            if (tag === "body") {
                path.push(tag);
            }
            else {
                const id = element.id || "";
                const [className] = element.className?.split(" ") || [];
                const a = Array.from(element.parentElement.children);
                const n = a.indexOf(element) + 1;
        
                const uniqueId = /^[A-Za-z0-9_-]+$/.test(id) ? document.querySelectorAll(`#${id}`).length === 1 : false;
                const uniqueClassName = tag && /^[A-Za-z0-9_-]+$/.test(className) ? document.querySelectorAll(`${tag}.${className}`).length === 1 : false;
                const onlyTag = element.parentElement.querySelectorAll(tag).length === 1;
                const onlyClassName = tag && /^[A-Za-z0-9_-]+$/.test(className) ? element.parentElement.querySelectorAll(`${tag}.${className}`).length === 1 : false;
        
                if (uniqueId)
                    path.push(`#${id}`);
                else if (uniqueClassName)
                    path.push(`${tag}.${className}`);
                else if (onlyTag)
                    path.push(tag);
                else if (onlyClassName)
                    path.push(`${tag}.${className}`);
                else
                    path.push(`${tag}:nth-child(${n})`);
                    //path.push(n === 1 ? `${tag}:first-child` : n === a.length ? `${tag}:last-child` : `${tag}:nth-child(${n})`);

                if (uniqueId || uniqueClassName)
                    break;
            }

            element = element.parentElement;
        }

        return path.reverse().join(" > ");
    }
}
