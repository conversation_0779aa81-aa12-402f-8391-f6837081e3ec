export interface ReverseFindSelectorCandidateOptions {
    /**
     * A selector that targets the single element for which to find selectors.
     */
    selector: string;

    /**
     * Hard limit on the total number of selector candidates to return.
     */
    limit?: number;

    /**
     * The maximum number of consecutive nth-of-type or first-of-type selectors allowed in a selector. (default=3)
     */
    nthOfTypeRunLimit?: number;

    /**
     * The maximum number of classes to consider for generating dual class selectors
     */
    dualClassLimit?: number;
}

/**
 * Finds an array of selectors that uniquely identify the element targeted by the specified className.
 * @param options The options for the query tracking.
 * @returns An array of selectors that uniquely identify the target element.
 */
export function reverseFindSelectorCandidates({ selector, limit = 100, dualClassLimit = 3, nthOfTypeRunLimit = 3 }: ReverseFindSelectorCandidateOptions): string[] {
    const result: string[] = [];
    const targetElement = document.querySelector(selector);
    if (targetElement) {
        const closed: string[] = [];
        let open: string[] = [];
        let element: Element | null = targetElement;
        let depth = 0;
        const MAX_DEPTH = 20; // Add a maximum depth limit
        
        while (element && element.tagName !== "BODY" && result.length < limit && depth < MAX_DEPTH) {
            depth++;
            const next = new Set<string>();

            const tag = element.tagName.toLowerCase();
            append(tag);

            const id = element.getAttribute("id");
            if (id && /^[A-Za-z]+[\w\-\:\.]*$/.test(id))
                append(`#${id}`);

            const class_attr = element.getAttribute("class");
            if (class_attr && /^[ _a-z0-9-]+$/i.test(class_attr)) {
                const classes = class_attr.split(" ")
                    .filter(name =>
                        /^-?[_a-z]+[_a-z0-9-]+$/i.test(name)
                        && name.length <= 40
                    )
                if (classes) {
                    // Limit the number of classes to process
                    const maxClassesToProcess = 10;
                    classes.slice(0, maxClassesToProcess).forEach(name => append(`.${name}`));

                    const dual_set = new Set<string>();
                    const k = Math.min(classes.length, dualClassLimit);
                    for (let i = 0; i < k; i++)
                        for (let j = i + 1; j < k; j++)
                            if (classes[i] !== classes[j])
                                dual_set.add(classes[i].localeCompare(classes[j]) < 0 ? `.${classes[i]}.${classes[j]}` : `.${classes[j]}.${classes[i]}`);
                    
                    // Limit dual class combinations
                    const maxDualClasses = 5;
                    Array.from(dual_set).slice(0, maxDualClasses).forEach(target => append(target));
                }
            }

            const attributes = Array.from(element.attributes)
                .filter(attr => 
                    /^[a-z][a-z0-9_-]*$/i.test(attr.name)
                    && attr.value.length <= 40
                    && !["id", "class", "style", "src", "href", "title", "lang"].includes(attr.name)
                )
                .map(attr => ({ name: attr.name, value: attr.value }));
            
            // Limit the number of attributes to process to avoid stack overflow
            const maxAttributesToProcess = 3; // Reduced from 5
            attributes.slice(0, maxAttributesToProcess).forEach(attr => {
                append(`[${attr.name}]`);
                // Only use simple attribute selectors to avoid potential recursion
                if (attr.value && attr.value.length < 20) {
                    append(`[${attr.name}='${attr.value.replace(/'/g, "\\'")}']`);
                }
            });

            if (next.size === 0)
                 break;

            // Limit the maximum number of open selectors to prevent combinatorial explosion
            const MAX_OPEN_SELECTORS = 20;
            open = Array.from(next).slice(0, MAX_OPEN_SELECTORS);
            element = element.parentElement;

            // Non-recursive append function to prevent stack overflow
            function append(target: string): void {
                // Limit selector complexity to prevent stack overflow
                if (target.length > 100) return;
                
                const paths = open.length > 0 ? open : [""];
                
                // Limit the maximum number of paths to process
                const MAX_PATHS = 20;
                const pathsToProcess = paths.slice(0, MAX_PATHS);
                
                for (const path of pathsToProcess) {
                    const selector = path ? `${target} > ${path}` : target;
                    
                    try {
                        // Add a check to prevent excessive complexity
                        if (selector.split(">").length > 5) continue; // Reduced from 10
                        
                        const elements = Array.from(document.querySelectorAll(selector)) as Element[];
                        
                        if (elements.length === 1 && elements[0].isEqualNode(targetElement)) {
                            closed.push(selector);
                            const i = open.indexOf(path);
                            if (i !== -1) open.splice(i, 1);
                        }
                        else if (element?.parentElement && /^[a-z]+$/.test(target)) {
                            const i = Array.from(element.parentElement.children)
                                .filter(child => child.tagName.toLowerCase() === target)
                                .findIndex(child => child.isEqualNode(element));
                            if (i === 0)
                                next.add(`${target}:first-of-type${path ? ` > ${path}` : ""}`);
                            else if (i > 0 && i < 10) // Limit nth-of-type to avoid deep recursion
                                next.add(`${target}:nth-of-type(${i + 1})${path ? ` > ${path}` : ""}`);
                        }
                        else if (element?.parentElement) {
                            const elements = element.parentElement.querySelectorAll(selector);
                            if (elements.length === 1 && elements[0].isEqualNode(targetElement))
                                next.add(selector);
                        }
                    } catch (error) {
                        // Catch any errors from querySelectorAll to prevent crashes
                        console.error(`Error with selector "${selector}":`, error);
                    }
                }
            }        
        }

        // Attempt to reduce nth-of-type runs with a limit on iterations
        const selectors = Array.from(new Set([...closed, ...open]));
        const MAX_SELECTOR_OPTIMIZATIONS = 50;
        const selectorsToProcess = selectors.length > MAX_SELECTOR_OPTIMIZATIONS ? 
            selectors.slice(0, MAX_SELECTOR_OPTIMIZATIONS) : selectors;
            
        for (let i = selectorsToProcess.length - 1; i >= 0; --i) {
            const selector1 = selectorsToProcess[i];
            if (/ > [a-z]+:(?:first-of-type|nth-of-type\(\d+\)) > /.test(selector1)) {
                try {
                    const elements1 = document.querySelectorAll(selector1);
                    const selector2 = selector1.replace(/( > [a-z]+:(?:first-of-type|nth-of-type\(\d+\)))+ > /g, " ");
                    const elements2 = document.querySelectorAll(selector2);
                    if (elements1.length === elements2.length && elements1[0]?.isEqualNode(elements2[0])) {
                        selectorsToProcess.splice(i, 1, selector2);
                    }
                    else if (selectorsToProcess.length > 1) {
                        const hits = Array.from(selector1.matchAll(/( > [a-z]+:(?:first-of-type|nth-of-type\(\d+\)))/g));
                        if (hits.length - 1 > nthOfTypeRunLimit)
                            selectorsToProcess.splice(i, 1);
                    }
                } catch (error) {
                    // Handle invalid selectors
                    selectorsToProcess.splice(i, 1);
                }
            }
        }

        result.push(
            ...Array.from(new Set(selectorsToProcess))
            .sort((a, b) =>
                (a.match(/:nth-/g) || []).length - (b.match(/:nth-/g) || []).length ||
                (a.match(/>/g) || []).length - (b.match(/>/g) || []).length ||
                a.length - b.length ||
                a.localeCompare(b)
            )
        );
    }

    return result.slice(0, limit);
}