import { expect } from "chai";
import { jsdom } from "./jsdom.js";
import { sliceHtml } from "./slice.js";

describe("slice", () => {
    before(() => jsdom(`
<!doctype html>
<html>
  <body>
    <h1>AAA</h1>
    <article id="a1">
        <ul>
            <li>a1</li>
            <li>a2</li>
            <li>a3</li>
        </ul>
    </article>
    <h1>BBB</h1>
    <article id="a2">
        <p>abc</p>
        <p>def</p>
        <p>ghi</p>
        <p>jkl</p>
        <p>mno</p>
    </article>
    <div id="L0">
        <div id="L1">
            <div id="L2">
                <div id="L3">
                </div>
            </div>
        </div>
    </div>
    <p id="abc">ABC <i>DEF</i> GHI</p>
  </body>
</html>
`));

    it("negative selector has expected value", () => {
        const result = sliceHtml({
            selector: "#xyz"
        });
        expect(result).to.be.undefined;
    });

    it("selector with all zero-order has expected value", () => {
        const result = sliceHtml({
            selector: "#a1",
            parents: 0,
            children: 0,
            siblings: 0,
            uncles: 0,
            nephews: 0
        });
        expect(result?.linenums).eql([1]);
        expect(result?.targets).eql(["#a1"]);
        expect(result?.html).equals(`
<article id="a1"></article>
`.trim());
    });

    it("selector with first-order parents has expected value", () => {
        const result = sliceHtml({
            selector: "#a1",
            parents: 1,
            children: 0,
            siblings: 0,
            uncles: 0,
            nephews: 0
        });
        expect(result).to.be.an("object");
        expect(result?.linenums).eql([2]);
        expect(result?.targets).eql(["#a1"]);
        expect(result?.html).equals(`
<body>
  <article id="a1"></article>
</body>
`.trim());
    });

    it("selector with first-order children has expected value", () => {
        const result = sliceHtml({
            selector: "#a1",
            parents: 0,
            children: 1,
            siblings: 0,
            uncles: 0,
            nephews: 0
        });
        expect(result?.linenums).eql([1]);
        expect(result?.targets).eql(["#a1"]);
        expect(result?.html).equals(`
<article id="a1">
  <ul></ul>
</article>
`.trim());
    });

    it("selector with unlimited parents and children has expected value", () => {
        const result = sliceHtml({
            selector: "#a1",
            parents: Infinity,
            children: Infinity,
            siblings: 0,
            uncles: 0,
            nephews: 0
        });
        expect(result?.linenums).eql([3]);
        expect(result?.targets).eql(["#a1"]);
        expect(result?.html).equals(`
<!doctype html>
<html>
  <body>
    <article id="a1">
      <ul>
        <li>a1</li>
        <li>a2</li>
        <li>a3</li>
      </ul>
    </article>
  </body>
</html>
`.trim());
    });

    it("selector with first-order siblings has expected value", () => {
        const result = sliceHtml({
            selector: "#a1",
            parents: 0,
            children: 0,
            siblings: 1,
            uncles: 0,
            nephews: 0
        });
        expect(result?.linenums).eql([2]);
        expect(result?.targets).eql(["#a1"]);
        expect(result?.html).equals(`
<h1>AAA</h1>
<article id="a1"></article>
`.trim());
    });

    it("selector with second-order siblings has expected value", () => {
        const result = sliceHtml({
            selector: "#a1",
            parents: 0,
            children: 0,
            siblings: 2,
            uncles: 0,
            nephews: 0
        });
        expect(result?.linenums).eql([2]);
        expect(result?.targets).eql(["#a1"]);
        expect(result?.html).equals(`
<h1>AAA</h1>
<article id="a1"></article>
<h1>BBB</h1>
`.trim());
    });

    it("selector with unlimited siblings has expected value", () => {
        const result = sliceHtml({
            selector: "#a2",
            parents: 0,
            children: 0,
            siblings: Infinity,
            uncles: 0,
            nephews: 2
        });
        expect(result?.linenums).eql([10]);
        expect(result?.targets).eql(["#a2"]);
        expect(result?.html).equals(`
<h1>AAA</h1>
<article id="a1">
  <ul>
    <li>a1</li>
    <li>a2</li>
    <li>a3</li>
  </ul>
</article>
<h1>BBB</h1>
<article id="a2"></article>
<div id="L0">
  <div id="L1">
    <div id="L2"></div>
  </div>
</div>
<p id="abc">
  ABC
  <i>DEF</i>
  GHI
</p>
`.trim());
    });

    it("selector targeting first-child with first-order parents has expected value", () => {
        const result = sliceHtml({
            selector: "#a2 p:nth-child(1)",
            parents: 1,
            children: 0,
            siblings: 0,
            uncles: 0,
            nephews: 0
        });
        expect(result?.linenums).eql([2]);
        expect(result?.targets).eql(["#a2 > p:nth-child(1)"]);
        expect(result?.html).equals(`
<article id="a2">
  <p>abc</p>
</article>
`.trim());
    });

    it("selector targeting multiple children with first-order parents has expected value", () => {
        const result = sliceHtml({
            selector: "#a2 > p",
            parents: 1,
            children: 0,
            siblings: 0,
            uncles: 0,
            nephews: 0
        });
        expect(result?.linenums).eql([2, 3, 4, 5, 6]);
        expect(result?.targets).eql([
          "#a2 > p:nth-child(1)",
          "#a2 > p:nth-child(2)",
          "#a2 > p:nth-child(3)",
          "#a2 > p:nth-child(4)",
          "#a2 > p:nth-child(5)"
        ]);
        expect(result?.html).equals(`
<article id="a2">
  <p>abc</p>
  <p>def</p>
  <p>ghi</p>
  <p>jkl</p>
  <p>mno</p>
</article>
`.trim());
    });

    it("selector targeting no children has expected value", () => {
      const result = sliceHtml({
          selector: "#abc",
          parents: 0,
          children: 0,
          siblings: 0,
          uncles: 0,
          nephews: 0
      });
      expect(result?.linenums).eql([1]);
      expect(result?.targets).eql(["#abc"]);
      expect(result?.html).equals(`
<p id="abc">
  ABC
  GHI
</p>
`.trim());
    });

    it("selector targeting first-order children has expected value", () => {
      const result = sliceHtml({
          selector: "#abc",
          parents: 0,
          children: 1,
          siblings: 0,
          uncles: 0,
          nephews: 0
      });
      expect(result?.linenums).eql([1]);
      expect(result?.targets).eql(["#abc"]);
      expect(result?.html).equals(`
<p id="abc">
  ABC
  <i>DEF</i>
  GHI
</p>
`.trim());
    });

    it("first-order anchor has expected value", () => {
      const result = sliceHtml({
          selector: "#L2",
          parents: Infinity,
          children: 1,
          siblings: 0,
          uncles: 0,
          nephews: 0,
          anchor: 1
      });
      expect(result?.linenums).eql([5]);
      expect(result?.anchors).eql(["#L1"]);
      expect(result?.targets).eql(["#L2"]);
      expect(result?.html).equals(`
<!doctype html>
<html>
  <body>
    <div id="L0">
      <div id="L1">
        <div id="L2">
          <div id="L3"></div>
        </div>
      </div>
    </div>
  </body>
</html>
`.trim());
    });

    it("third-order anchor has expected value", () => {
      const result = sliceHtml({
          selector: "#L2",
          parents: Infinity,
          children: 1,
          siblings: 0,
          uncles: 0,
          nephews: 0,
          anchor: 3
      });
      expect(result?.linenums).eql([5]);
      expect(result?.anchors).eql(["html > body"]);
      expect(result?.targets).eql(["#L2"]);
      expect(result?.html).equals(`
<!doctype html>
<html>
  <body>
    <div id="L0">
      <div id="L1">
        <div id="L2">
          <div id="L3"></div>
        </div>
      </div>
    </div>
  </body>
</html>
`.trim());
    });

});
