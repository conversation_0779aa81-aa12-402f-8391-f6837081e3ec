import { expect } from "chai";
import { jsdom } from "./jsdom.js";
import { findClosestHtmlSubstringMatch } from "./find-closest-html-substring-match.js";

describe("findClosestHtmlSubstringMatch", () => {
    before(() => jsdom(`
<!doctype html>
<html>
  <body>
    <ul  __label__="290">
      <li __label__="291">
          <span id="price" __label__="292">
            <span __label__="293">$9<sup>99</sup></span>
            <span class="hidden">$9.99</span>
          </span>
      </li>
    </ul>
  </body>
</html>
`));

    it("positive case has expected value", () => {
        const result = findClosestHtmlSubstringMatch({ label: 293, text: "$9.99" });
        expect(result).equals(292);
    });

    it("negative case has expected value", () => {
        const result = findClosestHtmlSubstringMatch({ label: 293, text: "$0.49" });
        expect(result).equals(293);
    });
});
