/**
 * Render an array of objects to CSV format.
 * @param input An array of objects
 * @returns A string that is a CSV representation of the input.
 */
export function renderCSV(
    input: Array<Record<string, unknown>>,
): string {
    if (input.length === 0)
        return "";
    
    const [first] = input;
    const keys = Object.keys(first);
    const lines = [];
    
    // Add headers if requested (default is true)
    lines.push(keys.map(key => escapeField(key)).join(","));
    
    // Add data rows
    for (const obj of input)
        lines.push(keys.map(key => escapeField(formatValue(obj[key]))).join(","));
    
    return lines.join("\n");
}

/**
 * Format a value to its string representation
 */
function formatValue(value: unknown): string {
    if (value === null || value === undefined)
        return "";
    else if (typeof value === "string")
        return value;
    else if (typeof value === "number")
        return value.toString();
    else if (value instanceof Date)
        return value.toISOString();
    else
        return JSON.stringify(value);
}

/**
 * Escape a field according to CSV RFC 4180 specification:
 * - Fields containing commas, double quotes, or newlines are enclosed in double quotes
 * - Double quotes within a field are escaped with another double quote
 */
function escapeField(value: string): string {
    // Check if the field needs to be quoted
    const needsQuoting = value.includes(",") || value.includes("\"") || value.includes("\n") || value.includes("\r");
    
    if (needsQuoting) {
        // Escape double quotes by doubling them
        const escapedValue = value.replace(/"/g, "\"\"");
        return `"${escapedValue}"`;
    }
    
    return value;
}