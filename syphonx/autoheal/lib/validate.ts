import { tryParse<PERSON>son } from "./json.js";

export type ValidationCode =
    "ok" |
    "non-numeric" |
    "text-length" |
    "unexpected-type" |
    "unexpected-url";

export interface ValidationResult {
    validation_code: ValidationCode;
    validation_message?: string;
}

export function validateSelectorResult(selector_name: string, value: unknown): ValidationResult {
    if (selector_name === "price")
        return validatePrice(value);
    else if (selector_name === "name")
        return validateName(value);
    else if (selector_name === "description")
        return validateDescription(value);
    else
        return { validation_code: "ok" };
}

function validateName(value: unknown): ValidationResult {
    if (typeof value !== "string")
        return { validation_code: "unexpected-type", validation_message: `Expected string bug got "${typeof value}"` };
    if (/^https?:\/\//.test(value.trim()))
        return { validation_code: "unexpected-url" };
    if (value.length > 1000)
        return { validation_code: "text-length", validation_message: `Excessive text length: ${value.length}` };
    return { validation_code: "ok", validation_message: "Unexpected URL" };
}

function validateDescription(value: unknown): ValidationResult {
    if (typeof value !== "string")
        return { validation_code: "unexpected-type", validation_message: `Expected string bug got "${typeof value}"` };
    if (/^https?:\/\//.test(value.trim()))
        return { validation_code: "unexpected-url", validation_message: "Unexpected URL" };
    if (value.length > 10000)
        return { validation_code: "text-length", validation_message: `Excessive text length: ${value.length}` };
    return { validation_code: "ok", validation_message: "Unexpected URL" };
}

function validatePrice(value: unknown): ValidationResult {
    if (typeof value === "number")
        return { validation_code: "ok" };
    else if (typeof value === "string" && /\d{1,5}\.\d{2}/.test(value))
        return { validation_code: "non-numeric", validation_message: "Non-numeric result" };
    else
        return { validation_code: "ok" };
}
