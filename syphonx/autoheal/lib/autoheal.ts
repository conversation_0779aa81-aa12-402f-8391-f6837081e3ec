/*
import { promises as fs } from "fs";
import { Template } from "syphonx-lib";

import {
    analyzeRegression,
    closeLogStream,
    commit,
    createLogStream,
    generateTimestamp,
    hookConsoleLog,
    loadTemplate,
    sum,
    unhookConsoleLog,
    updateTemplate,
    waitForKeypress,
    CommitOptions,
    Page,
    SelectorContext,
    Timer
}
from "./index.js";

export interface AutoHealOptions {
    name: string;
    capture_url: string;
    domain_name: string;
    page: Page;
    screenshot_url: string;
    selectors: SelectorContext[];
    template_path: string;
    targets: string[];
}

export interface AutoHealResult {
    generated_selectors: GeneratedSelector[];
    report_url: string;
    message?: string;
    usage: number;
    cost: number;
}

interface GeneratedSelector {
    selector_name: string;
    selector: string;
    template: Template;
}

export async function autoheal({
    capture_url,
    domain_name,
    name,
    page,
    screenshot_url,
    selectors,
    template_path,
    targets
}: AutoHealOptions): Promise<AutoHealResult> {
    const timer = new Timer();
    const { template } = await loadTemplate(template_path);
    const timestamp = generateTimestamp("milliseconds");
    const { stream, url: report_url } = createLogStream(`autoheal/${timestamp}.html`, { title: `AUTOHEAL ${name}` });
    hookConsoleLog();
    const result: AutoHealResult = {
        generated_selectors: [],
        report_url,
        usage: 0,
        cost: 0
    };

    try {
        const analyze_result = await analyzeRegression({
            page,
            live: true,
            name: `AUTOHEAL ${name}`,
            context: {
                domain_name,
                capture_url,
                template_path,
                screenshot_url,
                selectors
            },
            targets,
            template,
            stream
        });

        if (stream)
            await closeLogStream(stream, {
                tokens: sum(analyze_result.analyze, "tokens"),
                cost: sum(analyze_result.analyze, "cost"),
                elapsed: timer.elapsed()
            });
            unhookConsoleLog();

        if (analyze_result.ok) {
            for (const generated_selector of analyze_result.generated_selectors) {
                if (generated_selector.selector) {
                    result.generated_selectors.push({
                        selector_name: generated_selector.selector_name,
                        selector: generated_selector.selector!,
                        template
                    });
                }
            }

            if (analyze_result.generated_selectors.length > 0) {
                const updates = analyze_result.generated_selectors.filter(({ selector }) => !!selector).map(({ selector_name, selector }) => ({ selector_name, selector: selector! }));
                const update = updateTemplate(template, updates);

                console.log();
                console.log(`report: ${report_url}`);
                console.log(`template: ${template_path}`);
                for (const generated_selector of analyze_result.generated_selectors) {
                    console.log(`${generated_selector.selector_name}: ${generated_selector.selector}`);
                    const { before, after } = update.commits.find(commit => commit.selector_name === generated_selector.selector_name) || {};
                    if (before && after) {
                        console.log(`selector: ${generated_selector.selector_name}`);
                        console.log(`before: ${before}`);
                        console.log(`after: ${after}`);
                    }
                    else if (after) {
                        console.log(`selector: ${generated_selector.selector_name}`);
                        console.log(`before: (none)`);
                        console.log(`after: ${after}`);
                    }
                    else {
                        console.log(`selector: ${generated_selector.selector_name} (no change)`);
                    }
                }

                const commit_obj: CommitOptions = {
                    template_path,
                    analyze_url: report_url,
                    updates: analyze_result.generated_selectors.filter(({ selector }) => !selector).map(({ selector_name, selector }) => ({ selector_name, selector: selector! }))
                };
                await fs.writeFile("./commit.jsonc", JSON.stringify(commit_obj, null, 2));

                //if (params.commit) {
                    process.stdout.write("commit? [y/n] ");
                    const keypress = await waitForKeypress();
                    process.stdout.write("\n");
        
                    if (keypress.name.toLowerCase() === "y") {
                        const commit_id = await commit(commit_obj);
                        //index.write(`<a href="${file}" class="d-block" target="_blank">${row.name} <i>commit=${commit_id}</i></a>\n`);
                        //break;
                    }
                //}
            }

            //index.write(`<a href="${file}" class="d-block" target="_blank">${row.name} <i>${analyze_result.generated_selectors.length} selectors generated</i></a>\n`);
        }
        else if (analyze_result.code === "error") {
            //index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i title="${analyze_result.message?.replace(/"/g, "'")}">${truncate(analyze_result.message, 100)}</i></a>\n`);
            result.message = analyze_result.message;
        }
        else if (analyze_result.code) {
            //index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i>${analyze_result.code}${analyze_result.message ? ` ${analyze_result.message}` : ""}</i></a>\n`);
            result.message = analyze_result.code;
        }
        else {
            //index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i>selector not generated</i></a>\n`);
            result.message = "selector not generated";
        }
    }
    catch (err) {
        result.message = err instanceof Error ? err.message : JSON.stringify(err);
        //err = err instanceof Error ? err.message : JSON.stringify(err);
        //index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i title="${(err instanceof Error ? err.message : JSON.stringify(err).replace(/"/g, "'"))}">${truncate(err instanceof Error ? err.message : JSON.stringify(err), 100)}</i></a>\n`);
        //console.log(chalk.red(err));
    }

    return result;
}
*/