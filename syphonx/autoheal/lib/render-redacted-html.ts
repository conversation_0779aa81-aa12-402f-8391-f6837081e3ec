import Jim<PERSON> from "jimp";
import { createVirtualBrowser } from "./virtual-browser/index.js";
import { redact } from "./scripts/redact.js";
import { sleep } from "./utilities.js";
import { compositeImage } from "./image/index.js";

export interface RenderRedactedHtmlOptions {
    html: string;
    headless?: boolean;
    timeout?: number;
    snooze?: number;
}

export interface RenderRedactedHtmlResult {
    fingerprint?: string;
    fingerprint_image?: Buffer;
    error?: string;
    ok: boolean;
}

/**
 * @deprecated use renderFingerprint instead
 */
export async function renderRedactedHtml({ html, headless, snooze, timeout }: RenderRedactedHtmlOptions): Promise<RenderRedactedHtmlResult> {
    const browser = await createVirtualBrowser({ headless, viewport: { width: 1024, height: 512 } });
    const page = await browser.newPage();
    try {
        await page.goto("data:text/html,");
        await page.setContent(html);
        await page.waitForLoadState();

        if (snooze)
            await sleep(snooze);

        const screenshot_image = await page.screenshot();
        await page.evaluate(redact);

        const redacted_image = await page.screenshot();
        const fingerprint_image = await compositeImage(screenshot_image, redacted_image, { width: 250, right: 24, bottom: 24, border: 4 });

        const binary_value = await imageToBinary(redacted_image, 224);
        const fingerprint = binaryToHex(binary_value);

        return { ok: true, fingerprint, fingerprint_image };
    }
    catch (err) {
        return { ok: true, error: err instanceof Error ? err.message : JSON.stringify(err) };
    }
    finally {
        await page.close();
    }
}

async function imageToBinary(buffer: Buffer, threshold: number): Promise<string> {
    const image = await Jimp.read(buffer);
    image.resize(8, 8).greyscale();
    let result = '';
    for (let y = 0; y < 8; y++) {
        for (let x = 0; x < 8; x++) {
            const pixelColor = image.getPixelColor(x, y);
            const grayscaleValue = Jimp.intToRGBA(pixelColor).r;
            result += grayscaleValue <= threshold ? '1' : '0';
        }
    }
    return result;
}

function binaryToHex(binary_value: string): string {
    return parseInt(binary_value.slice(0, 32), 2).toString(16).padStart(8, "0") + parseInt(binary_value.slice(32), 2).toString(16).padStart(8, "0");
}
