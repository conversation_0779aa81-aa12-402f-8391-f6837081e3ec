import { createUrlParams, request, Throttle } from "./index.js";
import chalk from "chalk";

export interface DiffbotRequestData {
    api: string;
    fields: string;
    pageUrl: string;
    resolvedPageUrl: string;
    version: number;
}

export interface DiffbotCanonicalLink {
    url: string;
}

export interface DiffbotImage {
    xpath: string;
    naturalHeight: number;
    width: number;
    diffbotUri: string;
    title: string;
    url: string;
    naturalWidth: number;
    primary: boolean;
    height: number;
}

export interface DiffbotDiscussion {
    numPages: number;
    confidence: number;
    diffbotUri: string;
    pageUrl: string;
    numPosts: number;
    type: string;
    title: string;
    posts: DiffbotPost[];
    participants: number;
}

export interface DiffbotPost {
    date: string;
    humanLanguage: string;
    author: string;
    diffbotUri: string;
    html: string;
    pageUrl: string;
    id: number;
    text: string;
    type: string;
}

export interface DiffbotOfferPrice {
    symbol: string;
    amount: number;
    text: string;
    currencyCode: string;
}

export interface DiffbotCategory {
    id: number;
    name: string;
    level: number;
    confidence: number;
    isPrimary: boolean;
}

export type DiffbotSpec = Record<string, string>;
export type DiffbotNormalizedSpec = Record<string, DiffbotNormalizedSpecValue>;
export type DiffbotNormalizedSpecValue = Record<string, string | number>;

export interface DiffbotProductObject {
    canonicalLinks: DiffbotCanonicalLink[];
    images: DiffbotImage[];
    offerPrice: string;
    regularPrice: string;
    productId: string;
    diffbotUri: string;
    upc: string;
    productOrigin: string;
    prefixCode: string;
    discussion: DiffbotDiscussion;
    availability: boolean;
    type: string;
    title: string;
    canonicalLink: string;
    regularPriceDetails: DiffbotOfferPrice;
    offerPriceDetails: DiffbotOfferPrice;
    specs: DiffbotSpec;
    normalizedSpecs: DiffbotNormalizedSpec;
    humanLanguage: string;
    html_lang: string;
    pageUrl: string;
    text: string;
    category: string;
    categories: DiffbotCategory[];
    sku: string;
    brand: string;
    links: string;
    extlinks: string;
    meta: Record<string, unknown>;
    querystring: string;
    breadcrumb: string;
    content: string;
    dom: string;
}

export interface DiffbotProductResponse {
    request: DiffbotRequestData;
    objects: DiffbotProductObject[];
    error?: string;
    errorCode?: number;
}

const token = process.env.DIFFBOT_API_KEY;
const throttle = new Throttle(1); // throttle DIFFBOT API usage to one call per second
const max_retries = 3;
const cooldown_intervals = [5, 10, 30]; // seconds
let request_counter = 0;

export interface DiffbotAnalyzeResponse {
}

export module diffbot {
    export type DiffbotProductFields = "links" | "extlinks" | "meta" | "querystring" | "breadcrumb" | "content" | "dom";

    export interface DiffbotProductOptions {
        fields: DiffbotProductFields[];
        timeout: number;
        callback: string;
        proxy: string;
        proxyAuth: string;
        useProxy: string;
        discussion: boolean;
    }

    // https://docs.diffbot.com/reference/extract-analyze
    export async function analyze(url: string): Promise<[DiffbotAnalyzeResponse, string]> {
        const result = await retry_request("analyze", url);
        return result;
    }

    // https://docs.diffbot.com/reference/product
    export async function product(url: string, options: Partial<DiffbotProductOptions> = {}): Promise<[DiffbotProductResponse, string]> {
        const result = await retry_request("product", url, options);
        return result;
    }

    async function retry_request(action: string, url: string, options?: Partial<DiffbotProductOptions>): Promise<[DiffbotProductResponse, string]> {
        const id = ++request_counter;
        if (process.env.VERBOSE)
            console.log(chalk.gray(`DIFFBOT request #${id} ${url}`));

        const t0 = Date.now();
        await throttle.apply();
        let retry = 0;

        const params = createUrlParams({ ...options, url, token });
        const request_url = `https://api.diffbot.com/v3/${action}${params}`
        let response = await request.json(request_url);
        if (response.error && process.env.VERBOSE)
            console.log(chalk.red(`DIFFBOT ERROR request ${id} ${response.error}`));
        while (busy(response.error) && ++retry <= max_retries) {
            const cooldown_seconds = retry <= cooldown_intervals.length ? cooldown_intervals[retry - 1] : cooldown_intervals.at(-1)!;
            if (process.env.VERBOSE)
                console.log(chalk.yellow(`DIFFBOT too busy, request #${id} ${cooldown_seconds} second cooldown ${retry}/${max_retries}`));
            await throttle.cooldown(cooldown_seconds * 1000); // block everything
            response = await request.json(request_url); // try again
            if (response.error && process.env.VERBOSE)
                console.log(chalk.red(`DIFFBOT ERROR request ${id} ${response.error}`));
        }

        if (process.env.VERBOSE) {
            const elapsed = ((Date.now() - t0) / 1000).toFixed(1);
            if (!busy(response.error))
                console.log(chalk.gray(`DIFFBOT request #${id} completed in ${elapsed}s`));
            else
                console.log(chalk.red(`DIFFBOT too busy, request #${id} giving up after ${max_retries} retries in ${elapsed}s`));
        }

        return [response, request_url];
    }

    function busy(message: string): boolean {
        return message?.includes("(429)") || message?.includes("too many requests");
    }
}
