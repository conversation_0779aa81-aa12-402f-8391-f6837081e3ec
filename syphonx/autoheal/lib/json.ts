import * as fs from "fs";
import JSON5 from "json5";
import YAM<PERSON> from "yaml";

export function renderJsonRowData(input: Array<Record<string, unknown>>): string {
    const [first] = input;
    const keys = Object.keys(first);
    return `
{
"header":
[${keys.map(key => `"${key}"`).join(",")}],
"data": [
${input.map(obj => JSON.stringify(keys.map(key => obj[key]))).join(",\n")}
]}`.trim();
}

export function renderJsonValueToString(json: string | undefined): string {
    const obj = tryParseJson(json);
    if (obj instanceof Array && obj.every(item => typeof item === "string"))
        return obj.join("\n");
    else if (typeof obj === "string")
        return obj;
    else if (typeof obj === "number")
        return obj.toString();
    else if (typeof obj === "boolean")
        return obj.toString();
    else
        return json || "";
}

export function loadJsonFile(file: string): unknown {
    try {
        const text = fs.readFileSync(file, "utf-8");
        return JSON5.parse(text);
    }
    catch (err) {
        return undefined;
    }
}

export function loadYamlFile(file: string): unknown {
    const text = fs.readFileSync(file, "utf-8");
    return YAML.parse(text);
}

export function tryLoadJsonFile(file: string): unknown {
    try {
        const text = fs.readFileSync(file, "utf-8");
        return JSON5.parse(text);
    }
    catch (err) {
        return undefined;
    }
}

export function tryLoadYamlFile(file: string): unknown {
    try {
        const text = fs.readFileSync(file, "utf-8");
        return YAML.parse(text);
    }
    catch (err) {
        return undefined;
    }
}

export function tryParseJson(value: unknown): unknown {
    try {
        if (typeof value === "string")
            return JSON5.parse(value);
    }
    catch (err) {
        return undefined;
    }
}

export function tryParseYaml(value: unknown): unknown {
    try {
        if (typeof value === "string")
            return YAML.parse(value);
    }
    catch (err) {
        return undefined;
    }
}
