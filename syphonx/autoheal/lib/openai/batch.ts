import * as fs from "fs"
import * as path from "path";
import * as stream from "stream";
import * as util from "util";

import OpenAI from "openai";
const pipeline = util.promisify(stream.pipeline);

export interface BatchPromptOptions {
    custom_id: string;
    text: string;
    model?: string;
    images?: string[];
    max_tokens?: number;
}

export interface BatchCompletion {
    id: string;
    content: string;
    model: string;
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    cached_tokens: number;
    cost: number;
}

// https://openai.com/pricing
const price_map: Record<string, [number, number]> = {
    "gpt-4": [30, 60],
    "gpt-4o": [2.5, 10],
    "gpt-4o-mini": [0.15, 0.60],
    "gpt-4-turbo": [10, 30],
    "gpt-4-32k": [6, 12],
    "gpt-3.5-turbo": [1, 2]
};

export async function batchStatus(batch_id: string): Promise<OpenAI.Batch> {
    const openai = new OpenAI();
    const batch = await openai.batches.retrieve(batch_id);
    return batch;
}

export async function downloadBatchCompletions(batch_id: string, output_file_id?: string, output_file?: string): Promise<string> {
    const openai = new OpenAI();
    if (!output_file_id) {
        const batch = await openai.batches.retrieve(batch_id);
        if (!batch.output_file_id)
            throw new Error(`File not ready for ${batch_id}`);
        output_file_id = batch.output_file_id;            
    }

    const response = await openai.files.content(output_file_id);
    if (!response.ok || !response.body)
        throw new Error(`${batch_id} returned status ${response.status}`);

    if (!output_file)
        output_file = path.resolve(`${batch_id}_completions`);
    const writer = fs.createWriteStream(output_file, { encoding: "utf-8" });
    await pipeline(response.body, writer);
    return output_file;
}

export function formatBatchPrompt({ custom_id, text, images = [], model = "gpt-4o-mini", max_tokens }: BatchPromptOptions): string {
    return JSON.stringify({
        custom_id,
        method: "POST",
        url: "/v1/chat/completions",
        body: {
            model,
            messages: [{
                role: "user",
                content: images.length === 0 ? text :
                [
                    { type: "text", text },
                    ...images.map(url => ({ type: "image_url", image_url: { url } }))
                ]
            }],
            max_tokens
        }
    });
}

export function parseBatchCompletion(text: string): BatchCompletion {
    const obj = JSON.parse(text);
    const id = obj.custom_id;
    const content = obj.response.body.choices[0].message.content;
    const model = obj.response.body.model;
    const prompt_tokens = obj.response.body.usage.prompt_tokens;
    const completion_tokens = obj.response.body.usage.completion_tokens;
    const cached_tokens = obj.response.body.usage.prompt_tokens_details.cached_tokens;
    const total_tokens = obj.response.body.usage.total_tokens;
    const cost = calculateCompletionCost(model, prompt_tokens, completion_tokens, true)!;
    return {
        id,
        content,
        model,
        prompt_tokens,
        completion_tokens,
        cached_tokens,
        total_tokens,
        cost
    };
}

export async function submitBatch(file: string) {
    const openai = new OpenAI();
    const batch_file = await openai.files.create({
        file: fs.createReadStream(file),
        purpose: "batch"
    });
  
    const batch = await openai.batches.create({
        input_file_id: batch_file.id,
        endpoint: "/v1/chat/completions",
        completion_window: "24h"
    });

    return batch;
}

export function calculateCompletionCost(model: string, input_tokens: number, output_tokens: number, batch = false): number | undefined {
    const key = subkey(model);
    const pricing = price_map[key];
    if (pricing) {
        let cost = input_tokens * pricing[0] / 1000000 + output_tokens * pricing[1] / 1000000;
        if (batch)
            cost /= 2;
        return round(cost, 6);
    }
}

function round(value: number, n: number): number {
    return parseFloat(value.toFixed(n));
}

function subkey(key: string) {
    const i = key.search(/-\d{2,4}-/); // find 2-4 digit version number suffix
    return i >= 0 ? key.slice(0, i) : key;
}

