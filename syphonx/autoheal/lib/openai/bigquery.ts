import * as bigquery from "../bigquery.js";

export interface OpenAIBatch {
    batch_id: string;
    target_ids: number[];
}

export async function queryPendingBatches(target: string): Promise<string[]> {
    const rows = await bigquery.query(`SELECT batch_id FROM temp.openai_batch WHERE batch_target=${bigquery.safeValue(target)} AND completed_at IS NULL`);
    return rows.map(row => row.batch_id);
}

export async function queryBatch(batch_id: string): Promise<OpenAIBatch> {
    const [row] = await bigquery.query<OpenAIBatch>(`SELECT * FROM temp.openai_batch WHERE batch_id=${bigquery.safeValue(batch_id)}`);
    return row;
}

export interface InsertOpenAIBatchOptions {
    batch_id: string;
    batch_target: string;
    batch_size: number;
    target_ids: string[];
}

export async function insertBatch(params: InsertOpenAIBatchOptions): Promise<void> {
    await bigquery.insert("temp.openai_batch", { ...params, created_at: new Date() });
}

export interface UpdateOpenAIBatchOptions {
    batch_id: string;
    completion_count: number;
    completion_cost: number;
}

export async function updateBatch({ batch_id, completion_cost, completion_count }: UpdateOpenAIBatchOptions): Promise<void> {
    await bigquery.query(`UPDATE temp.openai_batch SET completion_count=${bigquery.safeValue(completion_count)}, completion_cost=${bigquery.safeValue(completion_cost)}, completed_at=CURRENT_TIMESTAMP() WHERE batch_id=${bigquery.safeValue(batch_id)}`);
}
