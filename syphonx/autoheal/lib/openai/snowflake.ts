import * as snowflake from "../snowflake.js";

export interface OpenAIBatch {
    batch_id: string;
    batch_size: number;
    created_at: Date;
    completed_at: Date;
    completion_count: number;
    target_ids: number[]
}

export async function queryPendingBatches(target: string): Promise<string[]> {
    const rows = await snowflake.query(`SELECT batch_id FROM SHARED.PROD.openai_batch WHERE batch_target=${snowflake.safeValue(target)} AND completed_at IS NULL`);
    return rows.map(row => row.batch_id);
}

export async function queryBatch(batch_id: string): Promise<OpenAIBatch> {
    const [row] = await snowflake.query<OpenAIBatch>(`SELECT * FROM SHARED.PROD.openai_batch WHERE batch_id=${snowflake.safeValue(batch_id)}`);
    return row;
}

export async function queryPendingBatchInfo(target: string): Promise<OpenAIBatch[]> {
    const rows = await snowflake.query<OpenAIBatch>(`SELECT batch_id, batch_size, created_at, completed_at, completion_count FROM SHARED.PROD.openai_batch WHERE batch_target=${snowflake.safeValue(target)} AND completed_at IS NULL ORDER BY created_at DESC`);
    return rows;
}

export interface InsertOpenAIBatchOptions {
    batch_id: string;
    batch_target: string;
    batch_size: number;
    target_ids: number[];
}

export async function insertBatch(params: InsertOpenAIBatchOptions): Promise<void> {
    await snowflake.insert("SHARED.PROD.openai_batch", { ...params, created_at: new snowflake.SafeLiteral("CURRENT_TIMESTAMP") });
}

export async function abortBatches(hours: number): Promise<number> {
    const [{ n }] = await snowflake.query(`SELECT COUNT(*) AS n FROM SHARED.PROD.openai_batch WHERE completed_at IS NULL AND created_at <= DATEADD(HOUR, ${snowflake.safeValue(-1 * hours)}, CURRENT_DATE())`);
    if (n > 0)
        await snowflake.execute(`UPDATE SHARED.PROD.openai_batch SET completed_at=CURRENT_TIMESTAMP() WHERE completed_at IS NULL AND created_at < DATEADD(HOUR, ${snowflake.safeValue(-1 * hours)}, CURRENT_DATE())`);
    return n;
}

export interface UpdateOpenAIBatchOptions {
    batch_id: string;
    completion_count: number;
    completion_cost: number;
}

export async function updateBatchStatus({ batch_id, completion_cost, completion_count }: UpdateOpenAIBatchOptions): Promise<void> {
    await snowflake.execute(`UPDATE SHARED.PROD.openai_batch SET completion_count=${snowflake.safeValue(completion_count)}, completion_cost=${snowflake.safeValue(completion_cost)}, completed_at=CURRENT_TIMESTAMP() WHERE batch_id=${snowflake.safeValue(batch_id)}`);
}
