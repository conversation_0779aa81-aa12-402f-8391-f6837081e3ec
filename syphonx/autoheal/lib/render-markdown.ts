import TurndownService from "turndown";
import { collapseWhitespace } from "./utilities.js";

const service = new TurndownService({ headingStyle: "atx" })
    .remove(["script", "style", "head", "noscript", "iframe", "canvas", "video", "audio", "map", "object", "embed", "svg" as any])
    .addRule("img", {
        filter: "img",
        replacement: (content, node) => {
            const element = node as HTMLImageElement;
            const label = element.title?.trim() || element.alt?.trim() || "image";
            let src = element.src?.trim() || "";
            if (src.startsWith("data:"))
                src = "data://image";
            return src ? `![${label}](${src})` : "";
        }
    })
    .addRule("a", {
        filter: "a",
        replacement: (content, node) => {
            const element = node as HTMLAnchorElement;
            const href = element.href?.trim() || "";
            const { text, links } = unnest(content?.trim().replace(/^(#*\s*)/g, "") || "");
            let result = text ? `[${text.replace(/\s+/g, " ")}](${href})` : href;
            if (links.length > 0)
                result += "\n" + links.map(link => "\t" + link).join("\n") + "\n";
            return result + "\n";
        }
    });

export function renderMarkdown(html: string): string {
    let markdown = service.turndown(html);
    markdown = collapseWhitespace(markdown, { singleSpaced: true });
    return markdown;
}

/**
 * Extracts Markdown-style links and image links from the given text.
 * Returns an object containing the cleaned text (with the links removed)
 * and an array of the extracted links.
 *
 * @param {string} text - The input text containing Markdown-style links.
 * @returns {{ text: string, links: string[] }} An object with two properties:
 * - `text`: The cleaned text with links removed.
 * - `links`: An array of extracted Markdown-style links.
 */
function unnest(text: string) {
    const links: string[] = [];
    let match = /!?\[[^\]]+\]\([^\)]+\)/.exec(text);
    while (match) {
        links.push(match[0]);
        text = text.replace(match[0], "");
        match = /!?\[[^\]]+\]\([^\)]+\)/.exec(text);
    }
    text = text.trim();
    return { text, links };
}
