import * as snowflake from "../../snowflake.js";

import {
    ProductMatchAnalyzeResult,
    QueryProductMatchAnalyzeOptions
} from "./index.js";

export async function queryProductMatchAnalyzeTrinity({
    account_keys,
    brand_name,
    country_codes,
    skus,
    domain_names,
    exclude,
    from,
    to,
    random,
    limit
}: Partial<QueryProductMatchAnalyzeOptions>): Promise<ProductMatchAnalyzeItem[]> {
    const params = [];
    const where = [
        "match_status IN ('CONFIRMED', 'REJECTED')",
        "resolution_type IN ('manual', 'automated')"
    ];

    if (account_keys && account_keys.length > 0)
        where.push(`account_key IN (${account_keys.map(key => snowflake.safeValue(key)).join(",")})`);

    if (brand_name) {
        params.push(brand_name);
        where.push(`brand_name=?`);
    }

    if (country_codes && country_codes.length > 0)
        where.push(`country_code IN (${country_codes.map(key => snowflake.safeValue(key.toUpperCase())).join(",")})`);

    if (skus && skus.length > 0)
        where.push(`sku IN (${skus.map(key => snowflake.safeValue(key)).join(",")})`);

    if (domain_names && domain_names.length > 0)
        where.push(`domain IN (${domain_names.map(key => snowflake.safeValue(key)).join(", ")})`);

    if (from && to)
        where.push(`resolution_timestamp BETWEEN '${from.toISOString()}' AND '${to.toISOString()}'`);
    else if (from)
        where.push(`resolution_timestamp >= ${from.toISOString()}`);
    else if (to)
        where.push(`resolution_timestamp < ${to.toISOString()}`);

    if (exclude)
        where.push(`domain NOT IN (${exclude.map(name => snowflake.safeValue(name)).join(",")})`);

    const n = 10;
    const subquery = `
SELECT *
FROM trinity.matching.matching_profile_prowl_view
WHERE ${where.join("\nAND ")}
QUALIFY ROW_NUMBER() OVER (
    PARTITION BY
        account_key,
        country_code,
        sku
    ORDER BY
        CASE
            WHEN match_status='CONFIRMED' THEN 1
            WHEN match_status='REJECTED' THEN 2
            ELSE 3
        END,
        CASE
            WHEN resolution_type='automated' THEN 1
            WHEN resolution_type='manual' THEN 2
            ELSE 3
        END,
        resolution_timestamp DESC
) <= ${n}`.trim();

    let query = `
WITH match_candidates AS (
${subquery.split("\n").map(line => `    ${line}`).join("\n")}
)
SELECT
    'prowl' AS app_name,
    account_key,
    country_code,
    sku,
    ANY_VALUE(product_id) AS product_id,
    ANY_VALUE(client_name) AS client_name,
    ANY_VALUE(client_id) AS client_id,
    ANY_VALUE(brand_name) AS brand_name,
    ANY_VALUE(brand_id) AS brand_id,
    ANY_VALUE(brand_currency) AS currency,
    ANY_VALUE(product_name) AS product_name,
    ANY_VALUE(upc) AS gtin,
    ANY_VALUE(target_price) AS target_price,
    ANY_VALUE(regular_map) AS regular_map,
    ANY_VALUE(msrp) AS msrp,
    ANY_VALUE(matching_profile_id) AS matching_profile_id,
    COUNT(*) AS product_match_count,
    ARRAY_AGG(
        OBJECT_CONSTRUCT(
            'domain_name', domain,
            'country_code', domain_country,
            'currency_code', domain_currency,
            'product_match_id', url_id,
            'url', source_url,
            'capture_date', capture_timestamp,
            'capture_id', capture_id,
            'page_id', CONCAT(account_key, '__', country_code, '__', sku, '__', domain),
            'url_id', url_id,
            'ad_product_id', ad_product_id,
            'match_status', match_status,
            'resolution_type', resolution_type,
            'resolution_timestamp', resolution_timestamp
        )
    )
    WITHIN GROUP (ORDER BY match_status, resolution_type, domain, source_url)
    AS pages
FROM match_candidates
GROUP BY ALL`.trim();

    if (random)
        query += "\nORDER BY RANDOM()";
    else
        query += "\nORDER BY 1, 2, 3, 4";

    if (limit)
        query += `\nLIMIT ${snowflake.safeValue(limit)}`;

    const rows = await snowflake.query(query, params);
    return rows as ProductMatchAnalyzeItem[];
}

export async function insertProductMatchAnalyzeResultTrinity(result: Partial<ProductMatchAnalyzeResult>): Promise<void> {
    if (!result.pages)
        return;

    const now = new Date();
    for (const row of result.pages) {
        if (!result.data)
            continue;
        const obj = result.data.pages.find(obj => obj.url === row.product_match_url);
        if (!obj)
            continue;
        await snowflake.insert(
            "trinity.matching.matching_profile_prowl_analyze",
            {
                matching_profile_id: result.data.matching_profile_id,
                capture_id: obj.capture_id,
                product_id: result.data.product_id,
                url_id: obj.url_id,
                url: obj.url,
                domain_name: obj.domain_name,
                match_status: obj.match_status,
                resolution_type: obj.resolution_type,
                resolution_timestamp: obj.resolution_timestamp,
                analyze_id: result.analyze_id,
                analyzed_at: now,
                analyze_status: row.product_match_status,
                analyze_explain: row.product_match_explain,
                page_id: obj.name,
                report_url: result.report_url
            }
        );
    }
}
