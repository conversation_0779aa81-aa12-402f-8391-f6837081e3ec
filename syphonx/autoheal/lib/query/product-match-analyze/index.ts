import { AnalyzeResult } from "../../index.js";
import { queryProductMatchAnalyzeMain, insertProductMatchAnalyzeResultMain } from "./product-match-analyze-main.js";
import { queryProductMatchAnalyzeTrinity, insertProductMatchAnalyzeResultTrinity } from "./product-match-analyze-trinity.js";

export interface QueryProductMatchAnalyzeOptions {
    app_name?: string;
    brand_name?: string;
    account_keys?: string[];
    country_codes?: string[];
    domain_names?: string[];
    exclude?: string[];
    seller_ids?: number[];
    skus?: string[];
    from?: Date;
    to?: Date;
    limit?: number;
    subset?: number;
    random?: boolean;
    force?: boolean;
    mode?: string;
    all?: boolean;
}

export interface ProductMatchAnalyzeResult {
    analyze_id: string;
    app_name: string
    account_key: string;
    country_code: string;
    sku: string;
    product_id: number;
    model: string;
    input_tokens: number;
    output_tokens: number;
    cached_tokens: number;
    total_tokens: number;
    diffbot_tokens: number;
    cost: number;
    run_id: string;
    report_url: string;
    data: ProductMatchAnalyzeItem;
    pages: Array<{
        product_match_url: string;
        product_match_status: string;
        product_match_explain: string;
        product_match_id: number;
        product_match_active: boolean;
        page_id: string;
        n1: number;
        n2: number;
    }>;
    analyze: AnalyzeResult[];
}

export async function queryProductMatchAnalyze(options: QueryProductMatchAnalyzeOptions): Promise<ProductMatchAnalyzeItem[]> {
    if (options.mode === "trinity") {
        const result = await queryProductMatchAnalyzeTrinity(options);
        return result;
    }
    else {
        const result = await queryProductMatchAnalyzeMain(options);
        return result;
    }
}

export async function insertProductMatchAnalyzeResult(options: Partial<ProductMatchAnalyzeResult>, mode: string): Promise<void> {
    if (mode === "trinity") {
        const result = await insertProductMatchAnalyzeResultTrinity(options);
        return result;
    }
    else {
        const result = await insertProductMatchAnalyzeResultMain(options);
        return result;
    }
}
