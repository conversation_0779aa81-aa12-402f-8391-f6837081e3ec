import * as bigquery from "../../bigquery.js";

import {
    ProductMatchAnalyzeResult,
    QueryProductMatchAnalyzeOptions
} from "./index.js";

export async function queryProductMatchAnalyzeMain({
    app_name,
    brand_name,
    account_keys,
    country_codes,
    domain_names,
    exclude,
    seller_ids,
    skus,
    limit,
    subset,
    random,
    force,
    all
}: QueryProductMatchAnalyzeOptions): Promise<ProductMatchAnalyzeItem[]> {
    const params: Record<string, any> = {};
    const where = [];
    const sub_where = [];
    let query = "SELECT *";

    if (app_name)
        where.push(`app_name=${bigquery.safeValue(app_name)}`);

    if (account_keys && account_keys.length > 0)
        where.push(`account_key IN (${account_keys.map(key => bigquery.safeValue(key)).join(",")})`);

    if (brand_name) {
        params.brand = brand_name;
        where.push(`brand_name=@brand`);
    }

    if (country_codes && country_codes.length > 0)
        where.push(`country_code IN (${country_codes.map(key => bigquery.safeValue(key.toUpperCase())).join(",")})`);

    if (skus && skus.length > 0)
        where.push(`sku IN (${skus.map(key => bigquery.safeValue(key)).join(",")})`);

    if (!force)
        where.push("(last_analyzed_at IS NULL OR last_analyzed_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY))");

    if (!all)
        sub_where.push("(html_url IS NOT NULL OR active IS FALSE)");

    //where.push("(SELECT COUNT(*) FROM UNNEST(pages) WHERE active) > 0");

    if (domain_names && domain_names.length > 0) {
        sub_where.push(`domain_name IN (${domain_names.map(key => bigquery.safeValue(key)).join(",")})`);
        //const constraint = `domain_name IN (${domain_names.map(key => bigquery.safeValue(key)).join(",")})`;
        const constraint = sub_where.join(" AND ");
        query += `\nREPLACE(ARRAY(SELECT AS STRUCT * FROM UNNEST(pages) WITH OFFSET i WHERE ${constraint}`;
        if (subset)
            query += ` QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name ORDER BY i) <= ${bigquery.safeValue(subset)}`;
        query += ") AS pages)";
        where.push(`(SELECT COUNT(*) FROM UNNEST(pages) WHERE ${constraint}) > 0`);
    }
    else if (seller_ids && seller_ids.length > 0) {
        sub_where.push(`seller_id IN (${seller_ids.map(key => bigquery.safeValue(key)).join(",")})`);
        //const constraint = `seller_id IN (${seller_ids.map(key => bigquery.safeValue(key)).join(",")})`;
        const constraint = sub_where.join(" AND ");
        query += `\nREPLACE(ARRAY(SELECT AS STRUCT * FROM UNNEST(pages) WITH OFFSET i WHERE ${constraint}`;
        if (subset)
            query += ` QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name ORDER BY i) <= ${bigquery.safeValue(subset)}`;
        query += ") AS pages)";
        where.push(`(SELECT COUNT(*) FROM UNNEST(pages) WHERE ${constraint}) > 0`);
    }
    else if (exclude && exclude.length > 0) {
        sub_where.push(`domain_name NOT IN (${exclude.map(key => bigquery.safeValue(key)).join(",")})`);
        const constraint = sub_where.join(" AND ");
        //const constraint = `domain_name NOT IN (${exclude.map(key => bigquery.safeValue(key)).join(",")})`;
        query += `\nREPLACE(ARRAY(SELECT AS STRUCT * FROM UNNEST(pages) WITH OFFSET i WHERE ${constraint}`;
        if (subset)
            query += ` QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name ORDER BY i) <= ${bigquery.safeValue(subset)}`;
        query += ") AS pages)";
        where.push(`(SELECT COUNT(*) FROM UNNEST(pages) WHERE ${constraint}) > 0`);
    }
    else if (subset) {
        const constraint = sub_where.join(" AND ");
        query += `\nREPLACE(ARRAY(SELECT AS STRUCT * FROM UNNEST(pages) WITH OFFSET i WHERE ${constraint} QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name ORDER BY i) <= ${bigquery.safeValue(subset)}) AS pages)`;
    }

    query += "\nFROM omega.product_match_analyze_pending";
    if (where.length > 0)
        query += `\nWHERE ${where.join("\nAND ")}`;
    if (random)
        query += "\nORDER BY RAND()";
    else
        query += "\nORDER BY app_name, account_key, country_code, brand_name, sku";
    if (limit)
        query += `\nLIMIT ${bigquery.safeValue(limit)}`;

    const rows = await bigquery.query<ProductMatchAnalyzeItem>(query, params);
    return rows;
}

export async function insertProductMatchAnalyzeResultMain(result: Partial<ProductMatchAnalyzeResult>): Promise<void> {
    await bigquery.insert("omega.product_match_analyze", {
        analyze_id: result.analyze_id,
        app_name: result.app_name,
        account_key: result.account_key,
        country_code: result.country_code,
        sku: result.sku,
        product_id: result.product_id,
        model: result.model,
        input_tokens: result.input_tokens,
        output_tokens: result.output_tokens,
        cached_tokens: result.cached_tokens,
        total_tokens: result.total_tokens,
        cost: result.cost,
        run_id: result.run_id,
        report_url: result.report_url,
        data: JSON.stringify(result.data),
        timestamp: new Date(),
        product_matches: result.pages?.map(page => ({
            product_match_url: page.product_match_url,
            product_match_status: page.product_match_status,
            product_match_explain: page.product_match_explain,
            product_match_id: page.product_match_id,
            product_match_active: page.product_match_active,
            page_id: page.page_id,
            n1: page.n1,
            n2: page.n2
        })),
        analyze: result.analyze
    });
}
