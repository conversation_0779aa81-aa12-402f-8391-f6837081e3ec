export interface ProductPage {
    app_name: string;
    page_id: string;
    account_key: string;
    country_code: string;
    sku: string;
    seller_name: string;
    seller_id: number;
    capture_url: string; // consider: rename to page_url
    capture_engine: string;
    capture_status: string;
    template_path: string;
    regression_count: number;
    gap_count: number;
    alert_count: number;
    last_capture: ProductPageCapture;
    last_attempt: ProductPageCaptureAttempt;
    selectors: ProductPageSelector[];
    template: ProductPageTemplate;
}

export interface ProductPageCapture {
    capture_id: string;
    capture_date: Date;
    html_url: string;
    screenshot_url: string;
    screenshot_class: PageClassification;
    status: string;
    data: string;
    data_keys: string;
}

export interface ProductPageCaptureAttempt extends ProductPageCapture {
    status: string;
    errors: string[];
    error_count: number;
}

export interface ProductPageCaptureReference {
    capture_id: string;
    capture_date: Date;
    selector_age_days: number;
    selector_json_value: string;
    html_url: string;
    screenshot_url: string;
}

export interface ProductPageSelector {
    selector_name: string;
    selector_json_value: string;
    selector_alerts: string;
    hits: number;
    hit_rate: number;
    regression: boolean;
    gap: boolean;
    reference?: ProductPageCaptureReference;
    query?: string[];
    query_json_value?: string;
    query_selector_path?: string[];
    query_diff?: string;
    query_diff_count?: number;
}

export interface ProductPageTemplate {
    selectors_changed: string;
    revision_count: number;
    created_at: Date;
    created_by: string;
    modified_at: Date;
    modified_by: string;
}
