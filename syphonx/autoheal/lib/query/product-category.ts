import * as fs from "fs";
import * as path from "path";
import * as snowflake from "../snowflake.js";
import chalk from "chalk";

export interface ProductCategory {
    category_id: number;
    category_name: string;
    category_description: string;
    category_examples: string;
    depth: number;
    descendants: number;
    category_vector: number[];
    parent?: ProductCategory;
}

export async function loadProductCategories(): Promise<ProductCategory[]> {
    let categories: ProductCategory[] | undefined = undefined;
    const temp_dir = process.env.TEMP_DIR || "tmp";
    const cache_file = path.resolve(temp_dir, "product-categories.json");
    if (fs.existsSync(cache_file)) {
        const { mtime: cache_timestamp } = fs.statSync(cache_file);
        const [{ timestamp: source_timestamp }] = await snowflake.query("SELECT MAX(last_modified_at) AS timestamp FROM SHARED.PUBLIC.google_product_taxonomy");

        // if timestamp of cache file is still newer than last time the source table was modified then use the cached data
        if (cache_timestamp > source_timestamp) {
            const text = fs.readFileSync(cache_file, "utf-8");
            try {
                categories = JSON.parse(text);
                if (process.env.VERBOSE)
                    console.log(chalk.gray("product-categories.json data loaded (cached)"));
            }
            catch (err) {
                if (process.env.VERBOSE) {
                    console.log(chalk.red("product-categories.json data loaded (invalid)"));
                    console.log(chalk.red.italic(`ERROR ${err instanceof Error ? err.message : JSON.stringify(err)}`));
                }
            }
        }
    }
    if (!categories) {
        categories = await queryProductCategories();
        fs.writeFileSync(cache_file, JSON.stringify(categories), "utf-8");
        if (process.env.VERBOSE)
            console.log(chalk.gray("product-categories.json data loaded (updated)"));
    }

    link(categories);
    return categories;
}

export async function queryProductCategories(): Promise<ProductCategory[]> {
    const rows = await snowflake.query("SELECT * FROM SHARED.PUBLIC.google_product_taxonomy ORDER BY 1");
    return rows.map(({ category_vector, ...row }) => ({
        ...row,
        category_vector: JSON.parse(category_vector)
    }));
}

function link(categories: ProductCategory[]) {
    const map = new Map<string, ProductCategory>();
    for (const category of categories.filter(({ category_name }) => category_name.includes(" > "))) {
        const key = category.category_name.slice(0, category.category_name.lastIndexOf(" > "));
        if (!map.has(key))
            map.set(key, categories.find(({ category_name }) => category_name === key)!);
        category.parent = map.get(key);
    }
}
