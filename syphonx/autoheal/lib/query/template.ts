import * as bigquery from "../bigquery.js";

export interface QueryTemplateChangeHistoryResult {
    timestamp: Date;
    app_name: string;
    country_code: string;
    domain_name: string;
    page_type: string;
}

export interface QueryConfluxSellersWithoutSyphonxTemplateResult {
    timestamp: Date;
    key: string;
    template: string;
    contract?: string;
}

export async function queryTemplateChangeHistory(template_path: string): Promise<QueryTemplateChangeHistoryResult[]> {
    const query = `
SELECT timestamp, agent, selectors_changed
FROM syphonx.template_change_history
WHERE template_path=${bigquery.safeValue(template_path)}
ORDER BY timestamp DESC
    `.trim();
    const rows = await bigquery.query<QueryTemplateChangeHistoryResult>(query);
    return rows;
}

// change this to a scheduled query, which sends its data to a queue, which creates SyphonX templates, in order, by key? (so we have version history...)
export async function queryConfluxSellersWithoutSyphonxTemplate(): Promise<QueryConfluxSellersWithoutSyphonxTemplateResult[]> {
    const query = `
WITH latest_conflux_templates AS (
SELECT a.timestamp, a.key, a.selectors, a.template, a.contract
FROM conflux.template_log a
WHERE key LIKE '%wtb%'
QUALIFY ROW_NUMBER() OVER(PARTITION BY CONCAT(a.key, a.fingerprint) ORDER BY timestamp DESC) = 1 -- to get each template change
-- QUALIFY ROW_NUMBER() OVER(PARTITION BY a.key ORDER BY timestamp DESC) = 1 -- to get just the lastest template
)
SELECT a.timestamp, a.key, a.template
FROM latest_conflux_templates a
LEFT JOIN syphonx.template_log b ON a.key=b.key
-- don't include timestamp, params, url... in template fingerprint when comparing...
AND FARM_FINGERPRINT(TO_JSON_STRING(a.template.actions))=FARM_FINGERPRINT(TO_JSON_STRING(b.template.actions))
WHERE b.key IS NULL 
-- AND LAX_BOOL(a.template.error_during_conversion) IS NOT TRUE
AND a.selectors IS NOT NULL AND a.template IS NOT NULL
ORDER BY a.key
LIMIT 100000
`.trim();

    const rows = await bigquery.query<QueryConfluxSellersWithoutSyphonxTemplateResult>(query);
    return rows;
}
