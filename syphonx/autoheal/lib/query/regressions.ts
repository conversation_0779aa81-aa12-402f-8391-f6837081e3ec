import * as bigquery from "../bigquery.js";
import { ProductPage } from "./common.js";
import { isEmpty } from "../index.js";

export interface QueryRegressionsOptions {
    app_name: string;
    account_key?: string;
    country_code?: string;
    seller_id?: number;
    selector_name?: string[];
    engine?: string;
    sku?: string;
    max?: number;
}

export interface QueryRegressionsResult extends ProductPage {
    name: string;
    domain_name: string;
    template_last_modified_at: Date;
}

export async function queryRegressions({ max = 100, ...options}: QueryRegressionsOptions): Promise<QueryRegressionsResult[]> {
    const where = createWhereClause(options);
    const query = `
SELECT *,
  NET.REG_DOMAIN(capture_url) AS domain_name,
  CONCAT(account_key, ' ', sku, ' at ', seller_name, ' (', country_code, ')') AS name,
  COALESCE(template.modified_at, template.created_at) AS template_last_modified_at
FROM conflux.product_page_daily
WHERE ${where.join("\nAND ")}
ORDER BY name
LIMIT ${max}
`.trim();
    const rows = await bigquery.query<QueryRegressionsResult>(query);
    return rows;
}

function createWhereClause({ app_name, account_key, country_code, seller_id, selector_name, engine, sku }: QueryRegressionsOptions): string[] {
    const where = [
        `app_name=${bigquery.safeValue(app_name)}`
    ];

    if (account_key)
        where.push(`account_key=${bigquery.safeValue(account_key.toLowerCase())}`);

    if (country_code)
        where.push(`country_code=${bigquery.safeValue(country_code.toUpperCase())}`);

    if (seller_id)
        where.push(`seller_id=${bigquery.safeValue(seller_id)}`);

    if (engine && (app_name && !["wtb", "prowl"].includes(app_name)))
        where.push(`capture_engine=${bigquery.safeValue(engine)}`);

    if (!isEmpty(selector_name))
        where.push(`(SELECT COUNT(*) FROM UNNEST(selectors) WHERE selector_name IN (${selector_name?.map(name => bigquery.safeValue(name)).join(", ")}) AND regression)>0`);
    else
        where.push("regression_count>0");

    if (sku) 
        where.push(`REGEXP_CONTAINS(sku, r${bigquery.safeValue(sku)})`);

    return where;
}
