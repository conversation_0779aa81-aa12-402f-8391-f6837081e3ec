import * as snowflake from "../snowflake.js";

export interface ProductEmbed {
    product_id: number;
    product_text: string;
    brand_summary: string;
    product_vector: number[];
    category_ids: number[];
}

export interface ProductCategoryCompletion {
    product_id: number;
    category_id?: number;
    product_text: string;
    prompt: string;
    line_num?: number;
    error_code?: string;
    error_message?: string;
    model: string;
    prompt_tokens: number;
    completion_tokens: number;
    cost: number;
    total_tokens: number;
}

export async function queryPendingProductCategoryProductIds({ limit }: { limit?: number } = {}): Promise<number[]> {
    let query = "SELECT product_id FROM WTB.PRODUCT_CATEGORY.product_category_pending";
    if (limit)
        query += ` LIMIT ${snowflake.safeValue(limit)}`;
    const rows = await snowflake.query(query);
    return rows.map(row => row.product_id);
}

export async function queryProductCategoryEmbeddings({ product_ids, limit, pending }: { product_ids?: number[], limit?: number, pending: boolean }): Promise<ProductEmbed[]> {
    let table = "";
    if (pending)
        table ="WTB.PRODUCT_CATEGORY.product_category_pending";
    else if (!pending && product_ids && product_ids.length > 0)
        table = "WTB.PRODUCT_CATEGORY.product_embeddings";
    if (!table)
        throw new Error("Invalid arguments");

    let query = `SELECT * FROM ${table}`;
    if (product_ids && product_ids.length > 0)
        query += ` WHERE product_id IN (${product_ids.join(",")})`;
    else if (limit)
        query += ` LIMIT ${limit}`;
    const rows = await snowflake.query(query);
    return rows.map(({ product_vector, category_ids, ...row }) => ({
        ...row,
        product_vector: JSON.parse(product_vector),
        category_ids: category_ids ? typeof category_ids === "string" ? JSON.parse(category_ids) : category_ids : []
    }));
}

export async function insertProductCategoryCompletions(completions: ProductCategoryCompletion[]): Promise<void> {
    await snowflake.insert("WTB.PRODUCT_CATEGORY.product_category_completions", completions.map(completion => 
        ({ ...completion, "timestamp": new snowflake.SafeLiteral("CURRENT_TIMESTAMP") })
    ));
}
