import * as bigquery from "../bigquery.js";
import { ProductPage } from "./common.js";

export interface QueryGapsOptions {
    app: string;
    account_key?: string;
    country_code?: string;
    seller_id?: number;
    selector_name?: string;
    engine?: string;
    sku?: string;
    limit?: number;
}

export async function queryGaps({ limit = 100, ...options}: QueryGapsOptions): Promise<ProductPage[]> {
    const where = createWhereClause(options);
    const query = `
SELECT *
FROM conflux.product_page_daily, UNNEST(selectors)
WHERE ${where.join("\nAND ")}
ORDER BY gap_count DESC, capture_age_days, page_id
LIMIT ${limit}
`.trim();
    const rows = await bigquery.query<ProductPage>(query);
    return rows;
}

function createWhereClause({ app, account_key, country_code, seller_id, selector_name, engine, sku }: QueryGapsOptions): string[] {
    const where = [
        `app_name=${bigquery.safeValue(app)}`,
        "gap IS TRUE"
    ];

    if (account_key)
        where.push(`account_key=${bigquery.safeValue(account_key.toLowerCase())}`);

    if (country_code)
        where.push(`country_code=${bigquery.safeValue(country_code.toUpperCase())}`);

    if (seller_id)
        where.push(`seller_id=${bigquery.safeValue(seller_id)}`);

    if (selector_name)
        where.push(`selector_name=${bigquery.safeValue(selector_name)}`);

    if (engine && (app && ![ 'wtb', 'prowl', ].includes(app)))
        where.push(`capture_engine=${bigquery.safeValue(engine)}`);

    if (sku)
        where.push(`REGEXP_CONTAINS(sku, r${bigquery.safeValue(sku)})`);

    return where;
}
