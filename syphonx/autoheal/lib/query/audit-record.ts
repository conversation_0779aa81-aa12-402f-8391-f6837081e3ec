import chalk from "chalk";

import {
    bigquery,
    generateUniqueId,
    summarize,
    Ana<PERSON><PERSON><PERSON><PERSON><PERSON>,
    Timer,
    ValidationCode
} from "../index.js";

export type AuditCode =
    "regression-confirmed" |
    "content-not-located" |
    "page-load-error" |
    "page-classification-error" |
    "screenshot-verification-error" |
    "template-load-error" |
    "selector-not-found" |
    "out-of-stock" |
    "product-discontinued" |
    "hidden-price" |
    "local-only" |
    "no-price" |
    "no-reviews" |
    "invalid-page-type" |
    "invalid-selector-result" |
    "blocked" |
    "broken-link" |
    "modal-dialog" |
    "selector-not-broken" |
    "unexpected-error";

export interface AuditRecord {
    app_name: string;
    page_type: string;
    page_id: string;
    selector_name?: string;
    seller_name: string;
    seller_id: number;
    account_key: string;
    country_code: string;
    sku: string;
    audit_url?: string;
    audit_code?: AuditCode;
    audit_message?: string;
    audited_at?: Date;
}

export class AuditRecordCreator implements AuditRecord {
    audit_id: string;
    app_name: string;
    page_type: string;
    page_id: string;
    selector_name?: string;
    seller_name: string;
    seller_id: number;
    account_key: string;
    country_code: string;
    sku: string;
    audit_url?: string;
    audit_code?: AuditCode;
    audit_message?: string;
    validation_code?: ValidationCode;
    screenshot_url?: string;
    analyze: AnalyzeResult[];
    private timer: Timer;

    constructor(params: AuditRecord) {
        this.audit_id = generateUniqueId();
        this.app_name = params.app_name;
        this.page_type = params.page_type;
        this.page_id = params.page_id;
        this.selector_name = params.selector_name;
        this.seller_name = params.seller_name;
        this.account_key = params.account_key;
        this.country_code = params.country_code;
        this.sku = params.sku;
        this.seller_id = params.seller_id;
        this.analyze = [];
        this.timer = new Timer();
    }

    reset(): void {
        this.audit_id = generateUniqueId();
        this.analyze = [];
        this.timer = new Timer();
    }

    async save(audit_code: AuditCode, message?: string): Promise<void> {
        this.audit_code = audit_code;
        this.audit_message = message;
        const { timer, ...data } = this;

        data.analyze = data.analyze.map(obj => {
            const { tokens, elapsed, ...data } = obj;
            return {
                ...data,
                usage: tokens,
                duration: elapsed
            } as unknown as AnalyzeResult
        }); // SHIM
        const { cost } = summarize(data.analyze); // SHIM
        await bigquery.insert("syphonx.page_selector_audits", {
            ...data,
            audited_at: new Date(),
            duration: timer.elapsed(),
            cost
        });

        console.log(chalk.gray(`audit ${this.audit_id} inserted (${this.audit_code})`));
    }
}

export interface QueryAuditsOptions {
    app_name: string;
    page_type: string;
    selector_name: string;
    seller_id?: number;
    days?: number;
}

export async function queryAudits({ app_name, page_type, selector_name, seller_id, days }: QueryAuditsOptions): Promise<AuditRecordCreator[]> {
    const where = [
        `app_name=${bigquery.safeValue(app_name)}`,
        `page_type=${bigquery.safeValue(page_type)}`,
        `selector_name=${bigquery.safeValue(selector_name)}`
    ];

    if (days)
        where.push(`audited_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL ${bigquery.safeValue(days)} DAY)`);

    if (seller_id)
        where.push(`seller_id=${bigquery.safeValue(seller_id)}`);

    const query = `
SELECT *
FROM syphonx.page_selector_audits
WHERE ${where.join("\nAND ")}
ORDER BY audited_at DESC;
`.trim();

    const rows = await bigquery.query(query);
    return rows;
}

export async function queryAuditBlackList(): Promise<string[]> {
    const rows = await bigquery.query("SELECT seller_name FROM syphonx.page_selector_audit_black_list ORDER BY 1") ;
    return rows.map(row => row.seller_name);
}

export function mapPageClassificationToAuditCode(classification: PageClassification): AuditCode {
    if (classification === "BLOCKED")
        return "blocked";
    else if (classification === "BROKEN")
        return "broken-link";
    else if (classification === "MODAL")
        return "modal-dialog";
    else
        return "invalid-page-type";
}
