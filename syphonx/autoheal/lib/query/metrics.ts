import * as bigquery from "../bigquery.js";

export interface QueryMetricsOptions {
    app: string;
    account_key?: string;
    country_code?: string;
    seller_id?: number;
    selector_name?: string;
    sku?: string;
    group_by: Set<string>;
    max?: number;
    offset?: number;
}

export type QueryMetricsResult = Record<string, any>;

export async function queryMetrics({ max = 1000, ...options }: QueryMetricsOptions): Promise<QueryMetricsResult[]> {
    const { app, group_by } = options;
    const unnest = group_by.has("selector") ? ", UNNEST(selectors)" : "";
    const dimensions = createDimensions(options);
    const measures = createMeasures(options);
    const where = createWhereClause(options);
    const groupings = Array(group_by.size).fill(0).map((_, i) => i + 1).join(", ");
    const query = `
SELECT
${[...dimensions, ...measures].join(",\n")}
FROM conflux.product_page_daily${unnest}
WHERE ${where.join("\nAND ")}
GROUP BY ${groupings}
ORDER BY ${groupings}
LIMIT ${max}
OFFSET ${options.offset}
`.trim();
    const rows = await bigquery.query<QueryMetricsResult>(query);
    return rows;
}

function createDimensions({ group_by }: QueryMetricsOptions): string[] {
    return Array.from(group_by)
        .map(key => {
            if (key === "account")
                return "account_key";
            else if (key === "country")
                return "country_code";
            else if (key === "seller")
                return "IF(NOT ENDS_WITH(seller_name, ')'), CONCAT(seller_name, ' (', country_code, ') ', seller_id), CONCAT(seller_name, ' ', seller_id)) AS seller_name";
            else if (key === "selector")
                return "selector_name";
            else
                throw `Invalid group by key "${key}"`;
        });
}

function createMeasures({ selector_name, group_by }: QueryMetricsOptions): string[] {
    const measures = [
        "COUNT(DISTINCT page_id) AS pages"
    ];

    if (selector_name) {
        measures.push(`SUM((SELECT COUNTIF(regression) FROM UNNEST(selectors) WHERE selector_name=${bigquery.safeValue(selector_name)})) AS regressions`);
        measures.push(`SUM((SELECT COUNTIF(gap) FROM UNNEST(selectors) WHERE selector_name=${bigquery.safeValue(selector_name)})) AS gaps`);
    }
    else if (group_by.has("selector")) {
        measures.push(`COUNTIF(regression) AS regressions`);
        measures.push(`COUNTIF(gap) AS gaps`);
    }
    else {
        measures.push("COUNTIF(regression_count > 0) AS regressions");
        measures.push("COUNTIF(gap_count > 0) AS gaps");
    }
    measures.push("COUNTIF(screenshot_class='blocked') AS blocked");
    measures.push("COUNTIF(screenshot_class='modal') AS modal");
    measures.push("udf.array_agg_to_string(ARRAY_CONCAT_AGG(ARRAY(SELECT selector_name FROM UNNEST(selectors) WHERE regression)), ',') AS regression_selectors");
    measures.push("STRING_AGG(DISTINCT capture_engine ORDER BY capture_engine) AS capture_engine");

    return measures;
}

function createWhereClause({ app, account_key, country_code, seller_id, sku }: QueryMetricsOptions): string[] {
    const where = [
        `app_name=${bigquery.safeValue(app)}`
    ];

    if (account_key)
        where.push(`account_key=${bigquery.safeValue(account_key.toLowerCase())}`);

    if (country_code)
        where.push(`country_code=${bigquery.safeValue(country_code.toUpperCase())}`);

    if (seller_id)
        where.push(`seller_id=${bigquery.safeValue(seller_id)}`);

    if (sku)
        where.push(`REGEXP_CONTAINS(sku, r${bigquery.safeValue(sku)})`);

    return where;
}
