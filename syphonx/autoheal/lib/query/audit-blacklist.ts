import * as bigquery from "../bigquery.js";

export const blacklist_reason_codes = [
    "blocked-captcha",
    "blocked-degraded",
    "blocked-message",
    "censored",
    "error-screenshot",
    "marketplace",
    "timeout-screenshot"
];

export interface BlacklistRecord {
    seller_name: string;
    reason_code: string;
}

export async function queryBlacklist(): Promise<BlacklistRecord[]> {
    const rows = await bigquery.query<BlacklistRecord>("SELECT seller_name, reason_code FROM syphonx.page_selector_audit_black_list ORDER BY 1");
    return rows;
}

export async function insertBlacklist(data: { seller_name: string, reason_code: string }): Promise<void> {
    await bigquery.insert("syphonx.page_selector_audit_black_list", data);
}
