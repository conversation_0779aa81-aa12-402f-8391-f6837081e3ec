import * as bigquery from "../bigquery.js";

export interface SyphonXAutoselectResult {
    domain_name: string;
    selector_name: string;
    selector: string;
    timetamp?: Date;
    disabled?: boolean;
}

export interface QuerySyphonXAutoselectOptions {
    domain_name?: string;
    selector_name?: string;
}

export async function querySyphonXAutoselect({ domain_name, selector_name }: QuerySyphonXAutoselectOptions = {}): Promise<SyphonXAutoselectResult[]> {
    const where = [];
    if (domain_name)
        where.push(`domain_name=${bigquery.safeValue(domain_name)}`);
    if (selector_name)
        where.push(`selector_name=${bigquery.safeValue(selector_name)}`);

    const query = `SELECT * FROM syphonx.autoselect${where.length > 0 ? ` WHERE ${where.join(" AND ")}` : ""}`;
    const rows = await bigquery.query(query);
    return rows;
}

export async function insertSyphonXAutoselect(obj: SyphonXAutoselectResult) {
    await bigquery.insert("syphonx.autoselect_log", {
        ...obj,
        timestamp: new Date()
    })
}
