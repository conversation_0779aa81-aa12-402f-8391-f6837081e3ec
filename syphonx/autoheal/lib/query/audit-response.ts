import * as bigquery from "../bigquery.js";
import { ProductPageSelector } from "./common.js";

export interface QueryAuditResponsesOptions {
    all?: boolean;
}

export interface AuditResponse extends AuditCapture {
    audit_id: string;
    audit_date: Date;
    page_id: string;
    audit_status: string;
    audit_context: string;
    audit_url: string;
    audit_duration: number;
    audit_comment: string;
    audit_result: AuditResult[];
    audit_count: number;
    html: string;
    audited_by: string;
    audited_at: Date;
    requested_by: string;
    requested_at: string;
    priority: number;
    ttl: number;
}

export interface AuditResult {
    selector_name: string;
    selector_path: string;
    selector_status: string;
    selector_comment: string;
    selector_html: string;
    selector_text: string;
    alt_clicks: string[];
    html_linenums: number[];
    screenshot: string;
    screenshot_rect: {};
    selector_rect: TargetRect;
    selector_metadata: TargetRect[];
}

export interface TargetRect {
    x: number;
    y: number;
    left: number;
    right: number;
    top: number;
    bottom: number;
}

// deprecated, use ProductPage instead
export interface AuditCapture {
    name: string;
    domain_name: string;
    account_key: string;
    country_code: string;
    sku: string;
    seller_name: string;
    seller_id: number;
    capture_id: string;
    capture_date: Date;
    capture_url: string;
    capture_engine: string;
    html_url: string;
    screenshot_url: string;
    screenshot_class: PageClassification;
    template_path: string;
    template_last_modified_at: Date;
    page_id: string;
    alert_count: number;
    regression_count: number;
    gap_count: number;
    selectors: ProductPageSelector[];
    template: TemplateInfo;
    href?: string;
    status?: string;
    predicted?: boolean;
}

export interface TemplateInfo {
    created_at: Date;
    created_by: string;
    modified_at: Date;
    modified_by: string;
    revision_count: number;
    selectors_changed: string;
}

export async function queryAuditResponses({
    all
}: QueryAuditResponsesOptions = {}): Promise<AuditResponse[]> {
    const query = "SELECT * FROM syphonx.audit_responses";
    const rows = await bigquery.query<AuditResponse>(query);
    for (const row of rows)
        for (const audit_result of row.audit_result)
            if (typeof audit_result.selector_metadata === "string")
                Object.assign(audit_result, JSON.parse(audit_result.selector_metadata));
    return rows;
}