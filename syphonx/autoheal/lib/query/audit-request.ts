import * as bigquery from "../bigquery.js";

export type AuditContext = "regressions" | "gaps" | "alerts" | "all";

export interface AuditRequest {
    audit_id: string;
    audit_date: string;
    selectors: string;
    account_key?: string;
    country_code?: string;
    seller_id?: string;
    page_id?: string;
    audit_count: number;
    audit_context: AuditContext;
    priority?: number;
    ttl?: number;
    requested_at: Date;
    requested_by: string;
}

export interface QueryCreateAuditOptions {
    app?: string;
    account_key?: string[];
    country_code?: string[];
    seller_id?: number[];
    selectors?: string[];
    audit_context: AuditContext;
    engine?: string;
    sku?: string;
}

export interface QueryCreateAuditResult {
    page_id: string;
    date: Date;
}

export async function queryCreateAudit({
    app,
    account_key,
    country_code,
    seller_id,
    selectors,
    audit_context,
    engine,
    sku
}: QueryCreateAuditOptions): Promise<QueryCreateAuditResult[]> {
    const where = [
        `app_name=${bigquery.safeValue(app)}`
    ];

    if (account_key)
        where.push(`account_key IN (${account_key.map(key => bigquery.safeValue(key)).join(", ")})`);
    if (country_code)
        where.push(`country_code IN (${country_code.map(key => bigquery.safeValue(key)).join(", ")})`);
    if (seller_id)
        where.push(`seller_id IN (${seller_id.map(key => bigquery.safeValue(key)).join(", ")})`);

    if (audit_context === "regressions" && !selectors)
        where.push("regression_count > 0");
    if (audit_context === "regressions" && selectors)
        where.push(`(SELECT COUNTIF(regression) FROM UNNEST(selectors) WHERE selector_name IN (${selectors?.map(selector => bigquery.safeValue(selector)).join(",")})) > 0`);

    if (audit_context === "gaps" && !selectors)
        where.push("gap_count > 0");
    if (audit_context === "gaps" && selectors)
        where.push(`(SELECT COUNTIF(gap) FROM UNNEST(selectors) WHERE selector_name IN (${selectors?.map(selector => bigquery.safeValue(selector)).join(",")})) > 0`);

    if (audit_context === "alerts" && !selectors)
        where.push("alert_count > 0");
    if (audit_context === "alerts" && selectors)
        where.push(`(SELECT COUNTIF(alert IS NOT NULL) FROM UNNEST(selectors) WHERE selector_name IN (${selectors?.map(selector => bigquery.safeValue(selector)).join(",")})) > 0`);

    if (engine && (app && ![ 'wtb', 'prowl', ].includes(app)))
        where.push(`capture_engine=${bigquery.safeValue(engine)}`);

    if (sku) 
        where.push(`REGEXP_CONTAINS(sku, r${bigquery.safeValue(sku)})`);

    const query = `
SELECT page_id, date
FROM conflux.product_page_daily
WHERE ${where.join("\nAND ")}
`.trim();

    const rows = await bigquery.query(query);
    return rows;
}

export async function queryAuditRequests({
    account_key,
    country_code,
    seller_id,
    selectors,
    audit_context,
    audit_date
}: Partial<AuditRequest>): Promise<AuditRequest[]> {
    const where = [
        `audit_date=${bigquery.safeValue(audit_date)}`,
        account_key ? `account_key=${bigquery.safeValue(account_key)}` : "account_key IS NULL",
        country_code ? `country_code=${bigquery.safeValue(country_code)}` : "country_code IS NULL",
        seller_id ? `seller_id=${bigquery.safeValue(seller_id)}` : "seller_id IS NULL",
        selectors ? `selectors=${bigquery.safeValue(selectors)}` : "selectors IS NULL",
        `audit_context=${bigquery.safeValue(audit_context)}`
    ];
    const rows = await bigquery.query(`SELECT * FROM syphonx.audit_request_log WHERE ${where.join("\nAND ")}`);
    return rows;
}

export async function createAuditRequest(request: AuditRequest): Promise<void> {
    await bigquery.insert("syphonx.audit_request_log", request);
}
