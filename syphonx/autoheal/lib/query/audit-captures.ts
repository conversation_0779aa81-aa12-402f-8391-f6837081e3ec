import * as bigquery from "../bigquery.js";
import { ProductPage } from "./common.js";

export interface QueryAuditCapturesOptions {
    app?: string;
    account_key?: string;
    country_code?: string;
    seller_id?: number;
    selector_name?: string;
    regressions?: number;
    gaps?: number;
    engine?: string;
    sku?: string;
    limit?: number;
}

export async function queryAuditCaptures({
    app,
    account_key,
    country_code,
    seller_id,
    selector_name,
    regressions,
    gaps,
    limit,
    engine,
    sku
}: QueryAuditCapturesOptions = {}): Promise<ProductPage[]> {
    const where = [
      `app_name=${bigquery.safeValue(app)}`
    ];

    if (account_key)
        where.push(`account_key=${bigquery.safeValue(account_key.toLowerCase())}`);

    if (country_code)
        where.push(`country_code=${bigquery.safeValue(country_code.toUpperCase())}`);

    if (seller_id)
        where.push(`seller_id=${bigquery.safeValue(seller_id)}`);

    if (regressions && !selector_name)
        where.push(`regression_count>=${bigquery.safeValue(regressions)}`);

    if (regressions && selector_name)
        where.push(`(SELECT COUNTIF(regression) FROM UNNEST(selectors) WHERE selector_name=${bigquery.safeValue(selector_name)}) > 0`);

    if (gaps && !selector_name)
        where.push(`gap_count>=${bigquery.safeValue(gaps)}`);

    if (gaps && selector_name)
        where.push(`(SELECT COUNTIF(gap) FROM UNNEST(selectors) WHERE selector_name=${bigquery.safeValue(selector_name)}) > 0`);

    if (!regressions && !gaps)
        where.push("(regression_count>0 OR gap_count>0)");

    if (engine && (app && ![ 'wtb', 'prowl', ].includes(app)))
        where.push(`capture_engine=${bigquery.safeValue(engine)}`);

    if (sku) 
        where.push(`REGEXP_CONTAINS(sku, r${bigquery.safeValue(sku)})`);

    const query = `
SELECT *
FROM conflux.product_page_daily
WHERE ${where.join("\nAND ")}
LIMIT ${limit ? bigquery.safeValue(limit) : "100"}
`.trim();

    const rows = await bigquery.query<ProductPage>(query);
    return rows;
}
