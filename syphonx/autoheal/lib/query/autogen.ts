import * as bigquery from "../bigquery.js";

export interface AutogenRequest {
    autogen_id: string;
    reqeusted_at: Date;
    country_code: string;
    domain_name: string;
    url: string;
    selector_profile: string; // new
    autogen_profile: string; // new
    profile: string; // deprecated
    template_path: string;
    mode: string;
    priority: number;
}

export interface QueryAutogenRequests {
    status?: string;
    limit?: number;
}

export async function queryAutogenRequests({ status, limit }: QueryAutogenRequests = {}): Promise<AutogenRequest[]> {
    const query = `SELECT * FROM syphonx.autogen_view WHERE status${status ? `=${bigquery.safeValue(status)}` : " IS NULL"}${limit ? ` LIMIT ${bigquery.safeValue(limit)}` : ""}`;
    const rows = await bigquery.query(query);
    return rows;
}

export async function queryAutogenRequest(autogen_id: string): Promise<AutogenRequest> {
    const query = `SELECT * FROM syphonx.autogen_view WHERE autogen_id=${bigquery.safeValue(autogen_id)}`;
    const [row] = await bigquery.query(query);
    return row;
}

export async function resetAutogenRequests(autogen_ids: string[]): Promise<AutogenRequest> {
    const query = `SELECT * FROM syphonx.autogen_view WHERE autogen_id IN (${autogen_ids.map(autogen_id => bigquery.safeValue(autogen_id)).join(",")})`;
    const [row] = await bigquery.query(query);
    return row;
}

export interface PostAutogenLog {
    autogen_id?: string;
    autogen_ids?: string[];
    status: string | null;
    data?: unknown;
}

export async function postAutogenStatus({ autogen_id, autogen_ids, status, data }: PostAutogenLog): Promise<void> {
    if (!autogen_ids && autogen_id)
        autogen_ids = [autogen_id];
    if (!autogen_ids)
        throw new Error("Invalid input");
    const rows = autogen_ids.map(autogen_id => ({
        autogen_id,
        status,
        timestamp: new Date,
        data: data ? JSON.stringify(data) : undefined
    }));
    await bigquery.insert("syphonx.autogen_log", rows);
}
