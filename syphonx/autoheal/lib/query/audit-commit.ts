import * as bigquery from "../bigquery.js";

export interface AuditCommit {
    commit_id: string;
    commit_date: Date;
    audit_id?: string;
    analyze_url?: string;
    template_path: string;
    template_hash?: number;
    commit: AuditCommitSelector[];
}

export interface AuditCommitSelector {
    selector_name: string;
    before?: string;
    after?: string;
    comment?: string;
}

export async function createAuditCommit(commit: AuditCommit): Promise<void> {
    const date = `${commit.commit_date.getFullYear()}-${commit.commit_date.getMonth() + 1}-${commit.commit_date.getDate()}`;
    await bigquery.insert("syphonx.template_commit_log", { ...commit, date });
}
