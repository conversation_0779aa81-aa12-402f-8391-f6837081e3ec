import * as bigquery from "../bigquery.js";
import { AnalyzeResult } from "../index.js";

export interface AutoselectPage {
    page_id: string;
    app_name: string;
    account_key: string;
    country_code: string;
    sku: string;
    group_name: string;
    group_id: string;
    seller_name: string;
    seller_id: number;
    domain_name: string;
    capture_status: string;
    capture_id: string;
    capture_date: Date;
    capture_url: string;
    content_url: string;
    last_scanned_at: Date;
}

export type AutoselectFields = "domain_name" | "seller_id" | "seller_name" | "page_id" | "capture_url";

export interface QueryAutoselectOptions {
    app_name: string;
    account_keys: string[];
    brand_name: string;
    country_codes: string[];
    skus: string[];
    domain_names: string[];
    seller_ids: number[];
    exclude: string[];
    capture_status: string[];
    fields: AutoselectFields[];
    limit: number;
    subset: number;
    random: boolean;
    force: boolean;
}

export interface PnuPage {
    page_id: string;
    app_name: string;
    seller_id: number;
    seller_domain: string;
    capture_id: string;
    lda: string;
    status: string;
    capture_url: string;
    screenshot_url: string;
    html_url: string;
    capture_engine: string;
    name: string;
    price: string;
    availability: string;
    review_score: string;
    review_count: string;
}

export async function queryAutoselect(options: Partial<QueryAutoselectOptions>) {
    const {
        fields,
        subset,
        random,
        limit
    } = options;
    const params: Record<string, unknown> = {};

    let query = !fields ? "SELECT *" : `SELECT ${fields.join(", ")}`;
    query += `\nFROM omega.page_scans_input\nWHERE ${where(options, params)}`;
    if (subset)
        query += `\nQUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name ORDER BY capture_date DESC) <= ${bigquery.safeValue(subset)}`;

    if (random)
        query += "\nORDER BY RAND()";
    else
        query += "\nORDER BY app_name, seller_name, account_key, country_code, sku";

    if (limit)
        query += `\nLIMIT ${bigquery.safeValue(limit)}`;

    const rows = await bigquery.query(query, params);
    return rows as AutoselectPage[];
}

export async function queryAutoselectByDomain(options: Partial<QueryAutoselectOptions>): Promise<Array<{ domain_name: string, n: number }>> {
    const params: Record<string, unknown> = {};
    const query = `
SELECT domain_name, COUNT(*) AS n
FROM omega.page_scans_input
WHERE ${where(options, params)}
GROUP BY 1
ORDER BY 2 DESC`.trim();
    const rows = await bigquery.query(query, params);
    return rows as Array<{ domain_name: string, n: number }>;
}

export async function queryAutoselectByDomainDictionary(options: Partial<QueryAutoselectOptions>): Promise<Record<string, number>> {
    const result: Record<string, number> = {};
    const rows = await queryAutoselectByDomain(options);
    for (const { domain_name: key, n } of rows)
        result[key] = n;
    return result;
}

export interface AutoselectLog {
    autoselect_id: string;
    domain_name: string;
    url: string;
    source: string;
    status: string;
    error?: string;
    page_class?: string;
    page_data?: {};
    report_url?: string;
    selectors?: Array<{
        selector_name: string;
        selector: string;
        attr?: string;
        filter?: string;
    }>;
    analyze?: AnalyzeResult[];
}

export async function insertAutoselectLog(data: AutoselectLog): Promise<void> {
    const { page_data, ...obj } = data;
    await bigquery.insert("omega.autoselect_log", {
        ...obj,
        page_data: JSON.stringify(page_data),
        timestamp: new Date()
    });
}

function where(options: Partial<QueryAutoselectOptions>, params: Record<string, unknown>): string {
    const {
        app_name,
        account_keys,
        brand_name,
        country_codes,
        skus,
        domain_names,
        seller_ids,
        exclude,
        capture_status,
        force,
    } = options;

    const where = ["content_url IS NOT NULL"];

    if (app_name)
        where.push(`app_name=${bigquery.safeValue(app_name)}`);

    if (account_keys && account_keys.length > 0)
        where.push(`account_key IN (${account_keys.map(key => bigquery.safeValue(key)).join(",")})`);

    if (brand_name) {
        params.brand = brand_name;
        where.push(`group_name=@brand`);
    }

    if (country_codes && country_codes.length > 0)
        where.push(`country_code IN (${country_codes.map(key => bigquery.safeValue(key.toUpperCase())).join(",")})`);

    if (skus && skus.length > 0)
        where.push(`sku IN (${skus.map(key => bigquery.safeValue(key)).join(",")})`);

    if (domain_names && domain_names.length > 0)
        where.push(`domain_name IN (${domain_names.map(key => bigquery.safeValue(key)).join(", ")})`);

    if (seller_ids && seller_ids.length > 0)
        where.push(`seller_id IN (${seller_ids.map(key => bigquery.safeValue(key)).join(", ")})`);

    if (exclude && exclude.length > 0)
        where.push(`domain_name NOT IN (${exclude.map(name => bigquery.safeValue(name)).join(",")})`);

    if (capture_status)
        where.push(`capture_status IN (${capture_status.map(key => bigquery.safeValue(key)).join(", ")})`);

    if (!force)
        where.push("(last_scanned_at IS NULL OR last_scanned_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 14 DAY))");

    return where.join("\nAND ");
}

export async function queryPnuPages(domain: string, limit?: number): Promise<PnuPage[]> {
    const params: Record<string, unknown> = {};
    let query = `
        SELECT DISTINCT
            page_id,
            app_name,
            seller_id,
            seller_domain,
            capture_id,
            lda,
            status,
            capture_url,
            screenshot_url,
            html_url,
            capture_engine,
            name,
            price,
            availability,
            review_score,
            review_count
        FROM conflux.pnu_crawl_log
        WHERE seller_domain = @domain
        QUALIFY ROW_NUMBER() OVER (PARTITION BY capture_url ORDER BY capture_id DESC) = 1
    `;
    params.domain = domain;

    if (limit)
        query += `\nLIMIT ${bigquery.safeValue(limit)}`;

    const rows = await bigquery.query(query, params);
    return rows as PnuPage[];
}
