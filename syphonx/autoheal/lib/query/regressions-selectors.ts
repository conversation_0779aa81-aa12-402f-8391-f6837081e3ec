import * as bigquery from "../bigquery.js";
import { ProductPage } from "./common.js";

export interface QueryTargetSelectorRegressionsOptions {
    app_name: string;
    selector_name: string;
    max?: number;
    country_code?: string;
    seller_id?: number;
    sku?: string;
    capture_age_days?: number;
    diagnose?: boolean;
}

export interface QueryTargetSelectorRegressionsResult extends ProductPage {
    domain_name: string;
    template_last_modified_date: Date;
    template_last_selectors_changed: string;
    template_revision_count: number;
    target_selector: TargetSelector;
}

export interface TargetSelector {
    selector_name: string;
    selector_json_value: string;
    capture_id: string;
    capture_date: Date;
}

export async function queryTargetSelectorRegressions({
    app_name,
    selector_name,
    country_code,
    seller_id,
    sku,
    capture_age_days,
    diagnose
}: QueryTargetSelectorRegressionsOptions): Promise<QueryTargetSelectorRegressionsResult[]> {
    const dimensions = [
        "app_name",
        "page_id",
        "account_key",
        "country_code",
        "sku",
        "NET.REG_DOMAIN(capture_url) AS domain_name",
        "IF(NOT ENDS_WITH(seller_name, ')'), CONCAT(seller_name, ' (', country_code, ')'), seller_name) AS seller_name",
        "seller_id",
        "capture_url",
        "capture_engine",
        "capture_status",
        "regression_count",
        "template_path",
        "COALESCE(template.modified_at, template.created_at) AS template_last_modified_date",
        "template.selectors_changed AS template_last_selectors_changed",
        "template.revision_count AS template_revision_count",
        `(SELECT AS STRUCT selector_name, reference.selector_json_value, reference.capture_id, reference.capture_date FROM UNNEST(selectors) WHERE selector_name=${bigquery.safeValue(selector_name)} LIMIT 1) AS target_selector`
    ];

    if (diagnose) {
        dimensions.push("selectors");
        dimensions.push("last_attempt");
        dimensions.push("last_capture");
    }
    else {
        dimensions.push("STRUCT(last_capture.capture_id, last_capture.capture_date, last_capture.screenshot_class, last_capture.screenshot_url) AS last_capture");
    }

    const where = [
        `last_capture.capture_id IS NOT NULL`,
        `app_name=${bigquery.safeValue(app_name)}`,
        `(SELECT COUNT(*) FROM UNNEST(selectors) WHERE selector_name=${bigquery.safeValue(selector_name)} AND regression)>0`
    ];

    if (country_code)
        where.push(`country_code=${bigquery.safeValue(country_code).toUpperCase()}`);

    if (seller_id)
        where.push(`seller_id=${bigquery.safeValue(seller_id)}`);

    if (sku) 
        where.push(`REGEXP_CONTAINS(sku, r${bigquery.safeValue(sku)})`);

    if (capture_age_days)
        where.push(`TIMESTAMP_DIFF(CURRENT_TIMESTAMP(), last_capture.capture_date, DAY)<=${bigquery.safeValue(capture_age_days)}`);

    const query = `
SELECT
  ${dimensions.join(",\n  ")}
FROM conflux.product_page_daily
WHERE ${where.join("\nAND ")}
ORDER BY domain_name, last_capture.capture_date DESC;
`.trim();

    const rows = await bigquery.query<QueryTargetSelectorRegressionsResult>(query);
    return rows;
}
