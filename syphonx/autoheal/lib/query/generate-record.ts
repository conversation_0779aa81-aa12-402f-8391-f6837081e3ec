import {
    bigquery,
    summarize,
    Analyze<PERSON><PERSON>ult,
    Timer
} from "../index.js";

export interface GenerateSelectorRecord {
    audit_id: string;
    page_id: string;
    selector_name: string;
}

export class GenerateSelectorRecordCreator implements GenerateSelectorRecord {
    audit_id: string;
    page_id: string;
    selector_name: string;
    analyze: AnalyzeResult[];
    private timer: Timer;

    constructor(params: GenerateSelectorRecord) {
        this.audit_id = params.audit_id;
        this.page_id = params.page_id;
        this.selector_name = params.selector_name;
        this.analyze = [];
        this.timer = new Timer();
   }

    reset(): void {
        this.analyze = [];
        this.timer = new Timer();
    }

    async save({ generated_selector, message, error_code }: { generated_selector?: string, message?: string, error_code?: string }): Promise<void> {
        const { timer, ...data } = this;

        data.analyze = data.analyze.map(obj => {
            const { tokens, elapsed, ...data } = obj;
            return {
                ...data,
                usage: tokens,
                duration: elapsed
            } as unknown as Analyze<PERSON><PERSON>ult
        }); // SHIM
        const { cost } = summarize(data.analyze); // SHIM
        await bigquery.insert("syphonx.page_selector_generate", {
            ...data,
            generated_selector,
            generated_at: new Date(),
            error_code,
            message,
            duration: timer.elapsed(),
            cost
        });
    }
}
