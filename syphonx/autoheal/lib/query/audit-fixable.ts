import * as bigquery from "../bigquery.js";

export interface QueryAuditFixableOptions {
    app_name: string;
    seller_id?: number;
    days?: number;
}

export interface QueryAuditFixableResult {
    page_id: string;
    audit_id: string;
    audit_code: string;
    audit_url: string;
    audit_message: string;
    audited_at: Date;
    selector_name: string;
    domain_name: string;
    seller_name: string;
    seller_id: number;
    country_code: string;
    template_path: string;
    last_generated_at: Date;
    last_generated_result: string;
    selector_json_value: string;
    reference_json_value: string;
    screenshot_url: string;
}

export async function queryAuditFixable({ app_name, seller_id, days }: QueryAuditFixableOptions): Promise<QueryAuditFixableResult[]> {
    const where = [
        "a.audit_code IN ('regression-confirmed', 'selector-not-found')",
        "a.audit_url IS NOT NULL"
    ];

    if (app_name)
        where.push(`a.app_name=${bigquery.safeValue(app_name)}`);

    if (seller_id)
        where.push(`a.seller_id=${bigquery.safeValue(seller_id)}`);

    if (days)
        where.push(`a.audited_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL ${bigquery.safeValue(days)} DAY)`);

    const query = `
WITH last_generated AS (
  SELECT
    audit_id,
    generated_at AS last_generated_at,
    IFNULL(error_code, 'ok') AS last_generated_result
  FROM syphonx.page_selector_generate
  QUALIFY ROW_NUMBER() OVER (PARTITION BY audit_id ORDER BY generated_at DESC) = 1
)
SELECT
  a.page_id,
  a.audit_id,
  a.audit_code,
  a.audit_url,
  a.audit_message,
  a.audited_at,
  a.selector_name,
  NET.REG_DOMAIN(a.audit_url) AS domain_name,
  a.seller_name,
  a.seller_id,
  a.country_code,
  b.template_path,
  c.last_generated_at,
  c.last_generated_result,
  (
    SELECT AS STRUCT
      selector_json_value,
      reference.selector_json_value AS reference_json_value
    FROM UNNEST(b.selectors) AS c
    WHERE c.selector_name=a.selector_name
    LIMIT 1
  ).*,
  b.last_capture.screenshot_url
FROM syphonx.page_selector_audits AS a
JOIN conflux.product_page_daily AS b USING (page_id)
LEFT JOIN last_generated AS c USING (audit_id)
WHERE ${where.join("\nAND ")}
ORDER BY a.audited_at DESC
`.trim();

    const rows = await bigquery.query(query);
    return rows;
}
