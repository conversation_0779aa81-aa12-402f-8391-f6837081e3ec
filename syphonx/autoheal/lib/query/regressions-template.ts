import * as bigquery from "../bigquery.js";

export async function queryTemplatePathFromUrl(url: string, app_name: string): Promise<string> {
    const query = `SELECT DISTINCT template_path FROM conflux.product_page_daily WHERE app_name=${bigquery.safeValue(app_name)} AND capture_url=${bigquery.safeUrl(url)}`;
    const rows = await bigquery.query(query);
    const [{ template_path }] = rows;
    return template_path;
}
