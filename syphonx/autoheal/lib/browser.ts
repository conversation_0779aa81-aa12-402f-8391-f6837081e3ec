import { createVirt<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, BrowserCreateOptions, PageScreenshotOptions } from "./virtual-browser/index.js";
import { sleep } from "./utilities.js";
import { sanitizeHtml } from "./scripts/index.js";

import { addLabels as addLabelsScript, removeLabels as removeLabelsScript } from "./scripts/labels.js";
import pretty from "pretty";
import chalk from "chalk";

export type WaitFor = "load" | "domcontentloaded" | "networkidle";
export { Browser, Page } from "./virtual-browser/index.js";

export interface OpenPageOptions extends BrowserCreateOptions {
    url?: string;
    html?: string;
    headless?: boolean;
    timeout?: number;
    waitfor?: WaitFor;
    scroll?: boolean;
    viewport?: { width: number, height: number };
}

export interface OpenPageResult {
    browser: Browser;
    page: Page;
    timeout: boolean;
    error?: string;
    ok: boolean;
}

let once = false;
export async function openPage({ url, html, waitfor, headless, viewport, shared, scroll = true, ...options }: OpenPageOptions = {}): Promise<OpenPageResult> {
    if (!url && !html)
        throw new Error("Either url or html must be specified");
    const proxy = process.env.PROXY_SERVER ? {
        server: process.env.PROXY_SERVER,
        username: process.env.PROXY_USER,
        password: process.env.PROXY_PASSWORD
    } : undefined;

    if (!once) {
        if (process.env.VERBOSE)
            console.log(chalk.gray(`Using ${process.env.BROWSER || "playwright"} browser with ${proxy ? `proxy ${process.env.PROXY_SERVER}`: "no proxy"}`));
        once = true;
    }

    const browser = await createVirtualBrowser({ headless, shared, proxy, viewport });
    const page = await browser.newPage();
    const result: OpenPageResult = {
        browser,
        page,
        timeout: false,
        ok: false
    };
    try {
        if (html) {
            if (url && !url.startsWith("data:")) {
                try {
                    await page.goto(url, { timeout: 100 });
                } catch (e) {
                    await page.goto("data:text/html,");
                }
            } else {
                await page.goto("data:text/html,");
            }
            await page.setContent(html);

            if (url && !url.startsWith("data:")) {
                await page.evaluate((actualUrl) => {
                    window.history.pushState({}, '', actualUrl);
                }, url);
            }
        }
        else if (url) {
            await page.goto(url, { timeout: options.timeout });
        }
        if (waitfor)
            await page.waitForLoadState(waitfor, { timeout: options.timeout });
        if (scroll)
            await scrollPage(page);
    }
    catch (err: any) {
        if (err.name === "TimeoutError")
            result.timeout = true;
        else if (err.message.includes("Execution context was destroyed"))
            await sleep(2000); // sometimes happens as a result of scrolling the page, just wait a little longer instead
        else
            return { ...result, ok: false, error: err instanceof Error ? err.message : JSON.stringify(err) };
    }
    result.ok = true;
    return result as OpenPageResult;
}

/**
 * @deprecated use openPage instead
 */
export async function openPageHtml(html: string, { scroll, waitfor, ...options }: OpenPageOptions = {}): Promise<Page> {
    const browser = await createVirtualBrowser(options);
    const page = await browser.newPage();
    await page.goto("data:text/html,");
    await page.setContent(html);
    if (waitfor)
        await page.waitForLoadState(waitfor, { timeout: options.timeout });
    if (scroll)
        await scrollPage(page);
    return page;
}

export async function addLabels(page: Page, css?: string): Promise<[string, number]> {
    const n = await page.evaluate(addLabelsScript, css);
    await sleep(1000);
    let html = await page.content();
    html = sanitizeHtml(html);
    html = html.replace(/<div\s+class="__wrapper__"[^>]*>\s*(<img[^>]+>)\s*<span[^>]+>[^<]+<\/span>\s*<\/div>/g, "$1"); // unwrap all labelled images  <div class="__wrapper__"><img><span>[...]</span></div> -> <img>
    html = pretty(html);
    html = html.replace(/\n{2,}/g, "\n") // collapse 2+ newlines to a single newline
    return [html, n];
}

export async function removeLabels(page: Page): Promise<void> {
    await page.evaluate(removeLabelsScript);
    await sleep(1000);
}

export async function tryPageScreenshot(page: Page, options?: PageScreenshotOptions): Promise<Buffer | undefined> {
    //let retries = 0;
    //while (true) {
        try {
            const buffer = await page.screenshot(options);
            return buffer;
        }
        catch (err: any) {
            //if (err.name === "TimeoutError" && ++retries <= 1) {
                //console.log(chalk.yellow(`screenshot timeout, retry #${retries}`));
                //continue;
            //}
            return undefined;
        }
    //}
}

export interface ViewHtmlOptions {
    html: string;
    zoom?: number;
}

export async function viewHtml({ html, zoom }: ViewHtmlOptions): Promise<void> {
    const browser = await createVirtualBrowser({ headless: false });
    const page = await browser.newPage();
    await page.setContent(html);
    if (zoom)
        await page.evaluate(`document.body.style.zoom=${zoom}`);
}

async function scrollPage(page: Page): Promise<void> {
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await sleep(500);
    await page.evaluate(() => window.scrollTo(0, 0));
    await sleep(500);
}
