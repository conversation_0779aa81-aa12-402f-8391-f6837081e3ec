import { sleep } from "./index.js";

/**
 * A concurrency-limiting throttle with built-in rate limiting and an optional cooldown mechanism.
 * 
 * This class ensures that only one operation runs at a time (serial execution). Additionally, it 
 * enforces a maximum number of calls (the "quota") within a given time window (the "interval"). 
 * If the quota is reached, subsequent calls wait until the rate-limit window frees up. The 
 * cooldown method similarly pauses **all** subsequent calls for a specified duration.
 *
 * @remarks
 * - All calls to {@link Throttle.apply} or {@link Throttle.cooldown} are queued and execute in a single lane (one at a time).
 * - If the throttle is already "busy" then further calls wait for the current task to finish.
 * - When the quota is reached, new calls wait until enough time has passed to free up slots.
 * - Calling {@link Throttle.cooldown} blocks every new call for the specified duration.
 */
export class Throttle {
    private timestamps: number[] = [];
    private mutex: Promise<void> = Promise.resolve();
    private readonly quota: number;
    private readonly interval: number;
    private busy = false;

    constructor(quota: number, interval = 1000) {
        this.quota = quota;
        this.interval = interval;
    }

    async apply(): Promise<void> {
        if (this.busy)
            await this.mutex;

        this.busy = true;
        this.mutex = this.mutex.then(async () => {
            // Remove timestamps out of the interval
            let now = Date.now();
            while (this.timestamps.length && this.timestamps[0] <= now - this.interval)
                this.timestamps.shift();

            // If we've hit the limit, wait until the oldest timestamp expires
            if (this.timestamps.length >= this.quota) {
                const diff = this.timestamps[0] + this.interval - now;
                await sleep(diff);
                
                // After waiting remove timestamps out of the interval again
                now = Date.now();
                while (this.timestamps.length && this.timestamps[0] <= now - this.interval)
                    this.timestamps.shift();
            }

            this.timestamps.push(now);
        });
        await this.mutex;
        this.busy = false;
    }

    async cooldown(ms: number): Promise<void> {
        if (this.busy)
            await this.mutex;
        this.busy = true;
        this.mutex = this.mutex.then(async () => {
            await sleep(ms);
            this.busy = false;
        });
        await this.mutex;
    }
}