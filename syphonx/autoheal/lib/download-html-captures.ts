import { SingleBar, Presets } from "cli-progress";
import { downloadHtmlFilesToCache } from "./index.js";

export interface DownloadHtmlCapturesRow {
    capture_url: string;
    html_url: string;
}

const concurrency = parseInt(process.env.CONFURRENCY || "1");

export async function downloadHtmlCaptures(rows: DownloadHtmlCapturesRow[]): Promise<Record<string, string>> {
    console.log("downloading html captures...");
    const html_urls: Array<[string, string]> = rows.map(row => [row.html_url, row.capture_url]);
    const progress = new SingleBar({}, Presets.shades_classic);
    progress.start(html_urls.length, 0);
    const [html_cache, errors] = await downloadHtmlFilesToCache(html_urls, () => progress.increment(), concurrency); //todo: pass outDir
    progress.stop();
    console.log(`${Object.keys(html_cache).length} files downloaded, ${errors.length} errors`);
    return html_cache;
}
