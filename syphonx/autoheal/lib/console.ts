import readline from "readline";

export interface KeypressEvent {
    name: string;
    ctrl: boolean;
    meta: boolean;
    shift: boolean;
}

export function waitForKeypress(): Promise<KeypressEvent> {
    return new Promise<KeypressEvent>(resolve => {
        const r = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        readline.emitKeypressEvents(process.stdin);
        if (process.stdin.isTTY) process.stdin.setRawMode(true);

        process.stdin.once("keypress", (_, key) => {
            if (key && key.ctrl && key.name === 'c')
                process.stdin.setRawMode(false);
            r.close();
            resolve(key);
        });
    });
}
