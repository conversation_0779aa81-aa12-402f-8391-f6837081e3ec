import {
    addLabels,
    openPage,
    renderScreenshot,
    sleep,
    OpenPageOptions,
    RenderScreenshotOptions,
    RenderScreenshotResult
} from "./index.js";

type CombinedOptions = OpenPageOptions & RenderScreenshotOptions;

export interface CreateScreenshotOptions extends CombinedOptions {
    labels?: boolean;
    snooze?: number;
}

export interface ScreenshotResult extends Partial<RenderScreenshotResult> {
    html: string;
}

export interface TryScreenshotResult extends Partial<ScreenshotResult> {
    ok: boolean;
    error?: string;
    timeout?: boolean;
}

export async function createScreenshot(url: string, { labels, snooze, ...options }: CreateScreenshotOptions = {}): Promise<ScreenshotResult> {
    const { page } = await openPage({ url, ...options });
    if (snooze)
        await sleep(snooze * 1000);
    try {
        if (labels)
            await addLabels(page);
        const result = await renderScreenshot(page, options);
        const html = await page.evaluate(() => document.querySelector("*")!.outerHTML);
        await page.close();
        return { ...result, html };
    }
    finally {
        await page.close();
    }
}

export async function tryCreateScreenshot(url: string, options: CreateScreenshotOptions): Promise<TryScreenshotResult> {
    try {
        const result = await createScreenshot(url, options);
        return { ...result, ok: true };
    }
    catch (err) {
        return {
            ok: false,
            error: err instanceof Error ? err.message : JSON.stringify(err),
            timeout: err instanceof Error && err.name === "TimeoutError"
        };
    }
}
