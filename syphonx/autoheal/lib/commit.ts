import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";
import * as syphonx from "syphonx-lib";

import {
    bigquery,
    createAuditCommit,
    generateUniqueId,
    loadTemplate,
    saveTemplate,
    updateTemplate
}
from "./index.js";
import { sleep } from "async-parallel";

export interface CommitOptions {
    template_path: string;
    updates: SelectorUpdate[];
    analyze_url: string;
}

export interface SelectorUpdate {
    selector_name: string;
    selector: string;
    query?: syphonx.SelectQuery;
}

export async function commit({ template_path, updates, analyze_url }: CommitOptions): Promise<string | undefined> {
    const { template } = await loadTemplate(template_path);

    const commit_id = generateUniqueId();
    const update = updateTemplate(template, updates);
    if (update.messages.length > 0)
        console.log(chalk.gray(update.messages.join("\n")));
    
    if (!update.ok) {
        console.log(chalk.yellow("template not updated"));
        return;
    }

    await saveTemplate(template_path, update.template);
    console.log(chalk.gray(`${template_path} template updated`));

    await createAuditCommit({
        commit_id,
        commit_date: new Date(),
        //audit_id: audit_result.audit_id,
        analyze_url,
        template_path,
        //template_hash: 0,
        commit: update.commits
    });

    console.log(chalk.gray("snoozing for 30 seconds for template change data propegation..."));
    await sleep(30000);

    console.log(chalk.gray("updating syphonx.template_change_history..."));
    await bigquery.query("CALL syphonx.update_template_change_history()");

    console.log(chalk.gray(`commit: ${commit_id}`));
    return commit_id;    
}
