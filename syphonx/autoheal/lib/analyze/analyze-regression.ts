import * as stream from "node:stream";
import chalk from "chalk";
import * as syphonx from "syphonx-lib";
import { flattenTemplateSelect, Template } from "syphonx-lib";

import {
    coerceValueToString,
    compareEmbeddings,
    escapeHtmlString,
    generateSelector,
    getSelectorProfile,
    isEmpty,
    locateContent,
    parseDomainName,
    sanitizeHtml,
    summarize,
    truncate,
    updateTemplate,
    AnalyzeResult,
    ClassifyPageContext,
    Page
}
from "./lib.js";

export interface AnalyzeRegressionOptions {
    page: Page;
    live: boolean;
    name: string;
    context: AnalyzeRegressionContext;
    template: Template; 
    targets?: string[];
    selector_profile: string;
    stream?: stream.Writable;
}

export interface AnalyzeRegressionContext extends ClassifyPageContext {
    template_path: string;
    selectors: SelectorContext[];
}

export interface SelectorContext {
    selector_name: string;
    selector_json_value?: string;
    selector_value?: string;
    reference_json_value?: string;
    reference_value?: string;
    regression: boolean;
}

export interface AnalyzeRegressionResult {
    generated_selectors: GeneratedSelector[];
    code?: AnalyzeRegressionCode;
    message?: string;
    ok: boolean;
    analyze: AnalyzeResult[];
}

export interface GeneratedSelector {
    selector_name: string;
    selector?: string;
    explain?: string;
    ok?: boolean;
}

export type AnalyzeRegressionCode = "no-regression-targets" | "not-a-product-page" | "not-a-regression" | "content-not-located" | "error";

export async function analyzeRegression({ page, context, stream, selector_profile, template, ...options }: AnalyzeRegressionOptions): Promise<AnalyzeRegressionResult> {
    const result: AnalyzeRegressionResult = {
        generated_selectors: [],
        ok: false,
        analyze: []
    };

    const target_selectors = context.selectors.filter(selector => selector.regression && (!options.targets || options.targets.includes(selector.selector_name)));
    const target_selector_names = options.targets || target_selectors.map(obj => obj.selector_name);

    if (target_selectors.length === 0) {
        const message = `No regression targets found for ${!isEmpty(options.targets) ? options.targets?.join(", ") : "(any)"}`;
        stream?.write(`<div>${escapeHtmlString(message)}</div>`);
        return {
            ...result,
            message,
            code: "no-regression-targets",
            ok: false
        };
    }

    try {
        console.log(chalk.gray("Checking selectors on live page..."));
        const content = await page.content();
        const html = sanitizeHtml(content, true);
        const select = flattenTemplateSelect(template.actions, target_selectors.map(target => target.selector_name));
        const select_result = syphonx.select(select, html);
        let precheck_value_json = "";
        const precheck_hits = target_selectors
            .filter(target => {
                const value = select_result.data[target.selector_name]?.value;
                if (!isEmpty(value)) {
                    precheck_value_json = JSON.stringify(value);
                    return true;
                }
            })
            .map(target => target.selector_name);

        if (precheck_hits.length === 0) {
            console.log(chalk.gray(`\u2713 verified selectors for "${target_selectors.map(target => target.selector_name).join(", ")}" not returning a value`));
        }
        else {
            const message = `Not a regression, selectors for ${precheck_hits.join(", ")} returned a value: ${truncate(precheck_value_json, 80, true)}`;
            console.log(chalk.yellow(`\u2717 ${message}`));
            stream?.write(`<section class="title">${escapeHtmlString(message)}</section>\n`);
            return {
                ...result,
                message,
                code: "not-a-regression",
                ok: false
            };
        }

        console.log(chalk.gray(`Locating content...${options.targets ? ` (${options.targets.join(", ")})` : ""}`));
        const locate_result = await locateContent({
            ...options,
            page,
            domain_name: context.domain_name,
            selector_profile,
            stream
        });
        result.analyze.push(...locate_result.analyze);

        if (!locate_result.ok || !locate_result.targets || !locate_result.labelled_html) {
            const message = `Locate error: ${locate_result.message}`;
            console.log(chalk.gray(message));
            stream?.write(`<section class="title">${escapeHtmlString(message)}</section>\n`);
            return {
                ...result,
                message: locate_result.message,
                code: "content-not-located",
                ok: false
            };
        }

        for (const key of Object.keys(locate_result.targets)) {
            const { labels, explain } = locate_result.targets[key];
            const targeted = target_selector_names.find(name => name === key);
            if (!isEmpty(labels))
                console.log(chalk.gray(`${targeted ? "> " : "  "}${chalk.bold(key)}: ${chalk.italic(explain)}`));
            else
                console.log(chalk.gray(`${targeted ? "> " : "  "}${chalk.strikethrough(key)}: ${chalk.italic(explain)}`));
        }

        const locate_hits = target_selector_names.filter(selector_name => !isEmpty(locate_result.targets![selector_name]?.labels));
        if (locate_hits.length === 0) {
            console.log(chalk.gray("No content located"));
            stream?.write(`<section class="title">No content located</section>\n`);
            return {
                ...result,
                code: "content-not-located",
                ok: false
            };
        }

        const { targets: selector_targets } = getSelectorProfile(selector_profile);
        for (const selector_name of locate_hits) {
            const locate_target = locate_result.targets[selector_name] || {};
            if (isEmpty(locate_target.labels)) {
                console.log(chalk.gray(`No content located for "${selector_name}"`));
                console.log(chalk.gray.italic(locate_target.explain));
                stream?.write(`<section class="title">GENERATE <code>${selector_name}</code> <i>(${escapeHtmlString(locate_target.explain)})</i></section>\n`);
                result.generated_selectors.push({ selector_name, explain: locate_target.explain, ok: false });
                continue;
            }

            const selector_target = selector_targets.find(profile_target => profile_target.name === selector_name);
            if (!selector_target) {
                console.log(chalk.gray(`No selector target for "${selector_name}"`));
                stream?.write(`<section class="title">GENERATE <code>${selector_name}</code> <i>(No selector target)</i></section>\n`);
                result.generated_selectors.push({ selector_name, explain: "No selector target", ok: false });
                continue;
            }

            const url = page.url();
            const domain_name = parseDomainName(url);
            const generate_result = await generateSelector({
                selector_name,
                domain_name,
                url,
                labelled_html: locate_result.labelled_html,
                selector_target,
                locate_target,
                stream
            });
            result.analyze.push(...generate_result.analyze);

            result.generated_selectors.push({
                selector_name,
                selector: generate_result.ok && generate_result.selector ? generate_result.selector : undefined,
                explain: generate_result.explain,
                ok: generate_result.ok
            });

            if (!generate_result.selector)
                continue;
    
            const update = [{ selector_name, selector: generate_result.selector }];
            const update_result = updateTemplate(template, update);
            if (!update_result.ok) {
                console.log(chalk.yellow("Unable to update template"));
                console.log(chalk.gray(update_result.messages.join("\n")));
                stream?.write(`<section class="title">VERIFY <code>${selector_name}</code> <i>(unable to update template)</i></section>\n`);
                continue;
            }

            const select = flattenTemplateSelect(update_result.template.actions, [selector_name]);
            if (select.length === 0) {
                console.log(chalk.gray(`No existing selector for "${selector_name}" found in template, assuming generated selector is valid`));
                stream?.write(`<section class="title">VERIFY <code>${selector_name}</code> <i>(no existing selector found in template)</i></section>\n`);
                result.generated_selectors.push({
                    selector_name,
                    selector: generate_result.selector,
                    explain: generate_result.explain,
                    ok: true
                });
            }

            const select_result = syphonx.select(select, html);
            let select_value = select_result.data[selector_name]?.value;
            if (!select_value) {
                console.log(chalk.yellow(`\u2717 no value selected for "${selector_name}"`));
                stream?.write(`<section class="title">VERIFY <code>${selector_name}</code> <i>(no value selected)</i></section>\n`);
                result.generated_selectors.push({
                    selector_name,
                    selector: generate_result.selector,
                    explain: generate_result.explain,
                    ok: false
                });
            }

            const reference_json_value = context.selectors?.find(selector => selector.selector_name === selector_name)?.reference_json_value;
            let reference_value = reference_json_value ? JSON.parse(reference_json_value) : undefined;
            if (!reference_value) {
                console.log(chalk.gray("No reference value for verification, assuming generated selector is valid"));
                result.generated_selectors.push({
                    selector_name,
                    selector: generate_result.selector,
                    explain: generate_result.explain,
                    ok: true
                });
            }

            reference_value = coerceValueToString(reference_value);
            select_value = coerceValueToString(select_value);
            const compare_result = await compareEmbeddings(reference_value, select_value);
            result.analyze.push({
                name: "generate-selector",
                elapsed: compare_result.duration,
                model: compare_result.model,
                tokens: compare_result.usage,
                cost: compare_result.cost
            });
            console.log(chalk.gray("PREVIOUS SELECTED TEXT"));
            console.log(chalk.gray.italic(reference_value));
            console.log(chalk.gray("CURRENT SELECTED TEXT"));
            console.log(chalk.gray.italic(select_value));
            console.log(chalk.gray.bold(`COMPARE RESULT SCORE: ${compare_result.score.toFixed(3)}`));
            const ok = compare_result.score >= 0.7;
            if (ok) {
                result.generated_selectors.push({
                    selector_name,
                    selector: generate_result.selector,
                    explain: generate_result.explain,
                    ok: true
                });
                console.log(chalk.green("\u2713 selector verified"));
            }
            else {
                console.log(chalk.yellow("\u2717 selector value mismatch"));
            }
            stream?.write(`
                <details>
                    <summary>VERIFY <code>${selector_name}</code> <i>(${ok ? "selector verified" : "value mismatch"})</i></summary>
                    <table>
                        <tr><td>OLD</td><td>${escapeHtmlString(reference_value)}</td></tr>
                        <tr><td>NEW</td><td>${escapeHtmlString(select_value)}</td></tr>
                        <tr><td>SCORE</td><td>${compare_result.score.toFixed(3)}</td></tr>
                    </table>
                </details>
            `);
        }

        result.ok = result.generated_selectors.length > 0 && result.generated_selectors.every(obj => obj.ok);
        if (!result.ok && !result.message)
            result.message = "Content not located";
        return result;
    }
    catch (err) {
        err = err instanceof Error ? err.message : JSON.stringify(err);
        console.log(chalk.gray(`unexpected error: ${err}`));
        stream?.write(`<p class="error">GENERATE (ERROR) <i>${err}</i></p>\n`);
        return {
            ...result,
            message: err instanceof Error ? err.message : JSON.stringify(err),
            code: "error",
            ok: false
        };
    }
    finally {
        const { summary } = summarize(result.analyze);
        stream?.write(`
        <p class="info">USAGE: ${summary}</p>
        <section>
            <table>
                <thead>
                    <tr>
                        <td>Name</td>
                        <td>Value</td>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>URL</td>
                        <td><a href="${context.capture_url}" target="_blank">${context.capture_url}</a></td>
                    </tr>
                    <tr>
                        <td>TIMESTAMP</td>
                        <td>${new Date()}</td>
                    </tr>
                    <tr>
                        <td>REGRESSIONS</td>
                        <td>${target_selectors.map(({ selector_name }) => selector_name).join(", ")}</td>
                    </tr>
                    <tr>
                        <td>TEMPLATE</td>
                        <td>${context.template_path}</td>
                    </tr>
                </tbody>
            </table>
        </section>
        `);
    }
}
