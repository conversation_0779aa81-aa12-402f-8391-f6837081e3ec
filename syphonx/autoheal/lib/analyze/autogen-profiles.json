[{"name": "default", "autoqc": {"model": "gemini-2.0-pro"}, "autoCloseDialog": {"model": "gpt-4o"}, "classifyScreenshot": {"model": "gpt-4o"}, "generateSelector": {"iterations": [{"slice": 0, "turns": 1, "model": "llama3-70b-8192"}, {"slice": 1, "turns": 3, "model": "llama3-70b-8192"}, {"slice": 2, "turns": 6, "model": "gemini-2.0-pro"}]}, "locateContent": {"model": "gpt-4o"}, "qualifySelector": {"model": "gemini-2.0-pro"}, "removeHashIds": {"model": "gemini-2.0-pro"}}, {"name": "chatgpt-standard", "autoqc": {"model": "gpt-4o"}, "autoCloseDialog": {"model": "gpt-4o"}, "classifyScreenshot": {"model": "gpt-4o"}, "generateSelector": {"iterations": [{"slice": 0, "turns": 1, "model": "gpt-4o"}, {"slice": 1, "turns": 3, "model": "gpt-4o"}, {"slice": 2, "turns": 6, "model": "gpt-4o"}]}, "locateContent": {"model": "gpt-4o"}, "qualifySelector": {"model": "gpt-4o"}, "removeHashIds": {"model": "gpt-4o"}}, {"name": "chatgpt-economy", "autoqc": {"model": "gpt-4o"}, "autoCloseDialog": {"model": "gpt-4o"}, "classifyScreenshot": {"model": "gpt-4o"}, "generateSelector": {"iterations": [{"slice": 0, "turns": 1, "model": "gpt-3.5-turbo"}, {"slice": 1, "turns": 3, "model": "gpt-3.5-turbo"}, {"slice": 2, "turns": 6, "model": "gpt-3.5-turbo"}]}, "locateContent": {"model": "gpt-4o"}, "qualifySelector": {"model": "gpt-4o"}, "removeHashIds": {"model": "gpt-4o"}}, {"name": "gemini-standard", "autoqc": {"model": "gemini-2.0-pro"}, "autoCloseDialog": {"model": "gemini-2.0-pro"}, "classifyScreenshot": {"model": "gemini-2.0-pro"}, "generateSelector": {"iterations": [{"slice": 0, "turns": 1, "model": "gemini-2.0-pro"}, {"slice": 1, "turns": 3, "model": "gemini-2.0-pro"}, {"slice": 2, "turns": 6, "model": "gemini-2.0-pro"}]}, "locateContent": {"model": "gemini-2.0-pro"}, "qualifySelector": {"model": "gemini-2.0-pro"}, "removeHashIds": {"model": "gemini-2.0-pro"}}, {"name": "gemini-economy", "autoqc": {"model": "gemini-2.0-flash"}, "autoCloseDialog": {"model": "gemini-2.0-flash"}, "classifyScreenshot": {"model": "gemini-2.0-flash"}, "generateSelector": {"iterations": [{"slice": 0, "turns": 1, "model": "gemini-2.0-flash"}, {"slice": 1, "turns": 3, "model": "gemini-2.0-flash"}, {"slice": 2, "turns": 6, "model": "gemini-2.0-flash"}]}, "locateContent": {"model": "gemini-2.0-flash"}, "qualifySelector": {"model": "gemini-2.0-flash"}, "removeHashIds": {"model": "gemini-2.0-flash"}}, {"name": "claude-premium", "autoqc": {"model": "claude-3-opus"}, "autoCloseDialog": {"model": "claude-3-opus"}, "classifyScreenshot": {"model": "claude-3-opus"}, "generateSelector": {"iterations": [{"slice": 0, "turns": 1, "model": "claude-3-opus"}, {"slice": 1, "turns": 3, "model": "claude-3-opus"}, {"slice": 2, "turns": 6, "model": "claude-3-opus"}]}, "locateContent": {"model": "claude-3-opus"}, "qualifySelector": {"model": "claude-3-opus"}, "removeHashIds": {"model": "claude-3-opus"}}, {"name": "claude-standard", "autoqc": {"model": "claude-3-5-sonnet"}, "autoCloseDialog": {"model": "claude-3-5-sonnet"}, "classifyScreenshot": {"model": "claude-3-5-sonnet"}, "generateSelector": {"iterations": [{"slice": 0, "turns": 1, "model": "claude-3-5-sonnet"}, {"slice": 1, "turns": 3, "model": "claude-3-5-sonnet"}, {"slice": 2, "turns": 6, "model": "claude-3-5-sonnet"}]}, "locateContent": {"model": "claude-3-5-sonnet"}, "qualifySelector": {"model": "claude-3-5-sonnet"}, "removeHashIds": {"model": "claude-3-5-sonnet"}}, {"name": "claude-economy", "autoqc": {"model": "claude-3-haiku"}, "autoCloseDialog": {"model": "claude-3-haiku"}, "classifyScreenshot": {"model": "claude-3-haiku"}, "generateSelector": {"iterations": [{"slice": 0, "turns": 1, "model": "claude-3-haiku"}, {"slice": 1, "turns": 3, "model": "claude-3-haiku"}, {"slice": 2, "turns": 6, "model": "claude-3-haiku"}]}, "locateContent": {"model": "claude-3-haiku"}, "qualifySelector": {"model": "claude-3-haiku"}, "removeHashIds": {"model": "claude-3-haiku"}}]