import { Select, Template } from "syphonx-lib";
import chalk from "chalk";

import {
    closeLogStream,
    createRemoteLogStream,
    diffbot,
    escapeHtmlString,
    generateTimestamp,
    hookConsoleLog,
    jsdom,
    sanitizeHtml,
    saveTemplate,
    truncate,
    unhookConsoleLog,
    AutogenOptions,
    AutogenResult
} from "../index.js";

import { findClosestDescendantContains } from "../scripts/find-closest-descendant.js";
import { generateSelector } from "../scripts/generate-selector.js";

export async function autogen_diffbot({ url, template_path, autogen_id, debug }: AutogenOptions): Promise<AutogenResult> {
    const { stream, url: report_url } = createRemoteLogStream(`autogen/${generateTimestamp("seconds")}.html`, { title: "AUTOGEN-DIFFBOT", subtitle: url, id: autogen_id });
    hookConsoleLog();

    const result: AutogenResult = {
        generated_selectors: [],
        analyze: [],
        report_url,
        ok: false
    };

    console.log(chalk.gray("Calling Diffbot API..."));
    const [response] = await diffbot.product(url, { fields: ["breadcrumb", "content", "dom", "meta"], discussion: true });

    const [{ dom, ...product }] = response.objects;
    const html = sanitizeHtml(dom);
    jsdom(html);

    stream.write(`
        <details>
          <summary>PRODUCT</summary>
          <pre>
${escapeHtmlString(JSON.stringify(product, null, 2))}
          </pre>
        </details>\n`);
    
    const [image] = product.images;
    const { singleNodeValue: node } = document.evaluate(image.xpath, document, null, window.XPathResult.FIRST_ORDERED_NODE_TYPE, null);
    if (!node)
        result.message = "No product image found in Diffbot response";

    const select: Select[] = [];
    if (node) {
        console.log();
        const targets: Record<string, Element | undefined> = {};
        targets["image"] = node as Element;
        targets["name"] = setTarget(targets["image"], "name", product.title, select);
        targets["price"] = setTarget(targets["image"], "price", product.offerPrice, select);
        targets["description"] = setTarget(targets["image"], "description", product.content, select, 0.5);
        targets["sku"] = setTarget(targets["image"], "sku", product.sku, select);
        targets["brand_name"] = setTarget(targets["image"], "brand", product.brand, select);

        const template: Template = { url, actions: [{ select }] };
        if (!debug) {
            await saveTemplate(template_path, template);
            console.log(chalk.gray(`Template saved: ${template_path}`));
        }
        else {
            console.log(chalk.gray(`Template NOT saved: ${template_path} (DEBUG MODE)`));
        }

        targets["image"].setAttribute("__target__", "image");
        targets["name"]?.setAttribute("__target__", "name");
        targets["price"]?.setAttribute("__target__", "price");
        targets["description"]?.setAttribute("__target__", "description");
        targets["sku"]?.setAttribute("__target__", "sku");
        targets["brand_name"]?.setAttribute("__target__", "brand_name");
        result.message = `${select.length} selectors generated`;
    }

    if (select.length > 0)
        stream.write(`
        <details>
          <summary>HTML Output</summary>
          <pre>
${escapeHtmlString(document.documentElement.outerHTML)}
          </pre>
        </details>\n`);

    stream.write(`
            <details>
              <summary>OUTPUT SUMMARY</summary>
              <p>${escapeHtmlString(result.message)}</p>
              ${select.length > 0 ? `
              <table>
                <thead>
                    <td>Name</td>
                    <td>Selector</td>
                </thead>
                <tbody>
                    ${select.map(obj => `
                    <tr>
                        <td>${obj.name}</td>
                        <td>${obj.query![0][0]}</td>
                    </tr>\n`)}
                </tbody>
              </table>\n` : ""}
            </details>\n`);

    result.ok = select.length > 0;
    await closeLogStream(stream, result);
    unhookConsoleLog();
    return result;
}

function setTarget(target: Element, key: string, text: string, select: Select[], threshold = 1): Element | undefined {
    if (!text?.trim()) {
        console.log(chalk.gray(`No text extracted for "${key}" from Diffbot response`));
        return;
    }

    const [element] = findClosestDescendantContains(target, text.trim().slice(0, 50), threshold);
    if (!element) {
        console.log(chalk.gray(`No content for "${key}" not found in HTML returned from Diffbot response`));
        return;
    }

    let selectors = generateSelector({ element });
    selectors.sort((a, b) => a.length - b.length);
    selectors = selectors.filter(selector => selector.length > 1);

    const [selector] = selectors;
    if (selector)
        select.push({ name: key, query: [[selector]] });

    console.log(chalk.cyan(`${key}: ${selector}`));
    console.log(chalk.gray.italic(truncate(text, 150)));

    const hits = Array.from(document.querySelectorAll(selector));
    console.log(chalk.gray(`${hits.length} hits:`));
    for (const hit of hits)
        console.log(chalk.gray.italic(truncate(hit.outerHTML, 150, true)));

    if (selectors.length > 1) {
        console.log(chalk.gray(`${selectors.length - 1} alternate selectors:`));
        for (const selector of selectors.slice(1))
            console.log(chalk.gray(`- ${selector}`));
    }
    console.log();
}
