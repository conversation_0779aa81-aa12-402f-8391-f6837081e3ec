import * as stream from "node:stream";

export interface AnalyzeResult {
    name: string;
    model?: string;
    input_tokens?: number;
    output_tokens?: number;
    tokens: number;
    cost: number;
    elapsed: number;
    message?: string;
    report_url?: string;
}

export interface AnalyzeResultSummary {
    model: string;
    tokens: number;
    input_tokens: number;
    output_tokens: number;
    cost: number;
    elapsed: number;
    summary: string;
}

export interface AnalyzeResultSummaryAll {
    models: string[];
    tokens: number;
    input_tokens: number;
    output_tokens: number;
    cost: number;
    elapsed: number;
    summary: string;
    breakdown: AnalyzeResultSummary[];
}

function summarizeAll(analyze: AnalyzeResult[]): AnalyzeResultSummaryAll {
    const models = Array.from(new Set(analyze.map(obj => obj.model!)));
    const tokens =  analyze.reduce((sum, obj) => sum + obj.tokens, 0);
    const input_tokens =  analyze.reduce((sum, obj) => sum + (obj.input_tokens||0), 0);
    const output_tokens =  analyze.reduce((sum, obj) => sum + (obj.output_tokens||0), 0);
    const cost = analyze.reduce((sum, obj) => sum + obj.cost, 0);
    const elapsed = analyze.reduce((sum, obj) => sum + obj.elapsed, 0);
    const summary = `${input_tokens.toLocaleString()} input tokens, ${output_tokens.toLocaleString()} output tokens (${formatCost(cost)}), ${formatElapsed(elapsed)}, using ${models.join(", ")}`;
    const breakdown = models.map(model => summarize(analyze.filter(obj => obj.model === model)));
    return { models, tokens, input_tokens, output_tokens, cost, elapsed, summary, breakdown };
}

function summarize(analyze: AnalyzeResult[]): AnalyzeResultSummary {
    const model = Array.from(new Set(analyze.map(obj => obj.model!))).join(", ");
    const tokens =  analyze.reduce((sum, obj) => sum + obj.tokens, 0);
    const input_tokens =  analyze.reduce((sum, obj) => sum + (obj.input_tokens||0), 0);
    const output_tokens =  analyze.reduce((sum, obj) => sum + (obj.output_tokens||0), 0);
    const cost = analyze.reduce((sum, obj) => sum + obj.cost, 0);
    const elapsed = analyze.reduce((sum, obj) => sum + obj.elapsed, 0);
    const summary = `${input_tokens.toLocaleString()} input tokens, ${output_tokens.toLocaleString()} output tokens (${formatCost(cost)}), ${formatElapsed(elapsed)}, using ${model}`;    
    return { model, tokens, input_tokens, output_tokens, cost, elapsed, summary };
}

function formatCost(cost: number) {
    return `$${cost.toFixed(5)}`;
}

const seconds = 1000;
const minutes = 60 * seconds;
function formatElapsed(elapsed: number): string {
    if (elapsed >= 5 * minutes)
        return Math.round(elapsed / minutes) + ' minutes';
    else if (elapsed >= minutes)
        return (elapsed / minutes).toFixed(1) + ' minutes';
    else if (elapsed < 10 * seconds)
        return (elapsed / minutes).toFixed(1) + ' seconds';
    else
        return Math.round(elapsed / seconds) + ' seconds';
}

export function printCostSummary(stream: stream.Writable, analyze: AnalyzeResult[]) {
    const total = summarizeAll(analyze);
    stream.write(`
<details>
    <summary>COST SUMMARY</summary>
    <table>
        <thead>
            <tr>
                <td>NAME</td>
                <td>MODEL</td>
                <td>INPUT TOKENS</td>
                <td>OUTPUT TOKENS</td>
                <td>COST</td>
                <td>ELAPSED</td>
            </tr>
        </thead>
        <tbody>
        ${analyze.map(({ name, model, input_tokens, output_tokens, cost, elapsed }) => `
            <tr>
                <td>${name}</td>
                <td>${model}</td>
                <td>${input_tokens}</td>
                <td>${output_tokens}</td>
                <td>${formatCost(cost)}</td>
                <td>${formatElapsed(elapsed)}</td>
            </tr>
            `).join("\n")}
        </tbody>
        <tfoot>
            <tr>
                <td>-</td>
                <td>-</td>
                <td>${total.input_tokens}</td>
                <td>${total.output_tokens}</td>
                <td>${formatCost(total.cost)}</td>
                <td>${formatElapsed(total.elapsed)}</td>
            </tr>
        </tfoot>
    </table>
</details>
<p class="info">USAGE: ${total.summary}</p>
`);
}

export { summarizeAll as summarize };