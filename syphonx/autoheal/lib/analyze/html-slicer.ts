import { jsdom, md5hash, regexpExtract } from "./lib.js";
import { sliceHtml, SliceHtmlResult } from "../scripts/slice.js";

export { SliceHtmlResult } from "../scripts/slice.js";

export interface SliceLabelledHtmlResult extends SliceHtmlResult {
    /** The HTML slice with line numbering format. */
    html_with_linenums: string;
}

export function findUncontainedTargets(html: string, targetSelectors: string[], rootSelectors: string[]): string[] {
    jsdomCache(html);
    const rootElements = rootSelectors.map(selector => document.querySelector(selector)).filter(Boolean) as Element[];
    const uncontained = [];
    for (const targetSelector of targetSelectors) {
        const targetElement = document.querySelector(targetSelector) as Element;
        const contained = targetElement ? rootElements.some(rootElement => rootElement.contains(targetElement)) : false;
        if (!contained)
            uncontained.push(targetSelector);
    }
    return uncontained;
}

export function sliceLabelledHtml(labelled_html: string, labels: number[], size: number): SliceLabelledHtmlResult | undefined {
    jsdomCache(labelled_html);

    for (const label of labels) {
        const selector = `[__label__='${label}']`;
        const element = document.querySelector(selector);
        if (!element) {
            console.error(`Error in sliceLabelledHtml: Label '${label}' not found in the provided labelled_html. Aborting slice.`);
            return undefined;
        }
    }

    let slice: SliceHtmlResult | undefined = undefined;
    const selector = labels.map(label => `[__label__='${label}']`).join(",");
    if (size === 0)
        slice = minimalSlice(selector);
    else if (size === 1)
        slice = maximalSlice(selector, 1500);
    else if (size >= 2)
        slice = maximalSlice(selector, 5000);

    if (!slice)
        return;

    slice.html = slice.html.replace(/\s*<span class="img-label">[^<]+<\/span>\s*/g, ""); // remove img-label spans
    slice.html = slice.html.replace(/\s+__show__/g, ""); // remove all __show__ attributes
    const slice_labels = slice.html.split("\n").filter(line => /__label__/.test(line)).map(line => regexpExtract(/__label__="(\d+)"/, line)) as string[];
    const [widest_label] = slice_labels.sort((a, b) => b.length - a.length);
    const field_width = widest_label.length;
    const html_with_linenums = slice.html
        .split("\n")
        .map(line => {
            const label = regexpExtract(/__label__="(\d+)"/, line);
            if (label)
                return `${labels.includes(Number(label)) ? ">" : " "} ${label.padStart(field_width, "0")} ${line.replace(/\s+__label__="\d+"/g, "")}`
            else
                return `${" ".repeat(field_width + 3)}${line}`;
        })
        .join("\n");
    slice.html = slice.html.replace(/\s+__label__="\d+"/g, ""); // remove all __label__ attributes

    return { ...slice, html_with_linenums };
}

function minimalSlice(selector: string): SliceHtmlResult | undefined {
    const slice = sliceHtml({
        selector,
        parents: 2,
        children: 4,
        siblings: 4,
        anchor: 1
    });
    return slice;
}

function maximalSlice(selector: string, maxLength: number): SliceHtmlResult | undefined {
    const options = {
        selector,
        parents: Infinity,
        children: Infinity,
        siblings: 10,
        uncles: 6,
        nephews: 6,
        anchor: 6
    };

    let result = sliceHtml(options);
    if (!result || result.html.length <= maxLength)
        return result;

    while (result.html.length > maxLength && options.uncles > 0) {
        options.uncles -= 1;
        result = sliceHtml(options)!;
    }
    options.uncles += 1;
    result = sliceHtml(options)!;

    while (result.html.length > maxLength && options.nephews > 0) {
        options.nephews -= 1;
        result = sliceHtml(options)!;
    }

    while (result.html.length > maxLength && options.siblings > 0) {
        options.siblings -= 1;
        result = sliceHtml(options)!;
    }

    if (result.html.length > maxLength) {
        options.children = 10;
        options.uncles -= 1;
        result = sliceHtml(options)!;
    }

    while (result.html.length > maxLength && options.children > 0) {
        options.children -= 1;
        result = sliceHtml(options)!;
    }

    if (result.html.length > maxLength) {
        options.parents = 6;
        options.anchor = 6;
        result = sliceHtml(options)!;
    }

    while (result.html.length > maxLength && options.parents > 0) {
        options.parents -= 1;
        options.anchor -= 1;
        result = sliceHtml(options)!;
    }

    return result;
}

function jsdomCache(html: string): void {
    const hash = md5hash(html);
    const meta = typeof document !== "undefined" ? document.querySelector("meta#hash")?.getAttribute("content") : undefined;
    if (meta !== hash) {
        jsdom(html);
        document.head.insertAdjacentHTML("beforebegin", `<meta id="hash" content="${hash}">`);
    }
}
