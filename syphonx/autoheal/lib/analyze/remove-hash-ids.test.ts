import { expect } from "chai";
import { unhashSelector } from "./remove-hash-ids.js";

describe("unhash", () => {
    it("__HASH__", function () { expect(unhashSelector(this.test?.title!)).equals("") });
    it("__HASH__ __HASH__", function () { expect(unhashSelector(this.test?.title!)).equals("") });
    it("__HASH__ __HASH__ __HASH__", function () { expect(unhashSelector(this.test?.title!)).equals("") });
    it(".foo __HASH__", function () { expect(unhashSelector(this.test?.title!)).equals(".foo") });
    it("__HASH__ .bar", function () { expect(unhashSelector(this.test?.title!)).equals(".bar") });
    it("#foo __HASH__ .bar", function () { expect(unhashSelector(this.test?.title!)).equals("#foo .bar") });
    it("#foo __HASH__ __HASH__ .bar", function () { expect(unhashSelector(this.test?.title!)).equals("#foo .bar") });
    it("__HASH__ #foo __HASH__ __HASH__ __HASH__ .bar __HASH__", function () { expect(unhashSelector(this.test?.title!)).equals("#foo .bar") });

    it("#foo-__HASH__", function () { expect(unhashSelector(this.test?.title!)).equals("[id*='foo-']") });
    it("#foo-__HASH__.bar", function () { expect(unhashSelector(this.test?.title!)).equals("[id*='foo-'].bar") });
    it("#__HASH__-foo", function () { expect(unhashSelector(this.test?.title!)).equals("[id*='-foo']") });
    it("#__HASH__-foo.bar", function () { expect(unhashSelector(this.test?.title!)).equals("[id*='-foo'].bar") });
    it(".bar#foo-__HASH__", function () { expect(unhashSelector(this.test?.title!)).equals(".bar[id*='foo-']") });

    it("#foo-__HASH__-bar", function () { expect(unhashSelector(this.test?.title!)).equals("[id*='foo-'][id*='-bar']") });
    it("#foo-__HASH__ .bar", function () { expect(unhashSelector(this.test?.title!)).equals("[id*='foo-'] .bar") });
    it("#foo-__HASH__", function () { expect(unhashSelector(this.test?.title!)).equals("[id*='foo-']") });

    it(".foo-__HASH__-bar", function () { expect(unhashSelector(this.test?.title!)).equals("[class*='foo-'][class*='-bar']") });
    it(".foo-__HASH__ .bar", function () { expect(unhashSelector(this.test?.title!)).equals("[class*='foo-'] .bar") });
    it(".foo-__HASH__", function () { expect(unhashSelector(this.test?.title!)).equals("[class*='foo-']") });
    it(".__HASH__-foo", function () { expect(unhashSelector(this.test?.title!)).equals("[class*='-foo']") });
    it(".foo-__HASH__ .bar", function () { expect(unhashSelector(this.test?.title!)).equals("[class*='foo-'] .bar") });

    it(".foo-____HASH__", function () { expect(unhashSelector(this.test?.title!)).equals("[class*='foo-__']") });
    it(".foo-____HASH____", function () { expect(unhashSelector(this.test?.title!)).equals("[class*='foo-__'][class*='__']") });

    it("#price-template--__HASH____main .price__regular .price-item--regular, #price-template--__HASH____main .price__sale .price-item--sale", function () {
        expect(unhashSelector(this.test?.title!)).equals("[id*='price-template--'][id*='__main'] .price__regular .price-item--regular, [id*='price-template--'][id*='__main'] .price__sale .price-item--sale")
    });
});
