import * as stream from "node:stream";
import chalk from "chalk";

import {
    autogenProfile,
    escapeHtmlString,
    renderClassifyPagePrompt,
    round,
    summarize,
    AnalyzeR<PERSON>ult,
    LLMChat,
    LLMModel
} from "./lib.js";

export interface ClassifyPageOptions {
    datauri: string;
    context: ClassifyPageContext;
    model?: LLMModel;
    stream?: stream.Writable;
}

export interface ClassifyPageContext {
    domain_name: string;
    capture_url: string;
    title?: string | null;
    screenshot_url?: string;
}

export interface ClassifyPageResult {
    page_class?: PageClassification;
    page_data?: Record<string, unknown>;
    explain?: string;
    prompt?: boolean;
    message?: string;
    analyze: AnalyzeResult[];
    ok: boolean;
}

export async function classifyPage({ datauri, context, stream, model = autogenProfile().classifyScreenshot.model }: ClassifyPageOptions): Promise<ClassifyPageResult> {
    const result: ClassifyPageResult = {
        analyze: [],
        ok: false
    };

    const chat = new LLMChat(model);
    const input = renderClassifyPagePrompt(context);
    const [response, output] = await chat.json<ClassifyPageResponse>(input, { images: [datauri] });
    const message = response ? response.explain : "Unable to convert LLM response to JSON";

    result.analyze.push({
        name: "classify-page",
        elapsed: chat.elapsed,
        model: chat.model,
        tokens: chat.tokens,
        input_tokens: chat.input_tokens,
        output_tokens: chat.output_tokens,
        cost: round(chat.cost, 6),
        message
    });
    const { summary } = summarize(result.analyze);

    if (response) {
        const { classification, explain, ...data } = response;
        result.page_class = response.classification;
        result.page_data = data;
        result.explain = explain;
        result.ok = true;

        stream?.write(`
            <p><i>${escapeHtmlString(response.explain)}</i>${response.classification === "MODAL" || response.popup_dialog ? "<br><b>Popup dialog detected!</b>" : "<br>No dialog detected."}</p>
            <details>
                <summary>CLASSIFY <code>${response.classification}</code></summary>
                <pre>${escapeHtmlString(JSON.stringify(data, null, 2))}</pre>
                <img src="${datauri}">
                <pre>${escapeHtmlString(input)}</pre>
                <hr>
                <pre>${escapeHtmlString(output)}</pre>
                <p class="info">USAGE: ${summary}</p>
            </details>
        `);
    }
    else {
        console.log(chalk.red(message));
        console.log(chalk.gray.italic(output));
        stream?.write(`
            <details>
                <summary>CLASSIFY <small>ERROR</small></summary>
                <img src="${datauri}">
                <div>${escapeHtmlString(message)}</div>
                <pre>${escapeHtmlString(input)}</pre>
                <hr>
                <pre>${escapeHtmlString(output)}</pre>
                <p class="info">USAGE: ${summary}</p>
            </details>
        `);
    }

    return result;
}
