- name: availability
  type: string
  repeated: false
  locate: |
    An indication on the page of the product availability of the product, in other words whether it is available or unavailable for purchase.
    Follow the steps below to identify the product availability on the page:
    - Look for a message near the price indicating stock status, such as "In stock", "Out of Stock", "# left in stock", "Backordered", "Discontinued", "Available", "Unavailable", etc.
    - Look for any button that looks like it allows the customer to purchase the product such as a button labelled "Buy", or "Add to Cart" or "Buy Now", etc.
    If any of the above are found, then flag that as an indicator of the product availability.
  generate: |
    Follow the steps below to identify the product availability on the page:
    - Look for a message near the price indicating stock status, such as "In stock", "Out of Stock", "# left in stock", "Backordered", "Discontinued", "Available", "Unavailable", etc.
      If a stock status message is found, then the selector should return the stock status message.
    - Look for any button that looks like it allows the customer to purchase the product such as a button labelled "Buy", or "Add to Cart" or "Buy Now", etc.
      If a button is found, then the selector should return the text of the button label.
  expect: "string-distance"


- name: brand_name
  type: string
  repeated: false
  locate: |
    The brand-name is the name of the company or "brand entity" that produced (or manufactured) the product.
    Follow the steps below to identify the brand-name for the product:
    - First look for the brand-name to appear adjacent to the product-name; either directly above, below, or to the left/right of the product-name.
    - The brand-name may be in a smaller font or color than the product-name, and sometimes looks like a clickable hyperlink.
    - The brand-name can also appear elsewhere on the page, with a label of "Brand: XYZ", "Manufacturer: XYZ" or similar label, or it may be in a link that looks like "Visit the XYZ store" or "Other XYZ products" or something like that (where "XYZ" is a placeholder for the actual brand-name).
    - If none of the above, determine if the brand-name appears within the product-name itself, for example at the front like "Sony" in "Sony - Playstation Portal" or "Roku" in "Roku Ultra 4K Streaming Media Player", or at the end like "Stanley" in "Stainles Tumbler by Stanley" (Stanely) and if it does then select the product-name also as the brand-name. In this case add a comment that the brand-nname was found within the product-name.
  expect: "string-match"


- name: currency
  type: string
  repeated: false
  locate: |
    The currency symbol for the sale price of the product. For example "$" for dollars, "€" for euros, "£" for pounds, etc.
  generate: |
    Generate a regex in addition to the generated selector that matches the currency symbol within the selected result.
  expect: "currency-symbol"


- name: description
  type: string
  repeated: false
  locate: |
    The product-description (or "long description") for the main product on the page that generally meets the following requirements:
    - The product-description is a single sentence or a series of short paragraphs describing the product.
    - The product-description may be found under a heading emphasized in bold or slightly larger text with a title suchas "Product Description" or "Details", etc.; or it may be a sentence or series of short paragaphs appearing near the product-name, price, or product-image.
    - If the selector targets a heading or a caption, please make the selector target the text or paragraphs under the heading instead of the heading. We don't want a heading we want the text of the description itself!!
    - If the selector targets text that contains a bulleted list that is preceded by one or more paragraphs, then the paragraphs should be targeted as the `description` and the bulleted list should be targeted as the `features`.
  expect: "substring-match"


- name: features
  type: string
  repeated: true
  locate: |
    A bulleted list of product features or a list of short sentences describing the product.
    Multiple items are expected.
    Follow the steps below to identify the product features:
    - First look for a heading on the page like "Features" or "Key features" or something similar, in this situation simply select what looks to be a list of features underneath that heading whether they have bullets or not, but not if it's a long paragraph.
    - Alternative look for a list in or near the product description, but explicitly using a bullet/list format instead of a paragraph type format.
    - If the selector targets a heading or a caption that may be bold and/or possibly with a slightly larget font size, like "Features" or "Product Features", please make the selector target the bulletted list under the heading instead of the heading or caption. We don't want a heading we want the bulleted list!!
    - If the selector targets text that contains a bulleted list that is preceded by one or more paragraphs, then the bulleted list should be targeted without the paragraph.
    - If no bulleted list is found, then look for a list that otherwise looks like a list of product features or short sentence descriptions but without the bullet decoration.
    - There should typically be 3 or more feature bullets and found near the `description`.
  expect: "substring-match"


- name: gtin
  type: string
  repeated: false
  locate: |
    Look for "GTIN" or "Global Trade Item Number" in the product specifications.
    It may also be listed "UPC" or "Universal Product Code", or "EAN" (European Article Number in the EU) or "ISBN" for books.
  expect: "string-match"


- name: image_url
  type: string
  repeated: false
  locate: |
    The url of the main product image (or hero image) for the product.
    This is typically the largest image "above the fold" near the product title and add-to-cart or buy button.
  generate: |
    IMPORTANT: Assuming the generated selector targets an image element, then it should be a jQuery selector that looks something like `$('img.product-image').attr('src')`.
    If there is no `<img src="...">` with a valid URL, look for other attributes that contain a URL such as `<img srcset="...">` or any other attribute that contains a valid URL.
    Also image URL's are sometimes specified in a background-image with the style like so: `<div style="background-image: url('image.jpg');"></div>`.
  qualify: |
    If the generated selector targets an image element and isn't a jQuery selector like `$('img.product-image').attr('src')` (in other words isn't extracting the URL from the `src` attribute or some other attribute), then output `INVALID IMG SELECTOR` explaining how the URL can be extracted using a jQuery selector.
    If there is no `<img src="...">` with a valid URL, look for other attributes that contain a URL such as `<img srcset="...">` or any other attribute that contains a valid URL.
    Also image URL's are sometimes specified in a background-image with the style like so: `<div style="background-image: url('image.jpg');"></div>`.
    Finally check the SELECTOR OUTPUT JSON to ensure it contains a string value that appears to be a valid URL to a product image on the site.
  expect: "url"


- name: images
  type: string
  repeated: true
  locate: |
    The product image gallery found next to the main hero image for the product.
    Expecting a list of url's from the `src` attribute of the targeted `img` elements.
  generate: |
    IMPORTANT: Assuming the generated selector targets image elements, then it should be a jQuery selector that looks something like `$('img.product-image-thumbnail').attr('src')`.
    There may be more images than indicated with the `>` arrow.
    Look for a repeated pattern of image elements within the same parent context and if more images with a strong association are found than indicated then include those in the generated selector as well.
    If there is no `<img src="...">` with a valid URL, look for other attributes that contain a URL such as `<img srcset="...">` or any other attribute that contains a valid URL.
    Also image URL's are sometimes specified in a background-image with the style like so: `<div style="background-image: url('image.jpg');"></div>`.
  qualify: |
    If the generated selector targets image elements and isn't a jQuery selector like `$('img.product-image-thumbnail').attr('src')` (in other words isn't extracting the URL from the `src` attribute or some other attribute), then output `INVALID IMG SELECTOR` explaining how the URL can be extracted using a jQuery selector.
    If there is no `<img src="...">` with a valid URL, look for other attributes that contain a URL such as `<img srcset="...">` or any other attribute that contains a valid URL.
    Also image URL's are sometimes specified in a background-image with the style like so: `<div style="background-image: url('image.jpg');"></div>`.
    Finally check the SELECTOR OUTPUT JSON to ensure it contains an array of strings that appear to be valid URL's to product images on the site, and that there are no duplicates.
  expect: "url"


- name: in_stock
  type: boolean
  repeated: false
  locate: |
    An indication on the page of whether or not the product is in-stock and available for purchase.
    This should generally use the same determination as availability, but produces a true/false result indicating whether the product can be purchased.


- name: mpn
  type: string
  repeated: false
  locate: |
    Look for "Manufacturer Part Number", "MPN", "Model", "Model Number", or "Model Name" in the product details or specifications.
    It's often listed near product deails, specifications, other product identifying information.
  expect: "string-match"


- name: name
  type: string
  repeated: false
  waitfor: true
  locate: |
    The product title (or product-name) that names the product and generally meets the following requirements:
    - The product-name is typically at the top of the page, near the product image and price or availability information.
    - The product-name is usually in a large, bold font, making it stand out from other text.
    - The label numbers are in brackets (for example`[34]`) and follow immediately after the name, price, or description.
    - DO NOT confuse the product-name with the category-name or brand-name, which may be in a smaller font or located elsewhere on the page.
    - DO NOT get the product-name from the breadcrumb if one is present on the page, ignore the breadcrumb entirely!!
    - There will only be one main product name on the page.
    - If the product-name is found in multiple places on the page, choose the one with the largest font or the one that is adjacent to the product image and the price/stock-status, or both.
  expect: "string-match"


- name: price
  type: number
  waitfor: true
  repeated: false
  locate: |
    The sale price of the product. Only one price, the primary final selling price of the product, is expected.
    If there is a message like "See price in cart" in the place of a price, do not output that as the price and instead mention in the explanation that the price is hidden.
  expect: "number-equals"


- name: product_condition
  type: string
  repeated: false
  locate: |
    An indication of whether the product is in new or used condition.


- name: review_count
  type: number
  repeated: false
  locate: |
    The number of reviews for the main product.
    - Typically appears next to the review-score and expressed as a whole number.
    - If a null value was output for review_score then a null value should also be output for review_count.
  expect: "number-equals"


- name: review_score
  type: number
  repeated: false
  locate: |
    The user generated review score of the main product typically expressed as a fractional number between 0 and 5, often appearing next to a graphical representation of five stars.
    - The review score that pertains to the main product is usually found directly next to the product-name or price for the main product.
    - Disregard reviews on the page pertaining to anything OTHER THAN the main product.
    - Disregard a review-score that appears directly under a third-party seller labelled like "Sold and shipped by" as that is a review for the third-party seller, not the main product.
    - If a fractional number (like 4.0 or 4.5) appears immediately to the right or left of the {{ stars }} then output the numeric value.
    - Otherwise interpret the review score from the {{ star }} rating shown counting fully filled {{ stars }} as 1.0 point and half filled {{ stars }} as 0.5 points.
    - If the page indicates no reviews, or there is a message like "be the first to review this product", then output a null value.
    - Describe what the review score pertains to in the explain field along with whether the output was taken "numerically" from an explicit number or "graphically" inferred from the stars.
    > NOTE: Some sites use a different glyph to represent review-score (for example newegg uses an egg icon instead of a star icon)--in this situation assume all references to the term "star" in this prompt mean the same thing as whatever glyph the site is using to represent review-score.
  expect: "number-equals"


- name: sku
  type: string
  repeated: false
  locate: |
    Find "SKU" or "Item Number" near the top of the product page or in the product details section.
    It's often retailer-specific.
  expect: "string-match"


- name: stock_status
  type: string
  repeated: false
  locate: |
    An indicatation of the stock-status of the product classified into one of the following:
    - `in-stock` - product is available to purchase, typically with the presence of an "Add to Cart" or "Buy" button
    - `out-of-stock` - product is temporarily unavailable with an expectation of being restocked in the near future
    - `discontinued` - product is no longer available and not expected to become available again at this retailer
    - `indeterminate` - there is no indication of product availability on the page, or otherwise unable to determine product availability
  expect: "string-match"


- name: videos
  type: string
  repeated: true
  locate: |
    An image that typically appears within an image gallery identified by a "play button" icon (i.e. a right pointing chevron icon inside a circle) super-imposed over the thumbnail.
  generate: |
    IMPORTANT: Assuming the generated selector targets image elements, then it should be a jQuery selector that looks something like `$('img.product-video').attr('src')`.
    There may be more images than indicated with the `>` arrow.
    Look for a repeated pattern of image elements within the same parent context and if more images with a strong association are found than indicated then include those in the generated selector as well.
    If there is no `<img src="...">` with a valid URL, look for other attributes that contain a URL such as `<img srcset="...">` or any other attribute that contains a valid URL.
    Also image URL's are sometimes specified in a background-image with the style like so: `<div style="background-image: url('image.jpg');"></div>`.
  qualify: |
    If the generated selector targets an image element and isn't a jQuery selector like `$('img.product-video').attr('src')` (in other words isn't extracting the URL from the `src` attribute or some other attribute), then output `INVALID IMG SELECTOR` explaining how the URL can be extracted using a jQuery selector.
    If there is no `<img src="...">` with a valid URL, look for other attributes that contain a URL such as `<img srcset="...">` or any other attribute that contains a valid URL.
    Also image URL's are sometimes specified in a background-image with the style like so: `<div style="background-image: url('image.jpg');"></div>`.
  expect: "url"


- name: vpn
  type: string
  repeated: false
  locate: |
    Look for a field labeled "Vendor Part Number," "VPN," or "Supplier Part Number".
    This may be found in the product specifications or additional details section.
  expect: "string-match"
