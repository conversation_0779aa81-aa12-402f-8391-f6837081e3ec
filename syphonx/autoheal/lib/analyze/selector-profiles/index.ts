import { fileURLToPath } from "url";
import { clone } from "../../index.js";
import { SelectType } from "syphonx-lib";
import * as fs from "fs";
import YAML from "yaml";

export type SelectorTargetType = "general-product-page" | "autoselect-product-page";

export interface SelectorProfile {
    name: string;
    type: SelectorTargetType;
    targets: Required<SelectorProfileTarget>[];
}

export type SelectorExpectType =
    "boolean" |
    "currency-symbol" |
    "number-equals" |
    "image" |
    "string-distance" |
    "similar-meaning"|
    "string-match" |
    "substring-match" |
    "url";

export interface SelectorProfileTarget {
    name: string;
    type: SelectType;
    repeated?: boolean;
    waitfor?: boolean;
    locate?: string;
    generate?: string;
    qualify?: string;
    expect?: SelectorExpectType;
    filter?: string; // predefined filter
    filterable?: boolean; // indicates whether generating a filter should be attempted, do not use if filter is specified
    generate_filter?: string; // instructions for generating a filter when filterable is true
}

interface ProfileRef {
    name: string;
    type: SelectorTargetType;
    targets: string[];
}

type DescriptorMap = Record<SelectorTargetType, SelectorProfileTarget[]>;

const profile_refs = loadYamlFile(fileURLToPath(new URL("./index.yaml", import.meta.url))) as ProfileRef[];
const descriptor_map: DescriptorMap = {
    "general-product-page": loadYamlFile(fileURLToPath(new URL("./general-product-page.yaml", import.meta.url))) as SelectorProfileTarget[],
    "autoselect-product-page": loadYamlFile(fileURLToPath(new URL("./autoselect-product-page.yaml", import.meta.url))) as SelectorProfileTarget[]
};
const profiles = expandLocateTargetRefs(profile_refs, descriptor_map);

export function selectorProfiles(): string[] {
    return profiles.map(profile => profile.name);
}

export function getSelectorProfile(name: string): SelectorProfile {
    const profile = profiles.find(profile => profile.name === name);
    if (!profile)
        throw new Error(`Selector profile "${name}" not found`);
    return clone(profile) as SelectorProfile;
}

export function tryGetSelectorProfile(name: string): SelectorProfile | undefined {
    const profile = profiles.find(profile => profile.name === name);
    if (profile)
        return clone(profile) as SelectorProfile;
}

function expandLocateTargetRefs(profile_refs: ProfileRef[], descriptor_map: DescriptorMap): SelectorProfile[]  {
    const profile_errors = profile_refs
        .map(obj => {
            const descriptors = descriptor_map[obj.type];
            if (!descriptors)
                return `Selector profile "${obj.name}" has unknown type "${obj.type}"`
            else if (obj.targets.some(key => !lookup(key, descriptors)))
                return `Selector profile "${obj.name}" has unknown descriptor references: ${obj.targets.filter(key => !lookup(key, descriptors)).join(", ")}`;
        })
        .filter(obj => !!obj);

    if (profile_errors.length > 0)
        console.error(profile_errors.join("\n"));

    const profiles = profile_refs.map(obj => ({
        ...obj,
        targets: obj.targets.map(key => lookup(key, descriptor_map[obj.type])).filter(Boolean)
    } as SelectorProfile));

    return profiles;
}

function lookup(ref: string, targets: SelectorProfileTarget[]) {
    const [a, b] = ref.split("->").map(value => value.trim());
    const name = a;
    const key = b || a;
    const target = targets.find(target => target.name === key);
    if (target)
        return { ...target, name };
}

function loadYamlFile(file: string): unknown {
    const text = fs.readFileSync(file, "utf-8");
    return YAML.parse(text);
}