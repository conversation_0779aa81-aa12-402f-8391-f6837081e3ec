- name: availability
  type: string
  repeated: false
  locate: |
    An indication on the page of the product availability of the product, in other words whether it is available or unavailable for purchase.
    Follow the steps below to identify the product availability on the page:
    - Look for a message near the price indicating stock status, such as "In stock", "Out of Stock", "# left in stock", "Backordered", "Discontinued", "Available", "Unavailable", etc.
    - Look for any button that looks like it allows the customer to purchase the product such as a button labelled "Buy", or "Add to Cart" or "Buy Now", etc.
    If any of the above are found, then flag that as an indicator of the product availability.
  filterable: true
  expect: "string-match"


- name: brand_name
  type: string
  repeated: false
  locate: |
    The brand-name is the name of the company or "brand entity" that produced (or manufactured) the product.
    Follow the steps below to identify the brand-name for the product:
    - First look for the brand-name to appear adjacent to the product-name—either directly above, below, or to the left/right of the product-name.
    - The brand-name may be in a smaller font or color than the product-name, and sometimes looks like a clickable hyperlink.
    - The brand-name can also appear elsewhere on the page, with a label of "Brand: XYZ", "Manufacturer: XYZ" or similar label, or it may be in a link that looks like "Visit the XYZ store" or "Other XYZ products" or something like that (where "XYZ" is a placeholder for the actual brand-name).
    - If none of the above, determine if the brand-name appears within the product-name itself, for example at the front like "Sony" in "Sony - Playstation Portal" or "Roku" in "Roku Ultra 4K Streaming Media Player", or at the end like "Stanley" in "Stainles Tumbler by Stanley" (Stanely) and if it does then select the product-name also as the brand-name. In this case add a comment that the brand-name was found within the product-name.
  filterable: true
  generate_filter: If the brand-name follows a label like "Brand" or "Manufacturer" then generate a filter like /Brand:\s*([^ ]+)/ or /Manufacturer:\s*([^ ]+)/. If the brand-name appears as the first word in the product-name, then generate a simple filter like /^(\w+)/ that extracts just the first word even if the brand-name is more than one word.
  expect: "string-match"


- name: gtin
  type: string
  repeated: false
  locate: |
    Look for "GTIN" or "Global Trade Item Number" in the product specifications.
    It may also be listed "UPC" or "Universal Product Code", or "EAN" (European Article Number in the EU) or "ISBN" for books.
    A valid GTIN code is 8-14 numeric digits.
    Only output the actual GTIN or UPC value, not the label, to the `text` field.
    For example if "UPC: 12345678" was found on the page, then output "12345678", not "UPC: 12345678".
  filter: /\d{8,14}/
  expect: "string-match"


- name: mpn
  type: string
  repeated: false
  locate: |
    Look for "Manufacturer Part Number", "MPN", "Model", "Model Number", or "Model Name" in the product details or specifications.
    It's often listed near product deails, specifications, other product identifying information.
    Only output the actual MPN value, not the label, to the `text` field.
    For example if "MPN: XYZ-123" was found on the page, then output "XYZ-123", not "MPN: XYZ-123".
  filterable: true
  generate_filter: If the MPN follows a label like "Manufacturer Part Number" then generate a filter like /Manufacturer Part Number:\s*([^ ]+)/. An MPN is generally a numeric or alphanumeric code that contains only non-space characters.
  expect: "string-match"


- name: price
  type: number
  waitfor: true
  repeated: false
  locate: |
    The sale price of the product. Only one price, the primary final selling price of the product, is expected.
    If there is a message like "See price in cart" in the place of a price, do not output that as the price and instead mention in the explanation that the price is hidden.
  filter: /(?:[$€£¥]\s*)?(?:\d{1,3},)?\d{1,3}(?:\.\d{2})?/
  expect: "number-equals"


- name: product_condition
  type: string
  repeated: false
  locate: An indication of the product condition (NEW, USED, REFURBISHED, etc.).
  filterable: true
  generate_filter: Look to generate a filter that targets the text indicating the actual product condition if possible (NEW, USED, REFURBISHED, etc.).
  expect: "substring-match"


- name: product_description
  type: string
  repeated: false
  locate: |
    The product-description (or "long description") for the main product on the page that generally meets the following requirements:
    - The product-description is a single sentence or a series of short paragraphs describing the product.
    - The product-description may be found under a heading emphasized in bold or slightly larger text with a title suchas "Product Description" or "Details", etc.; or it may be a sentence or series of short paragaphs appearing near the product-name, price, or product-image.
    - If the selector targets a heading or a caption, please make the selector target the text or paragraphs under the heading instead of the heading. We don't want a heading we want the text of the description itself!!
    - If the selector targets text that contains a bulleted list that is preceded by one or more paragraphs, then the paragraphs should be targeted as the `description` and the bulleted list should be targeted as the `features`.
  expect: "substring-match"


- name: product_image
  type: string
  repeated: false
  locate: |
    The main product image (or hero image) for the product.
    This is typically the largest image "above the fold" near the product title and add-to-cart or buy button.
    Look for a large photographic image of the main product or packaged product that fits the product-name and description.
    DO NOT confuse the product-image with a brand logo or the smaller images in a product-image-gallery.
    Look for a label number in square brackets inside the area of the product image, or directly adjacent to the image.
    If no label number is found within or near the product-image, then infer a label number from other labelled items that are closest to the product-image (or the space that might normally be occupied by the product-image).
  expect: "image"


- name: product_name
  type: string
  repeated: false
  waitfor: true
  locate: |
    The product title (or product-name) that names the product and generally meets the following requirements:
    - The product-name is typically at the top of the page, near the product image and price or availability information.
    - The product-name is usually in a large, bold font, making it stand out from other text.
    - The label numbers are in brackets (for example`[34]`) and follow immediately after the name, price, or description.
    - DO NOT confuse the product-name with the category-name or brand-name, which may be in a smaller font or located elsewhere on the page.
    - DO NOT get the product-name from the breadcrumb if one is present on the page, ignore the breadcrumb entirely!!
    - There will only be one main product name on the page.
    - If the product-name is found in multiple places on the page, choose the one with the largest font or the one that is adjacent to the product image and the price/stock-status, or both.
  expect: "string-match"


- name: product_variants
  type: string
  repeated: false
  locate: An option on the page to choose product variants such as color, size, etc.
  filterable: true
  generate_filter: Look to generate a filter that targets the text indicating the actual variant options if possible. For example "LARGE, MEDIUM, SMALL", "RED, GREEN, BLUE", "12 oz, 16 oz, 32 oz", etc.


- name: review_count
  type: number
  repeated: false
  locate: |
    The number of reviews for the main product.
    - Typically appears next to the review-score and expressed as a whole number.
    - If a null value was output for review_score then a null value should also be output for review_count.
  filter: /(?:\d?,?)\d{1,3}/
  expect: "number-equals"


- name: review_score
  type: number
  repeated: false
  locate: |
    The user generated review score of the main product typically expressed as a fractional number between 0 and 5, often appearing next to a graphical representation of five stars.
    - The review score that pertains to the main product is usually found directly next to the product-name or price for the main product.
    - Disregard reviews on the page pertaining to anything OTHER THAN the main product.
    - Disregard a review-score that appears directly under a third-party seller labelled like "Sold and shipped by" as that is a review for the third-party seller, not the main product.
    - If a fractional number (like 4.0 or 4.5) appears immediately to the right or left of the {{ stars }} then output the numeric value.
    - Otherwise interpret the review score from the {{ star }} rating shown counting fully filled {{ stars }} as 1.0 point and half filled {{ stars }} as 0.5 points.
    - If the page indicates no reviews, or there is a message like "be the first to review this product", then output a null value.
    - Describe what the review score pertains to in the explain field along with whether the output was taken "numerically" from an explicit number or "graphically" inferred from the stars.
    > NOTE: Some sites use a different glyph to represent review-score (for example newegg uses an egg icon instead of a star icon)--in this situation assume all references to the term "star" in this prompt mean the same thing as whatever glyph the site is using to represent review-score.
  filter: /\d(?:\.\d{1,3})?/
  expect: "number-equals"


- name: sku
  type: string
  repeated: false
  locate: |
    Find "SKU" or "Item Number" near the top of the product page or in the product details section.
    It's often retailer-specific.
    Only output the actual SKU value, not the label, to the `text` field.
    For example if "SKU: XYZ-123" was found on the page, then output "XYZ-123", not "SKU: XYZ-123".
  filterable: true
  generate_filter: If the SKU follows a label like "SKU" or "Item \#" then generate a filter like /SKU:\s*([^ ]+)/. A SKU is generally a numeric or alphanumeric code that contains only non-space characters.
  expect: "string-match"


- name: vpn
  type: string
  repeated: false
  locate: |
    Look for a field labeled "Vendor Part Number," "VPN," or "Supplier Part Number".
    This may be found in the product specifications or additional details section.
    Only output the actual VPN value, not the label, to the `text` field.
    For example if "VPN: 12345" was found on the page, then output "12345", not "VPN: 12345".
  filterable: true
  generate_filter: If the VPN follows a label like "Part No." or "Item \#" then generate a filter like /Part No\.:\s*([^ ]+)/. A VPN is generally a numeric or alphanumeric code that contains only non-space characters.
  expect: "string-match"
