import * as stream from "node:stream";

import {
    applyAutocloseSelectors,
    autoclose<PERSON><PERSON>yze,    
    autogenProfile,
    classifyLivePage,
    closeLogStream,
    createRemoteLogStream,
    createThumbnailImage,
    openPage,
    parseDomainName,
    sleep,
    tryDownload<PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    LLMModel,
    OpenPageOptions,
    Page
}
from "./lib.js";

import chalk from "chalk";

export interface OpenPageForVisionOptions extends OpenPageOptions {
    page_types?: PageClassification[],
    content_url?: string;
    domain_name?: string;
    model?: LLMModel;
    snooze?: number;
    thumbnail_size?: number;
    autoclose?: boolean;
    classify?: boolean;
    report_path?: string;
    page?: Page;
    stream?: stream.Writable;
}

export interface OpenPageForVisionResult {
    browser?: Browser;
    page?: Page;
    page_class?: PageClassification;
    page_data?: Record<string, unknown>;
    explain?: string;
    analyze: AnalyzeResult[];
    error_code?: string;
    message?: string;
    report_url?: string;
    ok: boolean;
}

export async function openPageForVision(url: string, {
    page_types,
    content_url,
    domain_name,
    model,
    snooze,
    page,
    stream,
    report_path,
    thumbnail_size = 800,
    autoclose = true,
    classify = true,
    ...options
}: OpenPageForVisionOptions = {}): Promise<OpenPageForVisionResult> {
    if (!domain_name && content_url)
        domain_name = parseDomainName(content_url);

    const result: OpenPageForVisionResult = {
        analyze: [],
        page,
        ok: false
    };

    if (report_path) {
        const obj = report_path ? createRemoteLogStream(report_path) : { stream: undefined, url };
        stream = obj.stream;
        result.report_url = obj.url;
    }

    try {
        if (!page) {
            if (process.env.VERBOSE) {
                console.log();
                console.log(chalk.gray(`Opening page... ${url}`));
            }
    
            let html: string|undefined = undefined;
            if (content_url) {
                const download_result = await tryDownloadContent(content_url, url);
                if (!download_result.content && !url) {
                    result.message = download_result.error;
                    if (process.env.VERBOSE)
                        console.log(chalk.red(result.message));
                    result.error_code = "content-download-error";
                    return result;
                }
                html = download_result.content;
            }
    
            const open_result = await openPage({ url, html, ...options });
            if (!open_result.page) {
                if (process.env.VERBOSE)
                    console.log(chalk.red(open_result.error));
                result.error_code = "page-open-error";
                result.message = open_result.error;
                return result;
            }
            page = open_result.page;
            result.page = page;
            result.browser = open_result.browser;

            if (snooze) {
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`Snoozing for ${snooze / 1000} seconds...`));
                await sleep(snooze);
            }
        }
    
        if (autoclose)
            await applyAutocloseSelectors(page, domain_name);
    
        if (stream) {
            const datauri = await createThumbnailImage({ page, size: thumbnail_size });
            if (datauri)
                stream.write(`<img src="${datauri}">`);
            else
                stream.write("<i>Failed to create thumbnail image</i>");
        }

        if (!classify) {
            result.ok = true;
            return result;
        }
    
        const context = {
            domain_name: parseDomainName(url),
            capture_url: url
        };
        if (process.env.VERBOSE)
            console.log(chalk.gray(`Classifying page... (using model ${model || autogenProfile().classifyScreenshot.model})`));
        let page_result = await classifyLivePage({ page, context, model, thumbnail_size, stream });
        if (page_result.analyze)
            result.analyze.push(...page_result.analyze);
        if (!page_result.ok) {
            if (process.env.VERBOSE)
                console.log(chalk.gray(`Error classifying page: ${page_result.message}`));
            result.ok = false;
            result.message = page_result.message;
            return result;
        }
        if (process.env.VERBOSE) {
            console.log(chalk.cyan(`page-class: ${chalk.bold(page_result.page_class)}`));
            if (page_result.page_data)
                console.log(chalk.gray(JSON.stringify(page_result.page_data)));
            console.log(chalk.gray.italic(page_result.explain));
        }

        if (!autoclose || !classify) {
            result.page_class = page_result.page_class;
            result.explain = page_result.explain;
            result.page_data = page_result.page_data;
            result.ok = true;
            return result;
        }
    
        let attempt = 0;
        const max_attempts = 1;
        while ((page_result.page_class === "MODAL" || page_result.page_data?.popup_dialog) && ++attempt <= max_attempts) {
            if (process.env.VERBOSE)
                console.log(chalk.gray(`Auto-closing popup dialog... (attempt ${attempt}/${max_attempts})`));
            const dialog_result = await autocloseAnalyze({ page, domain_name, model, attempt, thumbnail_size, stream });
            result.analyze.push(...dialog_result.analyze);
            if (!dialog_result.ok) {
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`Error auto-closing dialog: ${dialog_result.message}`));
                result.ok = false;
                result.message = dialog_result.message;
                return result;
            }
            if (process.env.VERBOSE) {
                console.log(chalk.gray(`${dialog_result.labels.length} dialog(s) closed...`));
                console.log(chalk.gray.italic(dialog_result.explain));
            }
    
            if (process.env.VERBOSE)
                console.log(chalk.gray(`Re-classifying page... (using model ${model || autogenProfile().classifyScreenshot.model})`));
            page_result = await classifyLivePage({ page, context, model, thumbnail_size, stream });
            if (page_result.analyze)
                result.analyze.push(...page_result.analyze);
            if (!page_result.ok) {
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`Error re-classifying page: ${page_result.message}`));
                result.ok = false;
                result.message = page_result.message;
                return result;
            }
            if (process.env.VERBOSE) {
                console.log(chalk.cyan(`page-class: ${chalk.bold(page_result.page_class)}`));
                if (page_result.page_data)
                    console.log(chalk.gray(JSON.stringify(page_result.page_data)));
                console.log(chalk.gray.italic(page_result.explain));
            }
        }
    
        if (page_result.page_data?.popup_dialog && process.env.VERBOSE) {
            console.log(chalk.yellow(`Unable to autoclose dialogs after ${max_attempts} attempts`));
            console.log(chalk.gray.italic(page_result.explain));
        }

        result.page_class = page_result.page_class;
        result.explain = page_result.explain;
        result.page_data = page_result.page_data;

        if (page_types && page_result.page_class && !page_types.includes(page_result.page_class)) {
            if (process.env.VERBOSE) {
                console.log(chalk.yellow(`Invalid page type ${page_result.page_class}, EXPECTED: ${page_types.join("|")}`));
                console.log(chalk.gray.italic(page_result.explain));
            }
            result.error_code = "invalid-page";
            result.message = `page-class: ${page_result.page_class}\n${page_result.explain}`;
            return result;
        }
    
        result.ok = page_result.ok;
        result.message = page_result.message;
        return result;
    }
    catch (err) {
        const message = `ERROR: ${err instanceof Error ? err.message : JSON.stringify(err)}`;
        if (process.env.VERBOSE)
            console.log(chalk.red(message));
        result.ok = false;
        result.message = message;
        return result;
    }
    finally {
        if (report_path && stream)
            await closeLogStream(stream);
    }
}
