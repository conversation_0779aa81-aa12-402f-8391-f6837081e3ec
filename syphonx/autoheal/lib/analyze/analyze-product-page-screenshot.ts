import { ScanResult } from "../../autoqc.js";
import chalk from "chalk";

import {
    autogenProfile,
    renderJinjaTemplateFile,
    AnalyzeResult,
    LLMChat
} from "./lib.js";


export interface AnalyzeProductPageScreenshotContext {
    domain: string;
}

export interface AnalyzeProductPageScreenshotOptions {
    context: AnalyzeProductPageScreenshotContext;
}

export interface AnalyzeProductPageScreenshotResult {
    ok: boolean;
    summary?: string;
    status?: string;
    scan_result?: Record<string, ScanResult>;
    input: string;
    output: string;
    analyze: AnalyzeResult[];
}

interface ChatResponse {
    status: string;
    summary: string;
    scan_result: any[];
}

export async function analyzeProductPageScreenshot(screenshot_path: string, { context }: AnalyzeProductPageScreenshotOptions): Promise<AnalyzeProductPageScreenshotResult> {
    const model = autogenProfile().autoqc.model;
    const temperature = 0.1;
    
    const input = renderJinjaTemplateFile("autoqc.md", context);
    console.log(chalk.gray(`analyzing screenshot... (using model ${model})`));
    const chat = new LLMChat(model);
    const [response, output] = await chat.json<ChatResponse>(input, { images: [screenshot_path], temperature, json: true });
    if (!response)
        return {
            ok: false,
            input,
            output,
            analyze: []
        };

    return {
        ok: true,
        status: response.status,
        summary: response.summary,
        scan_result: toDictionary(response.scan_result),
        input,
        output,
        analyze: [{
            name: "analyze",
            model,
            tokens: chat.tokens,
            input_tokens: chat.input_tokens,
            output_tokens: chat.output_tokens,
            cost: chat.cost,
            elapsed: chat.elapsed
        }]
    };
}

function toDictionary(scan_result: ScanResult[]) {
    if (scan_result) {
        const result:  Record<string, ScanResult> = {};
        for (const obj of scan_result)
            result[obj.name] = obj;

        if (typeof result.image_count.value === "number"
        && typeof result.video_count.value === "number"
        && result.video_count.value > 0
        && result.image_count.value >= result.video_count.value) {
            const revised_image_count = result.image_count.value - result.video_count.value;
            result.image_count.explain += ` Subtracted ${result.video_count.value} videos from ${result.image_count.value} images to get ${revised_image_count} images... ${result.video_count.explain}`;
            result.image_count.value = revised_image_count;
        }

        return result;
    }
}
