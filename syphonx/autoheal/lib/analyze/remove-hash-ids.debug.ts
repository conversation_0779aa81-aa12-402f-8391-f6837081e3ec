import * as dotenv from "dotenv";
import { fileURLToPath } from "url";

dotenv.config({ path: fileURLToPath(new URL("../.env", import.meta.url)) });

import { removeHashIds  } from "./remove-hash-ids.js";

const html = `
<div id="title" class="main"></div>
<div class="2eE9f"></div>
<div class="N0RSr"></div>
<div class="_3vAIs _11ysM"></div>
<div class="price-_3ap_v"></div>
`;

const result = await removeHashIds({ html });
console.log(result);
