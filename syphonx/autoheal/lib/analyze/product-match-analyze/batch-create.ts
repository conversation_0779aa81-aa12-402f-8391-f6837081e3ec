import * as async from "async-parallel";
import * as path from "path";
import { mkdirSync } from "fs";
import { writeFile } from "fs/promises";
import { SingleBar, Presets } from "cli-progress";

import {
    createProductMatchAnalyzePrompt,
    generateTimestamp,
    insertProductMatchAnalyzeResult,
    openai,
    OutputFileStream
}
from "../../index.js";

export interface CreateProductMatchAnalyzeBatchOptions {
    model: string;
    concurrency: number;
    mode: string;
    price_threshold: number;
}

export interface CreateProductMatchAnalyzeBatchResult {
    batch_id: string;
    batch_count: number;
}

const max_batch_file_size = 104857600; // 100MB

const temp_dir = process.env.TEMP_DIR || "tmp";
const output_dir = path.resolve(temp_dir, "kaboom");

export async function createProductMatchAnalyzeBatch(rows: ProductMatchAnalyzeItem[], { model, mode = "main", price_threshold = 0.45, concurrency = 1 }: Partial<CreateProductMatchAnalyzeBatchOptions>): Promise<CreateProductMatchAnalyzeBatchResult[]> {
    const timestamp = generateTimestamp("seconds");
    const context_dir = path.resolve(output_dir, timestamp);
    mkdirSync(context_dir, { recursive: true });

    const outputs: OutputFileStream[] = [];
    let current_output: OutputFileStream | undefined = undefined;
    let i = 0;

    const progress = new SingleBar({}, Presets.shades_classic);
    progress.start(rows.length, 0);
    await async.each(rows, async row => {
        const prompt_result = await createProductMatchAnalyzePrompt(row, { concurrency: concurrency > 1 ? 4 : 1, price_threshold });
        if (prompt_result.prompt) {
            await writeFile(`${context_dir}/${prompt_result.analyze_id}`, JSON.stringify({ row, prompt_result, price_threshold }));
            const prompt_line = openai.batch.formatBatchPrompt({
                model,
                custom_id: prompt_result.analyze_id,
                text: prompt_result.prompt,
                images: [prompt_result.datauri],
                max_tokens: 8192
            });

            if (!current_output)
                current_output = new OutputFileStream(path.resolve(output_dir, `batch_${timestamp}_${++i}.jsonl`));
            current_output.writeLine(prompt_line, row.snapshot_id);

            if (current_output.size > 0.99 * max_batch_file_size) {
                outputs.push(current_output);
                await current_output.close();
                current_output = undefined;
            }
        }
        else {
            await insertProductMatchAnalyzeResult({
                analyze_id: prompt_result.analyze_id,
                app_name: row.app_name,
                account_key: row.account_key,
                country_code: row.country_code,
                sku: row.sku,
                pages: row.pages.map(page => ({
                    product_match_url: page.url,
                    product_match_status: page.status,
                    product_match_explain: page.explain,
                    product_match_id: page.product_match_id,
                    product_match_active: page.active,
                    page_id: page.name,
                    n1: page.n1,
                    n2: page.n2
                })),
                data: row
            }, mode);
        }
        progress.increment();
    }, concurrency);
    progress.stop();

    if (current_output) {
        outputs.push(current_output);
        await (current_output as OutputFileStream).close();
    }

    const batches: CreateProductMatchAnalyzeBatchResult[] = [];
    for (const output of outputs) {
        const batch = await openai.batch.submitBatch(output.file);
        await openai.batch.bigquery.insertBatch({
            batch_id: batch.id,
            batch_target: "product_match_analyze",
            batch_size: output.keys.length,
            target_ids: output.keys
        });
        await writeFile(path.resolve(output_dir, `${batch.id}.txt`), timestamp);
        //rmSync(output.file);
        batches.push({ batch_id: batch.id, batch_count: output.keys.length });
    }
    return batches;
}
