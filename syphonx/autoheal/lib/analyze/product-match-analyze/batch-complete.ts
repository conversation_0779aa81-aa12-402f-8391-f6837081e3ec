import * as path from "path";
import { existsSync, readFileSync, rmSync, rmdirSync } from "fs";
import { SingleBar, Presets } from "cli-progress";

import chalk from "chalk";

import {
    createProductMatchAnalyzeCompletion,
    openai,
    readFromFileStreamAsync,
    round,
    tryLoadJsonFile,
    CreateProductMatchAnalyzePromptResult
}
from "../../index.js";

export interface CompleteProductMatchAnalyzeBatchResult {
    ok: boolean;
    completions?: number;
    errors?: string[];
}

interface Context {
    row: ProductMatchAnalyzeItem;
    prompt_result: CreateProductMatchAnalyzePromptResult;
    price_threshold: number;
}

const temp_dir = process.env.TEMP_DIR || "tmp";
const output_dir = path.resolve(temp_dir, "kaboom");

export async function completeProductMatchAnalyzeBatch(batch_id: string, mode: string): Promise<CompleteProductMatchAnalyzeBatchResult> {
    const batch = await openai.batch.batchStatus(batch_id);
    if (batch.status !== "completed" || !batch.output_file_id)
        return { ok: false, errors: [`Batch status=${batch.status}`] };

    const batch_file = path.resolve(output_dir, `${batch_id}.txt`);
    if (!existsSync(batch_file))
        return { ok: false, errors: [`Missing batch "${batch_file}"`] };
    
    const context_key = readFileSync(batch_file, "utf-8");
    const context_dir = path.resolve(output_dir, context_key);
    if (!existsSync(context_dir))
        return { ok: false, errors: [`Missing context directory "${context_dir}"`] };

    const batch_count = batch.request_counts?.completed || 0;
    const errors: string[] = [];

    console.log(chalk.gray("Downloading batch output..."));
    const completions_file = await openai.batch.downloadBatchCompletions(batch_id, batch.output_file_id, path.resolve(output_dir, `${batch_id}.jsonl`));
    if (batch.error_file_id) {
        const error_file = await openai.batch.downloadBatchCompletions(batch_id, batch.error_file_id, path.resolve(output_dir, `${batch_id}_errors.jsonl`));
        errors.push(`Batch errors "${error_file}"`);
    }

    console.log(chalk.gray(`Processing ${batch_count} completions...`));
    const progress = new SingleBar({}, Presets.shades_classic);
    progress.start(batch_count, 0);
    let completions = 0;
    await readFromFileStreamAsync(completions_file, async line => {
        const { id: analyze_id, content: completion, ...completion_info } = openai.batch.parseBatchCompletion(line);
        const context_file = path.resolve(context_dir, analyze_id);
        if (existsSync(context_file)) {
            const { row, prompt_result, ...context_info } = tryLoadJsonFile(context_file) as Context;
            await createProductMatchAnalyzeCompletion({
                row,
                ...prompt_result,
                ...completion_info,
                completion,
                mode,
                analyze: [{
                    name: "product-match-analyze",
                    elapsed: 0,
                    model: completion_info.model,
                    tokens: completion_info.total_tokens,
                    input_tokens: completion_info.prompt_tokens,
                    output_tokens: completion_info.completion_tokens,
                    cost: round(completion_info.cost, 6)
                }],
                ...context_info
            });
            completions += 1;
        }
        else {
            errors.push(`Missing context file "${context_file}"`);
        }
        progress.increment();
    });
    progress.stop();
    console.log(chalk.gray(`${batch_count} completed`));

    if (errors.length === 0) {
        //rmSync(completions_file);
        //rmdirSync(context_dir, { recursive: true });
    }

    return { ok: completions > 0, completions, errors };
}
