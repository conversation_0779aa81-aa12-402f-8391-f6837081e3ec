import * as async from "async-parallel";
import chalk from "chalk";

import {
    bufferToDatauri,
    generateUniqueId,
    lookupAutoselectDictionary,
    objIncrement,
    objLookup,
    parseNumber,
    renderProductMatchAnalyzePrompt,
    runAutoselect,
    truncate,
    tryDownloadFile,
    tryOpenPage,
    AutoselectDictionary,
    Browser,
    ImageList
}
from "../../index.js";

import {
    productRefPrice
} from "./common.js";

export interface CreateProductMatchAnalyzePromptResult {
    analyze_id: string;
    prompt: string;
    diffbot_tokens: number;
    datauri: string;
    distinct_image_counts: Record<string, number>;
    errors: number;
    message?: string;
    ok: boolean;
}

export interface CreateProductMatchAnalyzeOptions {
    concurrency: number;
    price_threshold: number;
}

export async function createProductMatchAnalyzePrompt(row: ProductMatchAnalyzeItem, { concurrency, price_threshold }: CreateProductMatchAnalyzeOptions): Promise<CreateProductMatchAnalyzePromptResult> {
    const result: CreateProductMatchAnalyzePromptResult = {
        analyze_id: generateUniqueId(),
        prompt: "",
        diffbot_tokens: 0,
        datauri: "",
        distinct_image_counts: {},
        errors: 0,
        message: "",
        ok: false
    };

    const browsers = new Set<Browser>();
    const image_list = new ImageList();
    await async.each(row.pages, async page => {
        /*
        if (page.active === false) {
            page.status = "DEACTIVATED";
            if (process.env.VERBOSE)
                console.log(chalk.gray.strikethrough(`${page.url} ${page.status}`));
            return;
        }
        */

        const autoselect_dictionary = lookupAutoselectDictionary(page.domain_name);
        if (!autoselect_dictionary?.product_name) {
            page.status = "ERROR";
            page.error = "Autoselect not found";
            if (process.env.VERBOSE)
                console.log(chalk.gray(`${page.url}${!page.active ? chalk.italic(" (deactivated)") : ""} ${chalk.yellow.italic(page.error || page.status)}`));
            return;
        }

        let html: string | undefined = undefined;
        if (page.html_url && page.active) { // don't waste effort trying to download content for deactivated matches because they've not been crawled recently
            const { content, error } = await tryDownloadFile(page.html_url);
            if (!error) {
                html = content;
            }
            else {
                page.error = `Content download failed, url=${page.html_url}, error=${error}`;
                result.errors += 1;
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`${page.url} -> ${page.html_url} ${chalk.yellow(page.error)}`));
            }
        }

        if (!html) {
            const open_result = await tryOpenPage(page.url, { shared: true, headless: false });
            if (!open_result.ok) {
                if (page.active) { // it's only an error if the match is active, otherwise it's not unexpected for an inactive match to not be there
                    page.status = "ERROR";
                    page.error = open_result.message || "Page open error";
                    if (process.env.VERBOSE)
                        console.log(chalk.gray(`${page.url}${!page.active ? chalk.italic(" (deactivated)") : ""} ${chalk.yellow.italic(open_result.message || page.status)}`));
                }
                else {
                    page.status = "NONE";
                }
                return;
            }
            if (open_result.page)
                html = await open_result.page.content();
            if (open_result.browser)
                browsers.add(open_result.browser);
            
            /*
            const { content, error, error_code } = await tryFetchHtml(product_match.url);
            if (!error) {
                html = content;
            }
            else {
                page.error = `PAGE-DOWNLOAD-ERROR: ${error}`;
                result.errors += 1;
                if (error_code === "invalid-content-type")
                    page.status = "INVALID-CONTENT-TYPE";
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`${page.url} ${product_match.error}`));
            }
            */
        }

        if (!html) {
            if (page.active) {
                page.status = "ERROR";
                page.error = "Content not found";;
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`${page.url}${!page.active ? chalk.italic(" (deactivated)") : ""} ${chalk.yellow.italic(page.error || page.status)}`));
            }
            else {
                page.status = "NONE";
            }
            return;
        }

        const page_data = await extractPageData({
            html,
            page,
            image_list,
            autoselect_dictionary
        });

        if (page_data.blocked)
            page.status = "BLOCKED";
        else if (page_data.pnf)
            page.status = "PNF";
        else if (page_data.signin)
            page.status = "SIGNIN";
        else if (page_data.broken)
            page.status = "BROKEN";
        else {
            const hits = Object.keys(page_data).filter(key => page_data[key] !== null);
            const misses = Object.keys(page_data).filter(key => page_data[key] === null);
            if (!page_extract_ok(page_data)) {
                if (page.active) {
                    page.status = "INSUFFICIENT-DATA";
                    page.error = `EXTRACTED: ${[hits.join(", "), `(${hits.length})`].join(" ")}, NOT EXTRACTED: ${[misses.join(", "), `(${misses.length})`].join(" ")}`;
                    if (process.env.VERBOSE)
                        console.log(chalk.gray(`${page.url}${!page.active ? chalk.italic(" (deactivated)") : ""} ${chalk.yellow.italic(page.error || page.status)}`));
                }
                else {
                    page.status = "NONE";
                }
            }
            if (process.env.VERBOSE)
                console.log(chalk.gray(`${page.url}${page.active ? "" : "[deactivated]"} ${chalk.green("EXTRACTED")}: ${[hits.join(", "), `(${hits.length})`].join(" ")}, NOT EXTRACTED: ${[misses.join(", "), `(${misses.length})`].join(" ")}`));
        }

        if (page.status) {
            if (process.env.VERBOSE)
                console.log(chalk.gray(`${page.url}${page.active ? "" : "[deactivated]"} ${chalk.cyan(page.status)}`));
            return;
        }

        page.cross_checks = [];

        if (contains(row.sku, page_data.mpn))
            page.cross_checks.push(`The MPN on the page matches the PRODUCT SKU.`);

        if (contains(row.product_name, page_data.mpn))
            page.cross_checks.push(`The MPN on the page is contained in the PRODUCT NAME.`);

        if (contains(row.sku, page_data.sku))
            page.cross_checks.push(`The SKU on the page matches the PRODUCT SKU.`);

        if (contains(row.product_name, page_data.sku))
            page.cross_checks.push(`The SKU on the page is contained in the PRODUCT NAME.`);

        if (contains(row.gtin, page_data.gtin))
            page.cross_checks.push(`The GTIN on the page matches the PRODUCT GTIN.`);

        if (contains(row.product_name, page_data.gtin))
            page.cross_checks.push(`The GTIN on the page is contained in the PRODUCT NAME.`);

        if (contains(row.sku, page_data.model))
            page.cross_checks.push(`The MODEL on the page matches the PRODUCT SKU.`);

        if (contains(row.product_name, page_data.model))
            page.cross_checks.push(`The MODEL on the page is contained in the PRODUCT NAME.`);

        if (contains(page_data.product_name, row.sku))
            page.cross_checks.push(`The PRODUCT NAME on the page contains the PRODUCT SKU.`);

        if (contains(page_data.product_name, row.gtin))
            page.cross_checks.push(`The PRODUCT NAME on the page contains the PRODUCT GTIN.`);

        if (contains(page_data.description, row.sku))
            page.cross_checks.push(`The PRODUCT DESCRIPTION on the page contains the PRODUCT SKU.`);

        if (contains(page_data.description, row.gtin))
            page.cross_checks.push(`The PRODUCT DESCRIPTION on the page contains the PRODUCT GTIN.`);

        if (contains(page.url, page_data.sku))
            page.cross_checks.push(`The URL contains the PRODUCT SKU.`);

        if (contains(page.url, page_data.gtin))
            page.cross_checks.push(`The URL contains the PRODUCT GTIN.`);

        if (page_data.variants)
            page.cross_checks.push(`There is an option to choose product variants on the page.`);

        if (page_data.currency && page_data.currency !== row.currency)
            page.cross_checks.push(`The currency (${page_data.currency}) does not match expected currency (${row.currency}).`);

        row.ref_price = productRefPrice(row);
        // const page_price = parseNumber(page_data.price);
        // if (row.ref_price && page_price) {
        //     const price_diff = Math.abs(page_price - row.ref_price) / row.ref_price;
        //     if (price_diff > price_threshold)
        //         page.cross_checks.push(`There is a ${(price_diff * 100).toFixed(0)}% difference between the price on the page (${page_price.toFixed(2)}) and the product price (${row.ref_price.toFixed(2)}) exceeding the ${price_threshold * 100}% threshold.`);
        // }
    }, concurrency);

    for (const browser of browsers.values())
        await browser.close();

    // first pass, ensure every row has a name (used for hyperlinking) and a domain id-number...
    const domains: Record<string, number> = {};
    row.pages.forEach(page => {
        page.n1 = objIncrement(domains, page.domain_name);
        page.name = `${page.domain_name}-${page.n1}`;
    });
    // second pass, add domain count for each row...
    row.pages.forEach(page => {
        page.n2 = domains[page.domain_name]
    });

    const pages = row.pages.filter(product_match => !product_match.status);
    if (pages.length > 0) {
        const image_strip = await image_list.createImageStrip();
        if (image_strip)
            result.datauri = bufferToDatauri(image_strip);
        
        result.distinct_image_counts = countDistinctImages(image_list);
        findPrimaryImage(pages, image_list, result.distinct_image_counts);    
        const last_keycode = Object.keys(image_list.keycodes).at(-1);

        pages.forEach(page => {
            if (page.image_refs && page.image_refs > 1)
                page.cross_checks.push(`The PRODUCT IMAGE on the page matches ${page.image_refs - 1} other pages.`);
        });

        const context = {
            ...row,
            pages,
            last_keycode,
            distinct_image_count: Object.keys(result.distinct_image_counts).length
        };
        fixProps(context, row);
        result.prompt = renderProductMatchAnalyzePrompt(context);
        result.ok = true;
    }

    const summary: Record<string, number> = {};
    row.pages.forEach(obj => objIncrement(summary, obj.status));
    result.message = `${row.pages.length} matches, ${pages.length} analyzed, ${Object.keys(summary).map(key => `${summary[key]} ${key.toLowerCase()}`).join(", ")}`;

    return result;
}

interface ExtractPageDataOptions {
    html: string;
    page: ProductMatchAnalyzeSubitem;
    image_list: ImageList;
    autoselect_dictionary: AutoselectDictionary;
}

async function extractPageData({ html, page, image_list, autoselect_dictionary }: ExtractPageDataOptions): Promise<Record<string, string | null>> {
    const data = runAutoselect({
        autoselect_dictionary,
        html,
        url: page.url
    });

    const { product_image, ...obj} = data;
    if (Object.keys(obj).length > 0)
        Object.assign(page, {
            source: "AUTOSELECT",

            product_name: truncate(data.product_name, 500, false),
            brand_name: data.brand_name ? data.brand_name.split(" ").slice(0, 3).join(" ") : undefined,
            price: data.price,
            mpn: data.mpn && data.mpn.length <= 40 ? data.mpn : undefined,
            vpn: data.vpn && data.vpn.length <= 40 ? data.vpn : undefined,
            sku: data.sku && data.sku.length <= 40 ? data.sku : undefined,
            gtin: data.gtin,
            condition: data.product_condition,
            description: truncate(data.product_description, 2000, true),
            availability: data.availability,
            variants: data.product_variants,

            blocked: data.blocked,
            pnf: data.pnf,
            signin: data.signin,
            broken: data.broken
        });

    if (product_image)
        await image_list.add(product_image, page.url);

    if (Object.keys(data).length > 0)
        page.page_data = data;

    return data;
}

function contains(source: string | null, target: string | null): boolean {
    if (!source || !target)
        return false;
    source = source.toLowerCase();
    target = target.toLowerCase();
    return source.includes(target);
}

function countDistinctImages(image_list: ImageList): Record<string, number> {
    // count number of images for each keycode...
    const distinct_image_counts = { ...image_list.keycodes };
    for (const key of Object.keys(distinct_image_counts)) {
        const group = image_list.keycodes[key];
        distinct_image_counts[key] = image_list.groups[group].length;
    }
    return distinct_image_counts;
}

function findPrimaryImage(pages: ProductMatchAnalyzeSubitem[], image_list: ImageList, distinct_image_counts: Record<string, number>): void {
    // find the primary image for each product-match...
    for (const page of pages) {
        const hrefs = image_list.images.filter(obj => obj.href === page.url);
        const [key] = hrefs.map(obj => objLookup(image_list.keycodes, obj.group)).sort();
        if (key) {                
            page.image_key = key;
            page.image_num = parseInt(key, 36) - 9; // A, B, C, ... -> 1, 2, 3, ...
            page.image_refs = distinct_image_counts[key];
            //const group = image_list.keycodes[keycode];
            //product_match.image_refs = image_list.groups[group].length;
        }
    }
}

function fixProps(context: ProductMatchAnalyzeContext, row: ProductMatchAnalyzeItem): void {
    if (row.gtin)
        row.gtin = row.gtin.replace(/^0+/, "");
    if (context.gtin)
        context.gtin = context.gtin.replace(/^0+/, "");
    for (const page of context.pages) {
        if (page.gtin)
            page.gtin = page.gtin.replace(/^0+/, "");
        if (page.currency === "$")
            page.currency = "USD";
    }
}

function page_extract_ok(obj: Record<string, string | null>): boolean {
    if (obj.product_name || obj.gtin || obj.mpn)
        return true;
    else if (Object.keys(obj).length >= 3 && (obj.product_name || obj.gtin || obj.mpn || obj.description || obj.product_image))
        return true;
    else
        return false;
}
