import {
    createProductMatchAnalyzeCompletion,
    createProductMatchAnalyzePrompt,
    round,
    AnalyzeResult,
    LLMChat,
    LLMModel
}
from "../../index.js";
import chalk from "chalk";

export interface ProcessProductMatchAnalyzeOptions {
    mode: string;
    price_threshold: number;
    model: LLMModel;
    concurrency: number;
    run_id?: string;
}

export interface ProcessProductMatchAnalyzeResult {
    analyze_id: string;
    report_url?: string;
}

export async function processProductMatchAnalyze(row: ProductMatchAnalyzeItem, { model = "gpt-4o-mini", price_threshold = 0.45, concurrency = 1, mode = "main", run_id }: Partial<ProcessProductMatchAnalyzeOptions>): Promise<ProcessProductMatchAnalyzeResult> {
    const analyze: AnalyzeResult[] = [];

    if (process.env.VERBOSE)
        console.log(chalk.gray(`${row.account_key} ${row.country_code} ${row.sku} (${row.pages.length} matches)`));

    const prompt_result = await createProductMatchAnalyzePrompt(row, { concurrency, price_threshold });
    const { analyze_id } = prompt_result;
    if (!prompt_result.prompt) {
        if (process.env.VERBOSE)
            console.log(chalk.yellow("No prompt generated"));
        return { analyze_id };
    }

    if (process.env.VERBOSE)
        console.log(chalk.gray(`running prompt (model=${model})`));

    const chat = new LLMChat({ model, max_tokens: 8192 });
    const completion = await chat.prompt(prompt_result.prompt, prompt_result.datauri ? { images: [prompt_result.datauri] } : undefined);
    if (process.env.VERBOSE)
        console.log(chalk.gray(`usage: ${chat.input_tokens} prompt tokens, ${chat.output_tokens} completion tokens ($${round(chat.cost, 6)}), ${(chat.elapsed/1000).toFixed(1)}s`));

    analyze.push({
        name: "product-match-analyze",
        elapsed: chat.elapsed,
        model: chat.model,
        tokens: chat.tokens,
        input_tokens: chat.input_tokens,
        output_tokens: chat.output_tokens,
        cost: round(chat.cost, 6)
    });

    const report_url = await createProductMatchAnalyzeCompletion({
        ...prompt_result,
        row,
        completion,
        model: chat.model,
        prompt_tokens: chat.input_tokens,
        completion_tokens: chat.output_tokens,
        cached_tokens: chat.cached_tokens,
        total_tokens: chat.tokens,
        cost: round(chat.cost, 6),
        mode,
        price_threshold,
        run_id,
        analyze
    });

    return { analyze_id, report_url };
}
