import * as async from "async-parallel";
import chalk from "chalk";

import {
    bigquery,
    lookupAutoselectDictionary,
    runAutoselectTest,
    tryDownloadFile,
    tryOpenPage,
    Browser
}
from "../../index.js";

export interface RunProductMatchAnalyzeTestOptions {
    capture_url: string;
    content_url: string;
    domain_name: string;
    run_id: string;
    test_id: string;
    page_id: string;
    browsers: Set<Browser>;
}

export interface RunProductMatchAnalyzeTestResult {
    status: string;
    obj?: Record<string, string | null>;
    hits?: string[];
    misses?: string[];
}

export async function runProductMatchAnalyzeTest({ capture_url, content_url, domain_name, run_id, test_id, page_id, browsers }: RunProductMatchAnalyzeTestOptions): Promise<RunProductMatchAnalyzeTestResult> {
    const autoselect_dictionary = lookupAutoselectDictionary(domain_name);
    if (!autoselect_dictionary) {
        const status = "AUTOSELECT-NOT-FOUND";
        await bigquery.insert("omega.autoselect_test", {
            timestamp: new Date(),
            domain_name,
            url: capture_url,
            status
        });
        if (process.env.VERBOSE)
            console.log(chalk.yellow(`${page_id} ${status}`));
        return { status };
    }

    let html: string | undefined = undefined;
    const { content, error } = await tryDownloadFile(content_url);
    if ((!content || error) && process.env.VERBOSE)
        console.log(chalk.yellow(`${page_id} ${error || `Unable to download stored HTML content`}\n${content_url}`));
    html = content;

    if (!html) {
        const open_result = await tryOpenPage(capture_url, { shared: true, headless: false });
        if (!open_result.ok && process.env.VERBOSE)
            console.log(chalk.yellow(`${page_id} ${error || "Unable to open page"}\n${capture_url}`));
        if (open_result.page)
            html = await open_result.page.content();
        if (open_result.browser)
            browsers.add(open_result.browser);        
    }

    if (!html) {
        const status = "CONTENT-DOWNLOAD-ERROR";
        await bigquery.insert("omega.autoselect_test", {
            timestamp: new Date(),
            domain_name,
            url: capture_url,
            status,
            error: error || "Unable to open page"
        });
        return { status };
    }

    const test_result = runAutoselectTest({
        autoselect_dictionary,
        html,
        url: capture_url
    });

    const obj: Record<string, string | null> = {};
    for (const { selector_name: key, selector_output: value } of test_result)
        if (value !== null && !obj[key])
            obj[key] = value;
        else if (!(key in obj))
            obj[key] = null;

    const hits = Object.keys(obj).filter(key => !!obj[key]);
    const misses = Object.keys(obj).filter(key => !obj[key]);
    const status = hits.length > 0 ? "OK" : "EMPTY";

    await bigquery.insert("omega.autoselect_test", {
        test_id,
        run_id,
        timestamp: new Date(),
        domain_name,
        url: capture_url,
        status,
        hits: hits.length > 0 ? hits.join(", ") : undefined,
        misses: misses.length > 0 ? misses.join(", ") : undefined,
        test_result
    });

    return { status, obj, hits, misses };
}
