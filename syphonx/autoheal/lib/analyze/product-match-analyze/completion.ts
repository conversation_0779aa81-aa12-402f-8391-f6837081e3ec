import {
    diffText,
    createRemoteLogFile,
    escapeHtmlString,
    insertProductMatchAnalyzeResult,
    le<PERSON><PERSON><PERSON>,
    parse<PERSON><PERSON>ber,
    renderProductMatchAnalyzeReport,
    AnalyzeResult
}
from "../../index.js";

export interface CreateProductMatchAnalyzeCompletionOptions {
    analyze_id: string;
    row: ProductMatchAnalyzeItem;
    prompt: string;
    completion: string;
    model: string;
    prompt_tokens: number;
    completion_tokens: number;
    cached_tokens: number;
    total_tokens: number;
    diffbot_tokens: number;
    cost: number;
    datauri: string;
    distinct_image_counts: Record<string, number>;
    mode: string;
    price_threshold: number;
    run_id?: string;
    analyze: AnalyzeResult[];
}

export async function createProductMatchAnalyzeCompletion({
    analyze_id,
    row,
    prompt,
    completion,
    datauri,
    distinct_image_counts,
    model,
    prompt_tokens, 
    completion_tokens,
    cached_tokens,
    total_tokens,
    diffbot_tokens,
    cost,
    mode,
    price_threshold,
    run_id,
    analyze
}: CreateProductMatchAnalyzeCompletionOptions): Promise<string> {
    // apply completion results
    const analyze_product_matches = applyCompletionResult(completion, row.pages);

    // post process analyzed matches with computed overrides (PRICE, CURRENCY etc.)
    applyPostAnalyzeOverrides(analyze_product_matches, row, price_threshold);

    // flag additional rows with same domain as status=DUPLICATE
    //const redundant_domains = findRedundantDomains(analyze_product_matches);
    //flagDuplicateMatches(analyze_product_matches, redundant_domains);

    const title = `${row.account_key} ${row.country_code} ${row.sku} (${row.app_name.toUpperCase()})`;
    const summary = {
        "ok": row.pages.filter(obj => obj.status === "OK").length,
        "mismatch": row.pages.filter(obj => obj.status === "MISMATCH").length,
        "questionable": row.pages.filter(obj => obj.status === "QUESTIONABLE").length,
        "other": row.pages.filter(obj => !["OK", "QUESTIONABLE", "MISMATCH"].includes(obj.status)).length,
        "total": row.pages.length
    };
    row.summary = summary;
    row.distinct_image_counts = distinct_image_counts;

    const pages = row.pages.map(page => ({
        ...page,
        product_name_diff: row.product_name !== page.product_name ? diffText(row.product_name, page.product_name, "word") : "",
        brand_name_diff: row.brand_name && page.brand_name && row.brand_name !== page.brand_name ? diffText(row.brand_name, page.brand_name, "word") : "",
        gtin_diff: row.gtin && page.gtin && row.gtin !== page.gtin ? diffText(row.gtin.replace(/^0+/, ""), page.gtin.replace(/^0+/, ""), "char") : "",
        gtin_distance: row.gtin && page.gtin ? levenshtein(row.gtin, page.gtin) : -1,
        mpn_diff: row.sku && page.mpn && row.sku !== page.mpn ? diffText(row.sku, page.mpn, "char") : "",
        mpn_distance: row.sku && page.mpn ? levenshtein(row.sku, page.mpn) : -1,
        model_diff: row.sku && page.model && row.sku !== page.model ? diffText(row.sku, page.model, "char") : "",
        model_distance: row.sku && page.model ? levenshtein(row.sku, page.model) : -1,
        price_diff: row.ref_price && page.price ? Math.round(100 * Math.abs(parseNumber(page.price)! - row.ref_price) / row.ref_price) : 0
    }));

    const report_html = renderProductMatchAnalyzeReport({
        analyze_id,
        product_id: row.product_id,
        app_name: row.app_name,
        title,
        prompt: escapeHtmlString(prompt),
        completion: escapeHtmlString(completion),
        model,
        prompt_tokens,
        completion_tokens,
        cached_tokens,
        total_tokens,
        cost,
        product_name: row.product_name,
        brand_name: row.brand_name,
        mpn: row.sku,
        price: row.ref_price || 0,
        currency: row.currency,
        gtin: row.gtin,
        active_matches: pages.filter(obj => groupMatches(obj) === 1).sort((a, b) => activeStatusOrder(a.status) - activeStatusOrder(b.status)),
        deactivated_matches: pages.filter(obj => groupMatches(obj) === 2).sort((a, b) => deactivatedStatusOrder(a.status) - deactivatedStatusOrder(b.status)),
        unanalyzed_matches: pages.filter(obj => groupMatches(obj) === 3),
        distinct_image_counts: Object.entries(distinct_image_counts),
        summary: Object.entries(summary),
        datauri,
        timestamp: new Intl.DateTimeFormat("en-US", { dateStyle: "full", timeStyle: "full" }).format(new Date()),
        obj: row
    });

    const report_url = await createRemoteLogFile(`product-match-analyze/${analyze_id}.html`, report_html);
    await insertProductMatchAnalyzeResult({
        analyze_id,
        app_name: row.app_name,
        account_key: row.account_key,
        country_code: row.country_code,
        sku: row.sku,
        product_id: row.product_id,        
        pages: row.pages.map(page => ({
            product_match_url: page.url,
            product_match_status: page.status,
            product_match_explain: page.explain,
            product_match_confidence: page.confidence,
            product_match_id: page.product_match_id,
            product_match_active: page.active,
            page_id: page.name,
            n1: page.n1,
            n2: page.n2
        })),
        model,
        input_tokens: prompt_tokens,
        output_tokens: completion_tokens,
        cached_tokens,
        total_tokens,
        diffbot_tokens,
        cost,
        run_id,
        report_url,
        data: row,
        analyze
    }, mode);

    return report_url;
}

function applyCompletionResult(completion: string, product_matches: ProductMatchAnalyzeSubitem[]): ProductMatchAnalyzeSubitem[] {
    const analyze_product_matches = product_matches.filter(product_match => !product_match.status);
    completion = completion.replace(/\n{2,}/g, "\n");
    const lines = completion.split("\n").filter(Boolean);
    for (const line of lines) {
        const match = line.match(/^(?<num>\d+)\.\s+(?<status>[^ ]+)\s+(-\s+)?(?<explain>.*)/);
        if (match) {
            // back apply status...
            const { num, status, explain } = match.groups as Record<string, string>;
            const i = parseInt(num) - 1;
            if (analyze_product_matches[i]) {
                analyze_product_matches[i].status = status;
                analyze_product_matches[i].explain = explain;
            }
        }
    }
    return analyze_product_matches;
}

/*
function findRedundantDomains(product_matches: ProductMatchAnalyzeSubitem[]): Record<string, number> {
    const result: Record<string, number> = {};
    for (const product_match of product_matches) {
        const { domain_name } = product_match;
        const same_domain = product_matches.filter(product_match => product_match.domain_name === domain_name);
        if (same_domain.length === 1) {
            product_match.name = domain_name;
        }
        else {
            const index = same_domain.findIndex(obj => obj === product_match);
            product_match.name = `${product_match.domain_name} (${index + 1})`;
            objIncrement(result, domain_name);
        }
    }
    return result;
}

function flagDuplicateMatches(analyze_product_matches: ProductMatchAnalyzeSubitem[], redundant_domains: Record<string, number>) {
    const keys = Object.keys(redundant_domains);
    for (const key of keys) {
        const repeats = analyze_product_matches
            .filter(obj => obj.domain_name === key && ["OK", "QUESTIONABLE"].includes(obj.status))
            .sort((a, b) => a.url.length - b.url.length);
        const [primary, ...duplicates] = repeats;
        duplicates.forEach(obj => {
            obj.status = "DUPLICATE";
            obj.explain += ` There are ${repeats.length - 1} other product matches for ${obj.domain_name}.`;
            obj.url_diff = diffText(primary.url, obj.url, "char");
        });
    }
}
*/

function applyPostAnalyzeOverrides(pages: ProductMatchAnalyzeSubitem[], row: ProductMatchAnalyzeItem, price_threshold: number) {
    for (const page of pages) {
        // transform status codes from prompt output
        if (page.status === "USED")
            page.status = "MISMATCH-USED";

        if (page.status === "LANGUAGE")
            page.status = "QUESTIONABLE-LANGUAGE";

        if (page.status === "MISMATCH") {
            if (page.variants)
                page.status = "QUESTIONABLE-VARIANTS";

            if (page.cross_checks.length > 0)
                page.status = "QUESTIONABLE";
        }            

        if (["OK", "QUESTIONABLE"].includes(page.status)) {
            if (page.currency && page.currency !== row.currency)
                page.status = "QUESTIONABLE-CURRENCY";

            const page_price = parseNumber(page.price);
            if (row.ref_price && page_price) {
                const price_diff = Math.abs(page_price - row.ref_price) / row.ref_price;
                if (price_diff > price_threshold && price_diff < 2.0) // if out of threshold and not too way out of range (200% or more) indicating a parsing error
                    page.status = "QUESTIONABLE-PRICE";
            }
        }
    }
}

function groupMatches({ status, active }: { status: string, active: boolean }): number {
    if (!active && status !== "ERROR")
        return 2;
    if (["OK", "MISMATCH", "DISCONTINUED"].includes(status) || status.startsWith("QUESTIONABLE"))
        return 1;
    else
        return 3;
}

function activeStatusOrder(status: string): number {
    if (status === "MISMATCH")
        return 1;
    else if (status === "DISCONTINUED")
        return 2;
    else if (status.startsWith("QUESTIONABLE"))
        return 3;
    else if (status === "OK")
        return 4;
    else
        return 5;
}

function deactivatedStatusOrder(status: string): number {
    if (status === "OK")
        return 1;
    else if (status.startsWith("QUESTIONABLE"))
        return 2;
    else if (status === "DISCONTINUED")
        return 3;
    else
        return 4;
}
