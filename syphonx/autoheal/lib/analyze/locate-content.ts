import * as stream from "node:stream";
import chalk from "chalk";
import pretty from "pretty";

import {
    autogenProfile,
    addLabels,
    escapeHtmlString,
    removeLabels,
    renderLocateContentPrompt,
    renderScreenshot,
    sanitizeHtml,
    getSelectorProfile,
    summarize,
    truncate,
    <PERSON><PERSON><PERSON>Result,
    LLMChat,
    LLMModel,
    Page,
    RenderHtmlResult
} from "./lib.js";
import { normalizeTargets } from "./autoselect/validator.js";

export interface LocateContentOptions {
    page: Page;
    selector_profile: string;
    domain_name: string;
    model?: LLMModel | LLMModel[];
    keepLabels?: boolean;
    viewport?: { width: number, height: number };
    scale?: number;
    stream?: stream.Writable;
}

export interface LocateContentResult {
    targets?: Record<string, LocateContentTarget>;
    screenshot?: RenderHtmlResult;
    labelled_html?: string;
    message?: string;
    analyze: AnalyzeResult[];
    ok: boolean;
}

export interface LocateContentTarget {
    text?: string;
    explain?: string;
    labels: number[];
    heading?: string;
}

type LocateResponse = Record<string, LocateContentTarget> & { summary: string };

export async function locateContent({
    page,
    selector_profile,
    domain_name,
    model,
    keepLabels,
    stream,
    viewport = { width: 1024, height: 2048 },
    scale
}: LocateContentOptions): Promise<LocateContentResult> {
    const result: LocateContentResult = {
        analyze: [],
        ok: false
    };

    if (process.env.VERBOSE)
        console.log(chalk.gray("Adding labels..."));

    await addLabels(page);

    if (process.env.VERBOSE)
        console.log(chalk.gray(`Rendering screenshot... ${viewport.width}x${viewport.height}${scale ? ` ${scale}X` : ""}`));

    const screenshot = await renderScreenshot(page, {
        maxHeight: viewport.height,
        maxWidth: viewport.width,
        fullPage: true,
        scale
    });

    const { targets } = getSelectorProfile(selector_profile);
    const input = renderLocateContentPrompt({ domain_name, targets });

    let chat: LLMChat | undefined = undefined;
    let locate_response: LocateResponse | undefined = undefined;
    let locate_output = "";

    const models = Array.isArray(model) ? model : model ? [model] : [autogenProfile().locateContent.model];
    for (const locate_model of models) {
        if (process.env.VERBOSE)
            console.log(chalk.gray(`Locating content... (using model ${locate_model})`));

        chat = new LLMChat(locate_model);
        try {
            [locate_response, locate_output] = await chat.json<LocateResponse>(input, { images: [screenshot.datauri] });
            console.log(`Locate_output -- FROM THE MODEL: ${JSON.stringify(locate_output)}`)
            break;
        }

        catch (err) {
            result.message = err instanceof Error ? err.message : JSON.stringify(err);
            stream?.write(`
                <details>
                    <summary>LOCATE <small class="error">ERROR using model ${locate_model}</small></summary>
                    <p>Unable to convert LLM response to JSON. ${escapeHtmlString(result.message)}</p>
                    <details>
                        <summary>PROMPT</summary>
                        <pre>${escapeHtmlString(input)}</pre>
                    </details>
                </details>
            `);
            if (process.env.VERBOSE)
                console.log(chalk.yellow(`Locate content failed (using model ${locate_model}), error=${result.message}`));
        }
    }

    if (!locate_response || !chat)
        return result;

    result.analyze.push({
        name: "locate-content",
        model: chat.model,
        tokens: chat.tokens,
        input_tokens: chat.input_tokens,
        output_tokens: chat.output_tokens,
        cost: chat.cost,
        elapsed: chat.elapsed,
        message: typeof locate_response?.summary === "string" ? locate_response.summary : "Unable to convert LLM response to JSON"
    });

    if (!locate_response) {
        const { summary } = summarize(result.analyze);
        stream?.write(`
            <details>
                <summary>LOCATE <small class="error">ERROR using model ${model}</small></summary>
                <p>Unable to convert LLM response to JSON</p>
                <details>
                    <summary>PROMPT</summary>
                    <pre>${escapeHtmlString(input)}</pre>
                    <hr>
                    <pre>${escapeHtmlString(formatJson(locate_output))}</pre>
                </details>
                <p class="info">USAGE: ${summary}</p>
            </details>
        `);
        if (process.env.VERBOSE)
            console.log(chalk.yellow(`Locate content failed (using model ${model}), error=Unable to convert LLM response to JSON`));
        await removeLabels(page);
        result.screenshot = screenshot;
        result.message = "Unable to convert LLM response to JSON";
        return result;
    }

    const { summary, ...locate_targets } = locate_response;
    console.log(`Locate Targets before processing: ${JSON.stringify(locate_targets)}`)

    // Use the normalizeTargets function to handle all cases of numeric indices
    const processedTargets = normalizeTargets<LocateContentTarget>(locate_targets as Record<string, LocateContentTarget>);

    console.log(`Processed Targets: ${JSON.stringify(processedTargets)}`)
    const keys = Object.keys(processedTargets);
    for (const key of keys) {
        const locate_target = processedTargets[key];
        if (locate_target)
            locate_target.labels = Array.isArray(locate_target.labels) ? locate_target.labels.filter((value: number) => typeof value === "number") : !isNaN(locate_target.labels as any) ? [locate_target.labels as number] : []
    }

    // Update reference for located/unlocated checks to use processedTargets
    const located = keys.filter(key => processedTargets[key]?.labels?.length > 0);
    const unlocated = keys.filter(key => !processedTargets[key]?.labels?.length);

    if (stream) {
        const { summary } = summarize(result.analyze);
        stream.write(`
        <style>
            label { background-color: khaki; font-size: 10px; margin-left: 4px; padding: 2px 4px 2px 4px; border-radius: 4px; }
            .label-target { background-color: khaki; }
            .label-text { background-color: aliceblue; font-size: smaller; }
            .label-explain { font-style: italic; font-size: smaller; margin-bottom: 1em; }
        </style>
        <details>
            <summary>LOCATE <small>(${located.length}/${keys.length} targets located) using model ${model}</small></summary>
            <p>${escapeHtmlString(locate_response.summary)}</p>
            <p>LOCATED: ${located.join(", ")} (${located.length})</p>
            <p>NOT LOCATED: ${unlocated.join(", ")} (${unlocated.length})</p>
            <p>
                <a href="#" data-params="?key=autoselect&action=accept&context=locate" style="margin-left: 8px;"><i class="fa fa-thumbs-o-up"></i></a>
                <a href="#" data-params="?key=autoselect&action=reject&context=locate" style="margin-left: 8px; margin-right: 8px;"><i class="fa fa-thumbs-o-down"></i></a>
                <input type="text" placeholder="Optional comment" style="width: 250px;">
            </p>
            <table>
                <tr>
                    <td>
                        ${keys
                .map(key => ({ ...processedTargets[key], key }))
                .map(({ key, labels, text, explain }) => `
                                <section style="width: 400px;">
                                    <div class="label-title">
                                        <b>${key}</b>
                                        ${labels.length > 0 ? labels.map(label => `<label>[${label}]</label>`).join("") : `<i class="ml1">(not found)</i>`}
                                    </div>
                                    ${text ? `<div class="label-text">${escapeHtmlString(text)}</div>` : ""}
                                    ${explain ? `<div class="label-explain">${escapeHtmlString(explain)}</div>` : ""}
                                </section>`).join("\n")}
                    </td>
                    <td>
                        <small>${screenshot.width}x${screenshot.height}${scale ? ` ${scale}X` : ""}</small>
                        <br>
                        <img src="${screenshot.datauri}">
                    </td>
                </tr>
            </table>
            <details>
                <summary>PROMPT</summary>
                <pre>${escapeHtmlString(input)}</pre>
                <hr>
                <pre>${escapeHtmlString(formatJson(locate_output))}</pre>
            </details>
            <p class="info">USAGE: ${summary}</p>
        </details>\n`.trimStart());
    }

    if (process.env.VERBOSE) {
        console.log(chalk.cyan(`${located.length}/${keys.length} TARGETS LOCATED`));
        for (const key of Object.keys(processedTargets)) {
            const { text, explain } = processedTargets[key];
            console.log(chalk.gray(`${key}: ${text ? truncate(text.trim(), 120, true) : chalk.italic(explain)}`));
        }
        console.log(chalk.gray(`${unlocated.length} not located${unlocated.length > 0 ? ": " + unlocated.join(", ") : ""}`));
    }

    const content = await page.content();
    const labelled_html = formatLabelledHtml(content);
    if (!keepLabels)
        await removeLabels(page);

    const message = `${summary}\n${located.length}/${keys.length} targets located\n${keys.map(key => `${key}: ${processedTargets[key]?.explain || "(not located)"}`).join("\n")}`;
    if (process.env.VERBOSE)
        console.log(chalk.gray("Locate content complete"));

    result.targets = processedTargets;
    result.screenshot = screenshot;
    result.labelled_html = labelled_html;
    result.message = message;
    result.ok = true;
    return result;
}

function formatJson(json: string): string {
    return json.replace(/\[(?:\s*\d+\s*,)*\s*\d+\s*\]/g, match => match.replace(/\s/g, ""));
}

function formatLabelledHtml(html: string): string {
    html = sanitizeHtml(html);
    return html;
}

