import * as stream from "node:stream";
import chalk from "chalk";

import {
    escapeHtmlString,
    renderAnalyzeSelectorVariantsPrompt,
    summarize,
    Analyze<PERSON><PERSON>ult,
    LLMChat,
    LLMModel
}
from "../../index.js";

export interface SelectOptimalSelectorOptions {
    targets: AnalyzeSelectorVariantsTarget[];
    model: LLMModel;
    stream?: stream.Writable;
}

export interface SelectOptimalSelectorResult {
    analyze: AnalyzeResult[];
    message?: string;
    ok: boolean;
}

export async function analyzeSelectorVariants({ targets, model, stream }: SelectOptimalSelectorOptions): Promise<SelectOptimalSelectorResult> {
    if (process.env.VERBOSE)
        console.log(chalk.gray(`Analyzing selector variants (using model ${model})`));

    const result: SelectOptimalSelectorResult = {
        analyze: [],
        ok: false
    };

    const input = renderAnalyzeSelectorVariantsPrompt({ targets });
    const chat = new LLMChat(model);

    let analyze_outputs: AnalyzeSelectorVariantsResponse[] = [];
    let output = "";
    try {
        const response = await chat.json<AnalyzeSelectorVariantsResponse[]>(input, { max_tokens: 30000 });
        if (Array.isArray(response[0]))
            analyze_outputs = response[0];
        output = response[1];
    }
    catch (err) {
        result.ok = false;
        result.message = err instanceof Error ? err.message : JSON.stringify(err);
        return result;
    }

    result.analyze.push({
        name: "analyze-selector-variants",
        model: chat.model,
        tokens: chat.tokens,
        input_tokens: chat.input_tokens,
        output_tokens: chat.output_tokens,
        cost: chat.cost,
        elapsed: chat.elapsed
    });

    if (analyze_outputs.length === 0) {
        result.ok = false;
        result.message = "No analyze outputs found";
        return result;
    }

    const { summary } = summarize(result.analyze);
    if (Array.isArray(analyze_outputs) && analyze_outputs.every(obj => typeof obj === "object" && obj !== null)) {
        for (const analyze_output of analyze_outputs) {
            const target = targets.find(target => target.name === analyze_output.name);
            if (target) {
                target.selector = analyze_output.selector;
                target.semantic = analyze_output.semantic;
                target.nth_child = analyze_output.selector.split("nth-child").length - 1;
                target.nth_of_type = analyze_output.selector.split("nth-of-type").length - 1;
                target.hash = analyze_output.hash;
                let formatted_selector = analyze_output.selector;
                
                for (const key of analyze_output.semantic)
                    formatted_selector = formatted_selector.replaceAll(key, `<span class="success">${key}</span>`);
                for (const key of analyze_output.hash)
                    formatted_selector = formatted_selector.replaceAll(key, `<span class="error">${key}</span>`);
                formatted_selector = formatted_selector.replace(/((?:nth-child|nth-of-type)\(\d+\))/g, `<span class="warning">$1</span>`);
                
                stream?.write(`
                    <details>
                        <summary>${target.name} <span style="font-weight: normal;">${formatted_selector}</span></summary>
                        <p><i>${escapeHtmlString(analyze_output.explain)}</i></p>
                        <div>${`${target.semantic.length} semantic, ${target.hash.length} hash, ${target.nth_child} nth-child, ${target.nth_of_type} nth-of-type`}</div>
                        <ul style="list-style-type: none; padding-left: 0px;">
                            ${target.candidates.map(candidate => `<li${candidate === target.selector ? ` style="background-color: #FFFF99;"` : ""}><span style="display: inline-block; width: 24px;">${candidate === target.selector ? `&gt;` : "&ndash;"}</span><span${candidate === target.selector ? ` style="font-weight: bold;"` : ""}>${escapeHtmlString(candidate)}</span></li>`).join("\n")}
                        </ul>
                        <p>
                            <a href="#" data-params="?key=autoselect&action=accept&context=variants&selector=${target.name}" style="margin-left: 8px;"><i class="fa fa-thumbs-o-up"></i></a>
                            <a href="#" data-params="?key=autoselect&action=reject&context=variants&selector=${target.name}" style="margin-left: 8px; margin-right: 8px;"><i class="fa fa-thumbs-o-down"></i></a>
                            <input type="text" placeholder="Optional comment" style="width: 250px;">
                        </p>
                    </details>
                `);                
            }
        }
        result.ok = true;
    }
    else {
        stream?.write(`<p class="error">LLM returned invalid data.</p>`);
        if (process.env.VERBOSE)
            console.log(chalk.red(`LLM returned invalid data.`));
    }
    stream?.write(`
        <details>
            <summary>PROMPT</summary>
            <pre>${escapeHtmlString(input)}</pre>
            <hr>
            <pre>${escapeHtmlString(output)}</pre>
        </details>
        <p class="info">USAGE: ${summary}</p>
    `);

    return result;
}
