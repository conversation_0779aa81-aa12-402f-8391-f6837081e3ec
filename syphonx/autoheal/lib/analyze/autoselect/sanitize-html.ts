import * as stream from "node:stream";

import {
    escapeHtmlString,
    removeHashIds,
    AnalyzeResult,
    LLMModel
}
from "../../index.js";

export interface SanitizeHtmlOptions {
    html: string;
    model?: LLMModel;
    stream: stream.Writable | undefined
}

export interface SanitizeHtmlResult {
    html: string;
    analyze: AnalyzeResult[];
}

export async function sanitizeHtml({ html, model, stream }: SanitizeHtmlOptions): Promise<SanitizeHtmlResult> {
    const hash_result = await removeHashIds({ html, model });

    let pre = escapeHtmlString(html);
    for (const hash_id of hash_result.hash_ids)
        pre = pre.replaceAll(hash_id, `<s class="error">${hash_id}</s>`);

    stream?.write(`
        <details>
            <summary style="font-weight: normal;">${hash_result.hash_ids.length} hash-ids removed using model ${hash_result.model}</summary>
            ${hash_result.hash_ids.length > 0 ? `
            <ul>
                ${hash_result.hash_ids.map(key => `<li>${key}</li>`).join("\n")}
            </ul>
            ` : ""}
            <pre>${pre}</pre>
        </details>
    `);

    return {
        html: hash_result.html,
        analyze: hash_result.analyze
    };
}
