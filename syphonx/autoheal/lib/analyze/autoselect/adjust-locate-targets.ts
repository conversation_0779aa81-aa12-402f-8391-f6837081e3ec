import * as stream from "node:stream";
import chalk from "chalk";

import {
    collapseWhitespace,
    escapeHtmlString,
    sliceLabelledHtml,
    truncate,
    LocateContentTarget,
    Page,
    SelectorProfileTarget
} from "../../index.js";

import { findClosestHtmlSubstringMatch } from "../../scripts/find-closest-html-substring-match.js";
import { findClosestImageByLabel } from "../../scripts/find-closest-image-by-label.js";
import { findClosestTextMatchByLabel } from "../../scripts/find-closest-text-match-by-label.js";
import { findClosestSubstringMatchByLabel } from "../../scripts/find-closest-substring-match-by-label.js"
import { applyFilter, querySelectorOutput } from "./common.js";

export interface AdjustLocateTargetOptions {
    name: string;
    locate_target: LocateContentTarget;
    profile_target?: SelectorProfileTarget;
    page: Page;
    labelled_html: string;
    stream?: stream.Writable;
}

export interface AdjustLocateTargetResult {
    label: number;
    selector_output: string | null | undefined;
}

const max_distance = 25; // max label distance allowed for retargeting

export async function adjustLocateTarget({ name, locate_target, profile_target, page, labelled_html, stream }: AdjustLocateTargetOptions): Promise<AdjustLocateTargetResult> {
    const [source_label] = locate_target.labels;
    let target_label = source_label;
    let debug = "";
    const expect = profile_target?.expect;

    if (process.env.VERBOSE)
        console.log(chalk.gray(`${name}: adjusting locate target`));

    if (expect === "image") {
        const script = findClosestImageByLabel;
        const options = { label: target_label, up: 6, minWidth: 150, minHeight: 150 };
        target_label = await page.evaluate(script, options);
        debug = `${script.name}(${JSON.stringify(options)}) -> ${target_label}`;
    }
    else if (expect === "string-distance" && locate_target.text) {
        const script = findClosestTextMatchByLabel;
        const options = { label: target_label, text: locate_target.text };
        target_label = await page.evaluate(script, options);
        debug = `${script.name}(${JSON.stringify(options)}) -> ${target_label}`;
    }
    else if (expect === "string-match" && locate_target.text) {
        const script = findClosestTextMatchByLabel;
        const options = { label: target_label, text: locate_target.text };
        target_label = await page.evaluate(script, options);
        debug = `${script.name}(${JSON.stringify(options)}) -> ${target_label}`;
    }
    else if (expect === "substring-match" && locate_target.text) {
        const script = findClosestSubstringMatchByLabel;
        const options = { label: target_label, text: locate_target.text };
        target_label = await page.evaluate(script, options);
        debug = `${script.name}(${JSON.stringify(options)}) -> ${target_label}`;
    }
    else if (expect === "number-equals" && locate_target.text) {
        const script = findClosestHtmlSubstringMatch;
        const options = { label: target_label, text: locate_target.text };
        target_label = await page.evaluate(script, options);
        debug = `${script.name}(${JSON.stringify(options)}) -> ${target_label}`;
    }

    const distance = Math.abs(source_label - target_label);
    const ok = distance <= max_distance;
    const status = ok ? `DISTANCE=${distance}` : `DISTANCE=${distance} LIMIT=${max_distance} EXCEEDED`;
    if (process.env.VERBOSE) {
        if (source_label !== target_label)
            console.log(chalk.gray(`${name}: RETARGET ${source_label} -> ${target_label} (${ok ? status : chalk.yellow(status)})`));
        else
            console.log(chalk.gray(`${name}: NO RETARGET`));
    }

    const selector = `[__label__='${ok ? target_label : source_label}']`;
    const attr = expect === "image" ? "src" : undefined;
    const selector_output = await querySelectorOutput(page, selector, attr);
    const filtered_output = profile_target?.filter && selector_output ? applyFilter(profile_target.filter, selector_output) : undefined;
    if (process.env.VERBOSE)
        console.log(chalk.gray(`${name}: selector output ${selector_output ? `${truncate(collapseWhitespace(selector_output, { singleLine: true }), 50)}${filtered_output ? ` -> ${truncate(collapseWhitespace(filtered_output, { singleLine: true }), 50)}filtered_output}` : ""}` : chalk.italic("(none)")}`));

    if (stream) {
        const slice = sliceLabelledHtml(labelled_html, [source_label, target_label], 0);
        stream.write("<details>\n");
        stream.write(`<summary>${name} <span style="font-weight: normal;">
            ${target_label !== source_label ?
                `<small>[${source_label}] &rarr; [${target_label}]${ok ? "" : ` <span${!ok ? ` class="error"`: ""}>(${status})</span>`}</small>` :
                `<small>[${target_label}]</small>`}
            ${selector_output ? `
                <small style="margin-left: 8px;">
                &rarr;
                ${escapeHtmlString(truncate(selector_output, 200, true))}
                ${filtered_output && filtered_output !== selector_output ? ` &rarr; ${escapeHtmlString(filtered_output)}` : ""}
                </small>
            ` : ""}
            </span></summary>\n`);
        if (debug)
            stream.write(`<p><code>${escapeHtmlString(debug)}</code></p>`);
        if (slice?.html_with_linenums)
            stream.write(`<pre>${escapeHtmlString(slice.html_with_linenums)}</pre>`);
        stream.write(`
            <p>
                <a href="#" data-params="?key=autoselect&action=accept&context=retargeting&selector=${name}" style="margin-left: 8px;"><i class="fa fa-thumbs-o-up"></i></a>
                <a href="#" data-params="?key=autoselect&action=reject&context=retargeting&selector=${name}" style="margin-left: 8px; margin-right: 8px;"><i class="fa fa-thumbs-o-down"></i></a>
                <input type="text" placeholder="Optional comment" style="width: 250px;">
            </p>\n`);
        stream.write("</details>\n");
    }

    return {
        label: ok ? target_label : source_label,
        selector_output
    };
}
