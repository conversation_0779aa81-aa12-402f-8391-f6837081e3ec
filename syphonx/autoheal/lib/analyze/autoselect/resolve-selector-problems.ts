import * as stream from "node:stream";
import chalk from "chalk";

import { CheerioAPI } from "cheerio";

import {
    collapseWhitespace,
    escapeHtmlString,
    parseKeyVal<PERSON>,
    renderResolveSelectorProblemsPrompt,
    summarize,
    truncate,
    AnalyzeR<PERSON>ult,
    LLMChat,
    LLMModel
}
from "../../index.js";

import {
    AnalyzeSelectorTarget,
    applyFilter,
    formatFilter
}
from "./common.js";

export interface RefineSelectorOptions {
    analyze_target: AnalyzeSelectorTarget;
    problems: string[];
    model: LLMModel;
    html: string; // has line-numbers, for display only to prompt, do not use for selecting!
    linenum: number; // target line # within line-numbered HTML
    retries: number;
    stream?: stream.Writable;
    $: CheerioAPI
}

export interface RefineSelectorResult {
    analyze: AnalyzeResult[];
    message?: string;
    ok: boolean;
}

const label_retarget_limit = 100;
const truncate_limit = 80;

export function detectSelectorProblems(analyze_target: AnalyzeSelectorTarget): string[] {
    const problems = [];
    const exact_match = !!analyze_target.locate_target && analyze_target.locate_target.text?.toLowerCase() === analyze_target.selector_output?.toLowerCase();

    if (analyze_target.semantic && analyze_target.semantic.length === 0)
        problems.push("The selector is too generic and lacks any semantic meaning, can you find any additional semantic meaning that can be attached to this selector? Consider targeting stable text content using `:contains()`.");

    if (analyze_target.hash && analyze_target.hash.length > 0)
        problems.push("The selector contains one or more hash identifers which may be unstable, can you revise the selector to avoid the hash-ids?");

    if (analyze_target.selector?.includes("nth-"))
        problems.push("The selector contains one or more nth-child or nth-of-type combinators, can you revise the selector to avoid nth-child or nth-of type? Consider targeting stable text content using `:contains()`.");

    if (analyze_target.profile_target?.filterable && !exact_match)
        problems.push(`The expected selector output does not match the actual selector output, can you add a filter and/or adjust the target element such that the actual selector output will match the expected output? ${analyze_target.profile_target.generate_filter || ""}`.trim());

    if (analyze_target.profile_target?.expect === "number-equals" && !exact_match)
        problems.push(`The selector does not match the expected result of "${analyze_target.locate_target.text}", can you adjust the target element?`);

    return problems;
}

export async function resolveSelectorProblems({
    analyze_target,
    model,
    problems,
    linenum,
    html,
    retries,
    stream,
    $
}: RefineSelectorOptions): Promise<RefineSelectorResult> {
    const result: RefineSelectorResult = {
        ok: false,
        analyze: []
    };

    if (!analyze_target.selector) {
        result.message = "Undefined selector.";
        if (process.env.VERBOSE)
            console.log(chalk.yellow(`${analyze_target.name}: cannot refine selector... ${result.message}`));
        stream?.write(`<p class="error">${escapeHtmlString(result.message)}</p>\n`);
        return result; // should never happen
    }

    const target_label = $(analyze_target.selector).attr("__label__");
    if (!target_label) {
        result.message = `No target label found for selector ${analyze_target.selector}.`;
        if (process.env.VERBOSE)
            console.log(chalk.yellow(`${analyze_target.name}: cannot refine selector... ${result.message}`));
        stream?.write(`<p class="error">${escapeHtmlString(result.message)}</p>\n`);
        return result; // should never happen
    }
    else if (parseInt(target_label) !== analyze_target.label) {
        result.message = `Target label [${target_label}] does not match expected label [${analyze_target.label}].`;
        if (process.env.VERBOSE)
            console.log(chalk.yellow(`${analyze_target.name}: cannot refine selector... ${result.message}`));
        stream?.write(`<p class="error">${escapeHtmlString(result.message)}</p>\n`);
        return result; // should never happen
    }

    if (process.env.VERBOSE) {
        console.log(chalk.gray(`${analyze_target.name}: resolving ${problems.length} selector problems ${chalk.cyan(analyze_target.selector)}... (using model ${model})`));
        console.log(chalk.gray(`PROBLEMS:\n${problems.map(line => `- ${line}`).join("\n")}`));
    }

    const original_selector = analyze_target.selector;
    const expected_output = analyze_target.locate_target.text?.replace(/(\[\d{1,4}\])/g, ""); // remove any label references, for example "Acme [34]" -> "Acme"
    const actual_output = analyze_target.selector_output;

    const input = renderResolveSelectorProblemsPrompt({
        selector: analyze_target.selector,
        context: analyze_target.name.replaceAll("_", "-"),
        filterable: analyze_target.profile_target?.filterable,
        html,
        linenum,
        problems,
        expected_output,
        actual_output
    });

    const chat = new LLMChat(model);
    let output;
    try {
        output = await chat.prompt(input);
    }
    catch (err) {
        if (process.env.VERBOSE)
            console.log(chalk.red(err instanceof Error ? err.message : JSON.stringify(err)));
        return result;
    }

    let label = undefined;
    let hits = 0;

    let obj = parseKeyValues(output) as Record<string, string | undefined>;
    if (obj.filter)
        obj.filter = formatFilter(obj.filter);

    while (obj.selector) {
        if (process.env.VERBOSE) {
            if (obj.selector)
                console.log(chalk.gray(`${analyze_target.name}: GENERATED SELECTOR ${chalk.cyan(obj.selector)}${obj.filter ? `, filter=${chalk.cyan(obj.filter)}` : ""}`));
            console.log(chalk.italic.gray(obj.explain));
        }

        let message = "";
        try {
            label = $(obj.selector).attr("__label__");
            hits = $(obj.selector).length;
            analyze_target.selector_output_unfiltered = $(obj.selector).text().trim();
            if (analyze_target.profile_target?.filterable)
                if (analyze_target.selector_output_unfiltered && obj.filter)
                    analyze_target.selector_output_filtered = applyFilter(obj.filter, analyze_target.selector_output_unfiltered);
        }
        catch (err) {
            message = `ERROR ${err instanceof Error ? err.message : JSON.stringify(err)}`;
            if (obj.selector)
                console.log(chalk.gray(`${analyze_target.name}: ${message}`));
            result.message += `\n${message}`;
            break;
        }

        if (hits === 0)
            message = "The selector resulted in no hits.";
        else if (hits !== 1)
            message = "The selector resulted in more than one hit where only a single hit is expected.";
        //else if (label !== target_label)
            //message = `The selector targeted the wrong element #${label}, expected #${target_label}.`;
        else if (label && target_label && label !== target_label && Math.abs(parseInt(label) - parseInt(target_label)) > label_retarget_limit)
            message = `The selector re-targeted another element #${label} that is too far away from the expected target element #${target_label}.`;
        else if (obj.filter && expected_output && !analyze_target.selector_output_filtered)
            message = `Applying the filter resulted in no output.`;
        else if (obj.filter && expected_output && expected_output.trim().toLowerCase() !== analyze_target.selector_output_filtered?.trim().toLowerCase())
            message = `The filtered output \`${truncate(collapseWhitespace(analyze_target.selector_output_filtered!), truncate_limit, true)}\` still does not match the expected output \`${truncate(expected_output, truncate_limit, true)}\`.`;
        else
            result.ok = true;

        if (message && process.env.VERBOSE)
            console.log(chalk.yellow(`${analyze_target.name}: ${message}${chat.turns > 1 && chat.turns <= retries ? ` (RETRY ${chat.turns - 1}/${retries})` : chat.turns > retries ? ` ${retries} RETRIES EXCEEDED` : ""}`));

        if (result.ok) {
            if (process.env.VERBOSE)
                console.log(chalk.green(`${analyze_target.name}: ${problems.length} problems resolved after ${chat.turns} turns`));
            break;
        }
        
        if (chat.turns > retries) {
            if (process.env.VERBOSE)
                console.log(chalk.red(`${analyze_target.name}: giving up with model ${model} after ${chat.turns} turns`));
            break;
        }

        try {
            output = await chat.prompt(`${message} Please try again.`);
        }
        catch (err) {
            if (process.env.VERBOSE)
                console.log(chalk.red(err instanceof Error ? err.message : JSON.stringify(err)));
            return result;
        }

        obj = parseKeyValues(output);
        if (obj.filter)
            obj.filter = formatFilter(obj.filter);
    }

    if (result.ok) {
        analyze_target.selector = obj.selector;
        if (obj.filter)
            analyze_target.filter = obj.filter;
        const updated_selector_output = analyze_target.selector_output_filtered || analyze_target.selector_output_unfiltered;
        if (updated_selector_output)
            analyze_target.selector_output = updated_selector_output;
    }
        
    result.message = obj.explain;

    result.analyze.push({
        name: "refine-selector",
        model: chat.model,
        tokens: chat.tokens,
        input_tokens: chat.input_tokens,
        output_tokens: chat.output_tokens,
        cost: chat.cost,
        elapsed: chat.elapsed
    });

    const { summary } = summarize(result.analyze);
    stream?.write(`
        <details>
            <summary style="font-weight: normal;">${problems.length} problems ${result.ok ? `<span class="success">resolved</span>` : `<span class="error">unresolved</span>`} using model ${model} in ${chat.turns} turns</summary>
            <ul style="list-style-type: '- '; padding: 0; margin-left: 32px;">
                ${problems.map(problem => `<li>${escapeHtmlString(problem)}</li>`).join("\n")}
            </ul>
            <div>ORIGINAL SELECTOR: <code>${escapeHtmlString(original_selector)}</code> <small>[${target_label}]</small></div>
            ${result.ok ? `
            <div>REVISED SELECTOR: <code>${escapeHtmlString(analyze_target.selector)}</code> <small>[${label}]</small></div>
            ${analyze_target.filter ? `<div>FILTER: <code>${escapeHtmlString(analyze_target.filter)}</code></div>` : ""}
            <div>STATUS: SOLVED (${chat.turns} turns)</div>
            <p><i>${escapeHtmlString(result.message)}</i></p>
            ` :
            `
            <div>REVISED SELECTOR: <i>(none)</i></div>
            <div>STATUS: UNSOLVED (${chat.turns} turns)</div>
            `}

            <p>
                <a href="#" data-params="?key=autoselect&action=accept&context=problems&selector=${analyze_target.name}" style="margin-left: 8px;"><i class="fa fa-thumbs-o-up"></i></a>
                <a href="#" data-params="?key=autoselect&action=reject&context=problems&selector=${analyze_target.name}" style="margin-left: 8px; margin-right: 8px;"><i class="fa fa-thumbs-o-down"></i></a>
                <input type="text" placeholder="Optional comment" style="width: 250px;">
            </p>

            <details>
                <summary>HTML</summary>
                <pre>${escapeHtmlString(html)}</pre>
            </details>

            <details>
                <summary>PROMPT</summary>
                ${chat.messages.map(message => `<u>${message.role}</u><pre>${escapeHtmlString(message.text)}</pre>`).join("\n<hr>\n")}
            </details>
            <p class="info">USAGE: ${summary}</p>
        </details>
    `);

    if (process.env.VERBOSE)
        console.log(chalk.gray(`${analyze_target.name}: resolved ${problems.length} problems in ${chat.turns} turns`));

    return result;
}
