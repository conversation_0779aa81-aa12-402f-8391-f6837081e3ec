import * as cheerio from "cheerio";

import {
    autoselectEvaluate,
    AutoselectDictionary
} from "./common.js";

export interface RunAutoselectOptions {
    autoselect_dictionary: AutoselectDictionary;
    html: string;
    url: string;
}

export function runAutoselect({ autoselect_dictionary, html, url }: RunAutoselectOptions): Record<string, string | null> {
    const result: Record<string, string | null> = {};

    const origin = new URL(url).origin;
    const $ = cheerio.load(html);

    for (const key of Object.keys(autoselect_dictionary)) {
        const selectors = autoselect_dictionary[key];
        for (const obj of selectors) {
            result[key] = autoselectEvaluate($, obj, origin);
            if (result[key])
                break;
        }
    }

    return result;
}

export interface AutoselectTestResult {
    selector_name: string;
    selector: string;
    selector_output: string | null;
}

export function runAutoselectTest({ autoselect_dictionary, html, url }: RunAutoselectOptions): AutoselectTestResult[] {
    const result: AutoselectTestResult[] = [];

    const origin = new URL(url).origin;
    const $ = cheerio.load(html);

    for (const key of Object.keys(autoselect_dictionary)) {
        const selectors = autoselect_dictionary[key];
        for (const obj of selectors) {
            const value = autoselectEvaluate($, obj, origin);
            result.push({
                selector_name: key,
                selector: obj.selector,
                selector_output: value || null
            });
        }
    }

    return result;
}
