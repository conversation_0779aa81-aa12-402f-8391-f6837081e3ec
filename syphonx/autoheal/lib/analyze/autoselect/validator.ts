import { LocateContentTarget } from "../locate-content.js";

/**
 * Removes numeric indices from an object and returns a clean array of values
 * This is useful for cleaning up locate_targets objects that may have numeric keys
 * @param obj Object with numeric keys like {"0": {...}, "1": {...}}
 * @returns Array of values without the numeric indices
 */
export function cleanNumericIndices<T>(obj: Record<string, T>): T[] {
    if (!obj) return [];

    return Object.entries(obj)
        .filter(([key]) => !isNaN(Number(key)))
        .map(([_, value]) => value);
}

/**
 * Transforms an object with numeric keys into a clean object with named properties
 * This preserves the structure but removes the numeric indices
 * @param obj Object with numeric keys
 * @returns Clean object without numeric indices
 */
export function transformTargets<T>(obj: Record<string, T>): Record<string, T> {
    if (!obj) return {};

    const result: Record<string, T> = {};

    for (const [key, value] of Object.entries(obj)) {
        if (!isNaN(Number(key))) continue;

        result[key] = value;
    }

    return result;
}

/**
 * Interface for objects that have a name property
 * This is used to type objects that can be normalized by name
 */
export interface NamedObject {
    name?: string;
    [key: string]: any;
}

/**
 * Normalizes a target object by removing numeric indices and using the target's
 * 'name' field as the key instead. Works on both direct index-to-target mappings
 * or more complex nested structures.
 * 
 * Handles these cases:
 * 1. {"0": {"name": "price", ...}, "1": {"name": "product_name", ...}} -> {"price": {...}, "product_name": {...}}
 * 2. {"price": {"name": "price", ...}, "product_name": {...}} -> (remains the same)
 * 3. Mixed case with both numeric and named keys
 * 
 * @param obj The target object with potential numeric keys
 * @returns A normalized object with named keys only
 */
export function normalizeTargets<T extends NamedObject>(obj: Record<string, T>): Record<string, T> {
    if (!obj) return {};

    const result: Record<string, T> = {};

    // First copy all non-numeric keys
    for (const [key, value] of Object.entries(obj)) {
        if (isNaN(Number(key))) {
            result[key] = value as T;
        }
    }

    // Then process numeric keys, using the 'name' property as the new key if available
    for (const [key, value] of Object.entries(obj)) {
        if (!isNaN(Number(key)) && value && typeof value === 'object') {
            // Get the 'name' property to use as the key
            const nameKey = value.name && typeof value.name === 'string' ? value.name : null;

            // Only add it if we have a valid name and it's not already in the result
            if (nameKey && !result[nameKey]) {
                result[nameKey] = value as T;
            }
        }
    }

    return result;
} 