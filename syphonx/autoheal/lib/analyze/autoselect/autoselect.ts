import chalk from "chalk";

import {
    closeLogStream,
    createRemoteLogStream,
    escapeHtmlString,
    generateUniqueId,
    insertAutoselectLog,
    openPageForVision,
    parseDomainName,
    printCostSummary,
    waitForKeypress,
    AutoselectLog,
    Page
}
from "../../index.js";

import { autoselect_page } from "./autoselect-page.js";

const open_page_model = "gemini-2.0-flash-exp";
const headless = false;
const viewport = { width: 1800, height: 2000 };
const scale = 0.55;

export interface AutoselectOptions {
    url: string;
    source: string;
    domain_name?: string;
    content_url?: string;
    snooze?: number;
    title?: string;
    autoclose?: boolean;
    classify?: boolean;
    pause?: boolean;
    page?: Page;
}

export interface AutoselectResult {
    autoselect_id: string;
    domain_name: string;
    report_url: string;
    status: string;
    elapsed: number;
    ok: boolean;
}

export async function autoselect({
    url,
    source,
    domain_name,
    content_url,
    title,
    snooze,
    autoclose,
    pause,
    classify,
    page
}: AutoselectOptions): Promise<AutoselectResult> {
    const autoselect_id = generateUniqueId();
    if (!domain_name)
        domain_name = parseDomainName(url);

    const { stream, url: report_url } = createRemoteLogStream(`autoselect/${autoselect_id}.html`, { title: "Ω AUTOSELECT", subtitle: title || domain_name });
    stream.write(`<a href="${url}" target="_blank">${escapeHtmlString(url)}</a>\n`);
    stream.write(`&nbsp;<a href="${escapeHtmlString(content_url)}" target="_blank">(stored)</a>\n`);
    stream.write("<br>\n");

    const result: AutoselectResult = {
        autoselect_id,
        domain_name,
        report_url,
        status: "",
        elapsed: 0,
        ok: false
    };
    const t0 = Date.now();

    const log: AutoselectLog = {
        autoselect_id,
        domain_name,
        url,
        source,
        status: "OK",
        report_url
    };
    log.analyze = [];

    try {
        let page_class: PageClassification = "PRODUCT-PAGE";
        let page_data = undefined;

        const open_result = await openPageForVision(
            url,
            {
                page_types: ["PRODUCT-PAGE", "MULTI-PRODUCT-PAGE"],
                model: open_page_model,
                domain_name,
                content_url,
                headless,
                page,
                stream,
                viewport,
                snooze,
                autoclose,
                classify
            }
        );

        page_class = open_result.page_class || "PRODUCT-PAGE";
        page_data = open_result.page_data;

        log.status = open_result.ok ? "OK" : open_result.error_code?.toUpperCase() || "ERROR";
        log.error =  open_result.ok ? undefined : open_result.message;
        log.page_class = page_class;
        log.page_data = page_data;
        log.analyze.push(...open_result.analyze);

        page = open_result.page;
        if (!page) {
            log.status = "ERROR";
            log.error = "Failed to open page";
            console.log(chalk.red(log.error));
            stream?.write(`<p class="error">ERROR: ${log.error}</p>`);

            result.status = log.status;
            result.ok = false;
            result.elapsed = Date.now() - t0;
            return result;
        }

        if (pause) {
            console.log("Press any key to continue...");
            await waitForKeypress();
        }

        const autoselect_result = await autoselect_page({
            page,
            page_class,
            page_data,
            domain_name,
            url,
            viewport,
            scale,
            stream
        });
        log.analyze.push(...autoselect_result.analyze);
        if (Object.keys(autoselect_result.selectors).length > 0)
            log.selectors = Object.keys(autoselect_result.selectors).map(key => ({
                selector_name: key,
                selector: autoselect_result.selectors[key]
            }));
        else if (log.status === "OK")
            log.status = "EMPTY";

        result.status = log.status;
        result.ok = true;
        result.elapsed = Date.now() - t0;
        return result;
    }
    catch (err) {
        log.status = "ERROR";
        log.error = err instanceof Error ? err.message : JSON.stringify(err);

        console.log(chalk.red(log.error));
        stream?.write(`<p class="error">ERROR: ${log.error}</p>`);
        if (err instanceof Error && err.stack) {
            console.log(chalk.red(log.error));
            stream?.write(`<pre>${escapeHtmlString(err.stack)}</pre>`);
        }

        result.status = log.status;
        result.ok = false;
        result.elapsed = Date.now() - t0;
        return result;
    }
    finally {
        printCostSummary(stream, log.analyze);
        await insertAutoselectLog(log);
        await closeLogStream(stream, { voting: true });
        if (page) {
            await page.close();
            await page.browser.close();
        }
        page = undefined;
    }
}
