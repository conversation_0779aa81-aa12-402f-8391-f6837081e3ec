import * as stream from "node:stream";
import chalk from "chalk";
import { CheerioAPI } from "cheerio";

import {
    escapeHtmlString,
    getSelectorProfile,
    LocateContentTarget,
    Page,
} from "../../index.js";

import { AnalyzeSelectorTarget } from "./common.js";
import { adjustLocateTarget } from "./adjust-locate-targets.js";
import { reverseFindSelectorCandidates } from "../../scripts/reverse-find-selector-candidates.js";
import { reverseFindFullPathToElement } from "../../scripts/reverse-find-full-path-to-element.js";
import { reverseFindSinglePathToElement } from "../../scripts/reverse-find-single-path-to-element.js";

export interface CreateAnalyzeSelectorTargetsOptions {
    page: Page;
    locate_targets: Record<string, LocateContentTarget>;
    selector_profile: string;
    labelled_html: string;
    max_selector_candidates?: number;
    stream?: stream.Writable;
    $: CheerioAPI;
}

export async function createAnalyzeTargets({
    page,
    locate_targets,
    selector_profile,
    labelled_html,
    max_selector_candidates = 30,
    stream,
    $
}: CreateAnalyzeSelectorTargetsOptions): Promise<AnalyzeSelectorTarget[]> {
    const analyze_targets: AnalyzeSelectorTarget[] = [];
    const { targets: profile_targets } = getSelectorProfile(selector_profile);

    stream?.write("<details>\n<summary>SELECTOR RETARGETING</summary>\n");
    try {
        for (const name of Object.keys(locate_targets)) {
            const locate_target = locate_targets[name];
            const profile_target = profile_targets.find(target => target.name === name);

            if (locate_target.labels?.length > 0) {
                let label = 0;
                let selector_output: string | null | undefined = "";
                let all_candidates: string[] = [];

                if (process.env.VERBOSE)
                    console.log(chalk.gray(`${name}: retargeting...`));

                try {
                    const result = await adjustLocateTarget({
                        name,
                        locate_target,
                        profile_target,
                        labelled_html,
                        page,
                        stream
                    });
                    label = result.label;
                    selector_output = result.selector_output;
                }
                catch (err) {
                    errout("adjustLocateTarget", locate_target.labels, err);
                    continue;
                }

                const f1 = reverseFindSelectorCandidates;
                const p1 = { selector: `[__label__='${label}']` };
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`${name}: running ${f1.name} script...`));
                try {
                    all_candidates = await page.evaluate(f1, p1);
                }
                catch (err) {
                    errout(f1.name, p1, err);
                }
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`${name}: ${f1.name}(${JSON.stringify(p1)}) returned ${all_candidates.length} candidates`));


                if (all_candidates.length === 0) {
                    const f2 = reverseFindSinglePathToElement;
                    const p2 = `[__label__='${label}']`;
                    if (process.env.VERBOSE)
                        console.log(chalk.gray(`${name}: running ${f2.name} script...`));
                    try {
                        const single_path = await page.evaluate(f2, p2);
                        const single_path_label = $(single_path).attr("__label__");
                        if (parseInt(single_path_label!) === label)
                            all_candidates = [single_path];
                    }
                    catch (err) {
                        errout(f2.name, p2, err);
                    }
                    if (process.env.VERBOSE)
                        console.log(chalk.gray(`${name}: ${f2.name}(${p2}) returned ${all_candidates.length} candidates`));
                }

                if (all_candidates.length === 0) {
                    const f3 = reverseFindFullPathToElement;
                    const p3 = `[__label__='${label}']`;
                    if (process.env.VERBOSE)
                        console.log(chalk.gray(`${name}: running ${f3.name} script...`));
                    try {
                        const full_path = await page.evaluate(f3, p3);
                        const full_path_label = $(full_path).attr("__label__");
                        if (parseInt(full_path_label!) === label)
                            all_candidates = [full_path];
                    }
                    catch (err) {
                        errout(f3.name, p3, err);
                    }
                    if (process.env.VERBOSE)
                        console.log(chalk.gray(`${name}: ${f3.name}(${JSON.stringify(p3)}) returned ${all_candidates.length} candidates`));
                }

                if (process.env.VERBOSE) {
                    const logMsg = `${name}: reverse find scripts initially found ${all_candidates.length} candidates.`;
                    if (all_candidates.length === 0) console.log(chalk.yellow(logMsg));
                    else console.log(chalk.gray(logMsg));
                }

                const nth_count = all_candidates.filter(selector => selector.includes("nth-")).length;
                let processed_candidates = all_candidates.filter(selector => {
                    if (selector.includes("__label__"))
                        return false;

                    if (selector.includes("nth-") && all_candidates.length > 4 && nth_count < 0.5 * all_candidates.length)
                        return false;

                    return true;
                });


                if (processed_candidates.length === 0) {
                    const directLabelSelector = `[__label__='${label}']`;
                    const message = `${name}: No suitable candidates after filtering (or none found initially). Defaulting to direct label selector: ${directLabelSelector}`;
                    if (process.env.VERBOSE) {
                        console.log(chalk.yellow(message));
                    }
                    stream?.write(`<p class="info"><b>${name}</b>: No valid selector candidates found through reverse-engineering. Using direct label selector <code>${escapeHtmlString(directLabelSelector)}</code> as a fallback.</p>\n`);
                    processed_candidates = [directLabelSelector];
                }

                if (processed_candidates.length === 0) {
                    stream?.write(`<p class="error"><b>${name}</b> failed to determine any selector candidate, even with fallback.</p>\n`);
                    if (process.env.VERBOSE) console.log(chalk.red(`${name}: failed to determine any selector candidate, even with fallback.`));
                    continue;
                }

                const analyze_target: AnalyzeSelectorTarget = {
                    name,
                    label,
                    locate_target,
                    profile_target,
                    selector_output,
                    candidates: processed_candidates,
                    attr: profile_target?.expect === "image" ? "@src" : undefined,
                    filter: profile_target?.filter
                };
                analyze_targets.push(analyze_target);

                if (process.env.VERBOSE) {
                    console.log(chalk.gray(`${name}: analyze target created with ${analyze_target.candidates.length} candidate(s).`));
                }

                if (analyze_target.candidates.length > max_selector_candidates) {
                    const original_count = analyze_target.candidates.length;
                    analyze_target.candidates = analyze_target.candidates.slice(0, max_selector_candidates);
                    if (process.env.VERBOSE) {
                        console.log(chalk.yellow(`${name}: ${original_count - analyze_target.candidates.length} selector candidates dropped. Using top ${analyze_target.candidates.length}.`));
                    }
                }
            }
            else if (process.env.VERBOSE) {
                console.log(chalk.gray(`${name}: analyze target skipped`));
            }

            function errout(script: string, params: unknown, err: unknown) {
                const code = `${script}(${JSON.stringify(params)})`;
                const error = err instanceof Error ? err.message : JSON.stringify(err);
                const message = `ERROR ${code} -> ${error}`;
                if (process.env.VERBOSE)
                    console.log(chalk.red(`${name}: ${message}`));
                stream?.write(`<p class="error"><b>${name}</b> <code>${code}</code> -> ${escapeHtmlString(error)}</p>\n`);
                stream?.write(voting(message));
            }

            function voting(comment?: string): string {
                return `
                <p>
                    <a href="#" data-params="?key=autoselect&action=accept&context=retargeting&selector=${name}" style="margin-left: 8px;"><i class="fa fa-thumbs-o-up"></i></a>
                    <a href="#" data-params="?key=autoselect&action=reject&context=retargeting&selector=${name}" style="margin-left: 8px; margin-right: 8px;"><i class="fa fa-thumbs-o-down"></i></a>
                    <input type="text" placeholder="Optional comment" style="width: 500px;"${comment ? ` value="${escapeHtmlString(comment)}"` : ""}>
                </p>\n`;
            }
        }
    }
    finally {
        stream?.write("</details>\n");
    }

    return analyze_targets;
}
