import * as stream from "node:stream";
import chalk from "chalk";

import {
    Page,
    addLabe<PERSON>,
    remove<PERSON>abe<PERSON>,
    getRedisService,
    parseDomainName
}
    from "../../index.js";

import { reverseFindSelectorCandidates } from "../../scripts/reverse-find-selector-candidates.js";
import {
    autoselectEvaluateHtml,
    tokenize
} from "./common.js";

export interface CheckPageClassOptions {
    page: Page;
    page_class?: PageClassification;
    page_data?: Record<string, any>;
    stream?: stream.Writable;
    domain_name?: string;
    url?: string;
}

export interface CheckPageClassResult {
    key: string;
    selector: string;
}

function getSelectorScore(selector: string): number {
    let score = 0;

    if (selector.includes("error") || selector.includes("message") || selector.includes("alert") ||
        selector.includes("warning") || selector.includes("notice") || selector.includes("info") ||
        selector.includes("notification") || selector.includes("danger") || selector.includes("problem")) {
        score += 15;
    }

    if (selector.includes("p") || selector.includes("span")) {
        score += 8;
    }

    if (selector.includes(".") && !selector.includes("#")) {
        score += 5;
    }

    if (selector.includes("#")) {
        if (selector.includes("#root") || selector.includes("#app") || selector.includes("#main") ||
            selector.includes("#container") || selector.includes("#wrapper")) {
            score -= 10;
        } else {
            score += 3;
        }
    }

    if (selector.includes("[") && (selector.includes("role") || selector.includes("aria") || selector.includes("data"))) {
        score += 4;
    }

    const complexity = (selector.match(/>/g) || []).length;
    if (complexity > 3) {
        score -= complexity * 2;
    }

    if (selector.includes("nth-")) {
        score -= 8;
    }

    if (selector.length < 5 && (selector === "div" || selector === "span" || selector === "p")) {
        score -= 5;
    }

    return score;
}

export async function checkPageClass({ page, page_class, page_data, stream, domain_name, url }: CheckPageClassOptions): Promise<CheckPageClassResult | undefined> {
    if (!(page_class && (page_data?.message || page_data?.title)))
        return;

    let key = "";
    if (["BLOCKED", "PNF", "SIGNIN", "BROKEN"].includes(page_class))
        key = page_class.toLowerCase();
    else if (page_data.discontinued)
        key = "discontinued";
    else
        return;

    const messages = [page_data.title, page_data.message].filter(Boolean);
    let cssSelector: string | undefined = undefined;
    let textSelector: string | undefined = undefined;

    try {
        await addLabels(page);

        for (const message of messages) {
            let target = message;

            if (page_data.keywords) {
                const keywords = page_data.keywords.split(" ").filter(Boolean) as string[];
                const i = Math.min(...keywords.map(keyword => message.indexOf(keyword)));
                const j = Math.max(...keywords.map(keyword => message.indexOf(keyword) + keyword.length));
                const left = target.length - i >= 5 ? message.slice(0, i).trim() : "";
                const right = j >= 5 ? message.slice(j).trim() : "";
                if (left.length >= right.length)
                    target = left;
                else if (right.length > 0)
                    target = right;
            }

            const labelMatches = await page.evaluate((searchText) => {
                const elements = Array.from(document.querySelectorAll('[__label__]'));
                const matches: number[] = [];

                const textMatches = elements.filter(element => {
                    const text = element.textContent?.trim() || '';
                    return text.toLowerCase().includes(searchText.toLowerCase());
                });

                const specificMatches = textMatches.filter(element => {
                    const elementText = element.textContent?.trim() || '';

                    const childrenWithText = Array.from(element.children).filter(child => {
                        const childText = child.textContent?.trim() || '';
                        return childText.toLowerCase().includes(searchText.toLowerCase());
                    });

                    if (childrenWithText.length === 0) {
                        return true;
                    }

                    let ownText = elementText;
                    for (const child of element.children) {
                        const childText = child.textContent?.trim() || '';
                        ownText = ownText.replace(childText, '').trim();
                    }

                    return ownText.toLowerCase().includes(searchText.toLowerCase());
                });

                for (const element of specificMatches) {
                    const label = element.getAttribute('__label__');
                    if (label) {
                        matches.push(parseInt(label));
                    }
                }

                return matches;
            }, target);

            if (labelMatches.length > 0) {
                for (const label of labelMatches) {
                    try {
                        const selectorCandidates = await page.evaluate(reverseFindSelectorCandidates, {
                            selector: `[__label__='${label}']`,
                            limit: 20
                        });

                        let filteredCandidates = selectorCandidates.filter(selector =>
                            !selector.includes("__label__")
                        );

                        const semanticCandidates = filteredCandidates.filter(selector =>
                            !selector.includes("nth-") &&
                            (selector.includes(".") || selector.includes("#") || selector.includes("["))
                        );

                        if (semanticCandidates.length > 0) {
                            semanticCandidates.sort((a, b) => {
                                const aScore = getSelectorScore(a);
                                const bScore = getSelectorScore(b);
                                return bScore - aScore;
                            });
                            cssSelector = semanticCandidates[0];
                            break;
                        } else if (filteredCandidates.length > 0) {
                            cssSelector = filteredCandidates[0];
                            break;
                        }
                    } catch (err) {
                        if (process.env.VERBOSE) {
                            console.log(chalk.yellow(`Failed to generate selector for label ${label}: ${err instanceof Error ? err.message : JSON.stringify(err)}`));
                        }
                    }
                }

                if (cssSelector) break;
            }
        }
    } finally {
        await removeLabels(page);
    }

    const html = await page.content();
    const hits = [];
    const misses = [];
    for (const message of messages) {
        let target = message;
        if (page_data.keywords) {
            const keywords = page_data.keywords.split(" ").filter(Boolean) as string[];
            const i = Math.min(...keywords.map(keyword => message.indexOf(keyword)));
            const j = Math.max(...keywords.map(keyword => message.indexOf(keyword) + keyword.length));
            const left = target.length - i >= 5 ? target = message.slice(0, i).trim() : "";
            const right = j >= 5 ? target = message.slice(j).trim() : "";
            if (left.length >= right.length)
                target = left;
            else if (right.length > 0)
                target = right;
        }
        const selector = `"${tokenize(target)}"`;
        if (autoselectEvaluateHtml(html, { selector }))
            hits.push(selector);
        else
            misses.push(selector);
    }

    if (hits.length > 0) {
        textSelector = hits.join("\n");
    }

    let finalSelector: string | undefined = undefined;
    if (cssSelector && textSelector) {
        finalSelector = `${cssSelector}\n${textSelector}`;
    } else if (cssSelector) {
        finalSelector = cssSelector;
    } else if (textSelector) {
        finalSelector = textSelector;
    }

    if (finalSelector) {
        console.log(chalk.cyan(`CHECK PAGE CLASS -> ${key.toUpperCase()} -> ${finalSelector}`));
        stream?.write(`
            <details>
                <summary><b>GENERATE ${key.toUpperCase()} SELECTOR</b> <span style="font-weight: normal;">&rarr; <code>${finalSelector}</code> <span class="success">(SELECTOR FOUND)</span></span></summary>
                ${cssSelector ? `<p>CSS selector: <code>${cssSelector}</code></p>` : ''}
                ${textSelector ? `<p>Text selector: <code>${textSelector}</code></p>` : ''}
                ${cssSelector && textSelector ? '<p>Combined for maximum reliability</p>' : ''}
            </details>
        `);

        if (process.env.VERBOSE)
            console.log(chalk.cyan(`GENERATE ${key.toUpperCase()} SELECTOR -> ${finalSelector}`));

        // Store blocked and PNF triggers in Redis
        if ((key === "blocked" || key === "pnf") && domain_name) {
            try {
                const redisService = getRedisService();
                const mainMessage = messages[0] || '';
                
                // Create a regex pattern from the message for phrase matching
                const phrase = `.*${mainMessage.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}.*`;
                
                // Clean up phrase for display by removing regex escaping and special characters
                const cleanPhrase = mainMessage.replace(/[.*+?^${}()|[\]\\]/g, '').trim();
                
                // Use only CSS selector for focus field, fallback to finalSelector if no CSS selector
                const focusSelector = cssSelector || finalSelector;
                
                if (key === "pnf") {
                    await redisService.storeProductNotFoundTrigger(domain_name, phrase, focusSelector);
                    console.log(chalk.green(`Stored PNF trigger in Redis for domain ${domain_name}`));
                    stream?.write(`<p class="success">Stored PNF trigger in Redis for domain ${domain_name}</p>`);
                } else if (key === "blocked") {
                    await redisService.storeBlockedTrigger(domain_name, phrase, focusSelector);
                    console.log(chalk.green(`Stored blocked trigger in Redis for domain ${domain_name}`));
                    stream?.write(`<p class="success">Stored blocked trigger in Redis for domain ${domain_name}</p>`);
                }
            } catch (error) {
                console.error(chalk.red(`Failed to store ${key} trigger in Redis:`, error));
                stream?.write(`<p class="error">Failed to store ${key} trigger in Redis: ${error instanceof Error ? error.message : 'Unknown error'}</p>`);
            }
        }

        return { key, selector: finalSelector };
    } else {
        console.log(chalk.cyan(`CHECK PAGE CLASS -> ${key.toUpperCase()} -> NO MATCH`));
        stream?.write(`
            <details>
                <summary><b>GENERATE ${key.toUpperCase()} SELECTOR</b> <span style="font-weight: normal;"><span class="error">(NO MATCHES)</span></span></summary>
                <p>Could not find any elements containing the target text to generate a selector.</p>
            </details>
        `);
        return undefined;
    }
}
