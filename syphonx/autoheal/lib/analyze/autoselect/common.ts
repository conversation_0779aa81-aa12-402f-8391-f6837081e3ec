import * as cheerio from "cheerio";
import chalk from "chalk";

import {
    collapseWhitespace,
    combineUrl,
    isAbsoluteUrl,
    LocateContentTarget,
    Page,
    SelectorProfileTarget
}
from "../../index.js";

export {
    combineUrl,
    isAbsoluteUrl
}
from "../../url.js";

export interface AnalyzeSelectorTarget extends AnalyzeSelectorVariantsTarget {
    locate_target: LocateContentTarget;
    profile_target?: Required<SelectorProfileTarget>;
}

export interface AutoselectObject {
    selector: string;
    attr?: string;
    filter?: string;
}
export type AutoselectDictionary = Record<string, AutoselectObject[]>;
export type AutoselectDictionaryCollection = Record<string, AutoselectDictionary>;

export function applyFilter(filter: string, text: string): string {
    const i = filter.startsWith("/") ? filter.lastIndexOf("/") : -1;
    const pattern = i > 0 ? filter.slice(1, i) : filter;
    try {
        const regexp = new RegExp(pattern);
        const result = text.match(regexp) || [];
        return result[1] || result[0] || "";
    }
    catch (err) {
        return "";
    }
}

export function autoselectEvaluate($: cheerio.CheerioAPI, { selector, attr, filter }: AutoselectObject, origin?: string): string | null {
    // special case for token matching...
    if (selector.startsWith(`"`)) {
        const content = $.text().trim();
        const title = $("title").text().trim();
        const source = title && !content.startsWith(title) ? `${title}\n${content}` : content;
        const target = selector.slice(1, -1);
        if (tokenizeMatch(source, target))
            return target;
        return null;
    }

    // CSS selector evaluation...
    const text = !attr ? $(selector).text()?.trim() : $(selector).attr(attr)?.trim();
    let value = filter && text ? applyFilter(filter, text) : text;
    if (attr === "src" && value) {
        if (!isAbsoluteUrl(value) && origin)
            value = combineUrl(origin, value);
        value = encodeURI(value);
    }
    else if (value) {
        value = collapseWhitespace(value, { singleLine: true, collapseSpaces: true });
    }

    return value || null;
}

export function autoselectEvaluateHtml(html: string, obj: AutoselectObject, origin?: string): string | null {
    const $ = cheerio.load(html);
    return autoselectEvaluate($, obj, origin);
}

export function formatMetaselector(analyze_target: AnalyzeSelectorTarget): string {
    if (!analyze_target.selector)
        throw new Error("Undefined analyze target selector.");
    return [
        analyze_target.selector,
        analyze_target.attr,
        analyze_target.filter
    ]
    .filter(Boolean)
    .join("; ");
}

export function formatFilter(filter: string): string | undefined {
    filter = filter.trim();
    if (filter.startsWith("/") && filter.endsWith("/"))
        filter = filter.slice(1, -1);
    if ([".*", "(.*)", ".+", "(.+)", "n/a", "(none)", "none"].includes(filter.toLowerCase()))
        return undefined;
    if (!filter.replaceAll("\\(", "__").replaceAll("(?:", "___").includes("("))
        filter = `(${filter})`;
    if (!filter.startsWith("/"))
        filter = `/${filter}/`;
    return filter;
}

export function isValidRegExp(regexp: string) {
    try {
        new RegExp(regexp);
    }
    catch (err) {
        return false;
    }
    return true;
}

export async function querySelectorOutput(page: Page, selector: string, attr?: string): Promise<string | null> {
    const querySelector = (selector: string) => document.querySelector(selector)?.textContent;
    const querySelectorAttr = ({ selector, attr }: { selector: string, attr: string }) => document.querySelector(selector)?.getAttribute(attr);
    try {
        let result;
        if (!attr)
            result = await page.evaluate(querySelector, selector);
        else
            result = await page.evaluate(querySelectorAttr, { selector, attr });
        return result?.trim() || null;
    }
    catch (err) {
        if (process.env.VERBOSE)
            console.log(chalk.red(err instanceof Error ? err.message : JSON.stringify(err)));
    }
    return null;
}

export function tokenize(text: string): string {
    return text
        .trim()
        .replace(/\s+/gm, " ") // collapse multiple whitespace characters into a single space
        .replace(/\p{P}/gu, "") // remove any punctuation characters as defined by Unicode
        .split(" ") // split the punctuation-cleansed string into an array of words
        .filter(word => word.length <= 16 && /^[\p{L}]+$/u.test(word)) // keep only words limited by length and consist entirely of Unicode letters
        .join(" "); //  combines the filtered words back into a string with single spaces between them
}

export function tokenizeMatch(source: string, target: string): boolean {
    source = tokenize(source).toLowerCase();
    target = tokenize(target).toLowerCase();
    return source.includes(target);
}
