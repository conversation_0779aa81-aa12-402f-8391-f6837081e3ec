import * as stream from "node:stream";
import * as fs from "node:fs/promises";
import * as cheerio from "cheerio";
import chalk from "chalk";

import {
    arrayMoveToFront,
    escapeHtmlString,
    locateContent,
    resolveUrl,
    sliceLabelledHtml,
    truncate,
    AnalyzeResult,
    LLMModel,
    Page
}
    from "../../index.js";

import { formatMetaselector } from "./common.js";
import { analyzeSelectorVariants } from "./analyze-selector-variants.js";
import { checkPageClass } from "./check-page-class.js";
import { createAnalyzeTargets } from "./create-analyze-targets.js";
import { detectSelectorProblems, resolveSelectorProblems } from "./resolve-selector-problems.js";
import { sanitizeHtml } from "./sanitize-html.js";

export interface AutoselectPageOptions {
    page: Page;
    page_class?: PageClassification;
    page_data?: Record<string, any>;
    domain_name: string;
    url: string;
    viewport?: { width: number, height: number };
    scale?: number;
    stream?: stream.Writable;
}

export type AutoselectPageResult = {
    selectors: Record<string, string>;
    analyze: AnalyzeResult[];
    message?: string;
    ok: boolean;
};

const text_models: LLMModel[] = [
    "gemini-2.0-flash-thinking-exp-01-21",
    //"gemini-2.5-pro-exp-03-25",
    //"gemini-2.0-pro-exp-02-05",
    //"llama-3.3-70b-versatile",
    //"deepseek-r1-distill-llama-70b", // hella slow
    "gpt-4o-mini"
];

const vision_models: LLMModel[] = [
    "gemini-2.0-flash-thinking-exp-01-21",
    //"llama-3.2-90b-vision-preview", // first attempt didn't perform very well... https://storage.googleapis.com/ps-syphonx/autoselect/MMhvdB7sCoHCWDX.html
    "gpt-4o-mini"
];

//const hash_id_model: LLMModel = "gemini-2.0-pro-exp-02-05";
const hash_id_model: LLMModel = "llama3-8b-8192";

const selector_profile = "autoselect-product-page";
const max_selector_candidates = 25;
const model_rotation = text_models.slice(0);

export async function autoselect_page({
    page,
    page_class,
    page_data,
    domain_name,
    url,
    stream,
    viewport,
    scale
}: AutoselectPageOptions): Promise<AutoselectPageResult> {
    const result: AutoselectPageResult = {
        selectors: {},
        analyze: [],
        ok: true
    };

    if (page_class) {
        const check_result = await checkPageClass({ page, page_class, page_data, stream, domain_name, url });
        if (check_result)
            result.selectors[check_result.key] = check_result.selector;

        if (check_result || page_class !== "PRODUCT-PAGE")
            return result;
    }

    if (process.env.VERBOSE)
        console.log(chalk.gray("Locating content..."));

    const locate_result = await locateContent({
        page,
        domain_name,
        model: vision_models,
        selector_profile,
        keepLabels: true,
        viewport,
        scale,
        stream
    });

    result.analyze.push(...locate_result.analyze);
    if (!locate_result.ok || !locate_result.targets || !locate_result.labelled_html || Object.keys(locate_result.targets).length === 0) {
        result.message = "Locate content failed";
        if (process.env.VERBOSE)
            console.log(chalk.red(result.message));
        stream?.write(`<p class="error">${result.message}</p>\n</details>\n`);
        return result;
    }

    const locate_targets = locate_result.targets;
    const labelled_html = locate_result.labelled_html;

    const $ = cheerio.load(labelled_html);
    const analyze_targets = await createAnalyzeTargets({
        page,
        locate_targets,
        selector_profile,
        max_selector_candidates,
        labelled_html,
        stream,
        $
    });
    if (analyze_targets.length === 0) {
        result.message = "No analyze variants found";
        if (process.env.VERBOSE)
            console.log(chalk.yellow(result.message));
        stream?.write(`<p class="error">${escapeHtmlString(result.message)}</p>`);
        return result;
    }

    if (process.env.VERBOSE)
        console.log(chalk.gray("Analyzing selector variants..."));
    stream?.write(`<details>\n<summary>SELECTOR CANDIDATES</summary>\n`);
    let analyze_selector_variants_ok = false;
    for (const model of model_rotation) {
        const selector_result = await analyzeSelectorVariants({
            targets: analyze_targets, // NOTE: side effects analyzeSelectorVariants alters objects in analyze_targets array
            model,
            stream
        });
        result.analyze.push(...selector_result.analyze);

        if (selector_result.ok) {
            // promote model to front of rotation except the last model
            if (model_rotation.indexOf(model) < text_models.length - 1)
                arrayMoveToFront(model_rotation, model);
            arrayMoveToFront(model_rotation, model);
            analyze_selector_variants_ok = true;
            stream?.write(`<p class="success">Analyze selector variants succeeded (using model ${model})</p>\n`);
            break;
        }

        result.message = `Analyze selector variants failed (using model ${model}), error=${selector_result.message}`;
        if (process.env.VERBOSE)
            console.log(chalk.yellow(result.message));
        stream?.write(`<p class="error">${escapeHtmlString(result.message)}</p>\n`);
    }
    if (!analyze_selector_variants_ok) {
        result.message = "All analyze attempts failed";
        if (process.env.VERBOSE)
            console.log(chalk.red(result.message));
        stream?.write(`<p class="error">${result.message}</p>\n</details>\n`);
        return result;
    }
    stream?.write(`</details>\n`);

    console.log(chalk.gray("Resolving selector problems..."));
    stream?.write(`<details>\n<summary>RESOLVE PROBLEMS</summary>\n`);
    try {
        let attempts = 0;
        for (const analyze_target of analyze_targets) {
            const problems = detectSelectorProblems(analyze_target);
            if (problems.length > 0) {
                attempts += 1;
                const slice = sliceLabelledHtml(labelled_html, [analyze_target.label], 2);
                if (!slice) {
                    result.message = `${analyze_target.name}: Failed to create HTML slice`;
                    if (process.env.VERBOSE)
                        console.log(chalk.red(result.message));
                    stream?.write(`<p class="error"><b>${analyze_target.name}</b> ERROR: Failed to create HTML slice</p>\n`);
                    continue;
                }

                try {
                    stream?.write(`<details>\n<summary>${analyze_target.name}</summary>\n`);
                    const sanitize_result = await sanitizeHtml({ html: slice.html_with_linenums, model: hash_id_model, stream });
                    result.analyze.push(...sanitize_result.analyze);
                    const [linenum] = slice.linenums;
                    let problems_solved = false;
                    for (const model of text_models) {
                        const problem_result = await resolveSelectorProblems({
                            analyze_target,
                            model,
                            problems,
                            linenum,
                            html: sanitize_result.html,
                            retries: 5,
                            stream,
                            $
                        });
                        result.analyze.push(...problem_result.analyze);
                        if (problem_result.ok) {
                            problems_solved = true;
                            break;
                        }
                    }
                    if (!problems_solved && process.env.VERBOSE)
                        console.log(chalk.red(`${analyze_target.name}: All models failed to resolve selector problems`));
                }
                finally {
                    stream?.write(`</details>\n`);
                }
            }
            if (analyze_target.selector)
                result.selectors[analyze_target.name] = formatMetaselector(analyze_target);
        }
        if (attempts === 0)
            stream?.write("<p>(No problems detected)</p>\n");
    }
    finally {
        stream?.write(`</details>\n`);
    }

    stream?.write(`
        <details open>
            <summary>OUTPUT SUMMARY</summary>
            <table style="margin-top: 8px;">
                <thead>
                    <tr>
                        <td>NAME</td>
                        <td>SELECTOR</td>
                        <td>OUTPUT</td>
                        <td>FILTERED</td>
                        <td>VOTING</td>
                    </tr>
                </thead>
                <tbody>
                ${analyze_targets.map(target => `
                    <tr>
                        <td id="${target.name}">${target.name}</td>
                        <td><code>${escapeHtmlString(result.selectors[target.name])}</code></td>
                        <td>
                            ${target.selector_output ?
            target.name.endsWith("_image") ?
                `<a href="${resolveUrl(target.selector_output, url)}" target="_blank">${escapeHtmlString(truncate(target.selector_output_unfiltered || target.selector_output, 150, true))}</a>`
                : `<span>${escapeHtmlString(truncate(target.selector_output_unfiltered || target.selector_output, 150, true))}<span>`
            : "<i>(none)</i>"}
                        </td>
                        <td>${target.filter && target.selector_output_filtered ? escapeHtmlString(truncate(target.selector_output_filtered, 150, true)) : "-"}</td>
                        <td>
                            <a href="#" data-params="?key=autoselect&action=accept&context=output&selector=${target.name}" style="margin-left: 8px;"><i class="fa fa-thumbs-o-up"></i></a>
                            <a href="#" data-params="?key=autoselect&action=reject&context=output&selector=${target.name}" style="margin-left: 8px; margin-right: 8px;"><i class="fa fa-thumbs-o-down"></i></a>
                            <input type="text" placeholder="Optional comment" style="width: 120px;">
                        </td>
                    </tr>
                `).join("\n")}
                </tbody>
            </table>
        </details>
    `);

    result.ok = true;
    return result;
}
