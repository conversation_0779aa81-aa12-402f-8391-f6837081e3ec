import { bigquery } from "../../index.js";

import { AutoselectDictionary, AutoselectDictionaryCollection, AutoselectObject } from "./common.js";
export { AutoselectDictionary, AutoselectDictionaryCollection, AutoselectObject };

export async function queryAutoselectDictionaryCollection(): Promise<AutoselectDictionaryCollection> {
    const rows = await bigquery.query<{ domain_name: string, selectors: Record<string, string>[] }>("SELECT * FROM omega.autoselect");
    const result: AutoselectDictionaryCollection = {};
    for (const row of rows) {
        const key = row.domain_name;
        result[key] = expandSelectors(row.selectors);
    }
    return result;
}

let cache: AutoselectDictionaryCollection | undefined = undefined;

export async function loadAutoselectDictionaryCollection() {
    cache = await queryAutoselectDictionaryCollection();
}

export function autoselectDictionaryKeys(): string[] {
    if (!cache)
        throw new Error("Autoselect cache not loaded");
    return Object.keys(cache);
}

export function lookupAutoselectDictionary(key: string | undefined): AutoselectDictionary | undefined  {
    if (!key)
        return;
    if (!cache)
        throw new Error("Autoselect cache not loaded");
    const obj = cache[key];
    return obj;
}

function expandSelectors(list: Array<Record<string, string>>): Record<string, AutoselectObject[]> {
    const result: Record<string, AutoselectObject[]> = {};
    for (const item of list) {
        const key = item.selector_name;
        result[key] = item.selector
            .split("\n")
            .filter(validateSelectorObject)
            .map(expandSelectorObject);
    }
    return result;
}

function expandSelectorObject(line: string): AutoselectObject {
    const i = line.indexOf("; ");
    if (i === -1)
        return { selector: line };
    
    const selector = line.slice(0, i);
    const j = i > 0 ? line.indexOf("; ", i + 1) : -1;
    if (j >= 0)
        return {
            selector,
            attr: line.slice(i + 3),
            filter: line.slice(j)
        };
    else if (line.slice(i + 2).startsWith("@"))
        return {
            selector,
            attr: line.slice(i + 3)
        };
    else if (line.slice(i + 2).startsWith("/"))
        return {
            selector,
            filter: line.slice(i + 2)
        };
    else
        return {
            selector
        };
}

function validateSelectorObject(line: unknown): boolean {
    if (typeof line !== "string")
        return false;
    if (line.trim().length === 0)
        return false;
    if (line.startsWith("@"))
        return false;
    if (line.startsWith("/"))
        return false;
    return true;
}
