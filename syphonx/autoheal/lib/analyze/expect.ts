import {
    compareEmbeddings,
    le<PERSON><PERSON><PERSON>,
    truncate,
    SelectorExpectType
} from "../index.js";
import { SelectType } from "syphonx-lib";

export interface ExpectOptions {
    mode: SelectorExpectType;
    type: SelectType;
    repeated: boolean;
    actual_value: unknown;
    expected_value: unknown;
}

export interface ExpectResult {
    ok: boolean;
    message: string;
}

export async function expect({ mode, actual_value, expected_value, repeated }: ExpectOptions): Promise<ExpectResult> {
    let result: ExpectResult;
    if (actual_value === undefined)
        result = { ok: false, message: "Output value is undefined." };
    else if (mode === "string-match")
        result = stringMatch(actual_value, expected_value);
    else if (mode === "substring-match")
        result = substringMatch(actual_value, expected_value);
    else if (mode === "number-equals")
        return result = numberEquals(actual_value, expected_value);
    else if (mode === "currency-symbol")
        result = validateCurrencySymbol(actual_value);
    else if (mode === "url")
        result = validateUrl(actual_value, repeated);
    else if (mode === "string-distance")
        result = stringDistance(actual_value, expected_value, 0.7);
    else if (mode === "similar-meaning")
        result = await similarMeaning(actual_value, expected_value, 0.7);
    else if (mode === "boolean")
        result = checkBoolean(actual_value);
    else
        result = { ok: true, message: "" };
    return result;
}

function checkBoolean(actual_value: unknown) {
    if (typeof actual_value === "boolean")
        return { ok: true, message: `${format(actual_value)} is a boolean value as expected` };
    else
        return { ok: false, message: `${format(actual_value)} is not a boolean value` };
}

function stringDistance(actual_value: unknown, expected_value: unknown, threshold: number) {
    const a = coerceToString(actual_value).toLowerCase();
    const b = coerceToString(expected_value).toLowerCase();
    const min_length = Math.min(a.length, b.length);
    const distance = levenshtein(a, b);
    const score = min_length > 0 && distance <= min_length ? (min_length - distance) / min_length : 0;
    if (score < threshold)
        return { ok: false, message: `${format(actual_value)} far distance from expected value ${format(expected_value)} (distance=${distance}, score=${score.toFixed(2)}, threshold=${threshold}).` };
    return { ok: true, message: `${format(actual_value)} close distance to expected value ${format(expected_value)} (distance=${distance}, score=${score.toFixed(2)}, threshold=${threshold}).` };
}

async function similarMeaning(actual_value: unknown, expected_value: unknown, threshold: number): Promise<ExpectResult> {
    const a = coerceToString(actual_value).toLowerCase().replace(/[^a-z]/g, "");
    const b = coerceToString(expected_value).toLowerCase().replace(/[^a-z]/g, "");
    const { score } = await compareEmbeddings(a, b);
    if (score < threshold)
        return { ok: false, message: `${format(actual_value)} does not have similar meaning to expected value ${format(expected_value)} (score=${score.toFixed(2)}, threshold=${threshold}).` };
    return { ok: true, message: `${format(actual_value)} has similar meaning to expected value ${format(expected_value)} (score=${score.toFixed(2)}, threshold=${threshold}).` };
}

function stringMatch(actual_value: unknown, expected_value: unknown): ExpectResult {
    let a = coerceToString(actual_value);
    let b = coerceToString(expected_value);
    a = a.toLowerCase().replace(/[^a-z]/g, "");
    b = b.toLowerCase().replace(/[^a-z]/g, "");
    if (a !== b)
        return { ok: false, message: `${format(actual_value)} does not match expected value ${format(expected_value)}.` };
    return { ok: true, message: `${format(actual_value)} matches expected value ${format(expected_value)}.` };
}

function substringMatch(actual_value: unknown, expected_value: unknown): ExpectResult {
    let a = coerceToString(actual_value);
    let b = coerceToString(expected_value);
    a = a.toLowerCase().replace(/[^a-z]/g, "");
    b = b.toLowerCase().replace(/[^a-z]/g, "");
    if (a.length < b.length)
        [b, a] = [a, b];
    if (!a.includes(b))
        return { ok: false, message: `${format(actual_value)} not found in ${format(expected_value)}.` };
    return { ok: true, message: `${format(actual_value)} found in ${format(expected_value)}.` };
}

function numberEquals(actual_value: unknown, expected_value: unknown): ExpectResult {
    let a = coerceToNumber(actual_value);
    let b = coerceToNumber(expected_value);
    if (isNaN(a))
        return { ok: false, message: `${format(actual_value)} is not a number.` };
    if (isNaN(b))
        return { ok: false, message: `${format(expected_value)} is not a number.` };
    if (a !== b)
        return { ok: false, message: `(${a}) not equal to expected value (${b}).` };
    return { ok: true, message: ` (${a}) equal to expected value (${b}).` };
}

function coerceToNumber(value: unknown): number {
    if (typeof value === "number" && !isNaN(value))
        return value;
    if (typeof value !== "string")
        return NaN;
    const i = value.search(/\d/); // find first digit 0-9
    return parseNumber(value.slice(i));
}

function coerceToString(value: unknown): string {
    if (typeof value === "string")
        return value;
    else if (Array.isArray(value))
        return value.map(obj => String(obj)).join("\n");
    else if (typeof value === "object")
        return JSON.stringify(value);
    else
        return String(value);
}

function validateCurrencySymbol(value: unknown): ExpectResult {
    if (typeof value !== "string" || !["$", "€", "£", "¥"].includes(value))
        return { ok: false, message: `${format(value)} is invalid. Expecting one of the following symbols: "$", "€", "£", "¥"` };
    return { ok: true, message: `${format(value)} is a valid currency symbol.` };
}

function validateUrl(value: unknown, repeated: boolean): ExpectResult {
    const result = repeated && Array.isArray(value) ? value.find(obj => validateUrl(obj, false)) : undefined;
    if (result)
        return result;
    if (typeof value !== "string" || !value.startsWith("https://"))
        return { ok: false, message: `${format(value)} is invalid. Expecting a URL like "https://www.example.com/xyz".` };
    if (value.includes("https://", 8))
        return { ok: false, message: `${format(value)} contains multiple URL's where only a single URL is expected.` };
    return { ok: true, message: `${format(value)} is a valid URL.` };
}

export function regexpExtract(regexp: RegExp, text: string) {
    const result = regexp.exec(text) || [];
    return result[1];
}

function parseNumber(value: string): number {
    // Remove any spaces around the string
    value = value.trim();

    // If there's both a dot and a comma, assume comma is thousand separator and dot is decimal
    if (value.includes('.') && value.includes(',')) {
        if (value.indexOf('.') < value.indexOf(','))
            value = value.replace(/\./g, '').replace(',', '.'); // Case like "5.921,01"
        else
            value = value.replace(/,/g, ''); // Case like "5,921.01"
    }
    else {
        // If there's only comma, assume it's decimal
        if (value.includes(','))
            value = value.replace(/,/g, '.');
    }

    // Handle leading decimal points without zero
    if (value.startsWith('.'))
        value = '0' + value;

    return parseFloat(value);
}

/*
// Examples
console.log(parseNumber("123"));       // 123
console.log(parseNumber("1.2"));       // 1.2
console.log(parseNumber("1,2"));       // 1.2
console.log(parseNumber("5,921.01"));  // 5921.01
console.log(parseNumber("5.921,01"));  // 5921.01
console.log(parseNumber(".12"));       // 0.12
console.log(parseNumber(",12"));       // 0.12
*/

function format(value: unknown): string {
    const text = coerceToString(value);
    return `\`${truncate(text.replaceAll("`", ""), 100)}\``;
}
