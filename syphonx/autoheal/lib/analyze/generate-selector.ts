import * as syphonx from "syphonx-lib";
import * as stream from "node:stream";
import chalk from "chalk";

import {
    autogenProfile,
    escapeHtmlString,
    escapeRegexpString,
    formatLineNums,
    isEmpty,
    removeHashIds,
    renderGenerateSelectorPrompt,
    renderGenerateSelectorHintPrompt,
    renderQualifySelector<PERSON>rompt,
    summarize,
    truncate,
    unhashSelector,
    AnalyzeResult,
    AutogenProfile,
    LocateContentTarget,
    LLMChat,
    LLMModel,
    SelectorProfileTarget
} from "./lib.js";

import {
    findUncontainedTargets,
    sliceLabelledHtml,
    SliceHtmlResult
} from "./html-slicer.js";

import {
    expect
} from "./expect.js";

export interface GenerateSelectorOptions {
    selector_name: string;
    domain_name: string;
    url: string;
    labelled_html: string;
    locate_target: LocateContentTarget;
    selector_target: SelectorProfileTarget;
    qualify?: boolean;
    stream?: stream.Writable;
}

export interface GenerateSelectorResult {
    selector?: string | null;
    explain?: string;
    analyze: AnalyzeResult[];
    message?: string;
    ok: boolean;
}

interface ChatResponse {
    selector?: string | null;
    regex?: string;
    explain?: string;
}

interface ExtendedGenerateSelectorOptions extends GenerateSelectorOptions {
    turns?: number;
}

const max_selector_length = 250;
const max_child_depth = 10;
const qualify_pass_count = 3;

export async function generateSelector(options: GenerateSelectorOptions): Promise<GenerateSelectorResult> {
    const analyze: AnalyzeResult[] = [];
    const profile = autogenProfile();
    const { locate_target, labelled_html, selector_name, stream } = options;
    const iterations = profile.generateSelector.iterations.map((obj, index) => ({ ...obj, index }));
    console.log(chalk.gray(`Generating selector for "${selector_name}"...`));
    for (const iteration of iterations) {
        console.log(chalk.gray(`Starting iteration #${iteration.index + 1}...`));
        stream?.write(`\n<details>\n<summary>GENERATE <code>${selector_name}</code> <small>(ITERATION #${iteration.index + 1})</small></summary>\n`);
        try {
            const slice = sliceLabelledHtml(labelled_html, locate_target.labels, iteration.slice);
            if (!slice) {
                const message = `Failed to generate selector for "${selector_name}"... unable to slice html content`;
                console.log(chalk.gray(escapeHtmlString(message)));
                stream?.write(`<p>${message}</p>\n</details>\n`);
                return { ok: false, analyze, message };
            }
        
            const unhash_result = await removeHashIds({ html: slice.html });
            analyze.push(...unhash_result.analyze);
    
            if (stream && unhash_result.analyze.length > 0) {
                const { summary } = summarize(analyze);
                let debug_html = escapeHtmlString(slice.html);
                if (unhash_result.hash_ids.length > 0) {
                    /* Explanation:
                    1. The outer replace function uses a regex to find id and class attributes:
                    - (id|class) matches either "id" or "class"
                    - =["']([^"']*)["'] matches the attribute value, enclosed in single or double quotes
                    2. For each match, the callback function receives:
                    - match: the entire matched attribute (e.g., 'id="some-id"')
                    - attr: the attribute name ("id" or "class")
                    - value: the attribute value (e.g., "some-id")
                    3. Inside the callback:
                    - We keep the original attribute name (${attr}=")
                    - We apply the inner replace function only to the value
                    - The inner replace uses the 'pattern' regex to find hash_ids
                    - Each matched hash_id is wrapped with <del> tags
                    - $& in the replacement string refers to the entire match (i.e., the hash_id)
                    4. The result is a new attribute string with <del> tags around matching hash_ids
                    This approach efficiently processes the HTML string in a single pass, 
                    modifying only the contents of id and class attributes.
                    */
                    const pattern = unhash_result.hash_ids.map(hash_id => escapeRegexpString(hash_id)).join("|");
                    debug_html = debug_html.replace(/\s(id|class)=["']([^"']*)["']/g, (match, attr, value) =>
                        `${attr}="${value.replace(new RegExp(pattern, "g"), "<del>$&</del>")}"`);
                }
                stream.write(`
                    <details>
                        <summary>${unhash_result.hash_ids.length} hash ids removed</summary>
                        <pre>REMOVED: ${unhash_result.hash_ids.length > 0 ? unhash_result.hash_ids.join(", ") : "(none)"}</pre>
                        <pre>${debug_html}</pre>
                        <p class="info">USAGE: ${summary}</p>
                    </details>\n`);
            }
    
            if (unhash_result.hash_ids.length > 0)
                slice.html = unhash_result.html;
    
            const result = await generate(iteration.model, slice, profile, iteration.index + 1, { ...options, turns: iteration.turns });
            analyze.push(...result.analyze);

            if (result.ok) {
                console.log(chalk.green(`Successfully generated selector for "${selector_name}" -> ${result.selector}`));
                console.log(chalk.gray.italic(result.explain));
                return { ...result, analyze };    
            }
        }
        catch (err) {
            const message = `ERROR: ${err instanceof Error ? err.message : JSON.stringify(err)}`;
            console.log(chalk.yellow(message));
            if (stream)
                stream.write(`<section>${escapeHtmlString(message)}</section>`);
            return { analyze, message, ok: false }
        }
        finally {
            if (stream)
                stream.write(`\n</details>\n`);
        }
    }

    const message = `Unable to generate selector after ${profile.generateSelector.iterations.length} iterations`;
    console.log(chalk.yellow(message));
    return { analyze, message, ok: false };
}

async function generate(
    model: LLMModel,
    slice: SliceHtmlResult,
    autogen_profile: AutogenProfile,
    iteration: number,
    {
        selector_name,
        domain_name,
        url,
        labelled_html,
        locate_target,
        selector_target,
        turns,
        qualify = true,
        stream
    }: ExtendedGenerateSelectorOptions
): Promise<GenerateSelectorResult> {

    const analyze: AnalyzeResult[] = [];
    const prompt_html = formatLineNums(slice.html, slice.linenums);
    const prompt = renderGenerateSelectorPrompt({
        selector_name,
        domain_name,
        content_value: locate_target.text,
        content_heading: locate_target.heading,
        generate_info: selector_target.generate,
        prompt_html
    });
    const max_turns = turns || 1;
    const chat = new LLMChat(model);

    let response: ChatResponse | undefined = undefined;
    let output = "";
    let status = "";
    let status_message = "";

    console.log(chalk.gray(`Analyzing HTML content... (using model ${model}), slice.html.length=${slice.html.length}, max_turns=${max_turns}, ITERATION #${iteration}`));
    response = undefined;
    output = "";
    try {
        [response, output] = await chat.json<ChatResponse>(prompt);
    }
    catch (err) {
        status_message = err instanceof Error ? err.message : JSON.stringify(err);
    }
    if (!response && output) {
        response = {
            selector: undefined,
            explain: output
        };
    }
    else if (!response) {
        status = "ERROR";
        if (!status_message)
            status_message = "Unable to convert LLM response to JSON";
        console.log(chalk.red(`ERROR: [LLM] ${status_message}`));
        console.log(chalk.gray.italic(output));
    }

    if (response && (response.selector || response.explain)) {
        console.log(chalk.gray(`>>> ${chalk.cyan(response.selector || "(none)")}${response.regex ? `, regex=${response.regex}` : ""}`));
        console.log(chalk.gray.italic(response.explain));
    }

    let consecutive_failure_count = 0;
    let made_additional_suggestion = false;
    let made_price_suggestion = false;
    let excessive_selector_length = false;
    let qualify_count = 0;

    while (chat.turns <= max_turns && !status && response) {
        let retry_prompt = "";

        if (response.selector === "null")
            response.selector = null;

        if (response.selector)
            consecutive_failure_count = 0;
        else
            consecutive_failure_count += 1;

        if (!response.selector && !made_additional_suggestion) {
            retry_prompt = renderGenerateSelectorHintPrompt({ content_heading: locate_target.heading });
            console.log(chalk.gray("empty result, making additional suggestions"));
            made_additional_suggestion = true;
        }

        let select_result: syphonx.ExtractState | undefined = undefined;
        let selector_json_value = undefined;
        if (response.selector) {
            response.selector = unhashSelector(response.selector);
            if (response.selector.length > max_selector_length && !excessive_selector_length) {
                retry_prompt = `The generated selector is too long, please try again to generate a selector that is less than ${max_selector_length} characters long.`;
                status_message = `Generated selector length (${response.selector.length}) exceeds maximum length (${max_selector_length})`;
                console.log(chalk.gray(status_message));
                excessive_selector_length = true;
            }
            else if (response.selector.split(" > ").length > max_child_depth && !excessive_selector_length) {
                retry_prompt = `The generated selector has too many child combinators, please try again to generate a selector that has less than ${max_child_depth} child combinators.`;
                status_message = `Generated selector depth (${response.selector.split(" > ").length}) exceeds maximum depth (${max_child_depth})`;
                console.log(chalk.gray(status_message));
                excessive_selector_length = true;
            }
            else if (response.selector) {
                if (!response.selector.startsWith("$") && response.regex)
                    response.selector = `$('${response.selector.replaceAll(`'`, `"`)}').extract("/${response.regex}/")`;

                // RUN SYPHONX QUERY
                let query: syphonx.SelectQuery | undefined = undefined;
                if (response.selector.startsWith("$"))
                    query = syphonx.parseJQuery(response.selector);
                else if (response.selector)
                    query = [response.selector];
                if (query) {
                    const select = [{
                        query: [query],
                        type: selector_target.type as syphonx.SelectType,
                        repeated: selector_target.repeated,
                        format: selector_target.expect === "url" ? "href" : undefined
                    }] as syphonx.Select[];
                    console.log(chalk.gray(`Running syphonx.select(${JSON.stringify(select)}) ...`));
                    select_result = undefined;
                    selector_json_value = undefined;
                    try {
                        select_result = syphonx.select(select, labelled_html, { url });
                    }
                    catch (err) {
                        status_message = err instanceof Error ? err.message : JSON.stringify(err);
                        retry_prompt = `
Generated selector \`${response.selector}\` caused an error:
${status_message}
Please try again to generate a selector that resolves this error.
Use the same output format.`.trim();
                        console.log(chalk.yellow(`ERROR: [SYPHONX] ${status_message}`));
                    }
                    if (select_result?.data?.value)
                        selector_json_value = JSON.stringify(select_result.data.value);

                    // CHECK 1/3 - HITS
                    if (!retry_prompt) {
                        const hits = select_result?.data?.nodes.length || 0;
                        console.log(chalk.gray(`syphonx.select returned ${hits} hits`));
                        if (hits === 0) {
                            retry_prompt = `
Generated selector \`${response.selector}\` didn't match any content.
Please try again to generate a more specific selector that includes all the targets.
Use the same output format.`.trim();
                            status_message = "Generated selector didn't match any content.";
                            console.log(chalk.gray(status_message));
                        }
                        else {
                            console.log(chalk.gray.italic(truncate(JSON.stringify(select_result?.data), 500)));
                        }
                    }

                    // CHECK 2/3 - UNCONTAINED
                    if (!retry_prompt && select_result?.data.nodes && slice.anchors.length > 0) {
                        const uncontained_targets = findUncontainedTargets(labelled_html, select_result?.data.nodes, slice.anchors);
                        if (uncontained_targets.length > 0) {
                            retry_prompt = `
The generated selector \`${response.selector}\` is too generic and is selecting the following elements that exist outside of the given context within the wider HTML document:
${uncontained_targets.slice(0, 10).join("\n")}
Please try again to generate a more specific selector that does not select the above elements.
Use the same output format.`.trim();
                            status_message = `Selector too generic, ${uncontained_targets.length} hits on entire page, expecting ${slice.targets.length} hits within target area.`;
                            console.log(chalk.gray(status_message));
                        }
                        /*
                        else if (
                            selector_name.toLowerCase().includes("price") // if price selector
                            && /\d+/.test(select_result.data.value) // result has a number
                            && !/\d+[.,]\d{2}$/.test(select_result.data.value) // result doesn't have a fractional part  BUG!
                            && !made_price_suggestion // only make this check once per chat session
                            && chat.turns < max_turns // waive this check if we're out of turns
                        ) {
                            retry_prompt = renderGenerateSelectorHintPricePrompt({});
                            made_price_suggestion = true;
                            console.log(chalk.gray(`Whole number price detected (${select_result.data.value}), double checking for formatted price...`));
                        }
                        */
                    }

                    // CHECK 2/3 - EXPECT
                    if (!retry_prompt &&  selector_target.expect) {
                        const actual_value = Array.isArray(select_result?.data?.value) ? select_result.data.value[0] : select_result?.data?.value; // if array only check first item
                        const expected_value = selector_target.expect === "url" ? "(url)" : locate_target.text;
                        console.log(chalk.gray(`EXPECT: ${selector_target.expect}`));
                        console.log(chalk.gray(`ACTUAL: ${chalk.italic(actual_value)}`));
                        console.log(chalk.gray(`EXPECTED: ${chalk.italic(expected_value)}`));
                        if (!isEmpty(expected_value)) {
                            const expect_result = await expect({
                                mode: selector_target.expect,
                                type: selector_target.type,
                                repeated: false,
                                //repeated: selector_target.repeated,
                                actual_value,
                                expected_value
                            });
                            if (expect_result.ok) {
                                console.log(chalk.gray(`Expected result verified.`));
                                if (expect_result.message)
                                    console.log(chalk.gray.italic(expect_result.message));
                            }
                            else {
                                retry_prompt = `The selector did not return the expected value.\n${expect_result.message}\nPlease try again to generate a selector that produces an expected value like "${expected_value}".`;
                                status_message = expect_result.message;
                                console.log(chalk.gray(`Expected result not verified.`));
                                console.log(chalk.gray.italic(expect_result.message));
                            }
                        }
                        else {
                            console.log(chalk.gray("Unable to verify expected result, no expected value available."))
                        }
                    }                    
                }
            }
        }

        if (qualify && response.selector && !retry_prompt) {
            console.log(chalk.gray(`Qualifying selector... (using model ${autogen_profile.qualifySelector.model})`));
            const qualify_prompt = renderQualifySelectorPrompt({
                selector: response.selector,
                selector_name,
                selector_json_value: truncate(selector_json_value, 250),
                qualify_info: selector_target.qualify
            });
            const qualify_chat = new LLMChat(autogen_profile.qualifySelector.model);
            const qualify_response = await qualify_chat.prompt(qualify_prompt);
            const qualify_ok = qualify_response.includes("OK");
            if (qualify_ok) {
                console.log(chalk.gray("Selector passed qualification check."));
            }
            else if (qualify_count >= qualify_pass_count) {
                status_message = `Selector failed qualifification check. ${qualify_response.trim()}`;
                console.log(chalk.gray(status_message));
            }
            else {
                retry_prompt = `
The generated selector \`${response.selector}\` appears to have the following issues:
${qualify_response}
Please try again to generate a selector that avoides these issues.
Use the same output format.`.trim();
                status_message = `Selector not qualified...\n${qualify_response.trim()}`;
                console.log(chalk.gray(status_message));
            }
            qualify_count += 1;
            analyze.push({
                name: "qualify-selector",
                model: qualify_chat.model,
                tokens: qualify_chat.tokens,
                input_tokens: qualify_chat.input_tokens,
                output_tokens: qualify_chat.output_tokens,
                cost: qualify_chat.cost,
                elapsed: qualify_chat.elapsed,
                message: qualify_response
            });
        }

        if (response.selector && !retry_prompt) {
            status = "OK";
            status_message = `Selector generated, validated, and qualified after ${chat.turns} turns.`;
            console.log(chalk.gray(status_message));
            break;
        }

        if (consecutive_failure_count > 1) {
            status = "ABORTED";
            const message = `Aborted after ${consecutive_failure_count} consecutive failures.`;
            console.log(chalk.gray(message));
            status_message = `${status_message} ${message}`.trim();
            break;
        }

        if (chat.turns >= max_turns) {
            status = "ABORTED";            
            const message = `Aborted after exceeding max turn limit of ${chat.turns}.`;
            console.log(chalk.gray(message));
            status_message = `${status_message} ${message}`.trim();
            break;
        }

        if (chat.turns > 2 && !made_additional_suggestion) {
            retry_prompt += "\n\n" + renderGenerateSelectorHintPrompt({ content_heading: locate_target.heading });
            made_additional_suggestion = true;
        }

        console.log(chalk.gray(`Reanalyzing HTML content... (using model ${model}) RETRY ${chat.turns}/${max_turns - 1}`));
        response = undefined;
        output = "";
        try {
            [response, output] = await chat.json<ChatResponse>(retry_prompt);
        }
        catch (err) {
            status_message = err instanceof Error ? err.message : JSON.stringify(err);
        }
        if (!response && output) {
            response = {
                selector: undefined,
                explain: output
            };
        }
        else if (!response) {
            status = "ERROR";
            if (!status_message)
                status_message = "Unable to convert LLM response to JSON";
            console.log(chalk.gray(`ERROR: [LLM] ${status_message}`));
            console.log(chalk.gray.italic(output));
            break;
        }

        console.log(chalk.gray(`>>> ${chalk.cyan(response.selector || "(none)")}${response.regex ? `, regex=${response.regex}` : ""}`));
        console.log(chalk.gray.italic(response.explain));
    }

    analyze.push({
        name: "generate-selector",
        model: chat.model,
        tokens: chat.tokens,
        input_tokens: chat.input_tokens,
        output_tokens: chat.output_tokens,
        cost: chat.cost,
        elapsed: chat.elapsed,
        message: response?.explain
    });

    let { selector, explain } = response || {};
    if (stream) {
        const { summary } = summarize(analyze);
        stream.write(`
            <section>
                <p>GENERATED SELECTOR: ${selector ? `<code>${escapeHtmlString(selector)}</code>`: "(none)"} <i>(${status})</i></p>
                ${explain ? `<p><i>${escapeHtmlString(explain)}</i><p>` : ""}
                <p><i>${escapeHtmlString(status_message)}</i></p>
                <pre>${escapeHtmlString(prompt_html)}</pre>
            </section>
            <details>
                <summary>PROMPT</summary>
                ${chat.messages.map(message => `<u>${message.role}</u><pre>${escapeHtmlString(message.text)}</pre>`).join("\n<hr>\n")}
            </details>
            <p class="info">USAGE: ${summary}</p>\n`);
    }

    if (!explain)
        explain = status_message;

    return { selector, explain, analyze, ok: status === "OK" };
}
