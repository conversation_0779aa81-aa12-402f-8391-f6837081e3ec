import chalk from "chalk";

import {
    autogen<PERSON>rofile,
    renderJinjaTemplateFile,
    AnalyzeResult,
    LLMChat,
    LLMModel
} from "../index.js";

export interface RemoveHashIdsOptions {
    html: string;
    model?: LLMModel;
}

export interface RemoveHashIdsResult {
    html: string;
    hash_ids: string[];
    model?: string;
    analyze: AnalyzeResult[];
}

export async function removeHashIds({ html, model }: RemoveHashIdsOptions): Promise<RemoveHashIdsResult> {
    const all_ids = extractHtmlIdentifiers(html);
    if (all_ids.length === 0)
        return { html, hash_ids: [], analyze: [] };

    if (!model)
        model = autogenProfile().removeHashIds.model;
    if (process.env.VERBOSE)
        console.log(chalk.gray(`Removing hash ids... (using model ${model})`));

    const prompt = renderJinjaTemplateFile("classify-hash-ids.md", { input: all_ids.join("\n") });
    const chat = new LLMChat(model);
    const answer = await chat.prompt(prompt);
    const response = extractTextResponse(answer);
    const hash_ids = response.trim().toLowerCase() !== "(none)" ? response.split("\n").map(line => line.trim()).filter(line => line.length > 2) : [];
    if (hash_ids.length > 0) {
        for (const identifier of hash_ids)
            html = html.replaceAll(identifier, "__HASH__");
        html = removeStandaloneHashIds(html);
        html = removeExtraneousWhitespaceFromClassesAndIds(html);
        html = removeBlankClassesAndIds(html);
        html = html.trim();
    }
    if (process.env.VERBOSE)
        console.log(chalk.gray(`${hash_ids.length} hash ids removed`));

    return {
        html,
        hash_ids,
        model,
        analyze: [{
            name: "remove-hash-ids",
            model: chat.model,
            tokens: chat.tokens,
            input_tokens: chat.input_tokens,
            output_tokens: chat.output_tokens,
            cost: chat.cost,
            elapsed: chat.elapsed
        }]
    };
}

function extractHtmlIdentifiers(html: string): string[] {
    const set = new Set<string>();
    const matches = html.matchAll(/(?:id|class)=["']([^"']+)["']/gi);
    for (const [, text] of matches)
        for (const identifier of text.split(/\s+/))
            set.add(identifier);
    return Array.from(set);
    //return removePrefixes(Array.from(set));
}

function extractTextResponse(text: string): string {
    text = text.replaceAll("\r\n", "\n");
    const i = text.indexOf(`"""`);
    const j = text.lastIndexOf(`"""`);
    return i >= 0 && j > i ? text.slice(i + 3, j).trim() : text;
}

function removeBlankClassesAndIds(html: string): string {
    return html.replace(/\s*(id|class)=(''|"")/g, ""); // remove id and class attributes left blank by the previous steps
}

function removeExtraneousWhitespaceFromClassesAndIds(html: string): string {
    return html.replace(/(class|id)\s*=\s*["']([^"']*)["']/g, (match, attribute, content) => {
        const cleaned = content.split(/\s+/).filter(Boolean).join(" ");
        return `${attribute}="${cleaned}"`;
    });
}

function removeStandaloneHashIds(html: string): string {
    return html.replace(/(?<![a-z0-9_-])__HASH__(?![a-z0-9_-])|(?<=["'])__HASH__(?=["'])/g, ''); // remove standalone redacts
}

export function unhashSelector(text: string): string {
    text = text.replace(/\.([^#. ]+)__HASH__([^#. ]+)/g, "[class*='$1'][class*='$2']"); // ".foo-__HASH__-bar" -> "[class*='foo-'][class*='-bar']"
    text = text.replace(/\.([^#. ]+)__HASH__/g, "[class*='$1']"); // ".foo-__HASH__" -> "[class*='foo-']"
    text = text.replace(/\.__HASH__([^#. ]+)/g, "[class*='$1']"); // ".__HASH__-foo" -> "[class*='-foo']"

    text = text.replace(/#([^#. ]+)__HASH__([^#. ]+)/g, "[id*='$1'][id*='$2']"); // "#foo-__HASH__-bar" -> "[id*='foo-'][id*='-bar']"
    text = text.replace(/#([^#. ]+)__HASH__/g, "[id*='$1']"); // "#foo-__HASH__" -> "[id*='foo-']"
    text = text.replace(/#__HASH__([^#. ]+)/g, "[id*='$1']"); // "#__HASH__-foo" -> "[id*='-foo']"

    text = text.replace(/__HASH__/g, ""); // remove any remaining
    text = text.replace(/\s{2,}/g, " "); // compress any residue whitespace to a single space
    return text.trim();
}

/*
function removePrefixes(names: string[]): string[] {
    names = names.sort().filter(name => name.length >= 4);
    const prefixes = new Set<string>();
    for (let i = 1; i < names.length; ++i) {
        const name = names[i];
        const prefix = names.slice(0, i).reverse().find(prefix => name.startsWith(prefix));
        if (prefix)
            prefixes.add(prefix);
    }
    return names.filter(name => !prefixes.has(name));
}
*/
