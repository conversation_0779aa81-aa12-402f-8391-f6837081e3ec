import { parseJ<PERSON><PERSON><PERSON>, Select, Template } from "syphonx-lib";
import chalk from "chalk";

import {
    closeLogStream,
    createLocalLogStream,
    createRemoteLogStream,
    escapeHtmlString,
    generateSelector,
    generateTimestamp,
    getSelectorProfile,
    hookConsoleLog,
    locateContent,
    openPageForVision,
    parseDomainName,
    saveTemplate,
    summarize,
    unhookConsoleLog,
    AutogenOptions,
    AutogenResult,
    AnalyzeRegressionResult,
    <PERSON>rows<PERSON>,
    WaitFor
} from "./lib.js";

export async function autogen_llm({ url, selector_profile, template_path, autogen_id, debug_targets, debug }: AutogenOptions): Promise<AutogenResult> {
    const result: AnalyzeRegressionResult = {
        generated_selectors: [],
        analyze: [],
        ok: false
    };

    const domain_name = parseDomainName(url);
    const log_name = generateTimestamp("seconds");
    const log_options = { title: "AUTOGEN", subtitle: url, id: autogen_id };
    const { stream, url: report_url } = !debug ?
        createRemoteLogStream(`autogen/${log_name}.html`, log_options) :
        createLocalLogStream(`${log_name}.html`, log_options);
    hookConsoleLog();
    console.log(chalk.gray(`opened log stream ${report_url}`));

    let _browser: Browser | undefined = undefined;
    try {
        do {
            const { page, browser, analyze, ok } = await openPageForVision(url, {
                page_types: ["PRODUCT-PAGE"],
                headless: false,
                snooze: 10000,
                timeout: 10000,
                waitfor: "networkidle" as WaitFor,
                stream
            });
            _browser = browser;
            result.analyze.push(...analyze.map(obj => ({ ...obj, report_url })));
            if (!ok || !page)
                break;
    
            const locate_result = await locateContent({ page, selector_profile, domain_name, stream });
            result.analyze.push(...locate_result.analyze.map(obj => ({ ...obj, report_url })));

            if (_browser) {
                await _browser.close();
                _browser = undefined;
            }

            if (!locate_result.ok || !locate_result.targets || !locate_result.labelled_html)
                break;

            const { targets: selector_targets } = getSelectorProfile(selector_profile);
            const locate_targets = Object.keys(locate_result.targets);
            for (const key of locate_targets) {
                console.log();
                const locate_target = locate_result.targets[key] || {};
                if (!locate_target || !locate_target.labels || locate_target.labels.length === 0) {
                    result.generated_selectors.push({
                        selector_name: key,
                        explain: `Content not located`,
                        ok: false
                    });
                    console.log(chalk.gray(`Content not located for "${key}"`));
                    if (locate_target.explain)
                        console.log(chalk.gray.italic(locate_target.explain));
                    continue;
                }
                if (debug_targets && !debug_targets.includes(key)) {
                    result.generated_selectors.push({
                        selector_name: key,
                        explain: `Skipped (debug mode)`,
                        ok: false
                    });
                    console.log(chalk.gray(`Skipping "${key}" (debug mode)`));
                    continue;
                }
                
                const selector_target = selector_targets.find(profile_target => profile_target.name === key);
                if (!selector_target) {
                    result.generated_selectors.push({
                        selector_name: key,
                        explain: `Skipped (missing selector profile target)`,
                        ok: false
                    });
                    console.log(chalk.gray(`Skipping "${key}" (missing selector profile target)`));
                    continue;
                }

                const generate_result = await generateSelector({
                    selector_name: key,
                    domain_name,
                    url,
                    locate_target,
                    selector_target,
                    labelled_html: locate_result.labelled_html,
                    stream,
                });
                result.analyze.push(...generate_result.analyze.map(obj => ({ ...obj, report_url })));
    
                result.generated_selectors.push({
                    selector_name: key,
                    selector: generate_result.ok && generate_result.selector ? generate_result.selector : undefined,
                    explain: generate_result.explain,
                    ok: generate_result.ok
                });
            }

            const generated_selectors = result.generated_selectors.filter(obj => !!obj.selector);
            stream.write(`
                <details>
                    <summary>OUTPUT SUMMARY <small>${`${generated_selectors.length}/${locate_targets.length} selectors generated`}</small></summary>
                    <p>${template_path}</p>
                    <table>
                        <thead>
                            <tr>
                                <td>Name</td>
                                <td>Selector</td>
                                <td>Explain</td>
                            </tr>
                        </thead>
                        <tbody>
                        ${locate_targets.map(key => {
                            const generated_selector = result.generated_selectors.find(obj => obj.selector_name === key);
                            const { selector_name, selector, explain } = generated_selector || { selector_name: key };
                            return `
                            <tr>
                                <td>${selector_name}</td>
                                <td>${selector ? `<code>${selector}</code>` : "(none)"}</td>
                                <td><i>${escapeHtmlString(explain) || "Content not located"}</i></td>
                            </tr>`;
                        }).join("\n")}
                        </tbody>
                    </table>
                </details>\n`);

            console.log();
            console.log(chalk.gray(`${generated_selectors.length}/${locate_targets.length} selectors generated...`));
            for (const key of locate_targets) {
                const generated_selector = generated_selectors.find(obj => obj.selector_name === key);
                const locate_target = locate_result.targets[key];
                if (generated_selector) {
                    const { selector_name, selector, explain } = generated_selector;
                    console.log(`${chalk.white(selector_name)}: ${selector ? chalk.cyan.bold(selector) : chalk.gray.italic("(none)")} ${chalk.gray.italic(explain)}`);
                }
                else if (locate_target?.explain) {
                    console.log(chalk.gray(`${chalk.white(key)}: ${chalk.italic(`(${locate_target.explain})`)}`));
                }
                else {
                    console.log(chalk.gray(`${chalk.white(key)}: ${chalk.italic("(none)")}`));
                }
            }

            if (template_path && generated_selectors.length > 0) {
                const { targets: selector_targets } = getSelectorProfile(selector_profile);
                const template: Template = {
                    url,
                    actions: [
                        {
                            select: generated_selectors
                                .filter(obj => !!obj.selector)
                                .map(obj => {
                                    const selector_target = selector_targets.find(selector_target => selector_target.name === obj.selector_name);
                                    return {
                                        name: obj.selector_name,
                                        query: obj.selector!.startsWith("$") ? [parseJQuery(obj.selector!)!] : [[obj.selector!]],
                                        type: selector_target?.type,
                                        repeated: selector_target?.repeated,
                                        waitfor: selector_target?.waitfor,
                                        format: selector_target?.expect === "url" ? "href" : undefined,
                                        comment: obj.explain
                                    } as Select;
                                })
                        }
                    ]
                };
                if (!debug) {
                    await saveTemplate(template_path, template);
                    console.log(chalk.gray(`Template saved: ${template_path}`));
                }
                else {
                    console.log(chalk.gray(`Template NOT saved: ${template_path} (DEBUG MODE)`));
                }
                result.ok = true;
            }
        }
        while (false);
    }
    catch (err) {
        result.message = err instanceof Error ? err.message : JSON.stringify(err);
        result.ok = false;
        console.log(chalk.gray(`ERROR: ${result.message}`));
    }
    finally {
        const { summary } = summarize(result.analyze);
        console.log();
        console.log(chalk.gray(summary));
        console.log(chalk.gray(`REPORT: ${report_url}`));

        await closeLogStream(stream, { analyze: result.analyze, console: true });
        unhookConsoleLog();
        if (_browser)
            await _browser.close();
    }

    const ok = result.generated_selectors.some(obj => obj.ok);
    return { ...result, report_url, ok };
}
