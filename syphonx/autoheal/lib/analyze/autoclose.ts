import * as stream from "node:stream";
import * as syphonx from "syphonx-lib";
import chalk from "chalk";

import {
    autogenProfile,
    addLabels,
    classifyLivePage,
    escapeHtmlString,
    generateSelector,
    insertSyphonXAutoselect,
    parseDomainName,
    querySyphonXAutoselect,
    remove<PERSON><PERSON><PERSON>,
    renderAutocloseDialogPrompt,
    renderScreenshot,
    sleep,
    summarize,
    AnalyzeResult,
    Page,
    LLMChat,
    LLMModel
} from "./lib.js";

export interface AutoCloseDialogOptions {
    page: Page;
    domain_name?: string;
    model?: LLMModel;
    attempt?: number;
    thumbnail_size?: number;
    stream?: stream.Writable;
}

export interface AutoCloseDialogResult {
    labels: string[];
    explain?: string;
    screenshot?: string;
    message?: string;
    analyze: AnalyzeResult[];
    ok: boolean;
}

export async function applyAutocloseSelectors(page: Page, domain_name?: string): Promise<void> {
    const url = page.url();
    if (!domain_name)
        domain_name = parseDomainName(url);
    const autoselects = await querySyphonXAutoselect({ domain_name, selector_name: "autoclose" });
    const autoclose_selectors = autoselects.map(obj => obj.selector);

    if (process.env.VERBOSE)
        console.log(chalk.gray(`${autoclose_selectors.length} autoclose selectors found for domain ${domain_name}`));
    if (autoclose_selectors.length > 0) {
        await runSelectors(page, autoclose_selectors);
        await sleep(250);
    }
}

export async function autocloseAnalyze({ page, domain_name, model, attempt = 1, thumbnail_size = 800, stream }: AutoCloseDialogOptions): Promise<AutoCloseDialogResult> {
    const url = page.url();
    if (!domain_name)
        domain_name = parseDomainName(url);
    if (!model)
        model = autogenProfile().locateContent.model;

    const result: AutoCloseDialogResult = {
        labels: [],
        analyze: [],
        ok: true
    };

    const autoselects = await querySyphonXAutoselect({ domain_name, selector_name: "autoclose" });
    const autoclose_selectors = autoselects.map(obj => obj.selector);
    const generated_selectors = [];

    if (process.env.VERBOSE)
        console.log(chalk.gray(`${autoclose_selectors.length} autoclose selectors found for domain ${domain_name}`));
    if (autoclose_selectors.length > 0) {
        await runSelectors(page, autoclose_selectors);
        const done = await reanalyzePage();
        if (done)
            return result;
    }

    if (process.env.VERBOSE)
        console.log(chalk.gray(`Analyzing labelled page... (using model ${model})`));
    const [labelled_html] = await addLabels(page);
    const screenshot = await renderScreenshot(page, { fullPage: false, maxWidth: 1024, maxHeight: 1024 });

    const prompt = renderAutocloseDialogPrompt({ attempt });
    const chat = new LLMChat(model);
    const response = await chat.prompt(prompt, { images: [screenshot.datauri] });

    result.analyze!.push({
        name: "autoclose",
        model: chat.model,
        tokens: chat.tokens,
        input_tokens: chat.input_tokens,
        output_tokens: chat.output_tokens,
        cost: chat.cost,
        elapsed: chat.elapsed,
        message: response
    });

    const labels = Array.from(new Set(response.match(/\[\d+\]/g)?.map(label => label.slice(1, -1)) || []));
    for (const label of labels) {
        if (process.env.VERBOSE)
            console.log(chalk.gray(`Generating selector ${labels.indexOf(label)+1}/${labels.length}...`));
        const generate_result = await generateSelector({
            selector_name: "autoclose",
            domain_name,
            url,
            locate_target: {
                text: "",
                explain: "",
                labels: [parseInt(label)]
            },
            selector_target: {
                name: "autoclose",
                type: "boolean"
            },
            labelled_html,
            qualify: false,
            stream
        });
        result.analyze.push(...generate_result.analyze);
        if (generate_result.ok && generate_result.selector) {
            if (!autoclose_selectors.includes(generate_result.selector)) {
                generated_selectors.push(generate_result.selector);
                await insertSyphonXAutoselect({
                    domain_name,
                    selector_name: "autoclose",
                    selector: generate_result.selector
                });
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`Selector ${generate_result.selector} added for domain ${domain_name}`));
            }
            else {
                if (process.env.VERBOSE)
                    console.log(chalk.gray(`Selector ${generate_result.selector} already added for domain ${domain_name}`));
            }
        }
    }

    await removeLabels(page); // remove if clicks are re-enabled!!!!
    if (process.env.VERBOSE)
        console.log(chalk.gray(`${generated_selectors.length} autoclose selectors generated for domain ${domain_name}`));
    if (generated_selectors.length > 0) {
        await runSelectors(page, generated_selectors);
        const done = await reanalyzePage();
        if (done)
            return result;
    }

    const { datauri: screenshot_after } = labels.length > 0 && stream ? await renderScreenshot(page, { fullPage: false, maxWidth: 1024, maxHeight: 1024 }) : { datauri: "" };
    result.labels = labels;
    result.screenshot = screenshot_after || screenshot.datauri;
    result.explain = response.slice(0, response.indexOf("[")).trim();
    return result;


    /*
    const clicks = [];
    if (attempt === 1 && response.includes("modal")) {
        // for first attempt try clicking outside the modal dialog
        await page.mouse.click(1, 1);
        clicks.push("modal-click");
    }
    else {
        // llm vision generally not good at returning coordinates right now...
        // https://community.openai.com/t/getting-gpt-vision-to-return-coordinates/671669/3
        // const coordinates = Array.from(new Set(response.match(/\{x=(\d+),y=(\d+)\}/g)?.map(label => [parseInt(regexpExtract(/x=(\d+)/, label)!),parseInt(regexpExtract(/y=(\d+)/, label)!)]) || []));
        // for (const coordinate of coordinates) {
        //     const x = coordinate[0] * screenshot.scale;
        //     const y = coordinate[1] * screenshot.scale;
        //     await page.mouse.click(x, y);
        //     clicks.push(`{x=${x},y=${y}}`);
        //     await sleep(1000);
        // }

        console.log(chalk.gray(`${labels.length} labels found`));
        for (const label of labels.slice(0, 3)) {
            console.log(chalk.gray(`[${labels.indexOf(label)+1}/${labels.length}] clicking label [${label}]`));
            try {
                await page.click(`[__label__='${label}']`);
            }
            catch (err) {
                result.ok = false;
                result.message = err instanceof Error ? err.message : JSON.stringify(err);
                clicks.push(`${label} ERROR ${result.message}`);
                break;
            }
            clicks.push(label);
            await sleep(1000);
        }
    }

    await removeLabels(page);
    const { datauri: screenshot_after } = labels.length > 0 && stream ? await renderScreenshot(page, { fullPage: false, maxWidth: 1024, maxHeight: 1024 }) : { datauri: "" };

    if (stream) {
        const { summary } = summarize(result.analyze!);
        stream.write(`
        <details>
            <summary>AUTOCLOSE</summary>
            <div>CLICKED ON ${clicks.join(", ")}...</div>
            <div>BEFORE</div>
            <img src="${screenshot.datauri}">
            <div>AFTER</div>
            <img src="${screenshot_after}">
            <details>
                <summary>PROMPT</summary>
                <pre>${escapeHtmlString(prompt)}</pre>
                <hr>
                <pre>${escapeHtmlString(response)}</pre>
            </details>
            <p class="info">USAGE: ${summary}</p>
        </details>\n`.trimStart());
    }

    const { datauri: screenshot_after } = labels.length > 0 && stream ? await renderScreenshot(page, { fullPage: false, maxWidth: 1024, maxHeight: 1024 }) : { datauri: "" };
    result.labels = labels;
    result.screenshot = screenshot_after || screenshot.datauri;
    result.explain = response.slice(0, response.indexOf("[")).trim();
    return result;
    */

    async function reanalyzePage() {
        console.log(chalk.gray(`Re-analyzing page... (using model ${model})`));
        const context = {
            domain_name: parseDomainName(url),
            capture_url: url
        };
        let page_result = await classifyLivePage({ page, context, thumbnail_size, stream });
        if (page_result.analyze)
            result.analyze.push(...page_result.analyze);
        if (!page_result.ok) {
            console.log(chalk.gray(`Error classifying page: ${page_result.message}`));
            result.ok = false;
            result.message = page_result.message;
            result.screenshot = page_result.datauri;
            if (stream)
                stream.write(`
                <details>
                    <summary>AUTOCLOSE</summary>
                    <div>${escapeHtmlString(page_result.message)}</div>
                </details>\n`.trimStart());
            return true;
        }
        console.log(chalk.gray(`Page classification: (${page_result.page_class})`));
        console.log(chalk.gray.italic(page_result.explain));
        if (page_result.page_class !== "MODAL" && !page_result.page_data?.popup_dialog) {
            console.log(chalk.gray(`All modal dialogs closed!`));
            result.ok = true;
            result.explain = page_result.message;
            result.screenshot = page_result.datauri;
            const { summary } = summarize(result.analyze!);
            stream?.write(`
            <details>
                <summary>AUTOCLOSE</summary>
                ${autoclose_selectors.length > 0 ? `
                <p>
                    <b>Known Selectors</b>
                    <ul>
                    ${autoclose_selectors.map(selector => `<li><code>${selector}</code></li>\n`)}
                    </ul>
                </p>\n` : ""}
                ${generated_selectors.length > 0 ? `
                <p>
                    <b>Generated Selectors</b>
                    <ul>
                    ${autoclose_selectors.map(selector => `<li><code>${selector}</code></li>\n`)}
                    </ul>
                </p>\n` : ""}
                <img src="${page_result.datauri}">
                <p><i>${escapeHtmlString(page_result.explain)}</i></p>
                <p class="info">USAGE: ${summary}</p>
            </details>\n`.trimStart());
            return true;
        }
        return false;
    }    
}

async function runSelectors(page: Page, selectors: string[]) {
    // Run selectors one-at-a-time with a delay in-between because clicking one dialog might change the disposition of the other dialogs
    console.log(chalk.gray(`Applying autoclose selectors...`));
    for (const selector of selectors) {
        console.log(chalk.gray(`CLICK ${chalk.bold(selector)} [${selectors.indexOf(selector) + 1}/${selectors.length}]`));
        const actions: syphonx.Action[] = [{ click: { query: [[selector]] } } as syphonx.ClickAction];
        try {
            await page.evaluate<syphonx.ExtractState>(`${syphonx.script}(${JSON.stringify({ actions })})`);
            await sleep(1000);
        }
        catch (err) {
            console.log(chalk.red(`ERROR: ${err instanceof Error ? err.message : JSON.stringify(err)}`));
        }
    }
}    
