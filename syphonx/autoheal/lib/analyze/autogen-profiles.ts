import { clone, LLMModel } from "../index.js";
import profiles from "./autogen-profiles.json" with { type: "json" };

let activeProfile: AutogenProfile = profiles.find(profile => profile.name === "default") as AutogenProfile;

export interface AutogenProfile {
    autoqc: {
        model: LLMModel;
    },
    autoCloseDialog: {
        model: LLMModel;
    },
    classifyScreenshot: {
        model: LLMModel;
    },
    generateSelector: {
        iterations: Array<{
            model: LLMModel;
            slice: number;
            turns: number;
        }>
    },
    locateContent: {
        model: LLMModel;
    },
    qualifySelector: {
        model: LLMModel;
    },
    removeHashIds: {
        model: LLMModel;
    }
}

export function setAutogenProfile(name: string): void {
    const profile = profiles.find(profile => profile.name === name) as AutogenProfile;
    if (!profile)
        throw new Error(`Profile "${name}" not found`);
    activeProfile = profile;
}

export function autogenProfiles(): string[] {
    return profiles.map(profile => profile.name);
}

export function autogenProfile(): AutogenProfile {
    return clone(activeProfile);
}
