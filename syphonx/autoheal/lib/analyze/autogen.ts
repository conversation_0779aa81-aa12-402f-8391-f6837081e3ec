import {
    setAutogenProfile,
    AnalyzeRegressionResult
} from "./lib.js";

import chalk from "chalk";
import { autogen_diffbot } from "./autogen-diffbot.js";
import { autogen_llm } from "./autogen-llm.js";

export interface AutogenOptions {
    url: string;
    selector_profile: string;
    autogen_id?: string;
    template_path: string;
    debug?: boolean;
    debug_targets?: string[];
}

export interface AutogenResult extends AnalyzeRegressionResult {
    report_url: string;
}

export async function autogen(profile: string, options: AutogenOptions) {
    let result: AutogenResult;

    if (profile === "diffbot") {
        result = await autogen_diffbot(options);
    }
    else if (profile && profile !== "default") {
        console.log(chalk.gray(`Setting profile "${profile}"...`));
        setAutogenProfile(profile);
        try {
            result = await autogen_llm(options);
        }
        finally {
            console.log(chalk.gray(`Resetting profile to default...`));
            setAutogenProfile("default");
        }
    }
    else {
        result = await autogen_llm(options);
    }
        
    return result;
}
