// aligns with playwright <PERSON><PERSON><PERSON> https://playwright.dev/docs/api/class-browser
export interface Browser {
    readonly closed: boolean;
    close(): Promise<void>;
    newPage(): Promise<Page>;
}

// aligns with playwright LaunchOptions https://github.com/microsoft/playwright/blob/main/packages/protocol/src/channels.ts#L914
export interface BrowserCreateOptions {
    args?: Array<string>;
    headless?: boolean;
    proxy?: BrowserProxy;
    viewport?: BrowserViewport;
    shared?: boolean;
}

export interface BrowserProxy {
    server: string;
    bypass?: string;
    username?: string;
    password?: string;
}

export interface BrowserViewport {
    width: number;
    height: number;
}

// aligns with playwright Page https://playwright.dev/docs/api/class-page
export interface Page {
    readonly browser: Browser;
    close(): Promise<void>;
    content(): Promise<string>;
    evaluate<R, Arg=any>(script: PageFunction<Arg, R>, arg?: Arg): Promise<R>;
    goto(url: string, options?: { timeout?: number, waitUntil?: WaitUntil }): Promise<void>;
    screenshot(options?: PageScreenshotOptions): Promise<Buffer>;
    setContent(html: string): Promise<void>;
    url(): string;
    waitForLoadState(state?: WaitUntil, options?: { timeout?: number }): Promise<void>;
}

export type PageFunction<Arg, R> = string | ((arg: Arg) => R | Promise<R>);

export interface Clip {
    width: number;
    height: number;
    x: number;
    y: number;
    scale: number;
}

export interface PageScreenshotOptions {
    fullPage?: boolean;
    path?: string;
    clip?: Clip;
    scale?: string;
}

export type WaitUntil = "load" | "domcontentloaded" | "networkidle" | "commit";
