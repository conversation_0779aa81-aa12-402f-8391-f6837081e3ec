import { <PERSON><PERSON><PERSON>, BrowserCreateOptions } from "./abstract-browser.js";
import { <PERSON>wright<PERSON>rowser } from "./playwright-browser.js";
import { ChromeBrowser } from "./chrome-browser.js";
import { Mutex } from "async-mutex";

export * from "./abstract-browser.js";
export type BrowserType = "playwright";

let browser: Browser | undefined = undefined;
const mutex = new Mutex();

export async function createVirtualBrowser({ shared, ...options }: BrowserCreateOptions = {}): Promise<Browser> {
    const key = process.env.BROWSER || "playwright";
    if (!shared) {
        const _browser = await createBrowserByKey(key, options);
        if (!_browser)
            throw new Error(`Invalid browser type "${key}"`);
        return _browser;
    }

    await mutex.runExclusive(async () => {
        if (!browser || browser.closed)
            browser = await createBrowserByKey(key, options);
    });

    if (!browser)
        throw new Error(`Invalid browser type "${key}"`);

    return browser;
}

async function createBrowserByKey(key: string, options: BrowserCreateOptions) {
    let browser: Browser | undefined = undefined;
    if (key === "playwright")
        browser = await PlaywrightBrowser.create(options);
    else if (key === "chrome")
        browser = await ChromeBrowser.create(options);
    return browser;
}