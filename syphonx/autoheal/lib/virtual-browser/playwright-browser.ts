import * as playwright from "playwright";
import { chromium } from "playwright-extra";
import stealth from "puppeteer-extra-plugin-stealth";
chromium.use(stealth());

import { Browser, BrowserCreateOptions, Page, BrowserViewport } from "./abstract-browser.js";

const userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36";

export class PlaywrightBrowser implements Browser {
    browser: playwright.Browser;
    viewport?: BrowserViewport;
    closed = false;

    private constructor(browser: playwright.Browser) {
        this.browser = browser;
    }

    async close(): Promise<void> {
        await this.browser.close();
        this.closed = true;
    }

    async newPage(): Promise<Page> {
        const page = await this.browser.newPage({ userAgent, viewport: this.viewport });
        if (!(page as any).browser)
            (page as any).browser = this.browser;
        return page as unknown as Page;
    }

    static async create({ viewport, ...options }: BrowserCreateOptions): Promise<Browser> {
        const obj = await chromium.launch(options);
        const browser = new PlaywrightBrowser(obj);
        browser.viewport = viewport;
        return browser;
    }
}
