import * as chrome from "chrome-launcher";
import CDP from "chrome-remote-interface";
import { promises as fs } from "fs";

import {
    <PERSON><PERSON><PERSON>,
    BrowserCreateOptions,
    Page,
    PageFunction,
    PageScreenshotOptions,
    WaitUntil
} from "./abstract-browser.js";

export class ChromeBrowser implements Browser {
    browser: chrome.LaunchedChrome;
    client?: CDP.Client;
    closed = false;

    private constructor(browser: chrome.LaunchedChrome) {
        this.browser = browser;
    }

    async close(): Promise<void> {
        if (this.client)
            await this.client.close();
        await this.browser.kill();
        this.closed = true;
    }

    async newPage(): Promise<Page> {
        // if a client doesn't exist, attach to the default page
        if (!this.client) {
            // attach to the default target (about:blank opened at launch)
            this.client = await CDP({ port: this.browser.port });
            return new ChromePage(this.client, this);
        }

        // otherwise, create a new target (new tab) as needed
        const { targetId } = await this.client.Target.createTarget({ url: 'about:blank' });
        const newClient = await CDP({
            port: this.browser.port,
            target: targetId
        });

        return new ChromePage(newClient, this);
    }

    static async create({ headless, proxy, viewport }: BrowserCreateOptions = {}): Promise<Browser> {
        const options: chrome.Options = {
            chromeFlags: []
        };
        if (headless !== false)
            options.chromeFlags!.push("--headless");
        if (proxy) {
            options.chromeFlags!.push(`--proxy-server=${proxy.server}`); // note: chrome does not support proxy auth
            options.chromeFlags!.push("--ignore-certificate-errors");
        }
        if (viewport?.width && viewport?.height)
            options.chromeFlags!.push(`--window-size=${viewport.width},${viewport.height}`);

        const browser = await chrome.launch(options);
        return new ChromeBrowser(browser);
    }
}

class ChromePage implements Page {
    client: CDP.Client;
    browser: Browser;
    _url: string;

    constructor(client: CDP.Client, browser: Browser) {
        this.client = client;
        this.browser = browser;
        this._url = "";
    }

    async close(): Promise<void> {
        this.client.close();
    }

    async content(): Promise<string> {
        const { root } = await this.client.DOM.getDocument();
        const { outerHTML } = await this.client.DOM.getOuterHTML({ nodeId: root.nodeId });
        return outerHTML;
    }

    async evaluate<R, Arg>(script: PageFunction<Arg, R> | string, arg?: Arg): Promise<R> {
        let expression: string;
        if (typeof script === "function")
            expression = `(${script.toString()})(${arg ? JSON.stringify(arg) : ""})`;
        else if (typeof script === "string")
            expression = script;
        else
            throw new Error("Invalid script specified");

        const { result, exceptionDetails } = await this.client.Runtime.evaluate({
            expression,
            awaitPromise: true,
            returnByValue: true
        });

        if (exceptionDetails)
            throw new Error(
                exceptionDetails.exception?.description ||
                `Page script evaluation failed: ${JSON.stringify(exceptionDetails.exception)}`
            );

        return result.value;
    }

    async goto(url: string, { timeout, waitUntil }: { timeout?: number, waitUntil?: WaitUntil } = {}): Promise<void> {
        await this.client.Page.enable();
        await this.client.Runtime.enable();
        await this.client.Page.navigate({ url });
        await Promise.race([
            this.client.Page.loadEventFired(),
            new Promise((_, reject) => setTimeout(() => reject(new Error("Page load timed out")), timeout || 30000))
        ]);
        this._url = url;
        if (waitUntil)
            await this.waitForLoadState(waitUntil, { timeout });
    }

    async screenshot({ path, fullPage, clip }: PageScreenshotOptions = {}): Promise<Buffer> {
        const screenshot = await this.client.Page.captureScreenshot({ format: "png", captureBeyondViewport: fullPage, clip });
        const buffer = Buffer.from(screenshot.data, "base64");
        if (path)
            await fs.writeFile(path, buffer);
        return buffer;
    }

    async setContent(html: string): Promise<void> {
        const { frameTree } = await this.client.Page.getFrameTree();
        await this.client.Page.setDocumentContent({ frameId: frameTree.frame.id, html });
    }

    url(): string {
        return this._url;
    }

    async waitForLoadState(state?: WaitUntil, { timeout }: { timeout?: number } = {}): Promise<void> {
        let promise;
        if (state === "load")
            promise = this.client.Page.loadEventFired();
        else if (state === "domcontentloaded")
            promise = this.client.Page.domContentEventFired();
        if (promise)
            await Promise.race([
                promise,
                new Promise((_, reject) => setTimeout(() => reject(new Error("Page load timed out")), timeout))
            ]);
    }
}
