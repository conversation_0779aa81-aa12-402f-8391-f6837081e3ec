// @ts-ignore - Redis may not be installed due to Node version compatibility
import { createClient, RedisClientType } from 'redis';

export const REDIS_DOMAIN_WTB_TRIGGERS_KEY = "config:scrape:trigger";

export interface ProductNotFoundTrigger {
    phrase: string;
    focus: string;
}

export interface DomainWtbTriggers {
    productNotFound?: ProductNotFoundTrigger;
    isBlocked?: ProductNotFoundTrigger;
}

export class RedisService {
    private client: any; // RedisClientType when redis is available
    private connected: boolean = false;

    constructor() {
        this.client = createClient({
            socket: {
                host: 'scout-redis.pricespider.com',
                port: 6379
            }
        });

        this.client.on('error', (err: any) => {
            console.error('Redis Client Error:', err);
        });

        this.client.on('connect', () => {
            console.log('Redis client connected');
            this.connected = true;
        });

        this.client.on('disconnect', () => {
            console.log('Redis client disconnected');
            this.connected = false;
        });
    }

    async connect(): Promise<void> {
        if (!this.connected || !this.client.isOpen) {
            try {
                await this.client.connect();
            } catch (error: any) {
                if (!error.message?.includes('already ready')) {
                    throw error;
                }
            }
        }
    }

    async disconnect(): Promise<void> {
        if (this.connected) {
            await this.client.disconnect();
        }
    }

    async isConnected(): Promise<boolean> {
        return this.connected && this.client.isOpen;
    }

    private async ensureConnection(): Promise<void> {
        if (!this.connected || !this.client.isOpen) {
            await this.connect();
        }
    }

    private normalizeDomain(domain: string): string {
        return domain.startsWith('www.') ? domain.substring(4) : domain;
    }

    async storeDomainTriggers(domain: string, triggers: DomainWtbTriggers): Promise<void> {
        await this.ensureConnection();

        const normalizedDomain = this.normalizeDomain(domain);
        const key = `${REDIS_DOMAIN_WTB_TRIGGERS_KEY}`;
        const value = JSON.stringify(triggers);

        await this.client.hSet(key, normalizedDomain, value);
        console.log(`Stored triggers for domain ${normalizedDomain}:`, triggers);
    }

    async getDomainTriggers(domain: string): Promise<DomainWtbTriggers | null> {
        await this.ensureConnection();
        const key = `${REDIS_DOMAIN_WTB_TRIGGERS_KEY}`;

        let value = await this.client.hGet(key, domain);

        if (!value) {
            const domainWithWww = domain.startsWith('www.') ? domain : `www.${domain}`;
            value = await this.client.hGet(key, domainWithWww);
            console.info(`Trying with www prefix: ${domainWithWww}`);
        }

        if (!value) {
            const domainWithoutWww = domain.startsWith('www.') ? domain.substring(4) : domain;
            value = await this.client.hGet(key, domainWithoutWww);
            console.info(`Trying without www prefix: ${domainWithoutWww}`);
        }

        if (!value) {
            console.info(`No triggers found for domain: ${domain}`);
            return null;
        }

        try {
            return JSON.parse(value) as DomainWtbTriggers;
        } catch (error) {
            console.error(`Failed to parse triggers for domain ${domain}:`, error);
            return null;
        }
    }

    async storeProductNotFoundTrigger(domain: string, phrase: string, focus: string): Promise<void> {
        const normalizedDomain = this.normalizeDomain(domain);
        const existingTriggers = await this.getDomainTriggers(normalizedDomain) || {};

        let finalPhrase = phrase;
        let finalFocus = focus;

        if (existingTriggers.productNotFound) {
            const existingPhrases = this.extractPhrasePatterns(existingTriggers.productNotFound.phrase);
            const newPhrases = this.extractPhrasePatterns(phrase);
            const allPhrases = [...new Set([...existingPhrases, ...newPhrases])];

            const hasExistingParentheses = existingTriggers.productNotFound.phrase.startsWith('(') &&
                existingTriggers.productNotFound.phrase.endsWith(')');

            if (hasExistingParentheses) {
                const existingPattern = existingTriggers.productNotFound.phrase.slice(1, -1);
                finalPhrase = `(${existingPattern}|${newPhrases.join('|')})`;
            } else {
                finalPhrase = allPhrases.length > 1 ? `(${allPhrases.join('|')})` : allPhrases[0];
            }

            const existingFocusSelectors = existingTriggers.productNotFound.focus.split(',').map(s => s.trim());
            const newFocusSelectors = focus.split(',').map(s => s.trim());
            const allFocusSelectors = [...new Set([...existingFocusSelectors, ...newFocusSelectors])];
            finalFocus = allFocusSelectors.join(',');
        }

        const updatedTriggers: DomainWtbTriggers = {
            ...existingTriggers,
            productNotFound: {
                phrase: finalPhrase,
                focus: finalFocus
            }
        };
        console.info(`Storing updated triggers for domain ${normalizedDomain}: ${JSON.stringify(updatedTriggers)}`);
        await this.storeDomainTriggers(normalizedDomain, updatedTriggers);
    }

    async storeBlockedTrigger(domain: string, phrase: string, focus: string): Promise<void> {
        const existingTriggers = await this.getDomainTriggers(domain) || {};

        let finalPhrase = phrase;
        let finalFocus = focus;

        if (existingTriggers.isBlocked) {
            const existingPhrases = this.extractPhrasePatterns(existingTriggers.isBlocked.phrase);
            const newPhrases = this.extractPhrasePatterns(phrase);
            const allPhrases = [...new Set([...existingPhrases, ...newPhrases])];

            const hasExistingParentheses = existingTriggers.isBlocked.phrase.startsWith('(') &&
                existingTriggers.isBlocked.phrase.endsWith(')');

            if (hasExistingParentheses) {
                const existingPattern = existingTriggers.isBlocked.phrase.slice(1, -1);
                finalPhrase = `(${existingPattern}|${newPhrases.join('|')})`;
            } else {
                finalPhrase = allPhrases.length > 1 ? `(${allPhrases.join('|')})` : allPhrases[0];
            }

            const existingFocusSelectors = existingTriggers.isBlocked.focus.split(',').map(s => s.trim());
            const newFocusSelectors = focus.split(',').map(s => s.trim());
            const allFocusSelectors = [...new Set([...existingFocusSelectors, ...newFocusSelectors])]; // Remove duplicates
            finalFocus = allFocusSelectors.join(',');
        }

        const updatedTriggers: DomainWtbTriggers = {
            ...existingTriggers,
            isBlocked: {
                phrase: finalPhrase,
                focus: finalFocus
            }
        };
        console.info(`Storing updated blocked trigger for domain ${domain}: ${JSON.stringify(updatedTriggers)}`);
        await this.storeDomainTriggers(domain, updatedTriggers); 
    }

    private extractPhrasePatterns(phrase: string): string[] {
        const match = phrase.match(/^\.\*\(([^)]+)\)\.\*$/);
        if (match) {
            return match[1].split('|').map(p => p.trim()).filter(p => p.length > 0);
        }

        const cleanPhrase = phrase.replace(/\\(.)/g, '$1');
        return cleanPhrase ? [cleanPhrase] : [];
    }

    async listAllDomains(): Promise<string[]> {
        await this.ensureConnection();

        const key = `${REDIS_DOMAIN_WTB_TRIGGERS_KEY}`;
        const domains = await this.client.hKeys(key);
        return domains;
    }

    async removeDomainTriggers(domain: string): Promise<boolean> {
        await this.ensureConnection();

        const normalizedDomain = this.normalizeDomain(domain);
        const key = `${REDIS_DOMAIN_WTB_TRIGGERS_KEY}`;
        const result = await this.client.hDel(key, normalizedDomain);

        return result > 0;
    }
}

let redisServiceInstance: RedisService | null = null;

export function getRedisService(): RedisService {
    if (!redisServiceInstance) {
        redisServiceInstance = new RedisService();
    }
    return redisServiceInstance;
}

export async function closeRedisConnection(): Promise<void> {
    if (redisServiceInstance) {
        await redisServiceInstance.disconnect();
        redisServiceInstance = null;
    }
}