export function combineUrl(base: string, relative: string): string {
    if (!relative)
        return base;
    if (!base)
        return relative;  
    return new URL(relative, base).toString();
}

export function isAbsoluteUrl(url: string): boolean {
    return url.startsWith("http://") || url.startsWith("https://");
}

export function parseUrl(url: string | undefined): { domain?: string, origin?: string } {
    if (typeof url === "string" && /^https?:\/\//.test(url)) {
        const [protocol, , host] = url.split("/");
        const a = host.split(":")[0].split(".").reverse();
        return {
            domain: a.length >= 3 && a[0].length === 2 && a[1].length === 2 ? `${a[2]}.${a[1]}.${a[0]}` : a.length >= 2 ? `${a[1]}.${a[0]}` : undefined,
            origin: protocol && host ? `${protocol}//${host}` : undefined
        };    
    }
    return {};
}

export function resolveUrl(url: string, href: string) {
    if (!isAbsoluteUrl(url)) {
        const { origin } = parseUrl(href);
        if (origin)
            return combineUrl(origin, url);
    }
    return url;
}
