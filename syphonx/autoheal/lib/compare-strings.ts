import OpenAI from "openai";
import { Timer } from "./timer.js";

export interface CompareStringsResult {
    score: number;
    model: string;
    usage: number;
    cost: number;
    duration: number;
}

export async function compareEmbeddings(a: string, b: string): Promise<CompareStringsResult> {
    const timer = new Timer();
    const e1 = await createEmbeddings(a);
    const e2 = await createEmbeddings(b);
    const score = cosineSimilarity(e1.data, e2.data);
    return {
        score,
        model: e1.model,
        usage: e1.usage + e2.usage,
        cost: e1.cost + e2.cost,
        duration: timer.elapsed()
    };
}

function cosineSimilarity(vecA: number[], vecB: number[]): number {
    let dotProduct = 0.0;
    let normA = 0.0;
    let normB = 0.0;
    for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        normA += vecA[i] * vecA[i];
        normB += vecB[i] * vecB[i];
    }
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

interface EmbeddingResult {
    data: number[];
    model: string;
    usage: number;
    cost: number;
}

async function createEmbeddings(input: string): Promise<EmbeddingResult> {
    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    const model = "text-embedding-ada-002";
    const { data, usage } = await openai.embeddings.create({ input, model });
    return {
        model,
        usage: usage.total_tokens,
        cost: parseFloat((usage.total_tokens * 0.0001 / 1000000).toFixed(6)),
        data: data[0].embedding
    };
}
