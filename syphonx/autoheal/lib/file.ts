import * as os from "os";
import * as path from "path";
import makeDir from "make-dir";
import * as fs from "fs";
import { access, createReadStream, statSync, WriteStream } from "fs";
import { copyFile, readdir, readFile } from "fs/promises";
import { createInterface } from "readline";
import { minimatch } from "minimatch";
import { tryParseJson } from "./json.js";

export const wsl = os.platform() === "linux" && os.release().toLowerCase().includes("microsoft");

export function closeStream(stream: WriteStream) {
    stream.end();
    return new Promise<void>(resolve => stream.end(() => resolve()));
}

export async function copyFiles(source: string[], sourceDir: string, targetDir: string, overwrite = false): Promise<void> {
    await makeDir(targetDir);
    for (const file of source) {
        const sourceFile = `${sourceDir}/${file}`;
        const targetFile = `${targetDir}/${file}`;
        const targetExists = !overwrite ? await fileExists(targetFile) : false;
        if (!targetExists)
            await copyFile(sourceFile, targetFile);
    }
}

export function fileExists(file: string): Promise<boolean> {
    return new Promise<boolean>(resolve =>
        access(file, fs.constants.F_OK, err => resolve(!err)));
}

export async function readFromFileStream(file: string, predicate: (line: string) => void): Promise<void> {
    const input = createReadStream(file);
    const reader = createInterface({ input });
    await new Promise<void>((resolve, reject) =>
        reader
            .on("line", predicate)
            .on("close", resolve)
            .on("error", reject));
    input.close();
}

export async function readFromFileStreamAsync(file: string, predicate: (line: string) => Promise<void>): Promise<void> {
    const input = createReadStream(file);
    const reader = createInterface({ input });  
    try {
        for await (const line of reader)
            await predicate(line);
    }
    finally {
        reader.close();
        input.close();
    }
}

/**
 * Scan files in a directory and return the first file that matches the test function, starting from the most recent file.
 * @param {string} path Directory path to scan.
 * @param {function} callback Test function to call on each file, returns true when file is found.
 * @returns {string[]} The first file that matches the test function.
 */
export async function scanFiles<T = {}>(dir: string, callback: (obj: T) => boolean): Promise<{ file?: string, obj?: T }> {
    const pattern = path.basename(dir);
    if (pattern)
        dir = path.dirname(dir);
    let files = await readdir(dir);
    if (pattern)
        files = files.filter(file => minimatch(file, pattern));
    files.sort((a, b) => statSync(path.join(dir, b)).mtime.getTime() - statSync(path.join(dir, a)).mtime.getTime());
    for (const file of files) {
        const fullpath = path.join(dir, file);
        const stat = statSync(fullpath);
        if (stat.isFile()) {
            const content = await readFile(fullpath, "utf-8");
            const obj = tryParseJson(content) as T;
            if (obj && callback(obj))
                return { file: fullpath, obj };
        }
    }
    return {};
}
