import { promises as fs } from "fs";

import {
    openPage,
    openPageHtml,
    Page,
    OpenPageOptions,
    OpenPageResult
}
from "./index.js";

export async function tryLoadContent(url: string): Promise<Partial<{ html: string, message: string }>> {
    try {
        const html = await fs.readFile(url, "utf-8");
        return { html };
    }
    catch (err) {
        return { message: err instanceof Error ? err.message : JSON.stringify(err) }
    }
}

export interface TryOpenPageResult extends Partial<OpenPageResult> {
    message?: string;
    ok: boolean;
}

export async function tryOpenPage(url: string, options: OpenPageOptions): Promise<Partial<TryOpenPageResult>> {
    try {
        if (url.startsWith("https://")) {
            const result = await openPage({ url, ...options });
            return { ...result, ok: true };
        }
        else {
            const { html } = await tryLoadContent(url);
            if (html) {
                const result = await openPageHtml(html, options);
                return { ...result, ok: true };
            }
        }
        return { message: "Unable to open page", ok: false };
    }
    catch (err) {
        return {
            message: err instanceof Error ? err.message : JSON.stringify(err),
            ok: false
        };
    }
}
