import * as async from "async-parallel";
import { promises as fs } from "fs";
import makeDir from "make-dir";

import {
    fileExists,
    removeBaseHtml,
    sanitizeHtml
} from "./index.js";

const temp_dir = process.env.TEMP_DIR || "tmp";

async function downloadFileToCache(url: string, filter?: (content: string, url: string) => string, extension?: string, concurrency = 10): Promise<string> {
    const filename = cacheKey(url, extension);
    const filepath = `${temp_dir}/${filename}`;
    const exists = await fileExists(filepath);
    if (!exists) {
        const response = await fetch(url);
        if (!response.ok)
            throw new Error(`Error downloading ${url} (status=${response.status})`);
        let text = await response.text();
        if (filter)
            text = filter(text, url);
        await fs.writeFile(filepath, text);
    }
    return filepath;
}

export async function downloadFilesToCache(urls: string[], filter?: (content: string, url: string) => string, progress?: (result: Record<string, string>, errors: string[]) => void, concurrency = 10): Promise<[Record<string, string>, string[]]> {
    await makeDir(temp_dir);
    const result: Record<string, string> = {};
    const errors: string[] = [];
    await async.each(
        urls,
        async url => {
            try {
                result[url] = await downloadFileToCache(url, filter);
            }
            catch (err) {
                errors.push(err instanceof Error ? err.message : JSON.stringify(err));
            }
            if (progress)
                progress(result, errors);
        },
        concurrency);
    return [result, errors];
}

export async function downloadHtmlFilesToCache(tuples: Array<[string, string]>, progress?: (result: Record<string, string>, errors: string[]) => void, concurrency = 10): Promise<[Record<string, string>, string[]]> {
    await makeDir(temp_dir);
    const result: Record<string, string> = {};
    const errors: string[] = [];
    await async.each(
        tuples,
        async tuple => {
            try {
                result[tuple[0]] = await downloadFileToCache(tuple[0], html => reformatHtml(html, tuple[1]), '.html');
            }
            catch (err) {
                errors.push(err instanceof Error ? err.message : JSON.stringify(err));
            }
            if (progress)
                progress(result, errors);
        },
        concurrency);
    return [result, errors];
}

export function cacheKey(url: string, extension = ""): string {
    if (!extension)
        [, extension] = /\.([a-z]{3,4})$/.exec(url) || ["", ""];
    if (extension && !extension.startsWith("."))
        extension = "." + extension;
    const [, uuid] = /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/.exec(url) || [];
    let result = "";
    if (uuid)
        result = uuid;
    else
        result = url
            .slice(0, extension ? -1 * extension.length : undefined)
            .replace(/^https?:\/\//, "")
            .replace(/[^a-z0-9]+/gi, "-")
            .replace(/-$/, "")
            .replace(/-{2,}/g, "-");
    return extension ? `${result}${extension}` : result;
}

export function reformatHtml(html: string, url: string): string {
    let result = sanitizeHtml(html);
    result = removeBaseHtml(html);
    const i = result.indexOf("<head>");
    if (i >= 0) {
        const left = result.slice(0, i + 6);
        const right = result.slice(i + 6).trim();
        const middle = `\n<base href="${new URL(url).origin}">\n`;
        result = left + middle + right;
    }
    return result;
}
