import * as fs from "fs";
import * as path from "path";
import * as stream from "node:stream";
import { Storage } from "@google-cloud/storage";
import chalk from "chalk";

import {
    bigquery,
    datauriToPng,
    downloadFile,
    dumpConsoleLog,
    escapeHtmlString,
    parseDomainName,
    renderScreenshot,
    summarize,
    <PERSON><PERSON><PERSON>R<PERSON>ult,
    Page,
    RenderScreenshotOptions
}
from "./index.js";

const storage = new Storage({ retryOptions: {
    autoRetry: true,
    maxRetries: 10,
    totalTimeout: 120000
} });

export interface DownloadHtmlResult {
    html: string;
    url: string;
}

export async function downloadHtml(capture_id: string): Promise<DownloadHtmlResult> {
    const url = `http://storage.googleapis.com/ps-colossus/${capture_id}/1.html`;
    const html = await downloadFile(url);
    return { html, url };
}

export async function tryDownloadHtml(capture_id: string): Promise<{ html?: string, url?: string, error?: any, ok: boolean }> {
    try {
        const result = await downloadHtml(capture_id);
        return { ...result, ok: true };
    }
    catch (err) {
        return { ok: false, error: err };
    }
}

export async function renderScreenshotToStorageFile(file: string, page: Page, options?: RenderScreenshotOptions): Promise<string | undefined> {
    try {
        const { datauri } = await renderScreenshot(page, options);
        const data = datauriToPng(datauri);
        await storage.bucket("ps-syphonx").file(file).save(data, { contentType: "image/png" });
        return `http://storage.googleapis.com/ps-syphonx/${file}`;
    }
    catch (err) {
        console.log(chalk.yellow(`Failed to render screenshot for ${page.url()} ${err instanceof Error ? err.message : JSON.stringify(err)}`));
    }
}

export function storagePathFromUrl(url: string): string {
    const i = url.indexOf("ps-syphonx/");
    return i > 0 ? url.slice(i + 11) : url;
}

export async function downloadStorageFile(file: string): Promise<Buffer> {
    file = storagePathFromUrl(file);
    const [buffer] = await storage.bucket("ps-syphonx").file(file).download();
    return buffer;
}

export async function saveToStorageFile(file: string, data: string | Buffer, contentType = contentTypeFromFilename(file)): Promise<string> {
    file = storagePathFromUrl(file);
    await storage.bucket("ps-syphonx").file(file).save(data, { contentType });
    return `http://storage.googleapis.com/ps-syphonx/${file}`;
}

export function createRemoteLogStream(file: string, options?: WriteLogStreamPreambleOptions): { stream: stream.Writable, url: string } {
    const { deferred = true } = options || {};
    if (process.env.VERBOSE)
        console.log(chalk.gray(`Creating log stream ${chalk.italic(deferred ? " (deferred)" : "")}... ${file}`));
    
    const url = `http://storage.googleapis.com/ps-syphonx/${file}`;
    if (!deferred) {
        const stream = storage.bucket("ps-syphonx").file(file).createWriteStream();
        if (options)
            writeLogStreamPreamble(stream, options);
        return { stream, url };
    }
    else {
        const { stream, file: __temp } = createLocalLogStream(file, options);
        Object.assign(stream, { __temp, __file: file });
        return { stream, url };
    }
}

export async function createRemoteLogFile(file: string, content: string): Promise<string> {
    await storage.bucket("ps-syphonx").file(file).save(content);
    return `http://storage.googleapis.com/ps-syphonx/${file}`;
}

export function createLocalLogStream(file: string, options?: WriteLogStreamPreambleOptions): { stream: stream.Writable, url: string, file: string, path: string } {
    file = path.resolve("tmp", file);
    if (!fs.existsSync(path.dirname(file)))
        fs.mkdirSync(path.dirname(file), { recursive: true });
    const stream = fs.createWriteStream(file);
    if (options)
        writeLogStreamPreamble(stream, options);
    const url = `file:///${file.replaceAll("\\", "/")}`;
    return { stream, url, file, path: path.dirname(file) };
}

export async function closeLogStream(s: stream.Writable, options?: WriteLogStreamPostambleOptions): Promise<void> {        
    writeLogStreamPostamble(s, options);
    await new Promise<void>(resolve => s.end(() => resolve()));
    const { __temp, __file } = s as unknown as Record<string, string>;
    if (__temp && __file) {
        const destination = storagePathFromUrl(__file);
        await storage.bucket("ps-syphonx").upload(__temp, { destination });
        //fs.rmSync(__temp);
    }
}

export interface UploadConfluxScreenshotOptions {
    file: string;
    capture_id: string;
    capture_date: Date;
}

export async function uploadConfluxScreenshot({ file, capture_id, capture_date }: UploadConfluxScreenshotOptions): Promise<void> {
    const filename = path.basename(file);
    await storage.bucket("ps-conflux").upload(file, {
        destination: `generated_screenshots/${filename}`,
        contentType: "image/png"
    });
    await bigquery.insert("conflux.generated_screenshots", { capture_id, capture_date });
    console.log(`file ${file} uploaded to ps-conflux/generated_screenshots/${filename}`);
}

export interface WriteLogStreamPreambleOptions {
    title: string;
    subtitle: string;
    id?: string;
    deferred?: boolean;
}

function createPreamble({ title, subtitle, id }: WriteLogStreamPreambleOptions): string {
    return `
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>${title}${subtitle ? /^https?:\/\//.test(subtitle) ? ` ${parseDomainName(subtitle)}` : ` ${subtitle}` : ""}${id ? ` ${id}` : ""}</title>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
        <style>
            body { font-family: sans-serif; }
            h1 { font-size: 24px; }
            h2 { font-size: 16px; }
            h3 { font-size: 16px; }
            a { text-decoration: none; }
            a:hover { text-decoration: underline; }
            section { margin-top: 1em; }
            table, th, td { border: 1px solid #aaa; border-collapse: collapse; }
            thead, th { background-color: #eee; font-weight: bold; }
            td { padding: 4px; vertical-align: top; word-wrap: break-word; }
            section { margin-top: 1em; }
            summary { cursor: pointer; }
            summary > small { font-weight: normal; }
            details { margin-top: 1em; }
            details > summary { font-weight: bold; }
            details > :not(:first-child) { margin-left: 1em; }
            code { font-family: monospace; font-size: 14px; white-space: pre; background-color: #eee; }
            pre del { color: red; }
            .d-block { display: block; }
            .title { font-weight: bold; }
            .success { color: green; background-color: #e6ffe6; }
            .error { color: red; background-color: #ffebe6; }
            .warning { color: black; background-color: #FFFF99; }
            .info { color: gray; }
            .stat { color: gray; }
        </style>
    </head>
    <body>
        <div style="position: absolute; top: 10px; right: 140px;">
            <i class="fa fa-user"></i>
            <input id="user" type="text" placeholder="Enter user name" style="width: 120px;">
        </div>

        ${title ? `<h1>${title}</h1>` : ""}
        ${subtitle ? /^https?:\/\//.test(subtitle) ? `<p><a href="${subtitle}" target="_blank">${subtitle}</a></p>` : `<p>${subtitle}</p>` : ""}
        <p>${new Intl.DateTimeFormat("en-US", { dateStyle: "full", timeStyle: "full" }).format(new Date())}</p>
        ${id ? `<p>ID: ${id}</p>`: ""}\n`.trimStart();
}

export function writeLogStreamPreamble(s: stream.Writable, { title, subtitle, id }: WriteLogStreamPreambleOptions): void {
    const preamble = createPreamble({ title, subtitle, id });
    s.write(preamble);
}

export interface WriteLogStreamPostambleOptions {
    analyze?: AnalyzeResult[];
    console?: boolean;
    voting?: boolean;
}

export function writeLogStreamPostamble(s: stream.Writable, { analyze, console, voting }: WriteLogStreamPostambleOptions = {}): void {
    if (analyze && analyze.length > 0) {
        const { summary, breakdown } = summarize(analyze);        
        s.write(`
        <details>
            <summary>USAGE SUMMARY <small>${summary}</small></summary>
            <table>
                <thead>
                    <tr>
                        <td>Model</td>
                        <td>Tokens</td>
                        <td>Cost</td>
                        <td>Elapsed</td>
                    </tr>
                </thead>
                <tbody>
                ${breakdown.map(obj => `
                    <tr>
                        <td>${obj.model}</td>
                        <td>${obj.tokens.toLocaleString()}</td>
                        <td>$${obj.cost.toFixed(6)}</td>
                        <td>${Math.round(obj.elapsed / 1000)}s</td>
                    </tr>`).join("\n")}
                </tbody>
            </table>
        </details>\n`);
    }

    if (console) {
        const output = dumpConsoleLog({
            timestamp: new Intl.DateTimeFormat("en-US", {
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
                fractionalSecondDigits: 3,
                hour12: false
            })
        });
        s.write(`<details><summary>CONSOLE OUTPUT</summary><pre>${escapeHtmlString(output)}</pre></details>\n`);
    }

    if (voting)
        s.write(`
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>
        <script src="../scripts/voting.js"></script>\n`);

    s.write(`\n</body>\n</html>`);
}

function contentTypeFromFilename(file: string): string {
    if (file.endsWith(".html"))
        return "text/html";
    if (file.endsWith(".png"))
        return "image/png";
    if (file.endsWith(".jpg") || file.endsWith(".jpeg"))
        return "image/jpeg";
    if (file.endsWith(".gif"))
        return "image/gif";
    if (file.endsWith(".pdf"))
        return "application/pdf";
    if (file.endsWith(".json"))
        return "application/json";
    if (file.endsWith(".txt"))
        return "text/plain";
    return "application/octet-stream";
}
