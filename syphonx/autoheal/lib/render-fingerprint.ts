import { Page } from "./virtual-browser/index.js";
import { redact as redactScript } from "./scripts/redact.js";
import { compositeImage } from "./image/index.js";
import { bufferToDatauri } from "./utilities.js";
import <PERSON><PERSON> from "jimp";

export async function renderFingerprint(page: Page): Promise<[string, string]> {
    const screenshot_image = await page.screenshot();
    await page.evaluate(redactScript);

    const redacted_image = await page.screenshot();
    const composite_image = await compositeImage(screenshot_image, redacted_image, { width: 250, right: 24, bottom: 24, border: 4 });
    const debug_image = await bufferToDatauri(composite_image);

    const binary_value = await imageToBinary(redacted_image, 224);
    const fingerprint = binaryToHex(binary_value);

    return [fingerprint, debug_image];
}

async function imageToBinary(buffer: Buffer, threshold: number): Promise<string> {
    const image = await Jimp.read(buffer);
    image.resize(8, 8).greyscale();
    let result = '';
    for (let y = 0; y < 8; y++) {
        for (let x = 0; x < 8; x++) {
            const pixelColor = image.getPixelColor(x, y);
            const grayscaleValue = Jimp.intToRGBA(pixelColor).r;
            result += grayscaleValue <= threshold ? '1' : '0';
        }
    }
    return result;
}

function binaryToHex(binary_value: string): string {
    return parseInt(binary_value.slice(0, 32), 2).toString(16).padStart(8, "0") + parseInt(binary_value.slice(32), 2).toString(16).padStart(8, "0");
}
