import * as fs from "fs";
import * as crypto from "crypto";

import { Readable } from "stream";
import { ReadableStream } from "stream/web";
import { finished } from "stream/promises";
import { diffChars, diffWords } from "diff";
import { rebaseHtml } from "./scripts/index.js";

const seconds = 1000;
const minutes = 60 * seconds;
const hours = 60 * minutes;
const days = 24 * hours;

export function arrayMoveToFront(a: string[], key: string) {
    const i = a.indexOf(key);
    if (i > 0) {
        a.splice(i, 1);
        a.unshift(key);
    }
}

export function bufferMimeType(buffer: Buffer): string | undefined {
    // Check the first few bytes to determine the file type
    if (buffer.slice(0, 2).equals(Buffer.from([0xFF, 0xD8])))
        return 'image/jpeg'; // JPEG files start with FF D8
    else if (buffer.slice(0, 8).equals(Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A])))
        return 'image/png'; // PNG files start with 89 50 4E 47 0D 0A 1A 0A
    else if (buffer.slice(0, 6).equals(Buffer.from([0x47, 0x49, 0x46, 0x38, 0x39, 0x61])) ||
        buffer.slice(0, 6).equals(Buffer.from([0x47, 0x49, 0x46, 0x38, 0x37, 0x61])))
        return 'image/gif'; // GIF files start with GIF89a or GIF87a
    else if (buffer.slice(0, 4).equals(Buffer.from([0x42, 0x4D])))
        return 'image/bmp'; // BMP files start with BM
    else if (buffer.slice(0, 4).equals(Buffer.from([0x49, 0x49, 0x2A, 0x00])) ||
        buffer.slice(0, 4).equals(Buffer.from([0x4D, 0x4D, 0x00, 0x2A])))
        return 'image/tiff'; // TIFF files start with II* or MM*
}

export function bufferToDatauri(buffer: Buffer): string {
    return `data:${bufferMimeType(buffer)};base64,${buffer.toString("base64")}`;
}

export function clone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
}

export function coerceValueToString(value: unknown): string {
    if (!value)
        return "";
    else if (typeof value === "string")
        return value;
    else if (typeof value === "number")
        return value.toString();
    else if (typeof value === "boolean")
        return value.toString();
    else if (Array.isArray(value) && value.every(value => typeof value === "string"))
        return value.join("\n");
    else if (Array.isArray(value))
        return value.map(coerceValueToString).join("\n");
    else if (typeof value === "object")
        return JSON.stringify(value);
    else
        return String(value);
}

export interface CollapseWhiteSpaceOptions {
    singleSpaced?: boolean;
    singleLine?: boolean;
    collapseSpaces?: boolean;
    unindent?: boolean;
}

export function collapseWhitespace(text: string, { singleSpaced = false, singleLine = false, collapseSpaces = true, unindent = false }: CollapseWhiteSpaceOptions = {}): string {
    if (typeof text === "string") {
        text = text
            .replace(/\r\n/g, "\n") // Reformat windows crlf to just a newline
            .replace(/\n(?:[\t ]*\n)+/g, "\n\n") // Collapse lines that only contain whitespace between newlines
            .replace(/^\s+|\s+$/g, '') // Trim leading and trailing whitespace
            .replace(/\n{3,}/g, "\n\n"); // Collapse 3+ newlines to two newlines
        if (singleSpaced)
            text = text.replace(/\n{2,}/g, "\n");
        if (singleLine)
            text = text.replace(/\n/g, " ");
        if (collapseSpaces)
            text = text.replace(/[ \t]{2,}/g, " ");
        if (unindent)
            text = text.replace(/^[ \t]+/gm, "");
        text = text.trim();
    }
    return text;
}

export function compareStringArrays(a: string[], b: string[]): { left: string[], right: string[], both: string[] } {
    const left = a.filter(x => !b.includes(x));
    const right = b.filter(x => !a.includes(x));
    const both = a.filter(x => b.includes(x));
    return { left, right, both };
}

export function createUrlParams(obj: {} | undefined): string {
    const params = [];
    if (obj) {
        const keys = Object.keys(obj);
        for (const key of keys) {
            let value = (obj as Record<string, unknown>)[key];
            if (typeof value === "string")
                value = encodeURIComponent(value);
            else if (typeof value === "number")
                value = value.toString();
            else if (typeof value === "boolean")
                value = value.toString();
            else if (Array.isArray(value) && value.every(value => typeof value === "string"))
                value = value.join(",");
            else if (value === null)
                value = null;
            else
                value = undefined;
            if (value !== undefined)
                params.push(value !== null ? `${key}=${value}` : key);
        }
    }
    return params.length > 0 ? `?${params.join("&")}` : "";
}

export function datauriToPng(datauri: string): Buffer {
    const [prefix, data] = datauri.split(",");
    if (prefix !== "data:image/png;base64")
        throw new Error("Invalid datauri format");
    const buffer = Buffer.from(data, "base64");
    return buffer;
}

export function diffDays(date1?: Date, date2?: Date): number {
    if (!date1)
        return 0;

    if (!date2) {
        date2 = date1;
        date1 = new Date();
    }

    const diff = Math.abs(date1.getTime() - date2.getTime());
    return Math.round(diff / days);
}

/**
 * Calculates the difference between two dates and returns a string representing
 * the difference in the most appropriate unit (days, hours, or minutes).
 * 
 * @param {Date} date1 The first date.
 * @param {Date} date2 The second date. If not specified then the first date is compared to the current date.
 * @returns {string} A string representing the difference in a concise format (e.g., "2d", "3h", "15m").
 */
export function diffDate(date1?: Date, date2?: Date): string {
    if (!date1)
        return "0s";

    if (!date2) {
        date2 = date1;
        date1 = new Date();
    }

    const diff = Math.abs(date1.getTime() - date2.getTime());
    if (diff >= days)
        return Math.round(diff / days) + 'd';
    else if (diff >= hours)
        return Math.round(diff / hours) + 'h';
    else if (diff >= minutes)
        return Math.round(diff / minutes) + 'm';
    else
        return Math.round(diff / seconds) + 's';
}

export function diffText(a: string, b: string, mode: "word" | "char" = "word"): string {
    let html = "";
    if (a && b) {
        const diff = mode === "char" ? diffChars(a, b) : diffWords(a, b);
        if (a && b)
            for (const part of diff)
                if (part.added)
                    html += `<ins>${escapeHtmlString(part.value)}</ins>`;
                else if (part.removed)
                    html += `<del>${escapeHtmlString(part.value)}</del>`;
                else
                    html += escapeHtmlString(part.value);
    }
    return html;
}

export class HttpError extends Error {
    url: string;
    status: number;
    constructor(url: string, status: number) {
        super(`HTTP request error: status=${status}, url=${url}`);
        this.url = url;
        this.status = status;
    }
}

export class ErrorCode extends Error {
    code: string;
    constructor(code: string, message: string) {
        super(message);
        this.code = code;
    }
}

export async function downloadFile(url: string): Promise<string> {
    try {
        const request = await fetch(url);
        if (request.ok) {
            const text = await request.text();
            return text;
        }
        else {
            throw new HttpError(url, request.status);
        }
    }
    catch (err) {
        if (err instanceof HttpError)
            throw err;
        else
            throw new Error(`Error downloading ${url}: ${err instanceof Error ? err.message : JSON.stringify(err)}`);
    }
}

export function escapeHtmlString(html: string | undefined): string {
    return html ? html.toString().replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;") : "";
}

export function escapeRegexpString(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

export async function fetchHtml(url: string): Promise<string> {
    try {
        const request = await fetch(url);
        if (!request.ok)
            throw new HttpError(url, request.status);

        const contentType = request.headers.get("content-type");
        if (!contentType?.startsWith("text/html"))
            throw new ErrorCode("invalid-content-type", `Unexpected content-type "${contentType}"`);

        const text = await request.text();
        return text;
    }
    catch (err) {
        if (err instanceof HttpError)
            throw err;
        else if (err instanceof ErrorCode)
            throw err;
        else
            throw new Error(`Error downloading ${url}: ${err instanceof Error ? err.message : JSON.stringify(err)}`);
    }
}

export async function downloadContent(content_url: string, original_url: string): Promise<string> {
    const html = await downloadFile(content_url);
    const origin = new URL(original_url).origin;
    return rebaseHtml(html, origin);
}

export interface DownloadContentResult {
    content?: string;
    error?: string;
    ok: boolean;
}

export async function tryDownloadContent(content_url: string, original_url: string): Promise<DownloadContentResult> {
    try {
        const content = await downloadContent(content_url, original_url);
        return { content, ok: true };
    }
    catch (err) {
        return {
            error: err instanceof Error ? err.message : JSON.stringify(err),
            ok: false
        };
    }
}

export function md5hash(value: string): string {
    return crypto.createHash("md5").update(value).digest("hex");
}

export function replaceAllIgnoreCase(text: string, search: string, replacement: string): string {
    if (!search)
        return text;

    const lowerText = text.toLowerCase();
    const lowerSearch = search.toLowerCase();

    let result = "";
    let startIndex = 0;
    let index = lowerText.indexOf(lowerSearch, startIndex);

    while (index !== -1) {
        result += text.substring(startIndex, index); // append the portion of 'str' from current startIndex up to the match
        result += replacement; // append the replacement
        startIndex = index + search.length; // move startIndex to the position right after the found match
        index = lowerText.indexOf(lowerSearch, startIndex); // look for the next case-insensitive match
    }

    result += text.substring(startIndex); // append the remainder of 'str' (after the last match)
    return result;
}

export interface TryDownloadFileResult {
    ok: boolean;
    content?: string;
    error_code?: string;
    error?: string;
}

export async function tryDownloadFile(url: string): Promise<TryDownloadFileResult> {
    try {
        const content = await downloadFile(url);
        return { ok: true, content };

    }
    catch (err) {
        return { ok: false, error: err instanceof Error ? err.message : JSON.stringify(err) };
    }
}

export async function tryFetchHtml(url: string): Promise<TryDownloadFileResult> {
    try {
        const content = await fetchHtml(url);
        return { ok: true, content };
    }
    catch (err) {
        if (err instanceof ErrorCode)
            return { ok: false, error_code: err.code, error: err.message };
        else if (err instanceof Error)
            return { ok: false, error: err.message };
        else
            return { ok: false, error: JSON.stringify(err) };
    }
}

export async function downloadToFile(url: string, file: string): Promise<void> {
    const response = await fetch(url);
    if (!response.ok)
        throw new Error(`Error downloading ${url} (status=${response.status})`);
    const stream = fs.createWriteStream(file);
    if (!response.body)
        throw new Error(`Error downloading ${url} (empty response body)`);
    await finished(Readable.fromWeb(response.body as ReadableStream).pipe(stream));
}

export function generateTimestamp(mode: "minutes" | "seconds" | "milliseconds"): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");
    const milliseconds = String(now.getMilliseconds()).padStart(3, "0");
    if (mode === "minutes")
        return `${year}${month}${day}${hours}${minutes}`;
    else if (mode === "seconds")
        return `${year}${month}${day}${hours}${minutes}${seconds}`;
    else
        return `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
}

export function isEmpty(obj: unknown): boolean {
    if (obj === null || obj === undefined)
        return true;
    else if (typeof obj === "string")
        return obj.trim().length === 0;
    else if (Array.isArray(obj))
        return obj.filter(value => !isEmpty(value)).length === 0;
    else if (typeof obj === "object")
        return Object.keys(obj as object).length === 0;
    else
        return false;
}

export function objIncrement(obj: Record<string, number>, key: string | undefined): number {
    if (!key)
        return 0;
    if (!obj[key])
        obj[key] = 0;
    obj[key] += 1;
    return obj[key];
}

export function objLookup(obj: Record<string, number>, value: number): string | undefined {
    for (const [key, val] of Object.entries(obj))
        if (val === value)
            return key;
}

export function pick<T extends {}, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
    const result = {} as Pick<T, K>;
    for (const key of keys) {
        if (key in obj) {
            result[key] = obj[key];
        }
    }
    return result;
}

export function regexpExtract(regexp: RegExp, text: string): string | undefined {
    const result = regexp.exec(text) || [];
    return result[1];
}

export function removeEmptyProps<T extends {}>(obj: T): T {
    const result: Record<string, unknown> = {};
    const keys = typeof obj === "object" && obj !== null ? Object.keys(obj) : [];
    for (const key of keys) {
        const value = (obj as Record<string, unknown>)[key];
        if (!isEmpty(value))
            result[key] = value;
    }
    return result as T;
}

export function round(value: number, n: number): number {
    return parseFloat(value.toFixed(n));
}

export function shuffle<T>(input: T[]): T[] {
    const a = input.slice();
    for (let i = a.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [a[i], a[j]] = [a[j], a[i]];
    }
    return a;
}

export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

type NumericKeys<T> = { [K in keyof T]: T[K] extends number ? K : never }[keyof T];
export function sum<T, K extends NumericKeys<T>>(a: T[], key: K): number {
    return a.reduce((result, obj) => result + (obj[key] as number), 0);
}

export function truncate(text: string | undefined | null, max: number, split = false): string {
    if (text && text.length > max) {
        if (!split) {
            text = text.slice(0, max);
            const i = text.lastIndexOf(" ");
            if (text.length - i > 0.15 * max)
                text = text.slice(0, i).trim();
            text += " ...";
        }
        else {
            const m = Math.floor(max / 2);
            const ix = text.slice(0, m).lastIndexOf(" ");
            const i = ix > (0.7 * m) ? Math.min(ix, m) : m;
            const jx = text.slice(-m).indexOf(" " + 1);
            const j = jx > (0.7 * m) ? Math.min(m - jx, m) : m;
            text = text.slice(0, i).trim() + " ... " + text.slice(-j).trim();
        }
    }
    return text || "";
}

export function utcDateFrom(days: number): Date {
    const date = new Date();
    date.setUTCDate(date.getUTCDate() - days);
    date.setUTCHours(0, 0, 0, 0);
    return date;
}

export function vectorCosineSimilarity(a: number[], b: number[]): number {
    let dp = 0;
    let ma = 0;
    let mb = 0;
    for (let i = 0; i < a.length; i++) {
        dp += a[i] * b[i];
        ma += a[i] * a[i];
        mb += b[i] * b[i];
    }
    ma = Math.sqrt(ma);
    mb = Math.sqrt(mb);
    return dp / (ma * mb);
}
