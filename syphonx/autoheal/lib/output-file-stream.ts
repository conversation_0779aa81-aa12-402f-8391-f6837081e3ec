import * as path from "path";
import { createWriteStream, WriteStream } from "fs";
import { closeStream } from "./index.js";

export class OutputFileStream {
    file: string;
    stream: WriteStream;
    size: number;
    keys: string[];

    constructor(file: string) {
        this.file = file;
        this.stream = createWriteStream(file, { flags: "w" });
        this.size = 0;
        this.keys = [];
    }

    writeLine(text: string, key?: string): void {
        this.stream.write(text + "\n");
        this.size += text.length + 1;
        if (key)
            this.keys.push(key);
    }

    async close(): Promise<void> {
        await closeStream(this.stream);
    }
}
