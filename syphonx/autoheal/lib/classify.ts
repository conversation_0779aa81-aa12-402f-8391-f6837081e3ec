import * as stream from "node:stream";

import {
    classifyPage,
    createRemoteLogStream,
    summarize,
    ClassifyPageContext,
    ClassifyPageResult,
    Page
}
from "./index.js";

import {
    bigquery,
    createThumbnailImage,
    LLMModel
}
from "./index.js";

export interface ClassifyScreenshotOptions {
    url: string;
    context: ClassifyPageContext;
    model: LLMModel,
    report_path?: string;
}

export interface ClassifyLivePageResult extends Partial<ClassifyPageResult> {
    report_url?: string;
    cached?: boolean;
    message?: string;
    datauri?: string;
    ok: boolean;
}

export interface ClassifyLivePageOptions {
    page: Page;
    context: ClassifyPageContext;
    model?: LLMModel;
    thumbnail_size?: number;
    report_path?: string;
    stream?: stream.Writable;
}

interface CacheRow {
    screenshot_url: string;
    screenshot_class: string;
    explain: string;
    report_url: string;
}

let cache: CacheRow[] | undefined = undefined;

export async function classifyScreenshotUrl({ url, context, report_path }: ClassifyScreenshotOptions): Promise<ClassifyLivePageResult> {
    if (!cache)
        cache = await bigquery.query("SELECT screenshot_url, screenshot_class, explain, log_url FROM syphonx.screenshots WHERE timestamp >= DATE_SUB(CURRENT_TIMESTAMP(), INTERVAL 3 DAY)");

    const cached = cache.find(row => row.screenshot_url === url);
    if (cached)
        return {
            page_class: cached.screenshot_class as PageClassification,
            explain: cached.explain,
            report_url: cached.report_url,
            cached: true,
            ok: true
        };

    const datauri = await createThumbnailImage({ url, size: 512 });
    if (!datauri)
        return { ok: false, message: `Invalid url ${url}` };

    const { stream, url: report_url } = report_path ? createRemoteLogStream(report_path) : { stream: undefined, url: undefined };
    const classify_result = await classifyPage({ datauri, context, stream });
    if (stream)
        await new Promise<void>(resolve => stream.end(() => resolve()));

    const { tokens, cost } = summarize(classify_result.analyze);
    await bigquery.insert("syphonx.screenshots", {
        timestamp: new Date(),
        domain_name: context.domain_name,
        screenshot_url: url,
        screenshot_class: classify_result.page_class,
        explain: classify_result.explain,
        report_url,
        usage: tokens,
        cost
    });

    return {
        ...classify_result,
        report_url,
        cached: false,
        ok: true
    };
}

export async function classifyLivePage({ page, context, model, report_path, stream, thumbnail_size = 512 }: ClassifyLivePageOptions): Promise<ClassifyLivePageResult> {
    const datauri = await createThumbnailImage({ page, size: thumbnail_size });
    if (!datauri)
        return { ok: false, message: "Failed to create thumbnail image" };

    context.title = await page.evaluate(() => document.querySelector("title")?.textContent);

    if (stream) {
        const classify_result = await classifyPage({ datauri, context, model, stream });
        return { ...classify_result, datauri };
    }
    else {
        const { stream, url: report_url } = report_path ? createRemoteLogStream(report_path) : { stream: undefined, url: undefined };
        const classify_result = await classifyPage({ datauri, context, stream });
        if (stream)
            await new Promise<void>(resolve => stream.end(() => resolve()));
        return { ...classify_result, report_url, datauri };
    }    
}
