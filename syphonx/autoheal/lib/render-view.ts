import { promises as fs } from "fs";
import { fileURLToPath } from "url";
import { collapseWhitespace } from "./utilities.js";
import he from "he";
import nunjucks from "nunjucks";

export const viewsDir = fileURLToPath(new URL("../views", import.meta.url));
export const promptsDir = fileURLToPath(new URL("../prompts", import.meta.url));

const env = nunjucks.configure([viewsDir, promptsDir], { autoescape: true });
env.addGlobal("pretty_json", pretty_json);
env.addGlobal("starts_with", starts_with);

export function renderJinjaTemplateFile(view: string, context?: object): string {
    let result = nunjucks.render(view, context);
    result = he.decode(result);
    result = collapseWhitespace(result);
    return result;
}

export function renderJinjaTemplate(text: string, context: object): string {
    let result = nunjucks.renderString(text, context);
    result = he.decode(result);
    result = collapseWhitespace(result);
    return result;
}

export function tryRenderView(view: string, context?: object): string | undefined {
    try {
        return nunjucks.render(view, context);
    }
    catch (err) {
        console.error(`ERROR rendering template view "${view}": ${err instanceof Error ? err.message : ""}`);
    }
}

export async function renderViewToFile(view: string, context?: object, file = view): Promise<string> {
    const output = renderJinjaTemplateFile(view, context);
    await fs.writeFile(file, output, "utf-8");
    return file;
}

function pretty_json(obj: unknown): string {
    try {
        return JSON.stringify(obj, null, 2);
    }
    catch (err) {
        return "";
    }
}

function starts_with(text: string, subtext: string): boolean {
    const result = typeof text === "string" && typeof subtext === "string" ? text.startsWith(subtext) : false;
    return result;
}
