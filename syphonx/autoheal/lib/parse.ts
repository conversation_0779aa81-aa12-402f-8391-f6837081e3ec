export function parseDomainName(url: string): string {
    try {
        const hostname = new URL(url).hostname;
        return hostname.replace(/^www\./, "");
    }
    catch (err) {
        return "";
    }
}

export function parseKeyValues(text: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = text.split("\n")
        .map(line => line.trim())
        .map(line => line.startsWith("- ") ? line.slice(2) : line)
        .filter(Boolean)
        .filter(line => line.includes(":"));
    for (const line of lines) {
        const i = line.indexOf(":");
        const key = line.slice(0, i).trim().toLowerCase().replaceAll(" ", "_");
        const value = line.slice(i + 1).trim();
        if (value && value.trim().toLowerCase() !== "n/a")
            result[key] = value;
    }
    return result;
}

export function parseNumber(value: unknown): number | undefined {
    if (typeof value === "number")
        return !isNaN(value) ? value : undefined;

    if (typeof value === "string") {
        let [, text] = /([0-9.,]+)/.exec(value) || [];
        if (/\.\d+$/.test(text))
            text = text.replace(/,/g, "");
        if (/,\d+$/.test(text))
            text = text.replace(/\./g, "");
        const result = parseFloat(text);
        return !isNaN(result) ? result : undefined;
    }

    return undefined;
}

export function parseYesNo(value: unknown, defaultValue = false): boolean {
    if(value === undefined)
        return defaultValue || false;
    if (typeof value === "boolean")
        return value;
    if (typeof value === "number")
        return value !== 0;
    if (typeof value === "string") {
        const text = value.trim().toLowerCase();
        if (["yes", "true", "1"].includes(text))
            return true;
        if (["no", "false", "0"].includes(text))
            return false;
    }
    return false;
}