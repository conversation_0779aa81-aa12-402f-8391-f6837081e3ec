import * as dotenv from "dotenv";
dotenv.config();

import * as fs from "fs";
import chalk from "chalk";

import {
    llm_models,
    parseArgs,
    LLMChat,
    LLMModel
} from "./lib/index.js";

const args = parseArgs({
    required: {
        model: llm_models.join(", ")
    },
    optional: {
        0: "file containing prompt",
        prompt: "specify prompt"
    },
    validate: args => {
        if (args[0] && args.prompt)
            return "Specify either a file or --prompt, not both";
    }
});

const model = args.model as LLMModel;
const input = fs.readFileSync(args[0], "utf-8");

const chat = new LLMChat(model);
const output = await chat.prompt(input);

console.log(output);
console.log();
console.log(chalk.gray(`${Math.round(chat.elapsed / 1000)} seconds, ${chat.tokens} tokens ($${chat.cost.toFixed(4)})`));
