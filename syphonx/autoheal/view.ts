import * as dotenv from "dotenv";
dotenv.config();

import { promises as fs } from "fs";
import * as async from "async-parallel";
import * as syphonx from "syphonx-lib";
import { flattenTemplateSelect, Select } from "syphonx-lib";
import { SingleBar, Presets } from "cli-progress";
import { diffWords } from "diff";
import open from "open";

import {
    cacheKey,
    copyFiles,
    downloadHtmlCaptures,
    formatJson,
    loadTemplate,
    parseArgs,
    queryRegressions,
    renderHtml,
    sum,
    tryLoadContent,
    tryLoadJsonFile,
    tryRenderView,
    uploadConfluxScreenshot,
    viewsDir,
    QueryRegressionsResult
}
from "./lib/index.js";

const params = parseArgs({
    optional: {
        app: "The PriceSpider product (brand_monitor, wtb, prowl)",
        account: "account key filter",
        country: "country code filter",
        seller: "seller id filter",
        target: "target filter, comma seperated",
        engine: "capture engine (conflux, syphonx, puppeteer), default=all",
        sku: "a regex filter reducing the result set to a set of skus",
        max: "max number of pages to view (default=500)",
        concurrency: "number of analyze instances to run concurrently (default=1)",
        verbose: "verbose log output"
    }
});
params.selector = params.target; //hack

const concurrency = parseInt(params.concurrency) || parseInt(process.env.CONCURRENCY || "1");
const temp_dir = process.env.TEMP_DIR || "tmp";

if (!params.app)
    params.app = "brand_monitor";

if (params.verbose)
    process.env.VERBOSE = "1";

interface QueryRegressionsResultEx extends QueryRegressionsResult {
    status: string;
    predicted: boolean;
    href: string;
}

const rows = await queryRegressions({
    app_name: params.app,
    account_key: params.account,
    country_code: params.country,
    seller_id: parseInt(params.seller),
    selector_name: params.target?.split(","),
    engine: params.engine,
    max: parseInt(params.max) || 10,
    sku: params.sku
}) as QueryRegressionsResultEx[];

console.log(`${rows.length} pages, ${sum(rows, "regression_count")} regressions`);
console.log();
if (rows.length === 0)
    process.exit(0);

let select: syphonx.Select[] | undefined = undefined;
const config = tryLoadJsonFile("./view.jsonc") as Select[];
if (config && config.length > 0) {
    console.log(`view.jsonc loaded: ${config.map(obj => obj.name).join(", ")}`);
    const template_paths = Array.from(new Set(rows.filter(row => /pricespider\//.test(row.template_path)).map(row => row.template_path)));
    if (template_paths.length > 1) {
        console.error("cannot apply view.jsonc: multiple templates found");
        process.exit(1);
    }
    else if (template_paths.length === 0) {
        console.error("cannot apply view.jsonc: no template found");
        process.exit(1);
    }
    const { template } = await loadTemplate(template_paths[0]);
    select = flattenTemplateSelect(template.actions);
    for (const config_obj of config) {
        const select_obj = select.find(obj => obj.name === config_obj.name);
        if (select_obj && select_obj.query && config_obj.query)
            select_obj.query = select_obj.query.concat(config_obj.query);
        else if (select_obj)
            select_obj.query = config_obj.query;
        else
            select.push(config_obj);
    }
}

const html_cache = await downloadHtmlCaptures(rows.map(row => ({ capture_url: row.capture_url, html_url: row.last_capture.html_url })));

const rowsWithoutScreenshots = rows.filter(row => !row.last_capture.screenshot_url);
if (rowsWithoutScreenshots.length > 0) {
    console.log();
    console.log("generating screenshots...");
    const progress = new SingleBar({}, Presets.shades_classic);
    progress.start(rowsWithoutScreenshots.length, 0);
    await async.map(rowsWithoutScreenshots, async row => {
        row.last_capture.screenshot_url = `${row.last_capture.capture_id}.png`;
        if (html_cache[row.last_capture.html_url]) {
            const html = await fs.readFile(html_cache[row.last_capture.html_url], "utf-8");
            const file = `${temp_dir}/${row.last_capture.screenshot_url}`;
            await renderHtml({ html, file });
            await uploadConfluxScreenshot({
                file,
                capture_id: row.last_capture.capture_id,
                capture_date: row.last_capture.capture_date
            });
            progress.increment();
        }
    }, concurrency);
    progress.stop();
    console.log(`${rowsWithoutScreenshots.length} screenshots generated`);
}

console.log();
console.log("generating output...");
const progress = new SingleBar({}, Presets.shades_classic);
progress.start(rows.length + 1, 0);
const errors: string[] = [];
await async.each(rows, async row => {
    try {
        if (select) {
            const { html } = await tryLoadContent(html_cache[row.last_capture.html_url]);
            if (html) {
                const result = syphonx.select(select, html);
                if (result.data.blocked?.value) {
                    row.status = "blocked";
                }
                else if (row.last_capture.screenshot_class === "BLOCKED") {
                    row.status = "blocked";
                    row.predicted = true;
                }
                else if (result.data.invalid?.value) {
                    row.status = "invalid";
                }
                else if (result.data.discontinued?.value) {
                    row.status = "discontinued";
                }
                else if (result.data.pnf?.value) {
                    row.status = "pnf";
                }
                else if (result.data.modal?.value) {
                    row.status = "modal";
                }
                else if (row.last_capture.screenshot_class === "MODAL") {
                    row.status = "modal";
                    row.predicted = true;
                }
                else {
                    row.status = "ok";
                }
        
                for (const selector of row.selectors) {
                    const query = select.find(obj => obj.name === selector.selector_name)?.query;
                    selector.query = query?.map(obj => syphonx.renderJQuery(obj)).filter(obj => !!obj) as string[] || [];
                    selector.query_json_value = result.data[selector.selector_name]?.value ? JSON.stringify(result.data[selector.selector_name]?.value) : "";
                    selector.query_selector_path = result.data[selector.selector_name]?.nodes || [];
                    const json_value = selector.regression ? selector.reference?.selector_json_value : selector.selector_json_value;
                    if (json_value && selector.query_json_value) {
                        const diffs = diffWords(json_value, selector.query_json_value);
                        selector.query_diff = diffs.map(diff => `<span class="${diff.added ? "diff-added" : diff.removed ? "diff-removed" : "diff-unchanged"}">${diff.value}</span>`).join("");
                        selector.query_diff_count = diffs.filter(diff => diff.added || diff.removed).length;
                    }
                    else {
                        selector.query_diff_count = 0;
                    }
                    selector.selector_json_value = formatJson(selector.selector_json_value);
                    selector.query_json_value = formatJson(selector.query_json_value);
                }
            }
        }
    
        row.href = `${cacheKey(row.page_id)}.html`;
        const content = tryRenderView("capture.html", row);
        if (content)
            await fs.writeFile(`${temp_dir}/${row.href}`, content, "utf-8");
    }
    catch (err) {
        errors.push(err instanceof Error ? err.message : JSON.stringify(err));
    }
    finally {
        progress.increment();
    }

}, concurrency);

if (errors.length> 0)
    console.error(errors.join("\n"));

const seller_names = Array.from(new Set(rows.filter(row => /pricespider\//.test(row.template_path)).map(row => row.seller_name)));
const overview = tryRenderView("capture-overview.html", {
    title: `${params.app} ${seller_names.join(", ")} (${rows.length} regressions)`,
    capture_count: rows.length,
    regression_count: sum(rows, "regression_count"),
    blocked_count: rows.filter(row => row.status === "blocked").length,
    invalid_count: rows.filter(row => row.status === "invalid").length,
    discontinued_count: rows.filter(row => row.status === "discontinued").length,
    modal_count: rows.filter(row => row.status === "modal").length,
    pnf_count: rows.filter(row => row.status === "pnf").length,
    screenshots: rows
        .map(obj => ({
            ...obj,
            regression_selector_names: obj.selectors.filter(obj => obj.regression).map(obj => obj.selector_name).join(", ")
        }))
});

progress.increment();
if (overview)
    await fs.writeFile(`${temp_dir}/_a.html`, overview, "utf-8");
await copyFiles(["main.css"], viewsDir, temp_dir, true);
progress.stop();

console.log(`report: ${temp_dir}/_a.html`);
open(`${temp_dir}/_a.html`);
process.exit(0);
