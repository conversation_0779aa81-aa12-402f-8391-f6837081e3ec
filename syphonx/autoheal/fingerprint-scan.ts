import chalk from "chalk";

import {
    bigquery,
    downloadFile,
    generateTimestamp,
    parseArgs,
    parseDomainName,
    regexpExtract,
    renderImageFingerprint,
    renderRedactedHtml,
    renderMarkdown,
    rebaseHtml,
    saveToStorageFile,
    truncate
}
from "./lib/index.js";

const args = parseArgs({
    required: {
        app: "app name",
        account: "account name"
    },
    optional: {
        country: "country filter",
        seller: "seller-id filter",
        n: "number of rows per seller (default=3)",
        preview: "preview only",
        verbose: "verbose log output"
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

const fingerprints: Record<string, string> = {};

const rows = await query({
    app_name: args.app,
    account_key: args.account,
    country_code: args.country,
    seller_id: parseInt(args.seller),
    n: parseInt(args.n) || 3
});
console.log(chalk.gray(`${rows.length} rows returned`));

if (args.preview) {
    let seller_id = 0;
    for (const row of rows) {
        if (seller_id !== row.seller_id) {
            console.log();
            console.log(chalk.underline(`${row.seller_name} ${row.seller_id}`));
            seller_id = row.seller_id;
        }
        console.log(`${row.page_id} ${chalk.gray(row.capture_url)}`);
    }
    process.exit(0);
}

const counters = {
    inserted: 0,
    errors: 0,
    consecutive_errors: 0
};

for (const row of rows) {
    try {
        console.log(`[${rows.indexOf(row)+1}/${rows.length}] ${row.page_id} ${chalk.gray(row.capture_url)}`);
        const html = await downloadFile(row.content_url);
        const result = await render(html, row.capture_url);
        //let result = await render(sanitizeHtml(html));
        //if (!result.ok || result.fingerprint?.includes("000000"))
            //result = await render(html);
    
        if (result.ok && result.fingerprint) {
            if (result.fingerprint) {
                const key = result.fingerprint;
                if (!fingerprints[key])
                    fingerprints[key] = await renderImageFingerprint(key);
            }
    
            await bigquery.insert("temp.page_fingerprints", {
                timestamp: new Date(),
                page_id: row.page_id,
                account_key: row.account_key,
                country_code: row.country_code,
                sku: row.sku,
                seller_name: row.seller_name,
                seller_id: row.seller_id,
                page_url: row.capture_url,
                title: truncate(result.title?.trim(), 512).replace(" …", "") || null,
                content: truncate(result.content?.trim(), 4096).replace(" …", "") || null,
                page_fingerprint: result.fingerprint,
                domain_name: parseDomainName(row.capture_url),
                capture_id: row.capture_id,
                content_url: row.content_url,
                screenshot_url: result.screenshot_url
            });
            counters.inserted += 1;
            counters.consecutive_errors = 0;
        }
    }
    catch (err) {
        console.log(chalk.red(err instanceof Error ? err.message : JSON.stringify(err)));
        counters.errors += 1;
        counters.consecutive_errors += 1;
        if (counters.consecutive_errors > 5) {
            console.log(chalk.red(`Aborting, too many errors...`));
            break;
        }
    }
}

console.log(`${counters.inserted} inserted, ${counters.errors} errors`);
process.exit();

async function render(html: string, url: string) {
    const origin = new URL(url).origin;
    html = rebaseHtml(html, origin);
    const result = await renderRedactedHtml({ html, headless: false });    
    const title = regexpExtract(/<title>([^<]+)<\/title>/gi, html);
    const content = renderMarkdown(html);
    const timestamp = generateTimestamp("milliseconds");
    const screenshot_url = await saveToStorageFile(`fingerprints/${timestamp}.png`, result.fingerprint_image!, "image/png");
    return { ...result, title, content, screenshot_url };
}

interface Row {
    page_id: string;
    account_key: string;
    country_code: string;
    sku: string;
    seller_name: string;
    seller_id: number;
    capture_id: string;
    capture_date: Date;
    capture_url: string;
    content_url: string;
}

async function query({ app_name, account_key, country_code, seller_id, n }: { app_name: string, account_key: string, country_code?: string, seller_id?: number; n?: number }) {
    const where = [
        "capture_status IN ('latest-invalid', 'never-valid')",
        "recently_scanned.page_id IS NULL",
        "last_attempt.html_url IS NOT NULL",
        `app_name=${bigquery.safeValue(app_name)}`,
        `account_key=${bigquery.safeValue(account_key)}`
    ];

    if (country_code)
        where.push(`country_code=${bigquery.safeValue(country_code.toUpperCase())}`);

    if (seller_id)
        where.push(`seller_id=${bigquery.safeValue(seller_id)}`);

    const query = `
WITH recently_scanned AS (
  SELECT DISTINCT page_id
  FROM temp.page_fingerprints
  WHERE timestamp <= TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 7 DAY)
)
SELECT
  page_id,
  account_key,
  country_code,
  sku,
  IF(NOT ENDS_WITH(seller_name, ')'), CONCAT(seller_name, ' (', country_code, ')'), seller_name) AS seller_name,
  seller_id,
  last_attempt.html_url AS content_url,
  last_attempt.capture_id,
  last_attempt.capture_date,
  capture_url
FROM conflux.product_page_daily
LEFT JOIN recently_scanned USING (page_id)
WHERE ${where.join("\nAND ")}
QUALIFY ROW_NUMBER() OVER (PARTITION BY account_key, seller_id ORDER BY last_attempt.capture_date DESC) <= ${bigquery.safeValue(n)}
ORDER BY app_name, seller_name, account_key, country_code, sku
`.trim();

    if (process.env.VERBOSE)
        console.log(chalk.gray(query));

    const rows = await bigquery.query(query);
    return rows as Row[];
}
