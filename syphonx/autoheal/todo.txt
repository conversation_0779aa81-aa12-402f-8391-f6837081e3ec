consider https://www.browserbase.com/ for addressing blacklist

check if we have fresh data in the product_page_daily table
new table: syphonx.page_selector_audit_log with audit_status, audit_comment, ability to thumb up/down an audit-code from the lookerstudio report and add commets

audit: detect out of stock for price and reviews -> stock-status-bypass
audit: detect hidden price (add to cart, price not available, see price in cart)
audit: cross check other selector_name results (for example if we saw product was discontinued for a price audit then we can skip that page for a features audit)
audit: add a proxy (DONE)
audit: auto-dismiss dialogs
audit: auto-click-to-reveal
repair: reselect all regressions
derived data quality alerts


audit_status, join audits to commits
add a list of name/usage/cost sub-rows to repair output
audit: verify selectors for xxx not returning a value--consider testing all regressions


integrate the seo template and pipe the output into the reference prompt input
generate-selector-hint-price: prompt is not properly escaping the backslashes


supported: name, descrition, brand_name, features, price
next:
blocked
discontinued
pnf
broken
images, videos
review_score, review_count
specifications
related_products



PNF
https://www.bestbuy.ca/en-ca/product/sony-srsxv800-waterproof-bluetooth-wireless-speaker-black/17144700

HIDDEN PRICE
https://www.toolup.com/Milwaukee-48-00-5302-9-7TPI-Torch-Metal-Cutting-Sawzall-Blade-with-Carbide-Teeth-3-Pack
https://www.summitracing.com/parts/mwt-2741-20

NO PRICE
https://www.samsung.com/fr/washers-and-dryers/washing-machines/front-load-9kg-white-ww90t4020eh-ef/
https://www.homehardware.ca/en/364l-pro-300-white-base-semi-gloss-interior-latex-paint/p/1828801

PRODUCT DISCONTINUED
https://www.northerntool.com/product/milwaukee-m12-fuel-hackzall-reciprocating-saw-kit-12-volt-model-2520-21xc-45880
https://www.katom.com/175-69112.html

PRODUCT UNAVAILABLE
https://www.samsclub.com/p/truly-hard-seltzer-tropical-mix-pack-12-12-oz-cans/prod24441317
https://www.reedssports.com/ice-fishing/13-fishing-combo-27-l-ff-ghost-tickle-stk-far-lh-usaff-lh-27l
https://www.noon.com/v8-absolute-cordless-vacuum-0-54-l-425-0-w-394483-01-nickel/N53378948A/p/?o=b611121e451bfcbd
https://www.macys.com/shop/product/dyson-v12-detect-slim-cordless-vacuum-nickel?ID=16917313
https://www.bhphotovideo.com/c/replacement_for/1286400-REG/apc_be425m_back_ups_6_outlet_425va_ups.html

LAZY LOADED
https://www.bestbuy.ca/en-ca/product/sony-sony-e-55-210mm-f-4-5-6-3-oss-zoom-lens-sel55210b/10288046
https://www.homedepot.com/p/Ayesha-Curry-Bakeware-Loaf-Pan-9-Inch-x-5-Inch-Copper-47001/306079723
https://www.conrad.de/de/p/fluke-117-hand-multimeter-digital-cat-iii-600-v-anzeige-counts-6000-122746.html?refresh=true




generate-selector: progressively optimize html content
locate-content: persist and rehydrate full data+html

validate all regressions and record number verified
load pages in a chrome extension for maximum stealth

record all outcomes: blocked, discontinued, not a regression, content not found, unable to generate selector, etc.
syphonx-core built in screenshot feature

BLOCKERS
CPAP Store Dallas (US) 23365706, ERROR: page.screenshot: Timeout 30000ms exceeded. Call log: taking page screenshot - waiting for fonts to load
Northern Tool & Equipment (US) 30627 (needs stealth)
inmac-wstore (FR) 576313 (needs stealth)

Cool Blue (NL) 1575659 -- discontinued
Farnell (GB) 3522263 -- discontinued

Manor (CH) 7048961 -- jquery, see https://storage.googleapis.com/ps-syphonx/analyze/20240207-1208/dyson-226397-01-at-Manor-CH.html



try crewai

BUGS:
unexpected error: '#5120ec8d83cb48e29df7dd475f47dec7lg' is not a valid selector
  repro: node repair --target=description --seller=23817914 --sku=476547-01


P1:
show days since capture for each url in audit tool output
show changed selectors visible in audit and repair tool alongside days since last change
consider appending selector to existing selector ","
show before/after in audit commit output

dismiss modals (POTHOLE)
auto-dismiss modals
generate jQuery selectors with more complex DOM traverals (COVERGE)
prompt with advanced traversal examples (:contains, next/prev, etc.)
audit table
add regression-count/fix-count in commit
workaround for page.screenshot timeout errors for sites like conrad.de, etc. (POTHOLE)
auto-click on show mores, panels, etc.
add modal click actions
add blocked errors
DONE: remove obuscated identifiers from HTML
DONE: verify selector after generating selector (POTHOLE)
DONE: return paths from sliceHtml, bring over querySelectorPath from syphonx-chrome, then in generate-selector use the return paths to judge if the selector is over-selecting (POTHOLE)
DONE: pipe regression info into generate selector prompt (POTHOLE)
DONE: support multiple selector updates to a template
DONE: analyze_log that includes analyze_url, usage, cost, and maybe page_id's fixed

P2:
explain root cause of regression
ability to use a proxy for live page preview
DONE: stop when we have 3 of the same result generator for each target
DONE: stop when too many non-product page classifications are found
DONE: always use screenshot to classify if available (POTHOLE)

P3:
convert index tool output to a web-page
autoCorrectLabels review_score near review_count and vice versa
autoCorrectLabels images and videos based on url's
zero day regressions are most likely delay loaded content
identify when a selector targets a clickable link
option to use live page instead of stored content
option to use stored screenshot instead of generating a screenshot
detect negative cases
detect videos
add syphonx api key to .env file
set repeated based on type of selector (images, videos, etc.)
show number of selectors generated and blocked/error in progress output
add a selector if it doesn't already exist in a template
save output to cloud storage bucket
include previous selector results in generate-selector prompt
add detected selectors that aren't defined
autocrop screenshots (example: Bargreen Ellingson (US) 22986660)
update documentation: https://pricespider.atlassian.net/wiki/spaces/MB/pages/34590163018/SyphonX+AUTOHEAL

NEGATIVE CASES:
no price due to out of stock
no review score due to "no reviews yet" or "be the first to review" message
no review score found



OUTPUT:
link to live page
template path
GENERATE should indicate name of selector 
LOCATE - put response and screenshot side-by-side in a table
output names of selectors generated instead of (N selectors generated)

SCENARIOS:
cart-price (1433130)
click-to-reveal (18933529)
modal (23339246, 1224311, 24316127, 24316132, 576126)
hero-image-only (1432304)
videos (1232480)
other (1432304, 141172)

PNF
Hubert (US) 7048774
Rachael Ray (US) 24316130
23339227

BLOCKED
1432164
3846175
Reno Depot (CA) 2995324

DISCONTINUED
5867606
1310981
Restaurant Supply (US) 7048778

EMPTY
Ferguson (US) 1461125
1461125
4844779
30627
297


CONDITIONAL
out-of-stock -> no-price (3868505)
out-of-stock -> no-reviews (22986135)
out-of-stock -> no-reviews (121)

MODAL:
review_score (235)

WAITFOR:
images (23338405, 2149886)
features (701081)
images (4002534)
description (1395689)

REMOVED:
reviews (2146)
videos (841)
review_count/review_score (988479, 1220414)

UNAVAILABLE:
description (19850245)

CONSIDER:
how to deal with image galleries and hero image as images
how to handle more complex jquery selectors
how to detect videos in image galleries
how to extract text

OPTIMIZER:
remove selectors that are no longer hitting


INVESTIGATE:
why couldn't the hero image be detected for Baileigh (US) 24759517
file://wsl.localhost/Ubuntu/home/<USER>/pricespider/mudder-of-all-repos/syphonx/autoheal/out/20231231-1428/index.html

INVESTIGATE:
was the selector generated for product-name or description?
how can we get the description to target what's under the description?
file://wsl.localhost/Ubuntu/home/<USER>/pricespider/mudder-of-all-repos/syphonx/autoheal/out/20231231-1446/index.html

.product__info-container .product__title h1
> 105                 <h3 class="product__section-heading">Description</h3>
  106                 <div class="product__description rte quick-add-hidden">
  107                   <p>The bottle says angry, the buzz says very, very happy.</p>
  108                   <p>The perfect balance of sweetness and bright â€¦ esulting in a complex, yet refreshing, hard cider.</p>
  109                   <p>12 pack; bottles. 5% ABV.</p>
  110                 </div>
  
  rerun this one after we output the selector name next to the generated selector


INVESTIGAtE:
CPAP Store Dallas (US) 23365706
description is fixable but screenshot doesn't render
try again when we have the live option


INVESTIGATE:
Circulon (US) 24316131
review_score is fixable but the locate hits are for tag numbers that aren't in the screenshot!
also why are a large range of tag numbers are missing from the screenshot?
file://wsl.localhost/Ubuntu/home/<USER>/pricespider/mudder-of-all-repos/syphonx/autoheal/out/20231231-1527/index.html


INVESTIGATE:
Costco (US) 191
file://wsl.localhost/Ubuntu/home/<USER>/pricespider/mudder-of-all-repos/syphonx/autoheal/out/20231231-1552/index.html
images, review_count, review_score is fixable but the locate hits are not as expected
why are two of the generated screenshots blank?
features is good, but all the others reference tags that aren't there or in the wrong place
retry when pass previous regression value to locate


INVESTIGATE:
DME Supply USA (US) 23339256
file://wsl.localhost/Ubuntu/home/<USER>/pricespider/mudder-of-all-repos/syphonx/autoheal/out/20231231-1611/index.html
images, features is fixable but locate hits are not right
has hidden labels again, images referencing wrong tag
retry when pass previous regression value to locate


INVESTIGATE:
DME Supply USA (US) 23339256
file://wsl.localhost/Ubuntu/home/<USER>/pricespider/mudder-of-all-repos/syphonx/autoheal/out/20231231-1611/index.html
images, features is fixable but locate hits are not right
has hidden labels again, images referencing wrong tag
retry when pass previous regression value to locate


MISSING LABELS:
https://storage.googleapis.com/ps-syphonx/analyze/20240109-0707/bosch-BBH32101-at-Media-Markt-DE.html (image labels are not visible)


BAD MATCHES:
Pottery Barn (US) 1224311
QVC (US) 203
Rachael Ray (US) 24316130


RETRY AFTER REGRESSION FEEDBACK
Dell (US) 349 / description / 

DONE:
output: show prompt result first then prompt collapsed
pass previous regression value to locate
output an array of tags for locate


ROADMAP:
fine tuning
regression mode, gap mode, anomaly mode
get clean, stay clean
optimizer (remove dead selectors)

PROBLEM CLASSIFICATION:
images (hero image vs image gallery)
videos (find the video in the image gallery)
blocked
delay loaded content
page disposition rules (out-of-stock so no price or reviews)
discontinued
clicks
locators
optimizer -- run all matches and remove unused selectors

