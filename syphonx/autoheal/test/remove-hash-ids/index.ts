import { expect } from "chai";
import { summarize, removeHashIds, RemoveHashIdsResult } from "../lib";
import { fileURLToPath } from "url";
import * as fs from "fs";
import * as path from "path";

describe("remove-hash-ids", () => {
    const files = fs.readdirSync(fileURLToPath(new URL(`./mocks`, import.meta.url)));
    const mocks = files.filter(file => file.endsWith(".html")).map(file => path.parse(file).name).sort();
    
    for (const mock of mocks)
        describe(`MOCK ${mock}`, () => {
            const html = fs.readFileSync(fileURLToPath(new URL(`./mocks/${mock}.html`, import.meta.url)), "utf-8");
            const text = fs.readFileSync(fileURLToPath(new URL(`./mocks/${mock}.txt`, import.meta.url)), "utf-8");
            let result: RemoveHashIdsResult;
            before(async () => {
                result = await removeHashIds({ html });
                const { summary } = summarize(result.analyze);
                console.log(summary);
            });

            it("has expected output", () => {
                // positive tests
                text.split("\n").filter(test => test.startsWith("+")).forEach(test => expect(result.html).to.include(test.trim().slice(1)));
                // negative tests
                text.split("\n").filter(test => test.startsWith("-")).forEach(test => expect(result.html).to.not.include(test.trim().slice(1)));
            });
        });
});
