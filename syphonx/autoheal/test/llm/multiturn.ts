import { expect } from "chai";
import { <PERSON><PERSON>hat, LLMModel } from "../lib.js";

const models: LLMModel[] = [
    "llama3-8b-8192",
    "gpt-3.5-turbo",
    "claude-3-haiku-20240307",
    "gemini-2.0-flash"
];

const q1 = "What is the capital of France?";
const q2 = "What is the most notable landmark in that city?";

describe("llm multiturn", () => {
    models.forEach(model => {
        describe(model, async () => {
            const chat = new LLMChat(model);
            const a: LLMChat[] = [];
            let a1 = "";
            let a2 = "";

            before(async () => {
                a1 = await chat.prompt(q1);
                a.push(chat.clone());
                a2 = await chat.prompt(q2);
                a.push(chat.clone());
            });

            it("first answer has expected value", () => expect(a1).includes("Paris"));
            it("first usage is greater than zero", () => expect(a[0].tokens).to.be.above(0));
            it("first cost is greater than zero", () => expect(a[0].cost).to.be.above(0));
            it("first elapsed is greater than zero", () => expect(a[0].elapsed).to.be.above(0));

            it("second answer has expected value", () => expect(a2).includes("Eiffel Tower"));
            it("second usage is greater than zero", () => expect(a[1].tokens).to.be.above(a[0].tokens));
            it("second cost is greater than zero", () => expect(a[1].cost).to.be.above(a[0].cost));
            it("second elapsed is greater than zero", () => expect(a[1].elapsed).to.be.above(a[0].elapsed));
        });
    });
});
