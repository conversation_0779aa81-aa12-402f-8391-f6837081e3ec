import { expect } from "chai";
import { LLMChat, LLMModel } from "../lib.js";
import { fileURLToPath } from "url";
import * as path from "path";

const models: LLMModel[] = [
    "gpt-4o",
    "claude-3-haiku-20240307",
    "gemini-2.0-flash"
];

const test_dir = fileURLToPath(new URL(".", import.meta.url));
const test_image = path.resolve(test_dir, "duck.jpg");

describe("llm vision", () => {
    models.forEach(model => {
        describe(model, async () => {
            const chat = new LLMChat(model);
            let answer = "";

            before(async () => {
                answer = await chat.prompt("Describe this image.", { images: [test_image] });
            });

            it("answer has expected value", () => expect(answer).includes("duck"));
            it("usage is greater than zero", () => expect(chat.tokens).to.be.above(0));
            it("cost is greater than zero", () => expect(chat.cost).to.be.above(0));
            it("elapsed is greater than zero", () => expect(chat.elapsed).to.be.above(0));
        });
    });
});
