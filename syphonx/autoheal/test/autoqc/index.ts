import * as path from "path";
import * as fs from "fs";
import chalk from "chalk";
import { expect } from "chai";
import { fileURLToPath } from "url";

import {
    analyzeProductPageScreenshot,
    summarize,
    AnalyzeProductPageScreenshotResult
} from "../lib.js";

const test_dir = fileURLToPath(new URL("./mocks", import.meta.url));

describe("autoqc", () => {

    describe("clipped", function () {
        let result: AnalyzeProductPageScreenshotResult;
        before(async function () {
            result = await common(this.test!.parent!.title!, "officedepot.com");
        });
        it("model result is ok", () => expect(result.ok).is.true);
        it("status has expected value", () => expect(result.status).equals("CLIPPED"));
    });

    describe("cdw1", () => {
        let result: AnalyzeProductPageScreenshotResult;
        before(async function () {
            result = await common(this.test!.parent!.title!, "cdw.com");
        });
        it("model result is ok", () => expect(result.ok).is.true);
        it("status has expected value", () => expect(result.status).equals("OK"));
        it("summary has expected value", () => expect(result.summary).is.a.string);
        it("name", () => expect(result.scan_result?.name?.value).equals("APC by Schneider Electric Smart-UPS, Lithium-Ion, 3000VA, 120V with SmartConnect Port"));
        it("price", () => expect(result.scan_result?.price?.value).equals(3793.99));
        it("image_count", () => expect(result.scan_result?.image_count?.value).equals(6));
        it("video_count", () => expect(result.scan_result?.video_count?.value).equals(0));
        it("review_score", () => expect(result.scan_result?.review_score?.value).equals(5));
        it("review_count", () => expect(result.scan_result?.review_count?.value).equals(31));
        it("stock_status", () => expect(result.scan_result?.stock_status?.value).is.a.string);
        it("in_stock", () => expect(result.scan_result?.in_stock?.value).is.true);
        it("description", () => expect(result.scan_result?.description?.value).is.a.string);
        it("feature_bullets", () => expect(result.scan_result?.feature_bullets?.value).is.a.string);
        it("brand_name", () => expect(result.scan_result?.brand_name?.value).to.include("APC"));
    });

    describe("newegg1", () => {
        let result: AnalyzeProductPageScreenshotResult;
        before(async function () {
            result = await common(this.test!.parent!.title!, "newegg.com");
        });
        it("model result is ok", () => expect(result.ok).is.true);
        it("status has expected value", () => expect(result.status).equals("OK"));
        it("summary has expected value", () => expect(result.summary).is.a.string);
        it("name", () => expect((result.scan_result?.name?.value as string)?.startsWith("APC UPS, 3000 VA Smart-UPS Sine Wave")).is.true);
        it("price", () => expect(result.scan_result?.price?.value).equals(2095.99));
        it("image_count", () => expect(result.scan_result?.image_count?.value).equals(6));
        it("video_count", () => expect(result.scan_result?.video_count?.value).equals(0));
        it("review_score", () => expect(result.scan_result?.review_score?.value).equals(4.5));
        it("review_count", () => expect(result.scan_result?.review_count?.value).equals(19));
        it("stock_status", () => expect(result.scan_result?.stock_status?.value).is.a.string);
        it("in_stock", () => expect(result.scan_result?.in_stock?.value).is.true);
        it("description", () => expect(result.scan_result?.description?.value).is.a.string);
        it("feature_bullets", () => expect(result.scan_result?.feature_bullets?.value).is.a.string);
        it("brand_name", () => expect(result.scan_result?.brand_name?.value).to.include("APC"));
    });

    describe("walmart1", () => {
        let result: AnalyzeProductPageScreenshotResult;
        before(async function () {
            result = await common(this.test!.parent!.title!, "walmart.com");
        });
        it("model result is ok", () => expect(result.ok).is.true);
        it("status has expected value", () => expect(result.status).equals("OK"));
        it("summary has expected value", () => expect(result.summary).is.a.string);
        it("name", () => expect(result.scan_result?.name?.value).equals("APC by Schneider Electric NetShelter Automatic Transfer Switch AP4450A"));
        it("price", () => expect(result.scan_result?.price?.value).equals(1204.97));
        it("image_count", () => expect(result.scan_result?.image_count?.value).equals(2));
        it("video_count", () => expect(result.scan_result?.video_count?.value).equals(0));
        it("review_score", () => expect(result.scan_result?.review_score?.value).is.null);
        it("review_count", () => expect(result.scan_result?.review_count?.value).is.null);
        it("stock_status", () => expect(result.scan_result?.stock_status?.value).is.a.string);
        it("in_stock", () => expect(result.scan_result?.in_stock?.value).is.false); // sold by third party seller
        it("description", () => expect(result.scan_result?.description?.value).is.a.string);
        it("feature_bullets", () => expect(result.scan_result?.feature_bullets?.value).is.a.string);
        it("brand_name", () => expect(result.scan_result?.brand_name?.value).to.include("APC"));
    });

    describe("walmart2", () => {
        let result: AnalyzeProductPageScreenshotResult;
        before(async function () {
            result = await common(this.test!.parent!.title!, "walmart.com");
        });
        it("model result is ok", () => expect(result.ok).is.true);
        it("status has expected value", () => expect(result.status).equals("OK"));
        it("summary has expected value", () => expect(result.summary).is.a.string);
        it("name", () => expect(result.scan_result?.name.value).equals("Dyson V8 Origin+ Cordless Vacuum | Purple | New"));
        it("price", () => expect(result.scan_result?.price.value).equals(299.99));
        it("image_count", () => expect(result.scan_result?.image_count?.value).equals(4));
        it("video_count", () => expect(result.scan_result?.video_count?.value).equals(1));
        it("review_score", () => expect(result.scan_result?.review_score?.value).equals(4.3));
        it("review_count", () => expect(result.scan_result?.review_count?.value).equals(2440));
        it("stock_status", () => expect(result.scan_result?.stock_status?.value).is.a.string);
        it("in_stock", () => expect(result.scan_result?.in_stock?.value).is.true);
        it("description", () => expect(result.scan_result?.description?.value).is.null);
        it("feature_bullets", () => expect(result.scan_result?.feature_bullets?.value).is.a.string);
        it("brand_name", () => expect(result.scan_result?.brand_name?.value).to.include("Dyson"));
    });
});

async function common(name: string, domain: string) {
    const result = await analyzeProductPageScreenshot(path.resolve(test_dir, `${name}.png`), { context: { domain }});
    const { summary } = summarize(result.analyze);
    console.log(chalk.gray(summary));
    fs.writeFileSync(path.resolve(test_dir, `${name}.prompt.out`), result.input);
    fs.writeFileSync(path.resolve(test_dir, `${name}.response.out`), `${result.output}\n\n${summary}`);
    return result;
}
