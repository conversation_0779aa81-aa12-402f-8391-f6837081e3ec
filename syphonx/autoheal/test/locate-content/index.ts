import { expect } from "chai";
import { summarize, openPage, locateContent, LocateContentResult } from "../lib";
import { fileURLToPath } from "url";
import * as fs from "fs";
import * as path from "path";

describe("locate-content", () => {
    const files = fs.readdirSync(fileURLToPath(new URL(`./mocks`, import.meta.url)));
    const mocks = files.filter(file => file.endsWith(".html")).map(file => path.parse(file).name).sort();

    for (const mock of mocks)
        describe(`MOCK ${mock}`, () => {
            let result: LocateContentResult;
            before(async () => {
                const url = `file://${fileURLToPath(new URL(`./mocks/${mock}.html`, import.meta.url))}`;
                const { page, browser } = await openPage({ url, headless: false });
                result = await locateContent({
                    page,
                    domain_name: "example.com",
                    selector_profile: "brand-monitor-product-page"
                });
                await browser.close();
                const { summary } = summarize(result.analyze);
                console.log(summary);
            });

            it("content is ok", () => expect(result.ok).to.be.true);
            it("content has labels", () => expect(result.labelled_html?.match(/__label__/g)?.length).is.greaterThanOrEqual(10));
            it("name has expected value", () => expect(result.targets?.name.text).to.include("Baseball Cap"));
            it("description has expected value", () => expect(result.targets?.description.text).to.include("Generic"));
            it("price has expected value", () => expect(result.targets?.price.text).to.include("$9.99"));
            it("review_score has expected value", () => expect(result.targets?.review_score.text).to.include("4.0"));
            it("review_count has expected value", () => expect(result.targets?.review_count.text).to.include("256"));
            it("brand_name has expected value", () => expect(result.targets?.brand_name.text).to.include("Acme"));
    });
});