import { expect } from "chai";
import { fileURLToPath } from "url";
import * as fs from "fs";
import * as path from "path";
import chalk from "chalk";

import {
    generateSelector,
    getSelectorProfile,
    summarize,
    tryLoadJsonFile,
    tryLoadYamlFile,
    AnalyzeResult,
    GenerateSelectorResult,
    LocateContentTarget
} from "../lib";

const override: Record<string, string[]> = {
    name: ["1"],
    selectors: ["price"]
};

describe("generate-selector", () => {
    const files = fs.readdirSync(fileURLToPath(new URL(`./mocks`, import.meta.url)));
    const mocks = override.name || files.filter(file => file.endsWith(".html")).map(file => path.parse(file).name).sort();
    const analyze: AnalyzeResult[] = [];

    after(() => {
        const { summary } = summarize(analyze);
        console.log();
        console.log(chalk.gray("SUMMARY"));
        console.log(chalk.gray(summary));
    });

    for (const mock of mocks)
        describe(`MOCK ${mock}`, () => {
            const labelled_html = fs.readFileSync(fileURLToPath(new URL(`./mocks/${mock}.html`, import.meta.url)), "utf-8");
            const locate_result = tryLoadJsonFile(fileURLToPath(new URL(`./mocks/${mock}.jsonc`, import.meta.url))) as Record<string, Required<LocateContentTarget>>;
            const selector_targets = tryLoadYamlFile(fileURLToPath(new URL(`./mocks/${mock}.yaml`, import.meta.url))) as Record<string, string[]>;
            const selector_names = override.selectors || Object.keys(selector_targets);
            const selector_profile = getSelectorProfile("product-page");

            for (const selector_name of selector_names)
                describe(`SELECTOR ${selector_name}`, () => {
                    const expected_result = selector_targets[selector_name];
                    const locate_target = locate_result[selector_name];
                    const selector_target = selector_profile.targets.find(selector_target => selector_target.name === selector_name)!;
        
                    let result: GenerateSelectorResult;
                    before(async () => {
                        result = await generateSelector({
                            selector_name,
                            domain_name: "example.com",
                            url: "https://www.example.com/",
                            labelled_html,
                            locate_target,
                            selector_target
                        });
                        analyze.push(...result.analyze);
                        const { summary } = summarize(result.analyze);
                        console.log(chalk.gray(summary));
                    });

                    it(`${selector_name} is ok`, () => expect(result.ok).is.true);
                    it(`${selector_name} has expected value`, () => expect(expected_result).to.include(result.selector));    
                });
        });
});
