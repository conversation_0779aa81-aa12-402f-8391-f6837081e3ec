import { expect } from "chai";
import { fileURLToPath } from "url";
import * as fs from "fs";
import * as path from "path";
import chalk from "chalk";

import {
    autogenProfile,
    getSelectorProfile,
    renderLocateContentPrompt,
    tryLoadJsonFile,
    LocateContentTarget,
    LLMChat
} from "../lib";

describe("locate-content-screenshot", () => {
    const files = fs.readdirSync(fileURLToPath(new URL(`./mocks`, import.meta.url)));
    const mocks = files.filter(file => file.endsWith(".png")).map(file => path.parse(file).name).sort();

    for (const mock of mocks)
        describe(`MOCK ${mock}`, () => {
            const file_path = fileURLToPath(new URL(`./mocks/${mock}`, import.meta.url));
            const data = tryLoadJsonFile(`${file_path}.jsonc`) as Record<string, { labels: number[], text: string }>;

            let result: Record<string, LocateContentTarget> | undefined = undefined;
            before(async () => {
                const domain_name = "example.com";
                const selector_profile = "common-product-page";

                const { targets } = getSelectorProfile(selector_profile);
                const input = renderLocateContentPrompt({ domain_name, targets });
            
                const model = autogenProfile().locateContent.model;
                const chat = new LLMChat(model);
                console.log(chalk.gray(`Locating content... (using model ${model})`));
                [result] = await chat.json<Record<string, LocateContentTarget>>(input, { images: [`${file_path}.png`] });
                console.log(chalk.gray(JSON.stringify(result, null, 2)));
            });

            it("result is not empty", () => expect(result).to.not.be.empty);
            for (const key of Object.keys(data))
                if (Array.isArray(data[key].labels) && data[key].labels.length > 0)
                    it(`${key} labels match`, () => expect(data[key].labels).deep.equal(result![key].labels));
    });
});