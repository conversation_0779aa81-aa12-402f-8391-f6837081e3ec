import { expect } from "chai";
import { fileURLToPath } from "url";
import * as fs from "fs";
import * as path from "path";
import { sliceLabelledHtml, SliceHtmlResult } from "../lib.js";

describe("slice-html", () => {
    const files = fs.readdirSync(fileURLToPath(new URL(`./mocks`, import.meta.url)));
    const mocks = files.filter(file => file.endsWith(".html")).map(file => path.parse(file).name).sort();

    for (const mock of mocks)
        describe(`MOCK ${mock}`, () => {
            const labelled_html = fs.readFileSync(fileURLToPath(new URL(`./mocks/${mock}.html`, import.meta.url)), "utf-8");
            let result: SliceHtmlResult | undefined = undefined;
            before(() =>
                result = sliceLabelledHtml(labelled_html, [220, 223, 238, 239, 240], 2));
            it("slice has expected length", () => expect(result?.html).is.have.lengthOf.within(4000, 5000));
        });
});
