import { expect } from "chai";
import { fileURLToPath } from "url";
import * as fs from "fs";
import * as path from "path";

import {
    autogenProfile,
    renderAutocloseDialogPrompt,
    setAutogenProfile,
    tryLoadJsonFile,
    LLMChat
} from "../lib";

describe.skip("auto-close-dialog", () => {
    setAutogenProfile("gemini-economy");
    const files = fs.readdirSync(fileURLToPath(new URL(`./mocks`, import.meta.url)));
    const mocks = files.filter(file => file.endsWith(".png")).map(file => path.parse(file).name).sort();
    
    for (const mock of mocks)
        describe(`MOCK ${mock}`, () => {
            const image = fileURLToPath(new URL(`./mocks/${mock}.png`, import.meta.url));
            const [expected_result] = tryLoadJsonFile(fileURLToPath(new URL(`./mocks/${mock}.jsonc`, import.meta.url))) as Array<{ label: string, x: number, y: number }>;

            const prompt = renderAutocloseDialogPrompt({ attempt: 0 });
            const model = autogenProfile().locateContent.model;
            const chat = new LLMChat(model);
        
            let response: string|undefined = undefined;
            before(async () => {
                response = await chat.prompt(prompt, { images: [image] });
            });

            it("has expected output", () => expect(response).includes(`[${expected_result.label}]`));
        });
});
