import { expect } from "chai";
import { openPage, addLabels } from "../lib";
import { fileURLToPath } from "url";
import * as fs from "fs";
import * as path from "path";

describe("label-page", () => {
    const files = fs.readdirSync(fileURLToPath(new URL(`./mocks`, import.meta.url)));
    const mocks = files.filter(file => file.endsWith("a.html")).map(file => path.parse(file).name.slice(0, -1)).sort();

    for (const mock of mocks)
        describe(`MOCK ${mock}`, () => {
            const expected_html = fs.readFileSync(fileURLToPath(new URL(`./mocks/${mock}b.html`, import.meta.url)), "utf-8").replace(/\r/g, "").trim();
            let output_html: string;
            before(async () => {
                const url = `file://${fileURLToPath(new URL(`./mocks/${mock}a.html`, import.meta.url))}`;
                const { page, browser } = await openPage({ url, headless: false });
                [output_html] = await addLabels(page);
                await browser.close();
            });
            it("ouptut matches expected result", () => expect(output_html).equals(expected_html));
        });
});
