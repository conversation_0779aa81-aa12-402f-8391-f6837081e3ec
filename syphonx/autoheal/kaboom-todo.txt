# P1
- gemini batch mode !!!!!!!!!!!!!
- direct match, use adsite

- set status on candidates
- set status on inactive matches
- indicate whether candidate match is exact
- add deactivated matches for retailers without any match
- list all previous analyze records so we can link them in the report

- consider extracting page data using markdown
- remove inactive matches where there is an active match for the same domain
- google shopping search for new matches


# DONE
- show source data from seo, diffbot, and syphonx (DONE)
- green check on blank fields (DONE)
- address diffbot 429 errors
- render image thumbnails for each match
