import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";
import clipboard from "clipboardy";

import {
    completeProductMatchAnalyzeBatch,
    createProductMatchAnalyzeBatch,
    createProductMatchAnalyzePrompt,
    generateUniqueId,
    loadAutoselectDictionaryCollection,
    lookupAutoselectDictionary,
    objIncrement,
    parseArgs,
    processProductMatchAnalyze,
    queryProductMatchAnalyze,
    sum,
    llm_models,
    LLMChat,
    LLMModel
}
    from "./lib/index.js";

const args = parseArgs({
    required: {
        0: "specify a command: run, batch, prompt, view"
    },
    optional: {
        1: "batch sub-command (create, complete)",
        app: "app-name (wtb, prowl, brand-monitor), default=all",
        account: "comma seperated list of account keys",
        brand: "filter by brand-name",
        country: "comma seperated list of country-codes",
        seller: "comma seperated list of seller-ids",
        domain: "comma seperated list of domain names",
        exclude: "comma seperated list of domain names to exclude",
        sku: "comma seperated list of skus",
        mode: "use to specify trinity mode",
        from: "from date",
        to: "to date",
        limit: "max number of skus, default=500",
        subset: "subset of rows to return per domain, default=3",
        model: "specify the LLM model to use",
        random: "choose product(s) at random, optionally within other constraints",
        run: "specify continuation run-id",
        concurrency: "concurrency limit (default=10)",
        force: "force override pending status",
        preview: "preview mode, options: (domains, products, urls), default=urls",
        verbose: "verbose log output"
    },
    help: {
        short: "Audits product matches to determine match integrity.",
        long: `
Commands:
- run: runs in immediate mode
- batch: runs in batch mnode, also specify (create, complete)
- prompt: generates a prompt for a single product, specify --preview to bypass running model
- view: views additional info, also specify (models)
`.trim()
    },
    validate: args => {
        if (!["run", "batch", "prompt", "view"].includes(args[0]))
            return "Specify a command: run, batch, prompt, view";
        if (args[0] === "batch" && !["create", "complete"].includes(args[1]))
            return "Specify a batch sub-command: create, complete";
        if (args[0] === "view" && !["models"].includes(args[1]))
            return "Specify a view sub-command: models";
        if (args.preview && !["domains", "products", "urls", "1"].includes(args.preview))
            return "Valid options for --preview are domains, products, urls";
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

const command = args[0] as "run" | "batch" | "prompt" | "view";
const subcommand = args[1] as "create" | "complete" | "models";
const mode = args.mode;

if (command === "view" && subcommand === "models") {
    console.log(llm_models.join("\n"));
    process.exit();
}

//const default_model = args.batch ? "gpt-4o-mini" : "gemini-2.5-pro-exp-03-25"; // gemini-2.0-flash-thinking-exp-01-21 gemini-2.0-pro-exp-02-05 gemini-2.0-flash-exp gemini-2.0-pro
const model = args.model as LLMModel || "gemini-2.0-flash-thinking-exp-01-21";
const limit = parseInt(args.limit) || 500;
const force = !!args.force || !!args.sku;
const concurrency = parseInt(args.concurrency) || 10;
const price_threshold = 0.80;

const query_options = {
    app_name: args.app,
    brand_name: args.brand,
    account_keys: args.account?.split(",").map(value => value.trim()).filter(Boolean),
    country_codes: args.country?.split(",").map(value => value.trim().toUpperCase()).filter(Boolean),
    domain_names: args.domain?.split(",").filter(Boolean),
    exclude: args.exclude?.split(",").filter(Boolean) || [],
    seller_ids: args.seller?.split(",").map(value => parseInt(value)).filter(Boolean),
    skus: args.sku?.split(",").map(value => value.trim()).filter(Boolean),
    from: args.from ? new Date(args.from) : undefined,
    to: args.to ? new Date(args.to) : undefined,
    subset: parseInt(args.subset) || 3,
    random: !!args.random,
    all: false,
    mode,
    limit,
    force
};
query_options.exclude.push(...["amazon.com", "ebay.com"]);

if (command === "prompt" && args.preview) {
    await loadAutoselectDictionaryCollection();
    const [row] = await queryProductMatchAnalyze({ ...query_options, limit: 1, force: true });
    const { prompt, datauri, message } = await createProductMatchAnalyzePrompt(row, { concurrency, price_threshold });
    if (prompt) {
        console.log(chalk.cyan(prompt));
        clipboard.writeSync(datauri);
        console.log(chalk.gray.italic("Product image strip copied to clipboard."));
    }
    else {
        console.log(chalk.gray(message));
        if (!args.verbose)
            console.log(chalk.gray.italic("Specify --verbose for more info."));
    }
    process.exit();
}

if (command === "prompt") {
    await loadAutoselectDictionaryCollection();
    const [row] = await queryProductMatchAnalyze({ ...query_options, limit: 1, force: true });
    const { prompt, datauri, message } = await createProductMatchAnalyzePrompt(row, { concurrency, price_threshold });
    if (prompt) {
        clipboard.writeSync(datauri);
        const chat = new LLMChat(model);
        const completion = await chat.prompt(prompt, { images: [datauri] });
        console.log(chalk.cyan(completion));
        console.log();
        console.log(chalk.gray(`${chat.input_tokens} prompt tokens, ${chat.output_tokens} completion tokens ($${chat.cost}) in ${(chat.elapsed / 1000).toFixed(1)} seconds`));
        console.log(chalk.gray.italic("Product image strip copied to clipboard."));
    }
    else {
        console.log(chalk.gray(message));
        if (!args.verbose)
            console.log(chalk.gray.italic("Specify --verbose for more info."));
    }
    process.exit();
}

if (command === "batch" && subcommand === "complete") {
    console.log(chalk.gray(`Processing batch completions...`));
    const batch_id = args.complete;
    const result = await completeProductMatchAnalyzeBatch(batch_id, mode);
    console.log(chalk.gray(`${result.completions} completed`));
    if (result.errors)
        console.log(chalk.yellow(result.errors.join("\n")));
    process.exit();
}

console.log(chalk.gray(`Query product match analyze pending...`));
const rows = await queryProductMatchAnalyze(query_options);
const total_match_count = sum(rows.map(row => ({ count: row.pages.length })), "count");
console.log(chalk.gray(`${rows.length} products, ${total_match_count} product matches`));

if (rows.length === 0)
    process.exit();

if (args.preview === "products") {
    for (const row of rows)
        console.log(chalk.gray(`${row.account_key} ${row.sku} (${row.country_code}), ${row.pages.length} matches`));
    console.log(chalk.gray(`${rows.length} products, ${total_match_count} product matches`));
    process.exit();
}

if (args.preview === "domains") {
    await loadAutoselectDictionaryCollection();
    console.log();
    const domains: Record<string, number> = {};
    for (const row of rows)
        for (const page of row.pages)
            objIncrement(domains, page.domain_name as string);
    for (const key of Object.keys(domains).sort().sort((a, b) => domains[b] - domains[a])) {
        const obj = lookupAutoselectDictionary(key);
        if (obj?.product_name)
            console.log(chalk.gray(`${key}: ${domains[key]}`));
        else
            console.log(chalk.yellow(`${key}: ${domains[key]}`));
    }
    process.exit();
}

if (args.preview) {
    await loadAutoselectDictionaryCollection();
    let current_name = "";
    for (const row of rows) {
        const name = `${row.account_key} ${row.sku} (${row.country_code})`;
        if (current_name !== name) {
            console.log();
            console.log(chalk.underline(name));
            current_name = name;
        }
        for (const page of row.pages) {
            const obj = lookupAutoselectDictionary(page.domain_name);
            console.log(chalk.gray(`${page.url}${!obj?.product_name ? chalk.yellow.italic(" (no autoselect)") : ""}`));
        }
    }
    console.log(chalk.gray(`${rows.length} products, ${total_match_count} product matches`));
    process.exit();
}

if (command === "batch" && subcommand === "create") {
    console.log(chalk.gray(`Batching ${rows.length} products...`));
    await loadAutoselectDictionaryCollection();
    const batches = await createProductMatchAnalyzeBatch(rows, { model, price_threshold, concurrency });
    const total_count = sum(batches, "batch_count");
    console.log(chalk.gray(`${batches.length} batches created, ${total_count} prompts`));
    for (const { batch_id, batch_count } of batches)
        console.log(chalk.gray(`${batch_id} ${batch_count}`));
    process.exit();
}

if (command === "run") {
    const run_id = args.run || generateUniqueId();
    console.log(chalk.gray(`Analyzing ${rows.length} products...`));
    await loadAutoselectDictionaryCollection();
    let errors = 0, consecutive_errors = 0;
    for (const row of rows) {
        try {
            const { analyze_id, report_url } = await processProductMatchAnalyze(row, { model, price_threshold, concurrency, mode, run_id });
            if (process.env.VERBOSE)
                console.log();
            console.log(chalk.white(`[${rows.indexOf(row) + 1}/${rows.length}] ${row.account_key} ${row.country_code} ${row.sku} (${row.pages.length} matches) ${chalk.gray(`id=${analyze_id}${report_url ? ` ${report_url}` : ""}`)}`));
            consecutive_errors = 0;
        }
        catch (err) {
            console.log(chalk.white(`[${rows.indexOf(row) + 1}/${rows.length}] ${row.account_key} ${row.country_code} ${row.sku} (${row.pages.length} matches) ${chalk.red(err instanceof Error ? err.message : JSON.stringify(err))}`));
            errors += 1;
            consecutive_errors += 1;
            if (errors >= 10 || consecutive_errors >= 3) {
                console.log(chalk.red("error limit exceeded"));
                break;
            }
        }
    }
    console.log(chalk.white(`${rows.length} products analyzed, ${errors} errors, run_id=${run_id}`));
    process.exit();
}
