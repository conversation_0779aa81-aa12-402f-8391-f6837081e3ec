import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";
import { promises as fs } from "fs";

import {
    analyzeRegression,
    autogenProfiles,
    closeLogStream,
    commit,
    createRemoteLogStream,
    diffDate,
    generateTimestamp,
    hookConsoleLog,
    loadTemplate,
    openPageForVision,
    parseArgs,
    playAudio,
    queryAuditFixable,
    setAutogenProfile,
    summarize,
    unhookConsoleLog,
    updateTemplate,
    waitForKeypress,
    CommitOptions,
    GenerateSelectorRecordCreator,
    QueryAuditFixableResult,
    SelectorUpdate,
    WaitFor
}
from "./lib/index.js";

const args = parseArgs({
    required: {
        0: "brand_monitor, wtb, prowl"
    },
    optional: {
        seller: "seller-id filter, one only",
        headless: "hide browser window (default=false)",
        pause: "pause for user input after each page load (ignored if cached option is used)",
        snooze: "amount of time in seconds to snooze after loading a live page (default=2) (ignored if cached or pause option is used)",
        waitfor: "specify load, domcontentloaded, or networkidle (default=none)",
        summary: "brief summary rollup by seller without doing any work",
        preview: "detailed page level preview without doing any work",
        force: "force by skipping last autoheal check",
        commit: "automatically commit template changes, otherwise prompts to confirm each commit",
        test: "run but don't record any autoheal results",
        profile: `set config profile: ${autogenProfiles().join(", ")}`,
        verbose: "verbose log output"
    },
    validate: params => {
        if (!["brand_monitor", "wtb", "prowl"].includes(params[0]))
            return "specify brand_monitor, wtb, or prowl only";
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

const snooze = parseInt(args.snooze) || 4;
const force = !!args.force || !!args.seller;
const test = !!args.test;
if (args.profile)
    setAutogenProfile(args.profile);

const rows = await queryAuditFixable({
    app_name: args[0],
    seller_id: parseInt(args.seller) || undefined
});

if (rows.length === 0) {
    console.log(chalk.gray(`0 fixable pages`));
    process.exit(0);
}

const seller_map: Record<string, QueryAuditFixableResult[]> = {};
for (const row of rows) {
    if (!seller_map[row.seller_name])
        seller_map[row.seller_name] = [];
    seller_map[row.seller_name].push(row);
}

if (args.summary || args.preview) {
    let ready_sellers = 0;
    const keys = Object.keys(seller_map);
    for (const key of keys) {
        const rows = seller_map[key];
        const [{ seller_id }] = rows;
        const selectors = Array.from(new Set(rows.map(row => row.selector_name)));
        if (args.preview) {
            console.log();
            console.log(chalk.gray.underline(`${key} ${seller_id} [${keys.indexOf(key)+1}/${keys.length}]`));
        }
        const last_generated = Math.max(...rows.map(row => row.last_generated_at ? row.last_generated_at.getTime() : 0));
        const last_generated_result = rows.find(row => row.last_generated_at?.getTime() === last_generated)?.last_generated_result;
        if (args.preview) {
            if (last_generated > 0)
                console.log(chalk.gray(`last autoheal attempt ${diffDate(new Date(last_generated))} ago`));
            console.log(chalk.gray(`${rows.length} audits: ${selectors.map(selector_name => `${selector_name} (${rows.filter(row => row.selector_name === selector_name).length})`).join(", ")}`));
        }
        let ready = 0, skipped = 0;
        for (const row of rows) {
            const skip = row.audited_at.getTime() <= last_generated && !force;
            if (!skip) {
                if (args.preview)
                    console.log(chalk.green(`${row.selector_name} ${row.audit_url} (audited ${diffDate(row.audited_at)} ago${skip ? chalk.italic(`, autoheal attempt ${diffDate(new Date(last_generated))} ago [${last_generated_result}], diff=${diffDate(new Date(last_generated), row.audited_at)}`) : ""})`));
                ready += 1;
            }
            else {
                if (args.preview)
                    console.log(chalk.gray(`SKIPPED ${row.selector_name} ${row.audit_url} (audited ${diffDate(row.audited_at)} ago${skip ? chalk.italic(`, autoheal attempt ${diffDate(new Date(last_generated))} ago [${last_generated_result}], diff=${diffDate(new Date(last_generated), row.audited_at)}`) : ""})`));
                skipped += 1;
            }
        }
        if (args.summary)
            console.log(chalk[ready > 0 ? "white" : "gray"](`${key} ${seller_id}: ${rows.length} audits: ${selectors.map(selector_name => `${selector_name} (${rows.filter(row => row.selector_name === selector_name).length})`).join(", ")}, ${ready} ready, ${skipped} skipped${last_generated > 0 ? `, last autoheal attempt ${diffDate(new Date(last_generated))} ago [${last_generated_result}]` : ""}`));
        else if (args.preview)
            console.log(chalk.gray(`${ready} ready, ${skipped} skipped`));
        if (ready > 0)
            ready_sellers += 1;
    }

    console.log();
    console.log(chalk.gray(`${ready_sellers}/${Object.keys(seller_map).length} sellers ready`));
    process.exit(0);
}

if (!force) {
    for (const key of Object.keys(seller_map)) {
        const rows = seller_map[key];
        const [{ seller_id }] = rows;
        const last_generated = Math.max(...rows.map(row => row.last_generated_at ? row.last_generated_at.getTime() : 0));
        if (last_generated) {
            const skipped_rows = rows.filter(row => row.audited_at.getTime() <= last_generated && !force);
            const ready_rows = rows.filter(row => !skipped_rows.includes(row));
            
            if (ready_rows.length > 0) {
                console.log(chalk.gray(`${key} ${seller_id} ${ready_rows.length} ready, ${skipped_rows.length} skipped`));
                seller_map[key] = ready_rows;
            }
            else {
                console.log(chalk.gray(`${chalk.strikethrough(`${key} ${seller_id}`)} ${ready_rows.length} ready, ${skipped_rows.length} skipped`));
                delete seller_map[key];
            }
        }
        else {
            console.log(chalk.gray(`${key} ${seller_id} ${rows.length} audits`));
        }
    }
}

const keys = Object.keys(seller_map);
console.log(chalk.gray(`${keys.length} ready`));
const counters = {
    generated: 0,
    updates: 0,
    commits: 0
};

for (const key of keys) {
    const rows = seller_map[key];
    const [{ seller_id }] = rows;

    console.log();
    console.log(chalk.gray.underline(`${key} ${seller_id} [${keys.indexOf(key)+1}/${keys.length}]`));

    const valid_generated_selectors = [];
    for (const row of rows) {
        const generate_record = new GenerateSelectorRecordCreator(row);

        const { template } = await loadTemplate(row.template_path);
        const timestamp = generateTimestamp("milliseconds");
        const { stream, url: report_url } = createRemoteLogStream(`autoheal/${timestamp}.html`, { title: "AUTOHEAL", subtitle: row.page_id });
        hookConsoleLog();

        const { page, analyze, error_code, message, ok } = await openPageForVision(row.audit_url, {
            page_types: ["PRODUCT-PAGE"],
            headless: false,
            snooze: snooze * 1000,
            timeout: 10000,
            waitfor: (args.waitfor as WaitFor | undefined) || (!args.snooze && !args.pause ? "networkidle" : undefined)
        });
        generate_record.analyze.push(...analyze.map(obj => ({ ...obj, report_url })));
        if (!ok || !page) {
            console.log(chalk.gray(`Unable to open page ${error_code}`));
            if (!test)
                await generate_record.save({ error_code, message });
            await closeLogStream(stream, { analyze: generate_record.analyze, console: true });
            const { summary } = summarize(generate_record.analyze);
            console.log(chalk.gray(summary));
            console.log(chalk.gray(`REPORT: ${report_url}`));
            continue;
        }

        const analyze_result = await analyzeRegression({
            page,
            live: true,
            name: `AUTOHEAL ${row.page_id}`,
            context: {
                domain_name: row.domain_name,
                capture_url: row.audit_url,
                template_path: row.template_path,
                screenshot_url: row.screenshot_url,
                selectors: [{
                    selector_name: row.selector_name,
                    selector_json_value: row.selector_json_value,
                    reference_json_value: row.reference_json_value,
                    regression: true
                }]
            },
            selector_profile: "brand-monitor-product-page",
            targets: [row.selector_name], // only one generated selector for now
            template,
            stream
        });

        analyze_result.analyze.forEach(record => record.report_url = report_url);
        generate_record.analyze.push(...analyze_result.analyze);

        await closeLogStream(stream, { analyze: analyze_result.analyze, console: true });
        unhookConsoleLog();
        const { summary } = summarize(analyze_result.analyze);
        console.log(chalk.gray(summary));
        console.log(chalk.gray(`REPORT: ${report_url}`));

        if (!analyze_result.ok) {
            const error_code = analyze_result.code || "analyze-general-error";
            console.log(chalk.gray(`analyze failed [${error_code}]`));
            if (analyze_result.message)
                console.log(chalk.gray.italic(analyze_result.message));
            if (!test)
                await generate_record.save({ error_code, message: analyze_result.message });
            continue;
        }

        const [generated_selector] = analyze_result.generated_selectors; // only one generated selector for now
        if (generated_selector.ok) {
            valid_generated_selectors.push({
                selector_name: generated_selector.selector_name,
                selector: generated_selector.selector!,
                url: row.audit_url,
                template,
                template_path: row.template_path,
                report_url
            });
            if (!test)
                await generate_record.save({ generated_selector: generated_selector.selector });
            console.log(chalk.gray(`added valid selector for ${generated_selector.selector_name}`));
        }
        else {
            if (!test)
                await generate_record.save({
                    generated_selector: generated_selector.selector,
                    error_code: "generate-error",
                    message: generated_selector.explain
                });
            console.log(chalk.gray(generated_selector.explain));
        }
    }

    if (valid_generated_selectors.length === 0) {
        console.log(chalk.gray(`no valid selectors generated`));
        continue;
    }

    console.log();
    const updates: SelectorUpdate[] = [];
    const set = new Set<string>();
    for (const { selector_name, selector, url } of valid_generated_selectors) {
        if (!set.has(selector_name)) {
            console.log(chalk.gray(`added: ${selector_name} ${selector} from ${url}`));
            console.log(chalk.gray(selector));
            updates.push({ selector_name, selector });
            counters.generated += 1;
        }
        else {
            console.log(chalk.gray(`skipped: ${selector_name} ${selector} from ${url}`));
        }
    }

    const [{ template, template_path, report_url }] = valid_generated_selectors;
    console.log();
    console.log(chalk.gray(`template update: ${template_path} (${updates.length} updates)`));

    const update = updateTemplate(template, updates);
    console.log(chalk.gray.italic(update.messages.join("\n")));
    if (!update.ok) {
        console.log(chalk.red("template update failed"));
        continue;
    }

    for (const { selector_name, selector } of updates) {
        console.log();
        console.log(`${selector_name}: ${selector}`);
        const { before, after } = update.commits.find(commit => commit.selector_name === selector_name) || {};
        if (before && after) {
            console.log(`before: ${before}`);
            console.log(`after: ${after}`);
        }
        else if (after) {
            console.log(`before: (none)`);
            console.log(`after: ${after}`);
        }
        else {
            console.log(`(no change)`);
        }
    }

    const commit_obj: CommitOptions = {
        template_path,
        analyze_url: report_url,
        updates
    };

    counters.updates += 1;
    if (!args.commit) {
        await fs.writeFile("./commit.jsonc", JSON.stringify(commit_obj, null, 2));
        playAudio("notify");
        process.stdout.write(chalk.cyan("commit? [y/n] "));
        const keypress = await waitForKeypress();
        process.stdout.write("\n");
        if (keypress.name.toLowerCase() === "y")
            await commit(commit_obj);
    }
    else {
        await commit(commit_obj);
        counters.commits += 1;
    }
}

console.log();
console.log(chalk.gray([
    `${keys.length} sellers`,
    `${rows.length} audits`,
    `${counters.updates} template updates`,
    `${counters.generated} generated selectors`,
    `${counters.commits} commits`    
].join(", ")));

process.exit(0);
