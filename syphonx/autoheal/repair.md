# Repair Process
- open live page
- quick classify page
- label all visible page elements
- locate content (AI)
- for each located content item...
    - extract slice from html using label
- for each targeted content item...
    - slice
    - remove hashed identifiers in slice HTML (AI)
    - generate selector (AI)
    - test selector with syphonx
    - qualify selector (AI)
    - auto-suggest advanced selectors (AI)
- qualify selector (AI)
- verify selector
- update template with generatd selectors
- for each regression
    - verify template with syphonx
- if verification passed...
    - commit template change
