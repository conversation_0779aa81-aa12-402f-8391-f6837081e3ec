import {
    bigquery,
    renderImageFingerprint
}
from "./lib/index.js";

const rows = await bigquery.query(`SELECT DISTINCT page_fingerprint FROM temp.page_fingerprints LEFT JOIN temp.page_fingerprint_images USING (page_fingerprint) WHERE page_fingerprint_image IS NULL`);
const page_fingerprints = rows.map(row => row.page_fingerprint);

for (const page_fingerprint of page_fingerprints) {
    const page_fingerprint_image = await renderImageFingerprint(page_fingerprint);
    await bigquery.insert("temp.page_fingerprint_images", { page_fingerprint, page_fingerprint_image });
}

console.log(`${page_fingerprints.length} rows inserted`);
process.exit();
