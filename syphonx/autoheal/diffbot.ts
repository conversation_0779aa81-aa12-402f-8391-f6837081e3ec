import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";
import { diffbot, parseArgs } from "./lib/index.js";

const args = parseArgs({
    required: {
        url: "page to analyze"
    }
});

const [response, request_url] = await diffbot.product(args.url, { fields: ["breadcrumb", "content", "dom", "meta"] });
console.log(chalk.gray(request_url));
console.log(JSON.stringify(response, null, 2));
