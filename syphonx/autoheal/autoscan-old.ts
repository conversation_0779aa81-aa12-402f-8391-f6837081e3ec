import * as dotenv from "dotenv";
dotenv.config();

import * as async from "async-parallel";
import chalk from "chalk";

import {
    bigquery,
    generateTimestamp,
    openPageForVision,
    parseArgs,
    renderScreenshotToStorageFile,
    setAutogenProfile,
    shuffle,
    sleep
} from "./lib/index.js";

const args = parseArgs({
    optional: {
        domain: "domain names to include, comma separated",
        limit: "max number of rows",
        snooze: "snooze duration after page open in seconds (default=none)",
        timeout: "timeout duration in seconds (default=10)",
        concurrency: "number of analyze instances to run concurrently (default=1)",
        force: "force override recent scans",
        blocks: "block limit (default=3)",
        fails: "fail limit (default=10)",
        cooldown: "seconds to cooldown after blocked (default=10)",
        "disable-autoclose": "diable autoclose"
    }
});

const options = {
    headless: false,
    scroll: false,
    snooze: args.snooze ? parseInt(args.snooze) * 1000 : undefined,
    timeout: args.timeout ? parseInt(args.timeout) * 1000 : 10000,
    waitfor: "networkidle" as const
};

const concurrency = parseInt(args.concurrency) || 1;
const max_blocked = parseInt(args.blocks) || 3;
const max_failed = parseInt(args.fails) || 10;
const cooldown = args.cooldown ? parseInt(args.cooldown) : 10;

if (concurrency !== 1)
    process.env.VERBOSE = undefined;

setAutogenProfile("gemini-economy");

const where = []
if (!args.force)
    where.push("(last_scanned_at IS NULL OR last_scanned_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 1 DAY))");
if (args.domain)
    where.push(`domain_name IN (${args.domain.split(",").map(domain => bigquery.safeValue(domain)).join(", ")})`);
const query = `SELECT * FROM temp.autoscan_input${where.length > 0 ? ` WHERE ${where.join(" AND ")}` : ""}${args.limit ? ` LIMIT ${parseInt(args.limit)}` : ""}`;

const rows = shuffle(await bigquery.query(query));
console.log(chalk.gray(`${rows.length} rows returned`));

interface Domain {
    succeeded: number;
    failed: number;
    blocked: number;
}
const domains: Record<string, Domain> = {};
const domain_names = new Set(rows.map(row => row.domain_name));
domain_names.forEach(key => domains[key] = { succeeded: 0, failed: 0, blocked: 0 });

let i = 0, succeeded = 0, failed = 0, skipped = 0, blocked = 0;
await async.each(rows, async row => {
    const { url, url_hash, domain_name } = row;
    const domain = domains[domain_name];
    if (domain.blocked >= max_blocked) {
        skipped += 1;
        return; // stop scanning domain after 3 blocks
    }
    if (domain.failed >= max_failed) {
        skipped += 1;
        return; // stop scanning domain after 10 failures
    }

    if (concurrency === 1)
        console.log();
    console.log(chalk.white(`[${++i}/${rows.length}] ${url}`));

    const timestamp = generateTimestamp("seconds");
    const result = await openPageForVision(url, {
        ...options,
        report_path: `autoscan/${timestamp}.html`,
        autoclose: args["disable-autoclose"] ? false : true,
        classify: domains[domain_name].succeeded <= 3 // stop using AI to autoclose after 3 scans for the same domain (relying on selectors only from that point onward)
    });

    const { page_class: classification, explain, message, report_url, analyze, browser, page, ok } = result;
    if (classification === "BLOCKED") {
        blocked += 1;
        domain.blocked += 1;
        if (domain.blocked >= max_blocked) {
            console.log(chalk.red(`${domain_name} block limit exceeded`));
        }
        else if (cooldown) {
            console.log(chalk.gray(`BLOCKED: ${cooldown} second cooldown`));
            await sleep(cooldown * 1000);
        }
        if (page) {
            await page.close();
            await browser!.close();
        }
        return; // don't record blocked page result
    }

    const [{ model, tokens, cost, elapsed }] = analyze.length > 0 ? analyze : [{} as Record<string, unknown>];
    let screenshot_url = undefined;
    if (page && ok) {
        screenshot_url = await renderScreenshotToStorageFile(`autoscan/${timestamp}.png`, page);
        await page.close();
        await browser!.close();
    }

    const data = {
        domain_name,
        page_url: url,
        page_class: classification,
        data: result.page_data ? JSON.stringify(result.page_data) : undefined, // todo: rename to page_data
        explain: explain || message,
        report_url,
        screenshot_url,
        elapsed,
        model,
        tokens,
        cost,
        url_hash,
        ok: result.ok,
        message: result.message,
        analyze: result.analyze,
        timestamp: new Date()
    };

    await bigquery.insert("temp.autoscan", data);
    if (ok) {
        succeeded += 1;
        domain.succeeded += 1;
    }
    else {
        failed += 1;
        domain.failed += 1;
        if (domain.blocked >= max_failed)
            console.log(chalk.red(`${domain_name} failed limit exceeded`));
    }
}, concurrency);

console.log();
console.log(chalk.gray(`${rows.length} pages scanned, ${succeeded} succeeded, ${failed} failed, ${blocked} blocked, ${skipped} skipped`));
process.exit(0);
