import * as dotenv from "dotenv";
dotenv.config();

import {
    blacklist_reason_codes,
    insertBlacklist,
    parseArgs,
    queryBlacklist
}
from "./lib/index.js";

const params = parseArgs({
    optional: {
        add: `adds a seller to the blacklist, example: --add="Amazon (US)" --reason=blocked-captcha`,
        reason: blacklist_reason_codes.join(", "),
    },
    validate: params => {
        if (params.add) {
            if (!params.reason)
                return `specify one of the following for reason-code: ${blacklist_reason_codes.join(", ")}`;
            if (!blacklist_reason_codes.includes(params.reason))
                return `invalid reason code specified, specify one of the following: ${blacklist_reason_codes.join(", ")}`;
        }
    }
});

if (params.add) {
    await insertBlacklist({ seller_name: params.add, reason_code: params.reason });
    console.log("1 record inserted");
}
else {
    const rows = await queryBlacklist();
    console.log(rows.map(row => `${row.seller_name}: ${row.reason_code}`).join("\n"));
    console.log(`${rows.length} rows`);
}

process.exit(0);
