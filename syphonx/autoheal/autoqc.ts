import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";

import {
    analyzeProductPageScreenshot,
    bigquery,
    datauriToPng,
    diffDate,
    generateUniqueId,
    isEmpty,
    parseArgs,
    parseDomainName,
    reformatHtml,
    saveToStorageFile,
    summarize,
    downloadClipImage,
    tryCreateScreenshot,
    TryScreenshotResult,
    Timer
} from "./lib/index.js";

export interface AnalyzeResponse {
    scan_result: ScanResult[];
    summary: string;
    status: string;
}

export interface ScanResult {
    name: string;
    value: string | number | boolean;
    labels: string;
    explain: string;
}

const args = parseArgs({
    optional: {
        account: "comma seperated list of accounts to analyze",
        seller: "comma seperated list of seller-ids to analyze, analyzes all if not specified, use --seller=-2 to exclude a seller",
        n: "number of pages per seller to analyze (default=10)",
        preview: "preview pages to be analyzed without doing any work",
        recent: "deprioritizes pages that have been analyzed in the specified number of days (default=7)",
        browser: "re-generate a screenshot instead of using the captured one",
        status: "show status of recent scans",
        test: "run but don't record any audit results",
        verbose: "verbose log output"
    },
    validate: args => {
        if (!args.account && !args.status)
            return "account not specified";
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

if (args.status) {
    const rows = await bigquery.query("SELECT * FROM autoqc.product_page_status");
    for (const row of rows)
        console.log(chalk.gray(`${row.seller_name} ${row.seller_id}: ${row.scan_count} scanned, ${row.flagged_count} flagged`));
    process.exit(0);
}

const accounts = args.account.split(",").map(name => `'${name.trim()}'`).join(",");
const sellers = args.seller?.split(",").map(id => Math.abs(parseInt(id))).filter(id => !isNaN(id)).join(",") || "";
const exclude_sellers = args.seller?.startsWith("-");
const test = !!args.test;
const n = parseInt(args.n) || 10;
const concurrency = parseInt(args.concurrency) || 1;
const recent = parseInt(args.recent) || 7;

const tile = 512;
const width = 3 * tile;
const height = 3 * tile;

const timer = new Timer();
const query = `SELECT * FROM autoqc.create_product_page_scan([${accounts}], [${exclude_sellers ? "" : sellers}], ${n}, ${recent})`;
if (process.env.VERBOSE)
    console.log(chalk.gray(query));

const rows = await bigquery.query(query);

if (process.env.VERBOSE)
    console.log(chalk.gray(`${rows.length} rows returned in ${Math.round(timer.elapsed() / 1000)} seconds`));

const total = {
    scanned: 0,
    skipped: 0,
    errors: 0,
    consecutive: 0,
    cost: 0,
    usage: 0
}

const seller_map: Record<string, number> = {};
for (const row of rows) {
    const key = `${row.seller_name} ${row.seller_id}`;
    if (!seller_map[key])
        seller_map[key] = 1;
    else
        seller_map[key] += 1;
}
for (const key of Object.keys(seller_map))
    console.log(chalk.gray(`${key}: ${seller_map[key]}`));
console.log();

if (args.preview) {
    let seller = "";
    for (const row of rows) {
        if (seller !== row.seller_name) {
            seller = row.seller_name;
            console.log();
            console.log(chalk.cyan.underline(`${row.seller_name}, id=${row.seller_id}, n=${rows.filter(row => row.seller_name === seller).length}`));
        }
        let warning = "";
        if (isEmpty(row.conflux_data?.selectors))
            warning = "no conflux data";
        else if (!row.conflux_data.screenshot_url)
            warning = "no screenshot";
        else if (row.last_scan_date)
            warning = `last scanned ${diffDate(row.last_scan_date)} ago`;
        console.log(chalk.white(row.page_id), chalk.gray(row.capture_url), warning ? chalk.yellow.italic(`(${warning})`) : "");
    }
        
    process.exit(0);
}

for (const row of rows) {
    const scan_id = generateUniqueId();
    const scan_date = new Date();

    const {
        account_key,
        country_code,
        sku,
        seller_name,
        seller_id,
        page_id,
        capture_url: scan_url,
        conflux_data,
        ...product_data
    } = row;

    const data = {
        scan_id,
        scan_date,
        account_key,
        country_code,
        sku,
        seller_name,
        seller_id,
        page_id,
        scan_url,
        domain_name: parseDomainName(scan_url),
        conflux_data: {
            capture_status: conflux_data.capture_status,
            screenshot_url: conflux_data.screenshot_url,
            selectors: conflux_data.selectors
        },
        product_data: JSON.stringify(product_data)
    };
    
    const excluded = exclude_sellers && sellers.includes(row.seller_id);
    if (exclude_sellers && sellers.includes(row.seller_id)) {
        console.log(chalk.white.strikethrough(`[${rows.indexOf(row) + 1}/${rows.length}] ${page_id} SKIPPED`));
        total.skipped += 1;
        continue;
    }

    if (concurrency === 1)
        console.log(chalk.white(`[${rows.indexOf(row) + 1}/${rows.length}] ${page_id} ${excluded ? "EXCLUDED" : ""}`));

    if (isEmpty(row.conflux_data?.selectors)) {
        Object.assign(data, {
            status: "NO-CONFLUX-DATA",
            summary: `No conflux data for page ${page_id}`
        });
        if (!test)
            await bigquery.insert("autoqc.product_page_scans", data);
        if (concurrency === 1)
            console.log(chalk.gray(`${scan_id} inserted`, chalk.yellow.italic("(no conflux data)")));
        else
            console.log(chalk.white(`[${rows.indexOf(row) + 1}/${rows.length}] ${page_id} ${scan_id} inserted`), chalk.yellow.italic("(no conflux data)"));
        continue;
    }

    try {
        let screenshot: TryScreenshotResult;
        if (!args.browser) {
            if (!row.conflux_data.screenshot_url) {
                Object.assign(data, {
                    status: "NO-SCREENSHOT",
                    summary: `No screenshot for capture ${product_data.capture_id}`
                });
                if (!test)
                    await bigquery.insert("autoqc.product_page_scans", data);
                if (concurrency === 1)
                    console.log(chalk.gray(`${scan_id} inserted`, chalk.yellow.italic("(no screenshot)")));
                else
                    console.log(chalk.white(`[${rows.indexOf(row) + 1}/${rows.length}] ${page_id} ${scan_id} inserted`), chalk.yellow.italic("(no screenshot)"));
                continue;
            }

            screenshot = await downloadClipImage(row.conflux_data.screenshot_url, { width, height });
            // workaround for dipshitery
            if (!screenshot.ok && row.conflux_data.alt_screenshot_url) {
                row.conflux_data.screenshot_url = row.conflux_data.alt_screenshot_url;
                console.log(chalk.gray(`trying alternate url ${row.conflux_data.screenshot_url}`));
                screenshot = await downloadClipImage(row.conflux_data.screenshot_url, { width, height });
                if (!screenshot.ok) {
                    const [,daynum] = /\d{4}\/(\d{1,3})\//.exec(row.conflux_data.screenshot_url) || [];
                    if (daynum) {
                        row.conflux_data.screenshot_url = (row.conflux_data.alt_screenshot_url as string).replace(daynum, `${parseInt(daynum)-1}`);
                        console.log(chalk.gray(`trying alternate url ${row.conflux_data.screenshot_url}`));
                        screenshot = await downloadClipImage(row.conflux_data.screenshot_url, { width, height });
                        if (!screenshot.ok) {
                            row.conflux_data.screenshot_url = (row.conflux_data.alt_screenshot_url as string).replace(daynum, `${parseInt(daynum)+1}`);
                            console.log(chalk.gray(`trying alternate url ${row.conflux_data.screenshot_url}`));
                            screenshot = await downloadClipImage(row.conflux_data.screenshot_url, { width, height });
                        }
                    }
                }
                
            }
        }
        else {
            if (concurrency === 1)
                console.log(chalk.gray(`opening page ${scan_url}`));
            screenshot = await tryCreateScreenshot(scan_url, {
                headless: false,
                labels: true,
                scroll: false,
                snooze: 4,
                viewport: { width, height }
            });
        }
    
        if (!screenshot.datauri) {
            Object.assign(data, {
                status: "SCREENSHOT-ERROR",
                summary: `Error generating screenshot for capture ${product_data.capture_id}\n${screenshot.error}`
            });
            if (!test)
                await bigquery.insert("autoqc.product_page_scans", data);
            console.log(chalk.red(`failed to capture screenshot for page ${page_id} ${screenshot.error}`));
            continue;
        }
    
        const screenshot_url = await saveToStorageFile(`screenshots/${scan_id}.png`, datauriToPng(screenshot.datauri));
        if (concurrency === 1)
            console.log(chalk.gray(`screenshot ${screenshot_url} [${screenshot.width}x${screenshot.height}]`));
        if (screenshot.html) {
            const html = reformatHtml(screenshot.html, scan_url);
            await saveToStorageFile(`screenshots/${scan_id}.html`, html);
        }
        
        const context = { domain: data.domain_name };
        const result = await analyzeProductPageScreenshot(screenshot.datauri, { context });
    
        const summary = summarize(result.analyze);
        Object.assign(data, {
            summary: result.summary,
            status: result.status,
            scan_result: result.scan_result ? Object.keys(result.scan_result).map(key => ({
                name: key,
                value: JSON.stringify(result.scan_result![key].value),
                explain: result.scan_result![key].explain
            })) : [],
            screenshot_url,
            model: summary.models.join(", "),
            usage: summary.tokens,
            cost: summary.cost,
            duration: summary.elapsed
        });
    
        if (!test)
            await bigquery.insert("autoqc.product_page_scans", data);
        if (concurrency === 1)
            console.log(chalk.gray(`${scan_id} inserted`));
        else
            console.log(chalk.white(`[${rows.indexOf(row) + 1}/${rows.length}] ${page_id} ${scan_id} inserted`));
    
        total.scanned += 1;
        total.consecutive = 0;
        total.cost += summary.cost;
        total.usage += summary.tokens;
    }
    catch (err) {
        Object.assign(data, {
            status: "ERROR",
            summary: err instanceof Error ? err.message : JSON.stringify(err)
        });
        if (!test)
            await bigquery.insert("autoqc.product_page_scans", data);
        total.errors += 1;
        total.consecutive += 1;
        if (total.consecutive >= 5) {
            console.log(chalk.red(`${total.consecutive} consecutive errors, aborting...`));
            break;
        }
    }
}

console.log();
console.log(chalk.gray(`${total.scanned} pages scanned, usage: ${total.usage} ($${total.cost.toFixed(3)})`));
console.log(chalk.gray(`${rows.length} rows inserted in ${Math.round(timer.elapsed() / 1000)} seconds`));
process.exit(0);
