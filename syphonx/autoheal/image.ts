import * as dotenv from "dotenv";
dotenv.config();

import {
    autogenProfile,
    createThumbnailImage,
    tryLoadJsonFile,
    viewHtml,
    waitForKeypress,
    LLMChat
}
from "./lib/index.js";

interface Params {
    prompt: string;
    image: string;
    image_detail?: "auto" | "low" | "high";
    max_tokens?: number;
    thumbnail_size?: number;
    json?: boolean;
}

const params = await tryLoadJsonFile("./image.jsonc") as Params;
if (!params) {
    console.log("analyze.jsonc file not found");
    process.exit(0);
}

console.log("downloading image...");
const image = await createThumbnailImage({ url: params.image, size: params.thumbnail_size || 512 });

const model = autogenProfile().classifyScreenshot.model;
console.log(`analyzing image... (using model ${model})`);
const chat = new LLMChat(model);
const response = await chat.prompt(params.prompt, { images: [image] });

await viewHtml({
    html: `
        <!doctype html>
        <html>
            <body>
                <h3>${params.prompt}</h3>
                <div>${response} <small><i>${chat.tokens} ($${chat.cost.toFixed(5)})</i></small></div>
                <img src="${image}">
            </body>
        </html>`,
    zoom: 1.5
});

console.log("Press any key to continue...");
await waitForKeypress();
process.exit(0);
