# Autogen SyphonX Template Diffbot

## Usage
```
node autogen --url=http://bicyclewarehouse.com/cdn/shop/products/MY22Stance292_ColorAGunmetalBlack.jpg?v=1679368219 --profile=default
```

## Autogen LLM
- name


## Autogen Diffbot

### Selectors
- name
- price
- description
- sku

> no review_score, review_count, in_stock, stock_status

### Notes
- runs in 2-3 seconds
- diffbot reliability
- cost 1 diffbot token per run ($0.009/1K)
- diffbot doesn't extract review-count/review-score
- diffbot only returns xpath to images
- using text content traversals from main image to infer other selectors (may be incorrect)
- fixed selector targeting
- generate basic css-selector style selectors only
- does not exclude hashed identifers
