*** autocat-complete do COPY at the end and remove sleep


*** add the batch_id to the completions table

*** integrate pending into batch table so we can deal with adding more pending while batches are in-progress

drive autocat-complete from a table
    - create a table to track batches (DONE)
    - insert submitted batches from autocat-batch (DONE)
    - check for completed batches from autocat-complete
    - automatically download batch from autocat-complete
    - update batch table

