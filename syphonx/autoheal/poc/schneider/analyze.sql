CREATE OR R<PERSON>LACE VIEW autoqc.ai_analyze_view
AS
SELECT
  a.page_id,
  b.country_code,
  b.account_key,
  b.seller_id,
  b.seller_name,
  b.sku,

  a.summary,
  a.usage,
  ROUND(a.cost, 2) AS cost,
  a.duration,

  SAFE_CAST(b.price AS STRING) AS price,
  (SELECT LAX_STRING(value) FROM UNNEST(a.data) WHERE name='price' LIMIT 1) AS price_ref,
  (SELECT regression FROM UNNEST(c.selectors) WHERE selector_name='price' LIMIT 1) AS price_regression,
  (SELECT SAFE_CAST(selector_json_value AS STRING) FROM UNNEST(selectors) WHERE selector_name='price' LIMIT 1) AS price_crawler_dq,
  (SELECT SAFE_CAST(reference.selector_json_value AS STRING) FROM UNNEST(selectors) WHERE selector_name='price' LIMIT 1) AS price_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(a.data) WHERE name='price' LIMIT 1) AS price_explain,
  
  SAFE_CAST(b.in_stock AS BOOL) AS in_stock,
  (SELECT LAX_BOOL(value) FROM UNNEST(a.data) WHERE name='in_stock' LIMIT 1) AS in_stock_ref,
  (SELECT regression FROM UNNEST(c.selectors) WHERE selector_name='in_stock' LIMIT 1) AS in_stock_regression,
  (SELECT SAFE_CAST(selector_json_value AS BOOL) FROM UNNEST(selectors) WHERE selector_name='in_stock' LIMIT 1) AS in_stock_crawler_dq,
  (SELECT SAFE_CAST(reference.selector_json_value AS BOOL) FROM UNNEST(selectors) WHERE selector_name='in_stock' LIMIT 1) AS in_stock_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(a.data) WHERE name='in_stock' LIMIT 1) AS in_stock_explain,

  SAFE_CAST(b.image_count AS INT64) AS image_count,
  (SELECT LAX_INT64(value) FROM UNNEST(a.data) WHERE name='image_count' LIMIT 1) AS image_count_ref,
  (SELECT regression FROM UNNEST(c.selectors) WHERE selector_name='images' LIMIT 1) AS image_count_regression,
  (SELECT ARRAY_LENGTH(JSON_QUERY_ARRAY(selector_json_value)) FROM UNNEST(selectors) WHERE selector_name='images' LIMIT 1) AS image_count_crawler_dq,
  (SELECT ARRAY_LENGTH(JSON_QUERY_ARRAY(reference.selector_json_value)) FROM UNNEST(selectors) WHERE selector_name='images' LIMIT 1) AS image_count_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(a.data) WHERE name='image_count' LIMIT 1) AS image_count_explain,

  SAFE_CAST(b.review_score AS FLOAT64) AS review_score,
  (SELECT LAX_FLOAT64(value) FROM UNNEST(a.data) WHERE name='review_score' LIMIT 1) AS review_score_ref,
  (SELECT regression FROM UNNEST(c.selectors) WHERE selector_name='review_score' LIMIT 1) AS review_score_regression,
  (SELECT SAFE_CAST(selector_json_value AS FLOAT64) FROM UNNEST(selectors) WHERE selector_name='review_score' LIMIT 1) AS review_score_crawler_dq,
  (SELECT SAFE_CAST(reference.selector_json_value AS FLOAT64) FROM UNNEST(selectors) WHERE selector_name='review_score' LIMIT 1) AS review_score_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(a.data) WHERE name='review_score' LIMIT 1) AS review_score_explain,

  SAFE_CAST(b.review_count AS INT64) AS review_count,
  (SELECT LAX_INT64(value) FROM UNNEST(a.data) WHERE name='review_count' LIMIT 1) AS review_count_ref,
  (SELECT regression FROM UNNEST(c.selectors) WHERE selector_name='review_count' LIMIT 1) AS review_count_regression,
  (SELECT SAFE_CAST(selector_json_value AS FLOAT64) FROM UNNEST(selectors) WHERE selector_name='review_count' LIMIT 1) AS review_count_crawler_dq,
  (SELECT SAFE_CAST(reference.selector_json_value AS FLOAT64) FROM UNNEST(selectors) WHERE selector_name='review_count' LIMIT 1) AS review_count_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(a.data) WHERE name='review_count' LIMIT 1) AS review_count_explain,

  c.capture_status,
  a.popups,
  a.capture_url,
  a.screenshot_url,
  b.capture_id AS production_capture_id,
  b.capture_date AS production_capture_date,
  a.capture_id AS ai_capture_id,
  a.capture_date AS ai_capture_date,
  IF(d.flagged_fields IS NULL, a.status, 'FLAGGED') AS status,
  IF(d.flagged_fields IS NULL, a.status_explain, d.flagged_fields) AS status_explain

FROM autoqc.product_page_captures_gpt AS a
JOIN autoqc.schneider_product_page AS b USING (page_id)
LEFT JOIN conflux.product_page_daily AS c USING (page_id)
LEFT JOIN autoqc.user_flagged_fields AS d ON d.capture_id=a.capture_id
WHERE c.date>=CURRENT_DATE()
AND c.app_name='brand_monitor'
AND c.account_key='schneider_electric'
QUALIFY ROW_NUMBER() OVER (PARTITION BY a.page_id ORDER BY a.capture_date DESC) = 1;