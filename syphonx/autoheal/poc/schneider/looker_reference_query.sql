SELECT
    product_page.country_code  AS product_page_country_code,
    product_page.country_code  AS product_page_country_icon,
    product_page.product_group  AS product_page_product_group,
    product_page.product_name  AS product_page_product_name,
    product_page.sku  AS product_page_sku,
    product_page.seller_name  AS product_page_seller_name,
    product_page.source  AS product_page_source,
    product_page.seller_id  AS product_page_seller_id,
    product_page.seller_name  AS product_page_retailer_logo,
    product_page.last_capture.metascore  AS product_page_metascore_nonaggregated,
    currency_codes.currency_symbol  AS currency_codes_currency_symbol,
    IFNULL((SAFE_CAST(product_page.msrp AS FLOAT64)), -1)  AS product_page_brand_price_icon,
    SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.price_data, "$.was_price") AS FLOAT64)  AS product_page_was_price,
    SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.price_data, "$.price") AS FLOAT64)  AS product_page_price,
    product_page.last_capture.price_score  AS product_page_price_metascore_nonaggregated,
    IF(ARRAY_LENGTH(product_page.last_capture.errors)>0, TO_JSON_STRING(product_page.last_capture.errors), NULL)  AS product_page_errors,
        (CASE WHEN SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.availability_data, "$.in_stock") AS BOOLEAN)  THEN 'Yes' ELSE 'No' END) AS product_page_in_stock,
        (CASE WHEN product_page.last_capture.discontinued  THEN 'Yes' ELSE 'No' END) AS product_page_discontinued,
    product_page.last_capture.availability_score  AS product_page_availability_metascore_nonaggregated,
    IFNULL(SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.review_data, "$.review_count") AS INT64), 0)  AS product_page_review_count,
    IF((SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.review_data, "$.review_score") AS FLOAT64)) = 0 AND ((IFNULL(SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.review_data, "$.review_count") AS INT64), 0)) = 0 OR (IFNULL(SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.review_data, "$.review_count") AS INT64), 0)) IS NULL), NULL, (SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.review_data, "$.review_score") AS FLOAT64)))  AS product_page_review_score,
    product_page.last_capture.review_score  AS product_page_review_metascore_nonaggregated,
    COALESCE(SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.image_data, "$.image_count") AS INT64), 0)  AS product_page_image_count,
    product_page.last_capture.image_score  AS product_page_image_metascore_nonaggregated,
        (CASE WHEN product_page_selectors.has_enhanced_content  THEN 'Yes' ELSE 'No' END) AS product_page_selectors_has_enhanced_content,
        (CASE WHEN SAFE_CAST(JSON_EXTRACT_SCALAR(product_page.last_capture.content_data, "$.enhanced_content") AS BOOLEAN)  THEN 'Yes' ELSE 'No' END) AS product_page_enhanced_content,
    product_page.last_capture.content_score  AS product_page_content_metascore_nonaggregated,
        (FORMAT_TIMESTAMP('%F %T', product_page.last_capture.capture_date , 'America/Los_Angeles')) AS product_page_collected_date,
        (DATE(product_page.last_capture.capture_date , 'America/Los_Angeles')) AS product_page_capture_date,
    CONCAT('https://bidev.pricespider.com/capture/', product_page.last_capture.capture_id, '/?context=product_page', '&capture_date=', (DATE(product_page.last_capture.capture_date)))  AS product_page_screenshot_url_full,
    product_page.last_capture.capture_url  AS product_page_capture_url,
    COALESCE(SUM(ARRAY_LENGTH( product_page.last_capture.flags  ) ), 0) AS product_page_flag_count
FROM brand_monitor.product_page AS product_page
LEFT JOIN public.currency_codes AS currency_codes ON currency_codes.country_code = product_page.country_code
LEFT JOIN brand_monitor.product_page_selectors AS product_page_selectors ON product_page_selectors.seller_id = product_page.seller_id
      AND product_page_selectors.source = product_page.source
WHERE
  (NOT product_page.last_capture.discontinued OR product_page.last_capture.discontinued IS NULL)
  AND product_page.timestamp BETWEEN TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, 'America/Los_Angeles'), INTERVAL 1 DAY) AND TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, 'America/Los_Angeles')
  AND product_page.account_key = 'schneider_electric'
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31
ORDER BY 5;
