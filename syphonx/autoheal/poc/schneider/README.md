# Overview

Goal is to proactively identify brand-monitor data accuracy issues for <PERSON>.

# How to run
1. Run the [snapshot](snapshot.sql) query to create a snapshot of current product-page data
2. `node extract-gpt` to capture 10 pages per retailer for Schneider
  - takes about an hour
  - data goes into temp.product_page_captures_gpt table
3. Run the [analyze](analyze.sql) query to analyze the results (try differen where clauses)

## Methodology
- automated BM product-page data checker https://bi.pricespider.com/embed/productpagedetails0173
- 10 pages per retailer, excluding walmart, amazon and others
- 80 analyzed out of 1,500
- 8 retailers: dell, insight, staples, shi, connection, bhphoto, officedepot, cdw
- new ai-extractor tool derived from autoheal toolkit
- not based on selectors!
- 1024x1024 viewport only
- playwright + ai-service -> bigquery
- **expirimental, unvetted data**

## Tools

### extract-diffbot
- diffbot api
- limited
  - product-page only
  - has product-name, price, description
  - has seo-data
  - has reviews (iffy)
  - no review-score
  - no screenshots
  - has MPP (early beta)
  - xpath on images only
- $0.90 per thousand

### extract-gpt
- gpt-4-vision
- prompt (new, minimal, intrinsic understanding of product-pages)
- unlimited
- $35 per thousand


# What's next
- more product-page checks (brand_name, was_price, etc.)
- auto-dismiss popups
- unblock amazon, walmart
- solve screenshot timeout problem with Newegg
- other page types (search-page)
- openai batch mode
- diffbot version
