CREATE OR <PERSON><PERSON><PERSON>CE TABLE conflux.page_content
(
  timestamp TIMESTAMP,
  account_key STRING,
  country_code STRING,
  sku STRING,
  seller_name STRING,
  seller_id INT64,
  domain_name STRING,
  page_url STRING,
  title STRING,
  content STRING,
  content_url STRING,
  page_id STRING,
  capture_id STRING
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name;

CREATE OR REPLACE TABLE conflux.page_fingerprint
(
  timestamp TIMESTAMP,
  domain_name STRING,
  fingerprint STRING,
  screenshot_url STRING,
  capture_id STRING
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name;

CREATE OR REPLACE TABLE conflux.page_classify
(
  timestamp TIMESTAMP,
  domain_name STRING,
  fingerprint STRING,
  page_class STRING,
  explain STRING,
  screenshot_url STRING,
  model STRING,
  usage INT64,
  cost FLOAT64,
  duration INT64,
  capture_id STRING
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name;

CREATE OR REPLACE TABLE conflux.page_ngram
(
  timestamp TIMESTAMP,
  domain_name STRING,
  fingerprint STRING,
  ngram STRING
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name;






CREATE OR REPLACE TABLE temp.page_fingerprints
(
  timestamp TIMESTAMP,
  page_id STRING,
  account_key STRING,
  country_code STRING,
  sku STRING,
  seller_name STRING,
  seller_id INT64,
  page_url STRING,
  title STRING,
  content STRING,
  page_fingerprint STRING,
  domain_name STRING,
  capture_id STRING,
  content_url STRING,
  screenshot_url STRING
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name;


CREATE OR REPLACE TABLE temp.page_fingerprint_classify
(
  timestamp TIMESTAMP,
  domain_name STRING,
  page_fingerprint STRING,
  page_class STRING,
  explain STRING,
  screenshot_url STRING,
  model STRING,
  usage INT64,
  cost FLOAT64,
  duration INT64
)
CLUSTER BY domain_name;



CREATE OR REPLACE TABLE temp.page_fingerprint_images
(
  page_fingerprint STRING,
  page_fingerprint_image STRING
);


CREATE OR REPLACE VIEW temp.page_fingerprint_view
AS
SELECT
  domain_name,
  page_fingerprint,
  COUNT(*) AS page_count,
  ANY_VALUE(STRUCT(page_class, explain, a.screenshot_url)).*,
  COUNT(DISTINCT page_class) AS page_class_count
FROM temp.page_fingerprint_classify AS a
LEFT JOIN temp.page_fingerprints USING (domain_name, page_fingerprint)
GROUP BY 1, 2
ORDER BY 1, 3 DESC, 2;
