# autoselect

## Commands
- `node autoselect --app=prowl --account=belkin --domain=target.com --test --verbose` tests auto-generated selectors for Belkin matches at target.com
- `node autoselect --view=target.com` to view auto-generated selectors for target.com

- `node autoselect --url=https://www.staples.com/belkin-usb-c-to-usb-adapter-male-to-female-f2cu036btblk/product_2398836 --verbose`
- `node autoselect --app=prowl --account=belkin --domain=staples.com --random --limit=1`
