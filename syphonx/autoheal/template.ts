import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";

import {
    formatJson,
    formatTable,
    parseArgs,
    queryTemplateChangeHistory,
    tryLoadTemplate
}
from "./lib/index.js";

const params = parseArgs({
    required: {
        0: "template_path",
    },
    optional: {
        history: "show template change history",
        verbose: "verbose log output"
    }
});

if (params.verbose)
    process.env.VERBOSE = "1";

const template_path = params[0];
if (params.history) {
    const rows = await queryTemplateChangeHistory(template_path);
    console.log(formatTable(rows));    
}
else {
    console.log(chalk.gray(`loading template ${template_path}...`));
    const { template, ok: template_ok, error: template_error } = await tryLoadTemplate(template_path);
    if (!template || !template_ok) {
        console.log(chalk.red(`unable to load template ${template_path}${template_error ? ` ${JSON.stringify(template_error)}`: ""}`));
        process.exit(0);
    }    
    console.log(formatJson(template));
}
process.exit(0);
