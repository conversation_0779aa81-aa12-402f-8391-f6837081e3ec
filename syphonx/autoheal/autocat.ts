import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";

import {
    insertProductCategoryCompletions,
    isEmpty,
    parseArgs,
    parseYesNo,
    queryProductCategoryEmbeddings,
    truncate,
    LLMChat,
    LLMModel
} from "./lib/index.js";

import {
    autocatInitialize,
    createAutocatPrompt,
    createCompletion,
    updateProductCategories
} from "./autocat-common.js";

const args = parseArgs({
    optional: {
        reprocess: "specify a product-id to reprocess",
        update: "update changes to final product_category table (default=yes)",
        verbose: "verbose log output"
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

const product_ids = args.reprocess ? args.reprocess.split(",").map(value => parseInt(value)).filter(Boolean) : undefined;
const pending = isEmpty(product_ids);

const model: LLMModel = "gpt-4o-mini";
const limit = 100;
const max_errors = 10;
const max_consecutive_errors = 3;

console.log(chalk.gray("Downloading product categories..."));
await autocatInitialize();

console.log(chalk.gray("Querying product embeddings..."));
let products = await queryProductCategoryEmbeddings({ product_ids, limit, pending });
console.log(chalk.gray(`${products.length} product embeddings returned`));
if (products.length === 0)
    process.exit();

let succeeded = 0;
let errors = 0;
let consecutive_errors = 0;
while (products.length > 0) {
    let i = 0;
    for (const { product_id, product_text, brand_summary, product_vector, category_ids: subcategory_ids } of products) {
        const line_info = `[${++i}/${products.length}] ${truncate(product_text, 60)} (product_id=${product_id})`;
        try {
            const prompt = createAutocatPrompt({ product_text, brand_summary, product_vector, subcategory_ids });
            const chat = new LLMChat(model);
            const content = await chat.prompt(prompt);
    
            const completion = createCompletion({
                prompt,
                content,
                model,
                prompt_tokens: chat.input_tokens,
                completion_tokens: chat.output_tokens,
                total_tokens: chat.tokens,
                product_id
            });

            await insertProductCategoryCompletions([completion]);
            console.log(chalk.gray(line_info));
            succeeded += 1;
            consecutive_errors = 0;
        }
        catch (err) {
            console.log(chalk.red(`${line_info} SKIPPED ${err instanceof Error ? err.message : JSON.stringify(err)}`));
            errors += 1;
            consecutive_errors += 1;
            if (errors >= max_errors || consecutive_errors >= max_consecutive_errors) {
                console.log(chalk.red(`Too many errors, aborting...`));
                console.log(chalk.gray(`${succeeded} completions, ${errors} errors`));
                process.exit();
            }
        }
    }

    if (pending) {
        console.log();
        console.log(chalk.gray("Querying product embeddings..."));
        products = await queryProductCategoryEmbeddings({ limit, pending: true });
        console.log(chalk.gray(`${products.length} product embeddings returned`));
        console.log();
    }
    else {
        break;
    }
}

console.log(chalk.gray(`${succeeded} completions, ${errors} errors`));

if (parseYesNo(args.update, true)) {
    console.log(chalk.gray("Updating product_category table..."));
    await updateProductCategories();        
}

process.exit();
