import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";
import makeDir from "make-dir";
import open from "open";
import { Template } from "syphonx-lib";
import { promises as fs } from "fs";

import {
    analyzeRegression,
    cacheKey,
    createRemoteLogStream,
    closeLogStream,
    commit,
    diffDays,
    downloadHtmlCaptures,
    generateTimestamp,
    hookConsoleLog,
    loadTemplates,
    openPageForVision,
    parseArgs,
    queryRegressions,
    sleep,
    sum,
    truncate,
    unhookConsoleLog,
    updateTemplate,
    waitForKeypress,
    CommitOptions,
    WaitFor
}
from "./lib/index.js";

interface GeneratedSelector {
    selector_name: string;
    selector: string;
    template: Template;
}

const params = parseArgs({
    required: {
        0: "brand_monitor, wtb, prowl",
        1: "selector name target, comma seperated (default=all)",
        seller: "seller-id filter, one only"
    },
    optional: {
        account: "account-key filter, one only",
        country: "country-code filter, one only",
        engine: "capture engine (conflux, syphonx, puppeteer), default=all",
        sku: "a regex filter reducing the result set to a set of skus",
        max: "max number of pages to view (default=500)",
        cached: "use cached html content instead of live page",
        headless: "hide browser window (default=false)",
        pause: "pause for user input after each page load (ignored if cached option is used)",
        snooze: "amount of time in seconds to snooze after loading a live page (default=2) (ignored if cached or pause option is used)",
        waitfor: "specify load, domcontentloaded, or networkidle (default=none)",
        preview: "preview query only, do not run",
        verbose: "verbose log output"
        //commit: "prompt to commit template changes"
    },
    validate: params => {
        if (!["brand_monitor", "wtb", "prowl"].includes(params[0]))
            return "specify brand_monitor, wtb, or prowl only";
        if (params[1].includes(","))
            return "only one target selector allowed"
    }
});

if (params.verbose)
    process.env.VERBOSE = "1";

if (!params.max)
    params.max = "1";

const snooze = parseInt(params.snooze) || 4;

const rows = await queryRegressions({
    app_name: params[0],
    account_key: params.account,
    country_code: params.country,
    seller_id: parseInt(params.seller),
    selector_name: params[1].split(","),
    engine: params.engine,
    max: parseInt(params.max),
    sku: params.sku
});

console.log(chalk.gray(`${rows.length} pages, ${sum(rows, "regression_count")} regressions`));
if (rows.length === 0)
    process.exit(0);

const targets = params[1]?.split(",");
console.log(chalk.gray(`targets: ${targets ? targets.join(", ") : "(all)"}`));
const hits = rows.filter(row => row.selectors.filter(selector => selector.regression && (!targets || targets.includes(selector.selector_name))));
console.log(chalk.gray(`hits: ${hits.length}`));
console.log("");

if (params.preview)
    process.exit(0);

const template_paths = Array.from(new Set(rows.filter(row => /pricespider\//.test(row.template_path)).map(row => row.template_path)));
const templates = await loadTemplates(template_paths);

const html_cache = params.cached ? await downloadHtmlCaptures(rows.map(row => ({ capture_url: row.capture_url, html_url: row.last_capture.html_url }))) : undefined;
const seller_names = Array.from(new Set(rows.filter(row => /pricespider\//.test(row.template_path)).map(row => `${row.seller_name} (${row.country_code})`)));
const timestamp = generateTimestamp("minutes"); 
const out_dir = `analyze/${timestamp}`;
await makeDir(out_dir);

const { stream: index, url: report_url } = await createRemoteLogStream(`${out_dir}/index.html`, { title: "REPAIR", subtitle: `${params.app} at ${seller_names.join(", ")}` });
const result = {
    generated_selectors: [] as GeneratedSelector[],
    errors: [] as string[]
};

for (const row of rows) {
    console.log(row.name);
    const file = `${cacheKey(row.name)}.html`;
    const template = templates[row.template_path];
    if (!template) {
        index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i>Unable to find template</i></a>\n`);
        continue;
    }

    const options = {
        headless: !!params.headless,
        timeout: 10000,
        waitfor: (params.waitfor as WaitFor | undefined) || (!params.snooze && !params.pause ? "networkidle" : undefined)
    };

    const url = params.cached ? html_cache![row.last_capture.html_url] : row.capture_url;
    console.log(chalk.gray(`opening page... ${url}`));
    const { page } = await openPageForVision(url, {
        page_types: ["PRODUCT-PAGE"],
        headless: !!params.headless,
        timeout: 10000,
        waitfor: (params.waitfor as WaitFor | undefined) || (!params.snooze && !params.pause ? "networkidle" : undefined)
    });
    if (!page) {
        index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i>Unable to load content</i></a>\n`);
        continue;
    }

    if (!params.cached) {
        if (params.pause) {
            console.log("press any key to continue...");
            await waitForKeypress();
        }
        else if (snooze) {
            console.log(chalk.gray(`snoozing for ${snooze} seconds`));
            await sleep(snooze * 1000);
        }
    }

    const { stream, url: analyze_url } = createRemoteLogStream(`${out_dir}/${file}`, { title: "REPAIR", subtitle: `${params.app} ${row.name}` });
    hookConsoleLog();
    try {
        const analyze_result = await analyzeRegression({
            page,
            live: !params.cached,
            name: `AUTOHEAL ${params.app} ${row.name}`,
            context: {
                domain_name: row.domain_name,
                capture_url: row.capture_url,
                template_path: row.template_path,
                screenshot_url: row.last_capture.screenshot_url,
                selectors: row.selectors
            },
            selector_profile: "brand-monitor-product-page",
            targets,
            template,
            stream
        });
        await closeLogStream(stream, { analyze: analyze_result.analyze, console: true });
        unhookConsoleLog();

        if (analyze_result.ok) {
            for (const generated_selector of analyze_result.generated_selectors) {
                if (generated_selector.selector) {
                    result.generated_selectors.push({
                        selector_name: generated_selector.selector_name,
                        selector: generated_selector.selector!,
                        template
                    });
                }
            }

            if (analyze_result.generated_selectors.length > 0) {
                const updates = analyze_result.generated_selectors.filter(({ selector }) => !!selector).map(({ selector_name, selector }) => ({ selector_name, selector: selector! }));
                const update = updateTemplate(template, updates);

                console.log("");
                console.log(`report: ${analyze_url}`);
                console.log(`template: ${row.template_path} (${diffDays(row.template_last_modified_at)}d)`);
                for (const generated_selector of analyze_result.generated_selectors) {
                    console.log(`${generated_selector.selector_name}: ${generated_selector.selector}`);
                    const { before, after } = update.commits.find(commit => commit.selector_name === generated_selector.selector_name) || {};
                    if (before && after) {
                        console.log(`selector: ${generated_selector.selector_name}`);
                        console.log(`before: ${before}`);
                        console.log(`after: ${after}`);
                    }
                    else if (after) {
                        console.log(`selector: ${generated_selector.selector_name}`);
                        console.log(`before: (none)`);
                        console.log(`after: ${after}`);
                    }
                    else {
                        console.log(`selector: ${generated_selector.selector_name} (no change)`);
                    }
                }

                const commit_obj: CommitOptions = {
                    template_path: row.template_path,
                    analyze_url,
                    updates: analyze_result.generated_selectors.filter(({ selector }) => !selector).map(({ selector_name, selector }) => ({ selector_name, selector: selector! }))
                };
                await fs.writeFile("./commit.jsonc", JSON.stringify(commit_obj, null, 2));

                //if (params.commit) {
                    process.stdout.write("commit? [y/n] ");
                    const keypress = await waitForKeypress();
                    process.stdout.write("\n");
        
                    if (keypress.name.toLowerCase() === "y") {
                        const commit_id = await commit(commit_obj);
                        index.write(`<a href="${file}" class="d-block" target="_blank">${row.name} <i>commit=${commit_id}</i></a>\n`);
                        break;
                    }
                //}
            }

            index.write(`<a href="${file}" class="d-block" target="_blank">${row.name} <i>${analyze_result.generated_selectors.length} selectors generated</i></a>\n`);
        }
        else if (analyze_result.code === "error") {
            index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i title="${analyze_result.message?.replace(/"/g, "'")}">${truncate(analyze_result.message, 100)}</i></a>\n`);
            result.errors.push(`${row.name}: ${analyze_result.message}`);
        }
        else if (analyze_result.code) {
            index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i>${analyze_result.code}${analyze_result.message ? ` ${analyze_result.message}` : ""}</i></a>\n`);
        }
        else {
            index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i>selector not generated</i></a>\n`);
        }
    }
    catch (err) {
        err = err instanceof Error ? err.message : JSON.stringify(err);
        index.write(`<a href="${file}" class="d-block error" target="_blank">${row.name} <i title="${(err instanceof Error ? err.message : JSON.stringify(err).replace(/"/g, "'"))}">${truncate(err instanceof Error ? err.message : JSON.stringify(err), 100)}</i></a>\n`);
        console.log(chalk.red(err));
    }

    await page.close();
    console.log("");
}

await closeLogStream(index);

if (result.errors.length > 0) {
    console.log("");
    console.log(chalk.gray(`${result.errors.length} errors`));
    console.log(chalk.gray(result.errors.join("\n")));
}

console.log("");
console.log(chalk.gray(`report: ${report_url}`));

open(report_url);
process.exit(0);
