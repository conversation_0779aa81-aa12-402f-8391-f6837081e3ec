import * as dotenv from "dotenv";
dotenv.config();

import * as functions from "@google-cloud/functions-framework";
import { BigQuery, BigQueryTimestamp } from "@google-cloud/bigquery";
import { PubSub } from "@google-cloud/pubsub";

const bigquery = new BigQuery();
const pubsub = new PubSub();

import { AutoselectObject } from "./lib/index.js";

interface Row {
    domain_name: string;
    selectors: SelectorObj[];
}

interface SelectorObj {
    selector_name: string;
    selector: string;
    timestamp: BigQueryTimestamp;
    autoselect_id: string;
    source: string;
    report_url: string;
}

const ttl = 15 * 60000; // 15 minutes
let cache: Row[] = [];
let timestamp = 0;
const recent = new Set<string>();

functions.http("autoselect", async (req, res) => {
    if (req.query.url) {
        const url = req.query.url;
        if (typeof url !== "string" || url.length > 1000 || !url.startsWith("https://")) {
            res.status(400).send("Bad request");
            return;
        }

        await pubsub.topic("syphonx-autogen").publishMessage({ data: Buffer.from(url) });
        res.status(200).send("OK");
        console.log("SEND", req.query.url);

        const key = parseDomainName(url);
        recent.add(key);
        return;
    }

    if (req.query.status !== undefined) {
        const status = `OK (t=${timestamp ? ttl - Date.now() - timestamp : 0})`;
        res.status(200).send(status);
        return;
    }

    if (req.query.q) {
        const q = req.query.q;
        const force = req.query.force !== undefined;
        if (typeof q !== "string" || q.length > 1000) {
            res.status(400).send("Bad request");
            return;
        }
    
        const now = Date.now();
        if (!cache || !timestamp || now - timestamp > ttl || (force && now - timestamp > 30000)) {
            cache = await queryAutoselect();
            timestamp = now;
            recent.clear();
        }
    
        const domains = q.split(",");
        const rows = cache.filter(({ domain_name: key }) => domains.includes(key));
        
        const metadata: Record<string, unknown> = {};
        for (const row of rows)
            for (const { autoselect_id: key, timestamp, source, report_url } of row.selectors)
                metadata[key] = {
                    timestamp: new Date(timestamp.value),
                    source,
                    report_url
                };

        const hits: Record<string, unknown> = {};
        for (const { domain_name: key, selectors } of rows)
            hits[key] = expandSelectors(selectors);

        const misses = domains.filter(key => !cache.some(({ domain_name }) => domain_name === key));
        for (const key of misses)
            if (!recent.has(key)) {
                console.log("SEND", key);
                await pubsub.topic("syphonx-autogen").publishMessage({ data: Buffer.from(key) });
                recent.add(key);
            }
    
        const response = {
            hits,
            misses,
            metadata,
            cache: now - timestamp
        };

        res.status(200).json(response);
        return;
    }

    res.status(400).send("Bad request");
});

async function queryAutoselect() {
    const [rows] = await bigquery.query("SELECT * FROM omega.autoselect");
    return rows;
}

function expandSelectors(list: SelectorObj[]): Record<string, AutoselectObject[]> {
    const result: Record<string, AutoselectObject[]> = {};
    for (const item of list) {
        const { selector_name: key, autoselect_id: id, selector } = item;
        const expanded = selector
            .split("\n")
            .filter(validateSelectorObject)
            .map(expandSelectorObject); // new Date(obj.value)
        result[key] = expanded.map(obj => ({ ...obj, id }));
    }
    return result;
}

function expandSelectorObject(line: string): AutoselectObject {
    const i = line.indexOf("; ");
    if (i === -1)
        return { selector: line };
    
    const selector = line.slice(0, i);
    const j = i > 0 ? line.indexOf("; ", i + 1) : -1;
    if (j >= 0)
        return {
            selector,
            attr: line.slice(i + 3),
            filter: line.slice(j)
        };
    else if (line.slice(i + 2).startsWith("@"))
        return {
            selector,
            attr: line.slice(i + 3)
        };
    else if (line.slice(i + 2).startsWith("/"))
        return {
            selector,
            filter: line.slice(i + 2)
        };
    else
        return {
            selector
        };
}

function parseDomainName(url: string): string {
    try {
        const hostname = new URL(url).hostname;
        return hostname.replace(/^www\./, "");
    }
    catch (err) {
        return "";
    }
}

function validateSelectorObject(line: unknown): boolean {
    if (typeof line !== "string")
        return false;
    if (line.trim().length === 0)
        return false;
    if (line.startsWith("@"))
        return false;
    if (line.startsWith("/"))
        return false;
    return true;
}

/*
GCP CLOUD RUN FUNCTION
https://console.cloud.google.com/functions/details/us-central1/autoqc-user-response

const functions = require("@google-cloud/functions-framework");

functions.http("log", async (req, res) => {
    // todo
});

{
    "dependencies": {
        "@google-cloud/functions-framework": "^3.0.0",
        "@google-cloud/bigquery": "^7.7.0",
        "@google-cloud/pubsub": "^4.5.0"
    }
}
*/