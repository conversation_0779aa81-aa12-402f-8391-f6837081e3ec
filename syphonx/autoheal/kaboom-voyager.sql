/*
CREATE OR R<PERSON>LACE TABLE omega.voyager_candidates
PARTITION BY DATE(responded_at)
CLUSTER BY source
AS
SELECT * FROM omega.voyager_candidates_view;

-- checks
SELECT product_id, url, COUNT(*) AS n FROM omega.voyager_candidates GROUP BY product_id, url HAVING COUNT(*)>1; --> empty
*/

CREATE OR REPLACE VIEW voyager.prowl_candidates
AS
WITH
voyager_requests AS (
  SELECT
    SAFE_CAST(application_product_id AS INT64) AS product_id,
    uuid AS request_id,
    created_at AS requested_at
  FROM voyager.product_match_api_requests
  WHERE DATE(created_at) >= '2024-10-01'
  AND source='PROWL' 
  AND SAFE_CAST(application_product_id AS INT64) IS NOT NULL
  QUALIFY ROW_NUMBER() OVER (PARTITION BY application_product_id ORDER BY created_at DESC) = 1
)
SELECT a.*,
  id AS response_id,
  created_date AS responded_at,
  NET.REG_DOMAIN(LAX_STRING(obj.candidate_url_data.url)) AS domain_name,
  seller_domain AS source_domain,
  LAX_STRING(obj.candidate_url_data.url) AS candidate_url,
  response_index
FROM voyager_requests AS a
JOIN voyager.voyager_beyond_beyond_results AS b ON b.request_uuid=a.request_id AND b.created_date >= '2024-10-01'
CROSS JOIN UNNEST(ARRAY(SELECT PARSE_JSON(a) FROM UNNEST(JSON_EXTRACT_ARRAY(crawl_result_json)) AS a)) AS obj
WITH OFFSET response_index
WHERE crawl_result_json IS NOT NULL
AND crawl_result_json != 'null'
AND obj.candidate_url_data.url IS NOT NULL
QUALIFY ROW_NUMBER() OVER (PARTITION BY product_id, candidate_url ORDER BY responded_at DESC, response_index) = 1;


CREATE OR REPLACE VIEW voyager.prowl_candidates_mapped
AS
WITH
candidates AS (
  SELECT *
  FROM voyager.prowl_candidates
),
distinct_product_domain AS (
  SELECT DISTINCT product_id, domain_name
  FROM candidates
),
url_match AS (
  SELECT
    a.product_id,
    domain_name,
    url,
    url_id,
    is_monitored,
    show_in_reports,
    date_last_checked,
    TRUE AS exact_match
  FROM candidates AS a
  JOIN prowl.pwl_url AS b ON b.product_id=a.product_id AND b.url=a.candidate_url
  QUALIFY ROW_NUMBER() OVER (PARTITION BY product_id, domain_name ORDER BY is_monitored DESC, show_in_reports DESC, date_last_checked DESC) = 1
),
domain_match AS (
  SELECT
    a.product_id,
    a.domain_name,
    url,
    url_id,
    is_monitored,
    show_in_reports,
    date_last_checked,
    FALSE AS exact_match
  FROM distinct_product_domain AS a
  JOIN prowl.pwl_url AS b ON b.product_id=a.product_id AND NET.REG_DOMAIN(b.url)=a.domain_name
),
mappings AS (
  SELECT
    product_id,
    domain_name,
    COALESCE(b.url, c.url) AS product_match_url,
    COALESCE(b.url_id, c.url_id) AS product_match_id,
    COALESCE(b.date_last_checked, c.date_last_checked) AS last_updated,
    COALESCE(b.is_monitored=1, c.is_monitored=1) AS active,
    CAST(NULL AS BOOLEAN) AS scheduled,
    COALESCE(b.exact_match, c.exact_match) AS exact_match,
    IF(COALESCE(b.url, c.url) IS NOT NULL, TO_JSON(STRUCT(
      COALESCE(b.is_monitored, c.is_monitored) AS is_monitored,
      COALESCE(b.show_in_reports, c.show_in_reports) AS show_in_reports,
      COALESCE(b.date_last_checked, c.date_last_checked) AS date_last_checked
    )), NULL) AS metadata
  FROM distinct_product_domain AS a
  LEFT JOIN url_match AS b USING (product_id, domain_name)
  LEFT JOIN domain_match AS c USING (product_id, domain_name)
),
schedule AS (
  SELECT DISTINCT product_match_id
  FROM mappings
  JOIN conflux.conflux_product_page_schedule USING (product_match_id)
  WHERE app_name='prowl'
)
SELECT * REPLACE (IF(active IS NOT NULL, schedule.product_match_id IS NOT NULL, NULL) AS scheduled)
FROM candidates
LEFT JOIN mappings USING (product_id, domain_name)
LEFT JOIN schedule USING (product_match_id);


CREATE OR REPLACE VIEW voyager.prowl_candidates_grouped
AS
WITH top_candidates AS (
  SELECT *
  FROM voyager.prowl_candidates_mapped
  QUALIFY ROW_NUMBER() OVER (PARTITION BY product_id, domain_name ORDER BY active DESC, last_updated DESC) <= 3
)
SELECT
  'prowl' AS app_name,
  product_id,
  ARRAY_AGG(
    STRUCT(
      domain_name,
      source_domain,
      candidate_url,
      request_id,
      requested_at,
      response_id,
      responded_at,
      response_index,
      product_match_id,
      product_match_url,
      last_updated,
      active,
      scheduled,
      metadata
    )
    ORDER BY domain_name, responded_at, response_index
  ) AS candidates
FROM top_candidates
GROUP BY 1, 2;