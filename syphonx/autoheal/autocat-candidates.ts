import { append } from "cheerio/lib/api/manipulation.js";
import {
    isEmpty,
    vectorCosineSimilarity,
    ProductCategory
} from "./lib/index.js";

export interface ProductCategoryCandidate extends ProductCategory {
    score?: number;
}

/**
 * Finds and returns a list of candidate product categories that are most relevant to a given product vector,
 * optionally scoped to a subset of subcategories. It uses cosine similarity to score each category based on
 * how well its vector matches the product vector.
 *
 * The function selects the top-scoring categories (up to `max_candidates`) and optionally enriches the result
 * by adding ancestor categories (up to `max_ancestors`) that form paths from the candidates to their respective roots.
 * This ensures hierarchical coverage and improves the quality of downstream categorization by preserving
 * lineage context.
 *
 * @param {ProductCategory[]} categories - The full list of product categories, each with a vector and hierarchy metadata.
 * @param {number[]} product_vector - The vector representation of the product being categorized.
 * @param {number[]} subcategory_ids - Optional list of subcategory IDs to constrain the candidate search to a subtree.
 * @param {number} max_candidates - Maximum number of top-scoring candidate categories to return (not including ancestors).
 * @param {number} append_ancestor_depth - Determines the depth of ancestors to append, forming paths toward the root.
 * @returns {ProductCategoryCandidate[]} A list of top candidate categories, scored by similarity and enriched with ancestor categories that connect them to the root.
 */
export function findProductCategoryCandidates(categories: ProductCategory[], product_vector: number[], subcategory_ids: number[], max_candidates: number, append_ancestor_depth: number): ProductCategoryCandidate[] {
    const subcategories = !isEmpty(subcategory_ids) ? categories.filter(({ category_id }) => subcategory_ids.includes(category_id)) : undefined;
    const subtree = subcategories ? categories.filter(category => isDescendant(category, subcategories)) : categories;

    const all_candidates = subtree
        .map(category => ({
            ...category,
            score: vectorCosineSimilarity(product_vector, category.category_vector)
        }))
        .filter(category => category.score >= 0.55)
        .sort((a, b) => b.score - a.score);
    const candidates = all_candidates.slice(0, max_candidates);

    const set = new Set<ProductCategoryCandidate>();
    for (const candidate of candidates) {
        let ancestor = candidate.parent;
        while (ancestor) {
            if (isFamily(ancestor, subcategories)) {
                const i = candidates.findIndex(({ category_id }) => category_id === ancestor!.category_id);
                if (i === -1) // if ancestor is not alrady in list of candidates then add it to list of ancestors
                    set.add(ancestor);
            }
            ancestor = ancestor.parent;
        }
    }

    if (set.size > 0) {
        // take ancestors sorted by descending depth then by name
        const ancestors = Array.from(set)
            .filter(obj => obj.depth <= append_ancestor_depth)
            .sort((a, b) => a.depth !== b.depth ? b.depth - a.depth : a.category_name.localeCompare(b.category_name));
        return [...candidates, ...ancestors];
    }

    return candidates;
}

/**
 * Determines if the specified category is a descendent of one of the specified parents.
 */
function isDescendant(category: ProductCategory, parents: ProductCategory[] | undefined): boolean {
    if (!parents)
        return true;
    else
        return parents.some(parent => category.category_name.startsWith(parent.category_name) && category.depth >= parent.depth);
}

/**
 * Determines if the specified category is a descendent of one of the specified parents, or is one of the parents themselves.
 */
function isFamily(category: ProductCategory, parents: ProductCategory[] | undefined): boolean {
    if (!parents)
        return true;
    else if (parents.map(parent => parent.category_id).includes(category.category_id))
        return true;
    else
        return isDescendant(category, parents);
}
