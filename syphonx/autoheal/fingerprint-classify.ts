import chalk from "chalk";

import {
    bigquery,
    classifyPage,
    createThumbnailImage,
    summarize
}
from "./lib/index.js";

interface Row {
    domain_name: string;
    page_fingerprint: string;
    page_url: string;
    screenshot_url: string;
    n: number;
}

const query = `
SELECT
  page_fingerprint,
  COUNT(*) AS n,
  ANY_VALUE(domain_name) AS domain_name,
  ARRAY_AGG(STRUCT(screenshot_url, page_url) ORDER BY timestamp DESC)[OFFSET(0)].*
FROM temp.page_fingerprints
GROUP BY 1
HAVING n>=3
ORDER BY n DESC
`.trim();

const rows: Row[] = await bigquery.query(query);
console.log(chalk.gray(`${rows.length} rows returned`));

for (const row of rows) {
    const { page_fingerprint, domain_name, screenshot_url, page_url: capture_url } = row;
    console.log(`[${rows.indexOf(row)+1}/${rows.length}] ${domain_name} ${page_fingerprint} ${chalk.gray(capture_url)}`);
    const datauri = await createThumbnailImage({ url: screenshot_url, size: 512 });

    const context = { domain_name, capture_url, screenshot_url };
    const classify_result = await classifyPage({ datauri, context });

    const { models, tokens, cost, elapsed } = summarize(classify_result.analyze);
    const data = {
        timestamp: new Date(),
        domain_name,
        page_fingerprint,
        page_class: classify_result.page_class,
        explain: classify_result.explain,
        screenshot_url,
        model: models.join(", "),
        usage: tokens,
        cost,
        duration: elapsed
    };

    await bigquery.insert("temp.page_fingerprint_classify", data);
}

console.log(`${rows.length} rows inserted`);
process.exit();
