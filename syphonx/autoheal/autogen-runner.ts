import * as dotenv from "dotenv";
dotenv.config();

import {
    autogen,
    parseArgs,
    postAutogenStatus,
    queryAutogenRequest,
    queryAutogenRequests,
    sleep
}
from "./lib/index.js";

import chalk from "chalk";
import { PubSub } from "@google-cloud/pubsub";

const args = parseArgs({
    optional: {
        rerun: "re-runs the specified autogen-id",
        reset: "resets status on the specified comma seperated autogen-ids ",
        debug: `runs in a "zero impact" debug mode that bypasses updating status and overwriting the template, specify a comma seperated list of selector targets to debug otherwise all targets will be run (requires --rerun)`,
    },
    validate: ({ rerun, debug }) => {
        if (debug && !rerun)
            return "--debug option also requires --rerun option";
    }
});

const debug = !!args.debug;
if (debug)
    console.log(chalk.gray.italic("Running in debug mode"));

const debug_targets = args.debug && args.debug !== "1" ? args.debug.split(",") : undefined;
if (debug_targets)
    console.log(chalk.gray.italic(`Debug targets: ${debug_targets.join(", ")}`));

if (args.reset) {
    const autogen_ids = args.reset.split(",");
    await postAutogenStatus({ autogen_ids, status: null });
    console.log(`${autogen_ids.length} requests reset`);
    process.exit(0);
}

if (args.rerun) {
    const row = await queryAutogenRequest(args.rerun);
    if (row)
        await run(row);
    else
        console.error(`autgen_id "${args.rerun}" not found`);
    process.exit(0);
}

const max_consecutive_errors = 3;
let running = false;
let signalled = false;

const pubsub = new PubSub();
pubsub.subscription("syphonx-autogen-runner").on("message", message => {
    message.ack();
    if (running)
        signalled = true;
    else
        runner();

});

await cleanup();
await runner();

async function runner() {
    running = true;
    while (true) {
        signalled = false;

        console.log(chalk.gray("Querying autogen requests..."));
        const rows = await queryAutogenRequests({ limit: 5 });
        console.log(chalk.gray(`${rows.length} rows returned`));
        if (rows.length === 0)
            break;
        else
            console.log();

        let consecutive_error_count = 0;
        for (const row of rows) {
            const ok = await run(row);
            if (ok)
                consecutive_error_count = 0;

            if (!ok && ++consecutive_error_count > max_consecutive_errors) {
                console.log(chalk.red(`Aborted after ${consecutive_error_count} consecutive failures`));
                process.exit(0);
            }

            if (signalled)
                break;

            console.log(chalk.gray(`Pausing for 10 seconds...`));
            await sleep(10000);
        }
    }

    console.log(chalk.gray(`Waiting for requests...`));
    running = false;
}

async function cleanup() {
    console.log(chalk.gray("Cleanup..."));
    const pending_rows = await queryAutogenRequests({ status: "pending" });
    for (const row of pending_rows) {
        const { autogen_id } = row;
        await postAutogenStatus({ autogen_ids: [autogen_id], status: null });
        console.log(chalk.gray(`${autogen_id} aborted`));
    }
}

interface RunOptions {
    autogen_id: string;
    url: string;
    autogen_profile: string;
    selector_profile: string;
    template_path: string;
}

async function run({ autogen_id, url, autogen_profile, selector_profile, template_path }: RunOptions): Promise<boolean> {
    console.log();
    console.log(chalk.gray(`REQUEST ${autogen_id} STARTED`));

    if (!debug)
        await postAutogenStatus({ autogen_id, status: "pending" });
    let status;
    let data;
    try {
        const result = await autogen(autogen_profile, {
            autogen_id,
            url,
            selector_profile,
            template_path: template_path,
            debug_targets,
            debug
        });
        status = result.ok ? "succeeded" : "failed";
        data = result;
    }
    catch (err) {
        status = "error";
        console.log(chalk.red(`ERROR: ${err instanceof Error ? err.message : JSON.stringify(err)}`));
    }

    console.log(chalk.gray(`REQUEST ${autogen_id} COMPLETE (${status.toUpperCase()})`));
    console.log();

    if (!debug)
        await postAutogenStatus({ autogen_id, status, data });
    return status === "succeeded";
}