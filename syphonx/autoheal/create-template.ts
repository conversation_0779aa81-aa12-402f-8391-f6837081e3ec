import * as dotenv from "dotenv";
dotenv.config();

import * as syphonx from "syphonx-lib";

import {
    parseArgs,
    queryConfluxSellersWithoutSyphonxTemplate,
    saveTemplate
}
from "./lib/index.js";

const params = parseArgs({
    optional: {
        app: "app-name filter (brand_monitor, wtb, prowl)",
        domain: "domain name filter (amazon, bestbuy, target, walmart)",
        country: "country code filter (us, ca, gb)",
        type: "page type filter (product_page, search_page)",
        verbose: "verbose log output"
    }
});

if (params.verbose)
    process.env.VERBOSE = "1";

const rows = await queryConfluxSellersWithoutSyphonxTemplate();

for (const row of rows) {    
    try {
        const template = await syphonx.parseTemplate(row.template);
        await saveTemplate(row.key, template);
        console.log(`template with key ${row.key} written to cloud storage.`);
    }
    catch (err) {
        console.log(err instanceof Error ? err.message : JSON.stringify(err));
    }
}
