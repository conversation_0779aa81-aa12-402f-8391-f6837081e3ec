# Audit

This program is designed for auditing and verifying web page content against specific criteria, primarily focusing on product pages. It leverages multiple custom and third-party libraries to perform tasks such as web scraping, content classification, and regression detection. The procedure outlined in the code can be summarized in several key steps:

### Initialization and Configuration
- **Environment Setup**: Uses `dotenv` to load environment variables, which could include configuration options and API keys.
- **Library Imports**: Loads necessary libraries for web scraping, content analysis, and logging. This includes a custom library `syphonx-lib` and `chalk` for coloring console output.
- **Parameter Parsing**: Parses command-line arguments with `parseArgs` to configure the audit process, including options for country code, seller ID, SKU, and various thresholds and filters for the auditing process.

### Data Preparation
- **Query Database**: Executes database queries to fetch regressions, audits, and blacklist information using functions like `queryTargetSelectorRegressions`, `queryAudits`, and `queryAuditBlackList`.
- **Data Organization**: Organizes fetched data into structured formats for processing, such as grouping regressions by seller and organizing audit records.

### Audit Process
- **Audit Preparation**: Sets up variables and status tracking for each seller, preparing for the audit loop.
- **Template and Selector Processing**: Attempts to load page templates and validate selector names for auditing. Skips entities based on various criteria, including blacklist status and previous audit records.
- **Page Analysis Loop**: For each seller and their associated web pages:
  - **Page Loading and Analysis**: Loads the web page, capturing screenshots and classifying the page based on its content (e.g., product pages). It uses functions like `openPage`, `classifyScreenshotUrl`, and `classifyLivePage`.
  - **Content Verification**: Verifies specific selectors on the page against expected values or criteria, attempting to confirm regressions or detect issues such as out-of-stock or discontinued products.
  - **Result Logging**: Logs audit results using a custom `AuditRecordCreator`, marking pages with issues or confirming that they meet the audit standards.

### Result Compilation and Exit
- **Summary and Cleanup**: Compiles audit results, prints summaries of confirmed regressions and audited pages, and exits the program.

### Key Functional Components
- **Error Handling and Logging**: The program includes robust error handling, logging errors, and audit findings with detailed messages and classifications.
- **Dynamic Program Execution**: Utilizes dynamic execution to run page evaluation checks, which allows for flexible content analysis based on loaded templates.
- **Interactive Features**: Includes options for interactive auditing, such as pausing for user input and configuring the audit's thoroughness with command-line arguments.

### Usage Scenarios
This program can be used in various scenarios, such as:
- **Brand Monitoring**: To ensure that product pages across different sellers align with brand guidelines or to detect unauthorized sellers.
- **Market Analysis**: For analyzing product availability, pricing, and presentation across different e-commerce platforms.
- **Quality Assurance**: As part of QA processes for e-commerce platforms, ensuring that product page information is accurate and up-to-date.

In summary, the program is a comprehensive tool for automated web content auditing, designed to verify the accuracy and quality of web page information against specified criteria, with a focus on product-related content.

### Usage Examples
The `audit` tool takes primary arguments of `app-name` and `selector-name`, for example: `node audit brand_monitor name` to audit the name selector within the brand-monitor app.
Run `node audit --help` to see other options.

- `node audit brand_monitor name --preview` to run the audit tool in "preview" mode to spot check regressions within each seller for a specified target selector
- `node audit brand_monitor name --seller=167` to run the audit tool with a specific seller to perform an automated audit on the retailer
- `node audit brand_monitor name --seller=167 --preview --diagnose` to see more info before running an automated audit


### Flow Chart
Here's a diagram that outlines the flow of the program. This diagram simplifies some aspects for clarity but captures the main process and decision points.

```mermaid
graph TD
    0[Start]
    --> 1[Query Regressions]
    --> 2[Query Audits]
    --> 3[Query Blacklist]
    --> 4[Skip Sellers]
    --> 5[For Each Seller]
    --> 6[For Each Page]
    --> 7[Load Page]
    --> 8[Classify Page]
    --> 8?{Product<br>Page?}
    8? -->|Yes| 9
    8? -->|No| 6

    9[Locate Content]
    --> 9?{Located?}
    9? -->|Yes| 10[Regression Confirmed] --Next Seller--> 5
    9? -->|No| 11[Content Not Found] --Next Page--> 6

```
