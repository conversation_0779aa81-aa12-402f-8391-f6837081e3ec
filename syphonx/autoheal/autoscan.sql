CREATE OR <PERSON><PERSON>LACE TABLE omega.page_scans
(
  analyze_id STRING,
  timestamp TIMESTAMP,
  domain_name STRING,
  page_id STRING,
  page_class STRING,
  page_title STRING,
  page_text STRING,
  page_metadata JSON,
  capture_id STRING,
  capture_date TIMESTAMP,
  capture_url STRING,
  explain STRING,
  report_url STRING,
  fingerprint STRING,
  analyze ARRAY<
    STRUCT<
      name STRING,
      model STRING,
      input_tokens INT64,
      output_tokens INT64,
      tokens INT64,
      cost FLOAT64,
      elapsed INT64,
      message STRING
    >
  >
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name;

CREATE OR REPLACE VIEW omega.page_scans_input
AS
WITH last_scanned AS (
  SELECT page_id, timestamp AS last_scanned_at
  FROM omega.page_scans
  QUALIFY ROW_NUMBER() OVER (PARTITION BY page_id ORDER BY timestamp DESC) = 1
)
SELECT
  page_id,
  app_name,
  account_key,
  country_code,
  sku,
  group_name,
  group_id,
  NET.REG_DOMAIN(capture_url) AS domain_name,
  IF(NOT ENDS_WITH(seller_name, ')'), CONCAT(seller_name, ' (', country_code, ')'), seller_name) AS seller_name,
  seller_id,
  capture_status,
  last_attempt.capture_id,
  last_attempt.capture_date,
  capture_url,
  last_attempt.html_url AS content_url,
  last_scanned_at
FROM conflux.product_page_daily
LEFT JOIN last_scanned USING (page_id);
