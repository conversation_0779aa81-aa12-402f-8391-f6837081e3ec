import * as dotenv from "dotenv";
import open from "open";

dotenv.config();

import {
    autogen,
    autogenProfiles,
    selectorProfiles,
    parseArgs,
    postAutogenStatus
}
from "./lib/index.js";

const args = parseArgs({
    optional: {
        url: "page url",
        profile: `specify autogen profile: ${autogenProfiles().join(", ")}`,
        select: `specify selector profile: ${selectorProfiles().join(", ")}`,
        reset: "reset status for an autogen-id",
        save: "template path to save"
    }
});

if (args.reset) {
    const autogen_id = args.reset;
    await postAutogenStatus({ autogen_id, status: null });
    console.log(`${autogen_id}: status reset`);
    process.exit();
}

const url = args.url;
const template_path = args.save;
const profile = args.profile || "default";
const selector_profile = args.select || selectorProfiles()[0]

const result = await autogen(profile, { url, selector_profile, template_path });
open(result.report_url);

process.exit(0);