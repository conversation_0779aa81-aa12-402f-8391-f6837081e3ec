import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";

import {
    diffDate,
    openai,
    parseArgs,
    parseYesNo,
    waitForKeypress
} from "./lib/index.js";

import {
    autocatInitialize,
    completeBatch,
    getBatchCompletionFiles,
    loadBatchCompletionsToTable,
    //updateProductCategories,
    BatchInsertMode
} from "./autocat-common.js";

const args = parseArgs({
    optional: {
        0: "batch_id to process completions",
        status: "check pending status",
        abort: "abort all pending jobs older than the specified number of hours",
        reload: "reload batch completions to table, reloads all files in temp or specify prompt to ask for each one",
        //update: "update changes to final product_category table (default=yes)",
        verbose: "verbose log output"
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

const insert_mode = "DIRECT" as BatchInsertMode;

if (args.abort) {
    const hours = parseInt(args.abort);
    console.log(chalk.gray(`Aborting batches older than ${hours} hours...`));
    const n = await openai.batch.snowflake.abortBatches(hours);
    console.log(chalk.gray(`${n} batches aborted`));
    process.exit();
}

if (args.reload) {
    const files = getBatchCompletionFiles();
    console.log(chalk.gray(`${files.length} batch outputs found`));
    if (files.length === 0) {
        // do nothing
    }
    else if (args.reload === "prompt") {
        for (const file of files) {
            console.log(`Reload ${file}?`);
            const keypress = await waitForKeypress();
            if (keypress.name.toLowerCase() === "y")
                await loadBatchCompletionsToTable([file]);
        }
    }
    else {
        await loadBatchCompletionsToTable(files);
    }
    process.exit();
}

if (args.status) {
    console.log(chalk.gray("Query pending batches..."));
    const batches = await openai.batch.snowflake.queryPendingBatchInfo("product_category_completions");
    for (const { batch_id, created_at } of batches)
        console.log(chalk.gray(`${batch_id}: ${diffDate(created_at)} ago`));
    console.log(chalk.gray(`${batches.length} pending batches`));
    process.exit();
}

let pending_batches;
if (!args[0]) {
    console.log(chalk.gray("Query pending batches..."));
    pending_batches = await openai.batch.snowflake.queryPendingBatches("product_category_completions");
    console.log(chalk.gray(`${pending_batches.length} pending batches`));
    if (pending_batches.length === 0)
        process.exit();
}
else {
    pending_batches = [args[0]];
}

console.log();
console.log(chalk.gray("Downloading product categories..."));
await autocatInitialize();

const t0 = new Date();
const files: string[] = []; // only used when insert_mode is STAGE mode
let batch_failed = 0;
let i = 0;
for (const batch_id of pending_batches) {
    const batch = await openai.batch.batchStatus(batch_id);
    console.log();
    console.log(chalk.white(`[${++i}/${pending_batches.length}] ${batch_id} (${batch.status})`));
    if (["completed", "expired"].includes(batch.status)) {
        if (batch.request_counts) {
            const { completed, failed, total } = batch.request_counts;
            console.log(chalk.gray(`${completed}/${total} (${(100 * completed/ total).toFixed(2)}%)`));
            if (completed < total)
                console.log(chalk.yellow(`WARNING: ${total - completed} incomplete`));
            if (failed > 0)
                console.log(chalk.yellow(`WARNING: ${failed} failed`));
        }

        if (batch.status === "expired")
            console.log(chalk.yellow(`WARNING: Batch is expired`));

        const { file } = await completeBatch(batch_id, batch.output_file_id, insert_mode, batch.request_counts?.completed);
        if (file)
            files.push(file);
        else
            batch_failed += 1;
    }
}

if (insert_mode === "STAGE")
    await loadBatchCompletionsToTable(files); // not called when insert_mode is DIRECT mode

console.log(chalk.gray(`${files.length}/${pending_batches.length} batches completed in ${diffDate(t0)}`));
if (batch_failed > 0)
    console.log(chalk.yellow(`${batch_failed} batches failed`));

//if (parseYesNo(args.update, true)) {
    //console.log(chalk.gray("Updating product_category table..."));
    //await updateProductCategories();        
//}

process.exit();
