WITH
a AS (
  SELECT DISTINCT product_id
  FROM prowl.pwl_product
  WHERE brand_id=6188
  AND is_monitored=1
  --AND date_last_checked>'2024-10-01'
  --> 249
),
b AS (
  SELECT DISTINCT SAFE_CAST(application_product_id AS INT64) AS product_id
  FROM voyager.product_match_api_requests
  JOIN voyager.voyager_beyond_beyond_results ON request_uuid=uuid
  WHERE DATE(created_at) >= '2024-10-01'
  AND source='PROWL' 
  AND brand='House of Rohl'
  AND crawl_result_json IS NOT NULL
  AND crawl_result_json != 'null'
  --> 187
),
c AS (
  SELECT DISTINCT * FROM (
    SELECT product_id FROM a
    UNION ALL
    SELECT product_id FROM b
  )
)
SELECT
  COUNTIF(a.product_id IS NOT NULL) AS a_count,
  COUNTIF(b.product_id IS NOT NULL) AS b_count,
  COUNTIF(a.product_id IS NOT NULL AND b.product_id IS NOT NULL) AS overlap_count,
  COUNT(*) AS total_count
FROM c
LEFT JOIN a USING (product_id)
LEFT JOIN b USING (product_id)
ORDER BY 1;
--> 249, 187, 187, 249