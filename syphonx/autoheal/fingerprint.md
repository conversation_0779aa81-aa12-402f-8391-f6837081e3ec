

```
node fingerprint-scan --app=wtb --account=dyson --country=us --seller=2 --preview
```


```sql
SELECT ngram, COUNT(*) AS n
FROM temp.page_fingerprints, UNNEST(ML.NGRAMS(udf.split_words(<PERSON>OW<PERSON>(title), TRUE), [3,5])) AS ngram
LEFT JOIN temp.page_fingerprint_classify USING (domain_name, page_fingerprint)
WHERE page_class='blocked'
GROUP BY 1
ORDER BY 2 DESC;
```

# Page Classification Process Overview
1. Extract content and title from stored page content for all captures (`conflux.page_content` table)
2. Query `conflux.page_classify_view` with null page_class
3. Stop if less than 10 pages found
4. Scan pages until 10 matching fingerprints are found (`conflux.page_fingerprint` table)
5. Stop if 10 pages with same fingerprint not found
6. Perform page classification on one of the fingerprinted pages from the previous step (`conflux.page_classify` table)
7. Determine longest ngram common among all pages with same fingerprint (`conflux.page_ngram` table)
8. Apply page classification to all pages matching the ngram (`conflux.page_classify_view` view)
9. Goto step 2


```mermaid
graph TD
    1 --> 2 --> stuffer
```