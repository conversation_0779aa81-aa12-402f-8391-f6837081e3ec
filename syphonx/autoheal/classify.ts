import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";
import open from "open";

import {
    openPageForVision,
    parseArgs,
    summarize,
    WaitFor,
}
from "./lib/index.js";

const params = parseArgs({
    required: {
        0: "page url",
    },
    optional: {
        headless: "hide browser window (default=false)",
        pause: "pause for user input after each page load (ignored if cached option is used)",
        waitfor: "specify load, domcontentloaded, or networkidle (default=none)"
    }
});

const options = {
    headless: !!params.headless,
    timeout: 10000,
    waitfor: (params.waitfor as WaitFor | undefined) || (!params.snooze && !params.pause ? "networkidle" : undefined)
};

const url = params[0];

console.log(chalk.gray("Classifying page..."));
const result = await openPageForVision(url, options);
console.log();

console.log(`classifcation: ${result.page_class === "PRODUCT-PAGE" ? chalk.green(result.page_class) : chalk.yellow(result.page_class)}`);
if (result.report_url) {
    console.log(chalk.gray(`report: ${result.report_url}`));
    open(result.report_url);
}
if (result.analyze) {
    const { summary } = summarize(result.analyze);
    console.log(chalk.gray(summary));
}

process.exit(0);
