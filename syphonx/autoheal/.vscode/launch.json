{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "outFiles": [
                "${workspaceFolder}/**/*.js"
            ],
            //"console": "externalTerminal", // WORKAROUND https://stackoverflow.com/questions/41100916/node-js-readline-in-debug-console-in-visual-studio-code
            //"outputCapture": "std", // WORKAROUND https://github.com/microsoft/vscode/issues/19750

            "program": "${file}",

            //"program": "${workspaceFolder}/autoselect.js",
            //"args": ["--url=https://agwayct.com/products/fruitables-healthy-dog-treats-pumpkin-apple-7-oz", "--verbose"]
            //"args": ["--url=https://www.gardenstatepetcenter.com/products/oxbow-animal-health-forage-wise-adult-rabbit-food-4-lb?variant=**************", "--verbose"]
            //"args": ["--app=prowl", "--account=companapet", "--verbose", "--preopen=3", "--disable=pause"]
            //"args": ["--app=prowl", "--account=kershaw", "--domain=serenityknives.com", "--n=1", "--limit=1", "--verbose", "--random", "--preopen", "--disable=pause"]
            "args": ["--url=https://www.homedepot.com/p/Real-Flame-Eliot-Grand-81-in-Freestanding-Electric-Fireplace-TV-Stand-in-White-1290E-W/*********", "--snooze=10", "--disable=classify,autoclose", "--verbose"]

            //"program": "${workspaceFolder}/pmanalyze.js",
            //"args": ["run", "--app=prowl", "--account=companapet", "--verbose", "--sku=1031628 ", "--country=US"]
            // TODO: debug this MISMATCH... http://storage.googleapis.com/ps-syphonx/product-match-analyze/LkArqxXdd0wYgb0.html (why no cross-check output?)

            //"args": ["run", "--app=prowl", "--account=kitchenaid_canada", "--country=ca", "--sku=KMT2115SX", "--domain=londondrugs.com", "--verbose", "--concurrency=1", "--force"]
            //"args": ["run", "--app=prowl", "--account=epson", "--country=us", "--random", "--verbose", "--concurrency=1", "--limit=1"]
            //"args": ["run", "--app=prowl", "--account=shure", "--country=us", "--sku=BLX24/PG58", "--force", "--verbose", "--concurrency=1", "--limit=1", "--random", "--domain=reverb.com,newegg.com,bonanza.com,target.com,bhphotovideo.com,adorama.com,goknight.com,creationnetworks.net,sweetwater.com,shure.com,soundpro.com"]
            //"args": ["run", "--app=prowl", "--account=moen", "--sku=1222", "--country=ca", "--force", "--verbose"]

            //"program": "${workspaceFolder}/prompt.js",
            //"args": ["Tell me a joke.", "--model=claude-3-haiku-********"]

            //"program": "${workspaceFolder}/autogen.js",
            //"args": ["--url=https://www.leafbike.com/products/e-bike-hub-motor/electric-fat-bike-motor-brushless-gear-hub-48v-52v-500w-750w-1000w-freewheel-cassette-snow-tire-1130.html?VariantsId=10079"]

            //"program": "${workspaceFolder}/autogen-diffbot.js",
            //"args": ["--url=https://www.carid.com/sf-pl-mpn/pid-**********-mmyid-424-ofmpnid-**********.html", "--save=/pricespider/sandbox/carid.json"]
            //"args": ["--url=https://huellabeauty.com/products/the-form-file"]

            //"program": "${workspaceFolder}/diffbot.js",
            //"args": ["--url=https://www.bhphotovideo.com/c/product/1794146-REG/apc_ap4450a_netshelter_rack_automatic_transfer.html"]

            //"program": "${workspaceFolder}/prompt.js",
            ///"args": ["test/a.txt", "--json", "--show", "--labels", "--url=https://www.bhphotovideo.com/c/product/1794146-REG/apc_ap4450a_netshelter_rack_automatic_transfer.html"]

            //"program": "${workspaceFolder}/audit.js",
            //"args": ["brand_monitor", "name", "--seller=2146"]
            //"args": ["brand_monitor", "description", "--seller=7048773"]
            //"args": ["brand_monitor", "price", "--seller=4002534"]
            //"args": ["brand_monitor", "description", "--seller=24245540"]
            //"args": ["wtb", "price"]
            //"args": ["brand_monitor", "price", "--seller=3868505", "--sku=00087692300502"]

            //"program": "${workspaceFolder}/autoheal.js",
            //"args": ["brand_monitor"]
            //"args": ["brand_monitor", "--seller=********", "--test"]

            //"program": "${workspaceFolder}/locate.js",
            //"args": ["--url=https://www.otcsuperstore.com/product/frizz-ease-secert-weapn-finish-cream-4oz/"]
            //"args": ["--url=https://huellabeauty.com/products/the-form-file"]
            //"args": ["--url=https://huellabeauty.com/products/the-form-file", "--targets=name,price"]

            //"program": "${workspaceFolder}/fingerprint-scan.js",
            //"args": ["--app=wtb", "--account=dyson", "--country=US"]
        },
        {
            "name": "Run All Tests",
            "type": "node",
            "request": "launch",
            "skipFiles": ["<node_internals>/**"],
            "outFiles": ["${workspaceFolder}/**/*.js"],
            "sourceMaps": true,
            "internalConsoleOptions": "openOnSessionStart",
            "outputCapture": "std", // WORKAROUND https://github.com/microsoft/vscode/issues/19750
            "cwd": "${workspaceFolder}",
            "program": "${workspaceFolder}/node_modules/mocha/lib/cli/cli.js",
            "args": ["--config", "${workspaceFolder}/.mocharc.yml"],
            "runtimeArgs": ["--experimental-specifier-resolution=node"] // WORKAROUND (see devnotes)
        },
        {
            "name": "Run Selected Test",
            "type": "node",
            "request": "launch",
            "skipFiles": ["<node_internals>/**"],
            "outFiles": ["${workspaceFolder}/**/*.js"],
            "sourceMaps": true,
            "internalConsoleOptions": "openOnSessionStart",
            "outputCapture": "std", // WORKAROUND https://github.com/microsoft/vscode/issues/19750
            "cwd": "${workspaceFolder}",
            "program": "${workspaceFolder}/node_modules/mocha/lib/cli/cli.js",
            "args": [
                "--config", "${workspaceFolder}/.mocharc.yml",
                "--grep", "${selectedText}"
            ],
            "env": {
                "DEBUG": "1"
            },
            "runtimeArgs": ["--experimental-specifier-resolution", "node"]
        }
    ]
}