- click to create jira ticket
- cross check jira for product-id
- add a way to flag as reviewed (up to a certain date), so someone else will know not to look at it

- ops is checking a few times a week
- ops occasionally uses it more to prep for a client call (but not always)

- expect to find 1-2 issues per day
- often it finds issues where a ticket already exists
- provides re-inforcement to look at the issue again
- 10-11 clients, 3 pages per retailer -> 700+ pages
- current sample size is okay

- other fields: enhanced content, features, description



- Don't flag fields if Status is not OK
- Only show thumb-down action for fields that are actually flagged
- Add thumb-down to status field
- Only flag image-count if AI is less than production
- Add image_status that indicates if value was inferred from the star count
- Add visual examples for how to interpret image counts
- Collapse Scan Date and Capture Date into a single field with an hours offset
- Collapse Capture URL and Screenshot URL links into a single column
- Eliminate wasted whitespace in Seller column and remove the flag
- Seperate FLAGGED out from Status
- Simplify field filters on dashboard
- Only show rows with errors by default with option to show all
- Auto close thumb popups
- Simplify how regressions are highlighted



