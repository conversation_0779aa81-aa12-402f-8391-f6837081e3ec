import * as dotenv from "dotenv";
dotenv.config();

import * as fs from "fs";
import * as async from "async-parallel";
import chalk from "chalk";

import { PubSub } from "@google-cloud/pubsub";

import {
    autoselect,
    autoselectDictionaryKeys,
    collapseWhitespace,
    diffDate,
    generateUniqueId,
    insertAutoselectLog,
    loadAutoselectDictionaryCollection,
    loadYamlFile,
    lookupAutoselectDictionary,
    objIncrement,
    openPage,
    parseArgs,
    parseDomainName,
    parseYesNo,
    pick,
    queryAutoselect,
    queryAutoselectByDomain,
    queryAutoselectByDomainDictionary,
    queryPnuPages,
    runProductMatchAnalyzeTest,
    truncate,
    waitForKeypress,
    AutoselectFields,
    AutoselectPage,
    AutoselectRecord,
    Browser,
    Page,
    PnuPage,
    QueryAutoselectOptions
}
    from "./lib/index.js";

const maxConsecutiveErrors = 10;
const maxErrors = 100;

const args = parseArgs({
    optional: {
        1: "csv file containing a list of direct url's to scan",
        url: "direct product-page url to scan",
        app: "app-name (wtb, prowl, brand-monitor), default=all",
        account: "comma seperated list of account keys",
        brand: "filter by brand-name",
        country: "comma seperated list of country-codes",
        seller: "comma seperated list of seller-ids",
        domain: "comma seperated list of domain names",
        exclude: "comma seperated list of domain names to exclude",
        sku: "comma seperated list of skus",
        status: "comma seperated status filter (latest-valid, latest-invalid, never-valid, all), default=all",
        limit: "max # of rows to return, default=1000",
        subset: "subset rows to return per domain, default=3",
        snooze: "seconds to snooze after opening page, default=10",
        test: "tests auto-generated selector output",
        preview: "preview only (domains, urls)",
        view: "views auto-generated selectors for specified domains",
        browser: "specify browser engine (playwright, chrome), default=chrome",
        has: "product_name, price, product_image, blocked",
        hasnot: "product_name, price, product_image, blocked",
        force: "force override recent scans",
        random: "choose product(s) at random, optionally within other constraints",
        autoclose: "autoclose popup dialogs (default=no)",
        classify: "classify page (default=yes)",
        pause: "pause before proceeding (default=yes)",
        verbose: "verbose log output",
        import: "import manually authored selectors from file autoselect.yaml",
        preopen: "open all pages before starting, automatically disabled autoclose and classify options, optionally specify a max limit of pages to preopen (default=10)",
        serve: "serves queue requests",
        pnu: "process PNU crawl log entries for specified domain"
    },
    help: {
        short: "Auto generates CSS selectors for specified urls."
    }
});

if (args.verbose || args.url || args.serve)
    process.env.VERBOSE = "1";

process.env.BROWSER = args.browser || "chrome";

const random = !!args.random;
const force = !!args.force;
const autoselect_filter = args.has || args.hasnot;
const limit = parseInt(args.limit) || 1000;
const snooze = parseInt(args.snooze) * 1000 || 10000;
const t0 = new Date();

let fields: AutoselectFields[] | undefined = undefined;
if (args.preview === "domains")
    fields = ["domain_name"];
else if (args.preview === "urls")
    fields = ["domain_name", "seller_id", "seller_name", "page_id", "capture_url"];

const query_options: Partial<QueryAutoselectOptions> = {
    app_name: args.app,
    account_keys: args.account?.split(",").map(value => value.trim()).filter(Boolean),
    brand_name: args.brand,
    country_codes: args.country?.split(",").map(value => value.trim().toUpperCase()).filter(Boolean),
    skus: args.sku?.split(",").map(value => value.trim()).filter(Boolean),
    seller_ids: args.seller?.split(",").map(value => parseInt(value)).filter(Boolean),
    domain_names: args.domain?.split(",").filter(Boolean),
    exclude: args.exclude?.split(",").filter(Boolean) || [],
    capture_status: args.status?.split(","),
    limit: !autoselect_filter && args.preview !== "domains" ? limit : undefined,
    subset: args.preopen ? 1 : parseInt(args.subset),
    fields,
    random,
    force
};
query_options.exclude!.push(...["amazon.com", "ebay.com"]);

if (args.import) {
    if (!fs.existsSync("autoselect.yaml")) {
        console.log(chalk.yellow("autoselect.yaml not found"));
        process.exit();
    }
    const obj = loadYamlFile("autoselect.yaml") as AutoselectRecord;
    if (!((!obj.domain || obj.url) && obj.selectors && Object.keys(obj.selectors).length > 0)) {
        console.log(chalk.yellow("autoselect.yaml insufficient information"));
        process.exit();
    }
    const autoselect_id = generateUniqueId();
    const domain_name = obj.domain || parseDomainName(obj.url);
    const selectors = Object.keys(obj.selectors).map(key => ({ selector_name: key, selector: obj.selectors[key] }));
    await insertAutoselectLog({
        autoselect_id,
        domain_name,
        url: obj.url,
        source: "IMPORT",
        status: "OK",
        page_class: "PRODUCT-PAGE",
        selectors
    });
    console.log(chalk.gray(`${domain_name} inserted (${autoselect_id})`));
    process.exit();
}

if (args.view) {
    await loadAutoselectDictionaryCollection();
    if (args.view !== "1") {
        const obj = lookupAutoselectDictionary(args.view);
        if (obj)
            console.log(chalk.gray(JSON.stringify(obj, null, 2)));
        else
            console.log(chalk.gray("not found"));
        console.log();
    }
    else {
        const keys = autoselectDictionaryKeys();
        console.log(chalk.gray(keys.join("\n")));
        console.log(chalk.gray(`${keys.length} domains`))
    }
    process.exit();
}

if (args.preview === "domains") {
    await loadAutoselectDictionaryCollection();
    const rows = await queryAutoselectByDomain(query_options);
    for (const { domain_name, n } of rows) {
        if (autoselect_filter) {
            if (autoselect_show(domain_name))
                console.log(chalk.gray(`${domain_name}: ${n}`));
        }
        else {
            let obj = lookupAutoselectDictionary(domain_name);
            const keys = obj ? Object.keys(obj) : [];
            const missing = [];
            if (!keys.includes("product_name"))
                missing.push("product_name");
            if (!keys.includes("product_image"))
                missing.push("product_image");
            if (missing.length === 0)
                console.log(chalk.gray(`${chalk.white(domain_name)}: ${chalk.cyan(n)} ${chalk.green("READY")}`));
            else if (keys.length === 0)
                console.log(chalk.gray(`${chalk.white(domain_name)}: ${chalk.cyan(n)} ${chalk.yellow("NOT READY")} ${chalk.italic("(none)")}`));
            else
                console.log(chalk.gray(`${chalk.white(domain_name)}: ${chalk.cyan(n)} ${chalk.yellow("NOT READY")} ${chalk.italic(missing.map(key => chalk.strikethrough(key)).join(", "))}${chalk.italic(keys.length > 0 ? `, ${keys.join(", ")}` : "")}`));
        }
    }
    process.exit();
}

if (args.serve) {
    const pubsub = new PubSub();
    const received = new Set<string>();
    const domains: Record<string, number> = {};
    const subscription = pubsub.subscription("syphonx-autogen-runner", { flowControl: { maxMessages: 1 } });
    const maxReceiveSize = 50;
    const maxDomainSize = 5;
    subscription.on("message", async message => {
        const data = message.data.toString();
        console.log(chalk.gray(`RECEIVED: ${data}`));
        if (received.has(data)) {
            console.log(chalk.gray("Already received this message, skipping..."));
            message.ack();
            return;
        }
        received.add(data);
        if (received.size >= maxReceiveSize) {
            console.log(chalk.gray("Hit max message limit, exiting..."));
            process.exit();
        }

        if (data.startsWith("https://")) {
            const domain_name = parseDomainName(data);
            objIncrement(domains, domain_name);
            if (domains[domain_name] >= maxDomainSize) {
                console.log(chalk.gray(`Hit max domain limit for ${domain_name}, skipping...`));
                message.ack();
                return;
            }

            console.log(chalk.gray(`Opening page ${data}`));
            try {
                const result = await autoselect({
                    url: data,
                    domain_name,
                    source: "AUTO",
                    snooze
                });
                console.log(chalk.cyan(`autoselect returned status ${result.status} in ${(result.elapsed / 1000).toFixed(1)} seconds`));
                console.log(chalk.gray(result.report_url));
            }
            catch (err) {
                console.log(chalk.red(`ERROR: ${err instanceof Error ? err.message : JSON.stringify(err)}`));
            }
        }
        else {
            const domain_name = data;
            objIncrement(domains, domain_name);
            if (domains[domain_name] >= 5) {
                console.log(chalk.gray(`Hit max domain limit for ${domain_name}, skipping...`));
                message.ack();
                return;
            }
            let rows: any;
            if (args.pnu) {
                rows = await queryPnuPages(
                   domain_name,
                   parseInt(args.limit) || 1,
               );
            } else {
                rows = await queryAutoselect({
                    domain_names: [domain_name],
                    limit: parseInt(args.limit) || 1,
                    subset: 3,
                    random: true,
                    force: true
                });
            }
            console.log(chalk.gray(`Querying for ${data}...`));

            for (const row of rows) {
                try {
                    console.log(chalk.gray(`Opening page ${row.capture_url}`));
                    const result = await autoselect({
                        url: row.capture_url,
                        domain_name: row.seller_domain,
                        content_url: row.capture_url,
                        source: "AUTO",
                        snooze,
                        pause: !args.preopen ? parseYesNo(args.pause, true) : false,
                    });
                    console.log(chalk.cyan(`autoselect returned status ${result.status} in ${(result.elapsed / 1000).toFixed(1)} seconds`));
                    console.log(chalk.gray(result.report_url));
                }
                catch (err) {
                    console.log(chalk.red(`ERROR: ${err instanceof Error ? err.message : JSON.stringify(err)}`));
                }
            }
        }
        message.ack();
    });
    console.log(chalk.gray("Waiting for messages..."));
}
else {
    let rows: Partial<AutoselectPage>[] = [];
    if (args.url) {
        rows = [{
            capture_url: args.url,
            domain_name: parseDomainName(args.url)
        }];
        await loadAutoselectDictionaryCollection();
    }
    else if (args[0]) {
        const text = fs.readFileSync(args[0], "utf-8");
        const lines = text.split("\n").filter(Boolean);
        rows = lines.map(line => ({ capture_url: line, domain_name: parseDomainName(line) }));
        console.log(chalk.gray(`${rows.length} urls loaded from file ${args[0]}`));
        await loadAutoselectDictionaryCollection();
    }
    else if (args.pnu) {
        const pnuRows = await queryPnuPages(args.pnu, limit);
        console.log(chalk.gray(`${pnuRows.length} PNU rows returned for domain ${args.pnu}`));

        if (pnuRows.length === 0)
            process.exit();

        rows = pnuRows.map((row: PnuPage) => ({
            capture_url: row.capture_url,
            domain_name: row.seller_domain,
            page_id: row.page_id
        }));
        await loadAutoselectDictionaryCollection();
    }
    else {
        rows = await queryAutoselect(query_options);
        console.log(chalk.gray(`${rows.length} rows returned`));

        if (rows.length === 0)
            process.exit();
        await loadAutoselectDictionaryCollection();

        if (args.preview) {
            let seller_id = 0;
            for (const row of rows) {
                if (autoselect_filter && !autoselect_show(row.domain_name!))
                    continue; // skip if we already have an autoselector
                if (row.seller_id && seller_id !== row.seller_id) {
                    const key = `${row.seller_name} ${row.seller_id}`;
                    console.log();
                    console.log(chalk.underline(autoselect_show(row.domain_name!) ? key : chalk.italic.yellow(key)));
                    seller_id = row.seller_id;
                }
                if (args.preview === "urls")
                    console.log(chalk.gray(row.capture_url));
                else
                    console.log(`${row.page_id} ${chalk.gray(row.capture_url)}`);
            }
            if (rows.some(row => !lookupAutoselectDictionary(row.domain_name)) && !autoselect_filter)
                console.log(chalk.italic.yellow("\nyellow = no autoselect"));
            process.exit();
        }
        else if (args.test) {
            const run_id = generateUniqueId();
            const counters: Record<string, number> = {};
            const browsers = new Set<Browser>();
            for (const row of rows as Array<Required<AutoselectPage>>) {
                const test_id = generateUniqueId();
                const test_result = await runProductMatchAnalyzeTest({ ...row, run_id, test_id, browsers });
                objIncrement(counters, ["OK", "EMPTY"].includes(test_result.status) ? test_result.status.toLowerCase() : "error");
                console.log(`[${rows.indexOf(row) + 1}/${rows.length}] ${row.page_id} ${test_result.status === "OK" ? chalk.green(test_result.status) : chalk.yellow(test_result.status)}${test_result.hits && test_result.misses ? chalk.gray(` (${test_result.hits.length} hits, ${test_result.misses.length} misses)`) : ""}`);
                if (process.env.VERBOSE) {
                    console.log(chalk.gray(row.capture_url));
                    if (test_result.obj)
                        for (const key of Object.keys(test_result.obj).sort())
                            console.log(chalk.gray(`- ${chalk.cyan(key)}: ${test_result.obj[key] ? key !== "product_image" ? truncate(collapseWhitespace(test_result.obj[key], { singleLine: true, collapseSpaces: true }), 120, true) : test_result.obj[key] : chalk.italic("(none)")}`));
                }
            }
            for (const browser of browsers.values())
                await browser.close();
            console.log();
            console.log(chalk.gray(`${rows.length} pages tested, ${counters.ok || 0} ok, ${counters.empty || 0} empty, ${counters.error || 0} errors`));
            process.exit();
        }
    }

    // sort rows by descending domain count
    await orderByDomainCountDescending(rows);

    console.log();
    const pages: Record<string, Page> = {};
    if (args.preopen)
        await preopen(rows, pages);

    let inserted = 0;
    let errors = 0;
    let consecutive_errors = 0;
    let summary = [];

    for (const row of rows) {
        const t1 = new Date();
        let page: Page | undefined = undefined;
        if (args.preopen) {
            page = pages[row.capture_url!];
            try {
                await page.evaluate(() => 0);
            }
            catch (err) {
                console.log(`[${rows.indexOf(row) + 1}/${rows.length}] ${row.domain_name} SKIPPED ${chalk.italic("(page closed)")}`);
                continue;
            }
        }

        if (!force && !args.url && !args.pnu) {
            if (autoselect_status(row.domain_name!) === "ready") {
                console.log(`[${rows.indexOf(row) + 1}/${rows.length}] ${row.domain_name} SKIPPED ${chalk.italic("(autoselect already exists)")}`);
                continue;
            }
        }

        if (autoselect_filter && limit && inserted >= limit) {
            if (process.env.VERBOSE)
                console.log(chalk.gray(`\nHit ${limit} insertions limit, exiting...`));
            break; // hit limit for autoselect filtering
        }

        if (process.env.VERBOSE) {
            console.log(`[${rows.indexOf(row) + 1}/${rows.length}] ${row.domain_name}`);
            console.log(chalk.gray(`Opening page ${row.capture_url}${row.page_id ? ` [${row.page_id}]` : ""}`));
        }

        const result = await autoselect({
            url: row.capture_url!,
            source: "AUTO",
            domain_name: row.domain_name,
            //content_url: row.content_url,
            title: row.page_id,
            snooze,
            autoclose: parseYesNo(args.autoclose, false),
            classify: parseYesNo(args.classify, !args.preopen), // classify by default unless in preopen mode
            pause: !args.preopen ? parseYesNo(args.pause, true) : false,
            page
        });

        if (result.ok) {
            inserted += 1;
            consecutive_errors = 0;
        }
        else {
            errors += 1;
            consecutive_errors += 1;
            if ((consecutive_errors > maxConsecutiveErrors || errors > maxErrors)) {
                console.log(chalk.red(`Aborting, too many errors...`));
                break;
            }
        }

        if (!process.env.VERBOSE)
            console.log(`[${rows.indexOf(row) + 1}/${rows.length}] ${result.domain_name} ${chalk.gray(result.report_url)}${result.status !== "OK" ? chalk.red(" FAILED") : ""} in ${diffDate(t1)}`);

        if (process.env.VERBOSE) {
            console.log(chalk.gray(`${chalk.gray(result.report_url)}${result.status !== "OK" ? chalk.red(" FAILED") : ""} in ${diffDate(t1)}`));
            console.log();
        }

        summary.push(`${result.report_url} in ${diffDate(t1)}`);
    }

    console.log(chalk.gray(`${inserted} inserted, ${errors} errors`));
    console.log();
    console.log(chalk.gray("SUMMARY"));
    console.log(chalk.gray(summary.join("\n")));
    console.log(`${summary.length} autoselect pages generated in ${diffDate(t0)}`)

    process.exit();
}

function autoselect_show(domain_name: string): boolean {
    let obj = lookupAutoselectDictionary(domain_name);
    if (!obj)
        return !!args.hasnot;

    if (args.has) {
        if (args.has === "product-page")
            args.has = "product_name,price";
        const keys = args.has.split(",");
        obj = pick(obj, keys);
        return Object.keys(obj).length === keys.length;
    }

    if (args.hasnot) {
        if (args.hasnot === "product-page")
            args.hasnot = "product_name,price";
        const keys = args.hasnot.split(",");
        obj = pick(obj, keys);
        return Object.keys(obj).length < keys.length;
    }

    return Object.keys(obj).length > 0;
}

function autoselect_status(domain_name: string): "ready" | "not-ready" {
    const obj = lookupAutoselectDictionary(domain_name);
    const keys = obj ? Object.keys(obj) : [];
    const missing = [];
    if (!keys.includes("product_name"))
        missing.push("product_name");
    if (!keys.includes("product_image"))
        missing.push("product_image");
    return missing.length === 0 ? "ready" : "not-ready";
}

async function orderByDomainCountDescending(rows: Partial<AutoselectPage>[]): Promise<void> {
    const domains = await queryAutoselectByDomainDictionary(query_options);
    rows.sort((a, b) => domains[b.domain_name!] - domains[a.domain_name!]);
}

async function preopen(rows: Partial<AutoselectPage>[], pages: Record<string, Page>, concurrency = 2): Promise<void> {
    const preopen_limit = args.preopen === "1" ? 10 : parseInt(args.preopen);
    console.log(chalk.gray(`Preopening ${rows.length} pages... (${preopen_limit} max)`));

    let i = 0;
    await async.each(rows, async row =>
        await preopen_page(row, pages, preopen_limit, ++i, rows.length), concurrency);

    if (Object.keys(pages).length === 0) {
        console.log(chalk.red("No pages preopened, exiting..."));
        process.exit(1);
    }

    console.log(chalk.gray(`${Object.keys(pages).length}/${rows.length} pages preopened`))
    if (parseYesNo(args.pause, true)) {
        console.log("Press any key to continue...");
        await waitForKeypress();
    }
}

async function preopen_page(row: Partial<AutoselectPage>, pages: Record<string, Page>, limit: number, i: number, n: number): Promise<void> {
    const { capture_url: url, domain_name } = row;
    if (Object.keys(pages).length > limit) {
        console.log(chalk.gray(`[${i}/${n}] ${url} SKIPPED ${chalk.yellow.italic("(hit preopen limit)")}`));
    }
    else if (!force && !args.url && !args.pnu && autoselect_status(domain_name!) === "ready") {
        console.log(chalk.gray(`[${i}/${n}] ${url} SKIPPED ${chalk.green.italic("(autoselect already exists)")}`));
    }
    else {
        console.log(chalk.gray(`[${i}/${n}] ${url}`));
        const open_result = await openPage({ url, headless: false });
        if (open_result.page)
            pages[url!] = open_result.page;
        else
            console.log(chalk.red(`ERROR: Failed to open page ${url}... ${open_result.error}`));
    }
}
