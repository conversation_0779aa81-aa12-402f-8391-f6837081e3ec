/*
ASSUMPTIONS:
capture_id, select_date tuple is unique
capture_id is persistent (once recorded never goes away)
capture_id is immutable

QUESTIONS:
1. What are all the different ways captures being are filtered between the conflux_product_page_captures and product_page_captures_all tables?
2. Why are there so many duplicates in the conflux_product_page_captures table?
3. Why are some captures being removed from the conflux_product_page_captures table? (example: c96e9477-46a0-4bbd-b7de-1d3fad0c940b)
*/
SELECT
  DATE(capture_date),
  COUNT(*) AS total_count,
  COUNT(DISTINCT capture_id) AS distinct_count,
  COUNTIF(select_date IS NOT NULL) AS reselect_count,
  COUNT(DISTINCT CONCAT(capture_id, capture_date)) - COUNT(DISTINCT capture_id) AS reuse_count,
  COUNT(*) - COUNT(DISTINCT capture_id) AS duplicate_count,
  ROUND(SAFE_DIVIDE(COUNT(*) - COUNT(DISTINCT capture_id), COUNT(*)), 3) AS duplicate_percent,
FROM brand_monitor.conflux_product_page_captures
WHERE DATE(capture_date)>='2024-02-26'
GROUP BY 1
ORDER BY 1 DESC;

SELECT capture_id, COUNT(*),
FROM brand_monitor.conflux_product_page_captures
WHERE DATE(capture_date)>='2024-02-26'
GROUP BY 1
ORDER BY 2 DESC;


SELECT * FROM (
  SELECT
    capture_id,
    capture_date,
    COUNT(*) OVER (PARTITION BY capture_id) AS hit_count
  FROM brand_monitor.conflux_product_page_captures
  WHERE DATE(capture_date)>='2024-02-26'
)
WHERE hit_count>1
ORDER BY 1 DESC;

SELECT *
FROM brand_monitor.conflux_product_page_captures
WHERE DATE(capture_date)>='2024-02-26'
AND capture_id IN ('69be3366-0158-45e5-886d-0a84c6a142d9');

SELECT *, FARM_FINGERPRINT(TO_JSON_STRING(all_data))
FROM brand_monitor.conflux_product_page_captures
WHERE DATE(capture_date)>='2024-02-26'
AND capture_id IN ('69be3366-0158-45e5-886d-0a84c6a142d9');


SELECT
  DATE(capture_date),
  COUNT(*) AS total_count,
  COUNT(DISTINCT capture_id) AS distinct_count,
  COUNTIF(select_date IS NOT NULL) AS reselect_count,
  COUNT(DISTINCT CONCAT(capture_id, capture_date)) - COUNT(DISTINCT capture_id) AS reuse_count,
  COUNT(*) - COUNT(DISTINCT capture_id) AS duplicate_count,
  ROUND(SAFE_DIVIDE(COUNT(*) - COUNT(DISTINCT capture_id), COUNT(*)), 3) AS duplicate_percent,
FROM brand_monitor.product_page_captures_all
WHERE DATE(capture_date)>='2024-02-10'
GROUP BY 1
ORDER BY 1 DESC;