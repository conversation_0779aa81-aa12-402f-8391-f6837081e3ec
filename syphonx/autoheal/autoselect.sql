CREATE OR R<PERSON>LACE TABLE omega.autoselect
(
  autoselect_id STRING,
  timestamp TIMESTAMP,
  domain_name STRING,
  url STRING,
  source STRING,
  status STRING,
  error STRING,
  page_class STRING,
  page_data JSON,
  report_url STRING,
  selectors ARRAY<STRUCT<
    selector_name STRING,
    selector STRING
  >>,
  analyze ARRAY<STRUCT<
    name STRING NOT NULL,
    message STRING,
    model STRING,
    tokens INT64,
    input_tokens INT64,
    output_tokens INT64,
    cost FLOAT64,
    elapsed INT64
  >>
)
PARTITION BY DATE(timestamp)
CLUSTER BY status, domain_name;


CREATE OR REPLACE TABLE omega.autoselect_log
PARTITION BY DATE(timestamp)
CLUSTER BY status, domain_name
AS
SELECT
  autoselect_id,
  timestamp,
  domain_name,
  url,
  source,
  status,
  error,
  page_class,
  page_data,
  report_url,
  selectors,
  analyze
FROM omega.autoselect;


CREATE OR REPLACE VIEW omega.autoselect
AS
WITH
selectors AS (
  SELECT
    domain_name,
    selector_name,
    selector,
    source,
    autoselect_id,
    timestamp,
    report_url
  FROM omega.autoselect_log, UNNEST (selectors)
  WHERE status='OK'
  QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name, selector_name, selector ORDER BY timestamp DESC) = 1
),
grouped_selectors AS (
  SELECT
    domain_name,
    selector_name,
    ARRAY_TO_STRING(ARRAY_AGG(selector ORDER BY timestamp DESC), '\n') AS selector,
    MAX(timestamp) AS timestamp,
    ANY_VALUE(autoselect_id) AS autoselect_id,
    ANY_VALUE(source) AS source,
    ANY_VALUE(report_url) AS report_url
  FROM selectors
  GROUP BY 1, 2
)
SELECT
  domain_name,
  ARRAY_AGG(STRUCT(
    selector_name,
    selector,
    timestamp,
    autoselect_id,
    source,
    report_url
  ) ORDER BY selector_name) AS selectors
FROM grouped_selectors
GROUP BY 1
ORDER BY 1;


CREATE OR REPLACE VIEW lookerstudio.autoselect_audits
AS
SELECT
  DATETIME(timestamp, 'America/Los_Angeles') AS timestamp,
  REGEXP_EXTRACT(LAX_STRING(data.href), r'/([^/]+)\.html$') AS autoselect_id,
  LAX_STRING(data.selector) AS selector,
  LAX_STRING(data.context) AS context,
  LAX_STRING(data.comment) AS comment,
  LAX_STRING(data.action) AS action,
  LAX_STRING(data.href) AS href,
  LAX_STRING(data.user) AS user,
  ip_address
FROM omega.user_log
WHERE key='autoselect';
