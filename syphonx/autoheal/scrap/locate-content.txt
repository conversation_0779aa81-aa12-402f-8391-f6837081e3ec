autoCorrectLabels(html, target, 50, labelCount);


function autoCorrectLabels(html: string, target: ResponseTarget, distance: number, max: number): void {
    if (target.text) {
        const $ = cheerio.load(html);
        for  (const label of target.labels) {
            const selector = `[__label__="${label}"]`;
            const text = $(selector).text().trim();
            const score = text ? matchText(target.text, text) : 0;
            if (score < 0.5) {
                const hits = matchLabels($, target.text, label, distance, max);
                const [hit] = hits;
                if (hit && hit[2] >= 0.7)
                    target.labels[0] = hit[0];
            }
        }
    }
}

function matchLabels($: cheerio.CheerioAPI, target: string, label: number, distance: number, max: number): Array<[number, string, number]> {
    const result = generateRange(label, distance, max)
        .map(i => ([i, $(`[__label__="${i}"]`).text().trim()] as [number, string]))
        .filter(([, text]) => text.length > 0)
        .map(([i, text]) => [i, text, matchText(target, text)] as [number, string, number])
        .filter(([, , score]) => score > 0.1)
        .sort((a, b) => b[2] - a[2]);
    return result;
}

/**
 * Algorithm that performs "loose" string matching with a specific focus on comparing alphanumeric
 * content and accounting for differences in length and content of the two strings.
 */
/*
function matchText(a: string, b: string): number {
    // If either a or b is empty or null, the function returns 0, indicating no similarity.
    if (!a || !b)
        return 0;

    // Both strings are converted to lowercase and all non-alphanumeric characters (anything other than letters and numbers) are removed, standardizing the strings for comparison
    let ax = a.toLowerCase().replace(/[^a-z0-9]/g, "");
    let bx = b.toLowerCase().replace(/[^a-z0-9]/g, "");

    // If the length of b (after preprocessing) is greater than a, their values are swapped to ensure that ax is always the longer string for the subsequent steps.
    const a0 = ax;
    if (bx.length > ax.length) {
        ax = bx;
        bx = a0;
    }

    // If ax (the longer string) contains bx (the shorter one), the function returns the length of bx divided by the length of ax giving a ratio indicating how much of ax is made up by bx.
    if (ax.includes(bx)) {
        const score = bx.length / ax.length;
        return score;
    }
    else {
        // If ax is significantly larger than bx, then fast-forward to the point at which bx starts appearing within ax.
        if (ax.length - bx.length > 5)
            ax = fastForwardTo(ax, bx);

        // Compute distance as a measure of how many single-character edits (insertions, deletions, or substitutions) are required to change one word into the other.
        const distance = levenshtein(ax, bx);

        // Compute a score as a measure of similarity that accounts for both the Levenshtein distance and the proportion of the original a that had to be discarded to find the match.
        const score = ((ax.length - distance) / ax.length) - (0.5 * (a0.length - ax.length) / ax.length);
        return score;
    }
}

function fastForwardTo(a: string, b: string): string {
    let n = b.length;
    let x = b.slice(0, n--);
    let i = a.indexOf(x);
    while (n > 0 && i === -1) {
        x = b.slice(0, n--);
        i = a.indexOf(x);
    }
    return i >= 0 ? a.slice(i, i + b.length) : a;
}

function generateRange(n: number, distance: number, max: number): number[] {
    const range = [];
    for (let i = 1; i <= distance; i++) {
        if (n - i >= 0)
            range.push(n - i);
        if (n + i <= max)
            range.push(n + i);
    }
    return range;
}
