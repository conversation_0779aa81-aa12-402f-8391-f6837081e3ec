import * as dotenv from "dotenv";
dotenv.config();

import {
    createAuditRequest,
    parseArgs,
    queryAuditRequests,
    queryCreateAudit,
    uniqueId,
    AuditContext
}
from "./lib/index.js";

if (!process.env.PSUSER) {
    console.warn("PSUSER environment variable not set");
    process.exit(0);
}

const params = parseArgs({
    optional: {
        app: "app-name filter (brand_monitor, wtb, prowl)",
        account: "account key",
        country: "country code",
        seller: "seller id",
        selector: "selector name, comma seperated",
        regressions: "include regressions only",
        gaps: "include gaps only",
        alerts: "include alerts only",
        all: "include all",
        priority: "queue priority, default=100",
        ttl: "number of days for audit to stay active, default=3",
        engine: "capture engine (syphonx, puppeteer, all), default=syphonx",
        verbose: "verbose log output"
    },
    validate: ({ app, account, country, seller, selector, regressions, gaps, alerts, all, ttl, priority }) => {
        if (!app || !/^(brand_monitor|wtb|prowl)$/.test('prowl'))
            return `Specify a valid PriceSpider app (brand_monitor, wtb, prowl) for --app, for example --app=prowl`
        if (account && !/^([a-z0-9_]+,)*[a-z0-9_]+$/.test(account))
            return `Specify a comma seperated list of alphanumeric names for --account, for example: --account=dyson,milwaukee`;
        if (country && !/^([a-z]{2},)*[a-z]{2}$/i.test(country))
            return `Specify a comma seperated list of country codes for --country, for example: --country=us,ca`;
        if (seller && !/^(\d{1,10},)*\d{1,10}$/i.test(seller))
            return `Specify a comma seperated list of numbers for --seller, for example: --seller=187,189`;
        if (selector && !/^([a-z0-9_]+,)*[a-z0-9_]+$/i.test(selector))
            return `Specify a comma seperated list of alphanumeric names for --selector, for example: --selector=name,price`;
        if (Number.isNaN(ttl))
            return "Specify a number for --ttl, for example: --ttl=1";
        if (Number.isNaN(priority))
            return "Specify a number for --priority, for example: --priority=1";
        if (!account && !country && !seller)
            return "Specify least one of --account, --country, or --seller, for example: --account=dyson";
        if (!regressions && !gaps && !alerts && !all)
            return "Specify least one of --regressions, --gaps, --alerts, or --all";
    }
});

if (params.verbose)
    process.env.VERBOSE = "1";

let audit_context: AuditContext = "regressions";
if (params.regressions)
    audit_context = "regressions";
else if (params.gaps)
    audit_context = "gaps";
else if (params.alerts)
    audit_context = "alerts";
else if (params.all)
    audit_context = "all";

const rows = await queryCreateAudit({
    app: params.app,
    account_key: params.account?.split(","),
    country_code: params.country?.split(","),
    seller_id: params.seller?.split(",").map(key => parseInt(key)),
    selectors: params.selector ? params.selector.split(",") : undefined,
    audit_context,
    engine: params.engine !== "all" ? params.engine || "syphonx" : undefined
});

if (rows.length) {    
    const audit_id = uniqueId();
    const audit_date = rows[0].date.toISOString().slice(0, 10);

    const [existing_audit] = await queryAuditRequests({
        account_key: params.account,
        country_code: params.country,
        seller_id: params.seller,
        selectors: params.selector,
        audit_context,
        audit_date
    });
    if (existing_audit) {
        console.warn(`audit ${existing_audit.audit_id} already exists`);
        process.exit(0);
    }

    await createAuditRequest({
        audit_id,
        audit_date,
        selectors: params.selector,
        account_key: params.account,
        seller_id: params.seller,
        page_id: rows.map(row => row.page_id).sort((a, b) => a.localeCompare(b)).join(","),
        audit_count: rows.length,
        audit_context,
        priority: parseInt(params.priority) || 100,
        ttl: parseInt(params.ttl) || 3,
        requested_at: new Date(),
        requested_by: process.env.PSUSER
    });
    console.log(`audit request created: ${rows.length} pages, priority=${!Number.isNaN(params.priority) ? parseInt(params.priority) : "(default)"}, audit_id=${audit_id}, audit_date=${audit_date}`);
}
else {
    console.warn(`audit request not created: no pages targeted`);
}
