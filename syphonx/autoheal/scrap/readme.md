## Overview

```mermaid
flowchart TB
    captures[product page captures]
    --> analyze[analyze captures]
    --> classify[regressions, gaps]
    --> revision[template revision history]
    --> query[query tool]
    --> check[check tool, stored captures]
    --> request[audit request tool]
    --> audit[audit tool]
    --> autoheal
    --> metrics
```

- manual metrics
- targeting

## Query
- `node metrics` lists regressions & gaps for all accounts
- `node metrics --account=sony --group=country` breakdown sony by country
- `node metrics --account=sony --group=seller` breakdown sony by seller
- `node metrics --account=sony --group=seller` breakdown sony by seller
- `node metrics --account=dyson --country=us --group=seller` breakdown dyson by seller within the US only
- `node metrics --account=milwaukee --group=selector` breakdown milwaukee by selector
- `node metrics --account=milwaukee --group=seller,selector` breakdown milwaukee by seller then by selector

# Check
- `node check --account=sony` checks selectors for sony
- `node check --account=milwaukee --seller=acehardware `

# More
`node metrics --group=seller --country=us` view by seller in the US only
