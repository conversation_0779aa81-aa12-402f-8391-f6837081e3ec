import * as dotenv from "dotenv";
dotenv.config();

import * as fs from "fs";
import { fileURLToPath } from "url";
import * as syphonx from "syphonx-lib";

import {
    createAuditCommit,
    download,
    //explainRegression,
    formatLineNums,
    formatTemplate,
    //loadCapture,
    loadTemplate,
    openai,
    parseArgs,
    queryAuditResponses,
    saveTemplate,
    tryParseJsonObject,
    updateTemplate
}
from "./lib/index.js";

interface PromptResponse {
    selector: string;
}

const params = parseArgs({
    optional: {
        verbose: "verbose log output"
    }
});

if (params.verbose)
    process.env.VERBOSE = "1";

const templates ={
    generate_prompt: fs.readFileSync(fileURLToPath(new URL("./prompts/generate.txt", import.meta.url)), "utf-8")
};

const rows = await queryAuditResponses();
const [row] = rows;

console.log(`${row.audit_id} ${row.page_id}`);

const audit_html = row.html ? await download(row.html) : undefined;

const template_path = row.template_path;
const { template } = await loadTemplate(template_path);

const [audit_result] = row.audit_result;

const prompt_html = formatLineNums(audit_result.selector_html, audit_result.html_linenums);
const prompt = formatTemplate(templates.generate_prompt, { prompt_html });
const { messages, usage } = await openai.chat({ prompt, json: true });

const { selector: generated_selector } = tryParseJsonObject(messages.at(-1)?.content) as PromptResponse;
if (!generated_selector) {
    console.log(`${audit_result.selector_name}: no selector found`);
    process.exit(0);
}

console.log(`${audit_result.selector_name}: generated selector "${generated_selector}"`);
console.log(`usage=${usage}`);

const update = updateTemplate(template, audit_result.selector_name, generated_selector);
if (update.messages.length > 0)
    console.log(update.messages.join("\n"));

if (!update.ok || !audit_html) {
    console.log(`${audit_result.selector_name}: unable to update template`);
    process.exit(0);
}

if (!update.select) {
    console.log(`${audit_result.selector_name}: unable to validate selector`);
    process.exit(0);
}

const state = syphonx.select([update.select], audit_html);
if (!state.data[audit_result.selector_name].value.includes(audit_result.selector_text)) {
    console.log(`${row.page_id}  ${audit_result.selector_name}: selector validation failed`);
    process.exit(0);
}

console.log(`${row.page_id}  ${audit_result.selector_name}: selector validated!`);

/*
const { reference } = row.selectors.find(obj => obj.selector_name === audit_result.selector_name) || {};
if (reference) {
    const html = await loadCapture(reference.capture_id);
    if (html) {
        await explainRegression(html, audit_result.selector_name, template, generated_selector, prompt_html);
    }
}
*/

let passed = 0;
let failed = 0;
for (const row of rows.slice(1)) {
    const audit_html = row.html ? await download(row.html) : undefined;
    if (audit_html) {
        const state = syphonx.select([update.select], audit_html);
        //if (state.data[audit_result.selector_name].value.includes(audit_result.selector_text)) {
        if (state.data[audit_result.selector_name].value) {
            passed += 1;
            console.log(`${row.page_id} ${audit_result.selector_name}: selector validated`);
        }
        else {
            failed += 1;
            console.log(`${row.page_id} ${audit_result.selector_name}: selector validation failed`);
        }        
    }
}

if (passed > 1 && failed < 0.1 * passed) {
    await saveTemplate(template_path, update.template);
    await createAuditCommit({
        commit_id: "",
        commit_date: new Date(),
        audit_id: row.audit_id,
        template_path,
        seller_name: row.seller_name,
        seller_id: row.seller_id,
        commit: [{
            selector_name: audit_result.selector_name,
            before: audit_result.selector_path,
            after: generated_selector,
            comment: `selector validated on ${passed} pages`
        }]
    });
    console.log(`${template_path} template updated`);
}

process.exit(0);
