# Ω OMEGA AI Toolkit

This is an AI-powered web scraping maintenance system called "Autoheal" that automatically detects and repairs broken product page selectors in the CONFLUX data extraction pipeline.

### Key Features
- Analyzes product pages using various AI models (Claude, GPT-4, etc.) to detect data accuracy issues
- Supports multiple retailers/sellers (excluding certain major platforms like Amazon, Walmart)
- Uses AI-based extraction rather than traditional selectors
- Includes tools for:
    - Classification of page types
    - Location of product data (name, price, etc.)
    - Automated selector generation
    - Template revision management
    - Regression detection and analysis

The system follows a workflow of:
1. Analyzing product pages
2. Identifying regressions/issues
3. Auditing with AI
4. Automatically healing broken selectors
5. Tracking changes in template commits

The is focused on maintaining data quality and automation for e-commerce product data extraction at scale.
