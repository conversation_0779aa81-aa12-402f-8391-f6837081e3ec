import * as dotenv from "dotenv";
dotenv.config();

import * as fs from "fs";
import chalk from "chalk";

import {
    diffDate,
    parseArgs,
    queryPendingProductCategoryProductIds
} from "./lib/index.js";

import {
    autocatInitialize,
    bufferPendingProducts,
    generateBatchFiles,
    submitBatches
} from "./autocat-common.js";

const args = parseArgs({
    optional: {
        check: "check pending",
        pending: "max number of pending rows to process (default=100000)",
        "batch-limit": "max number of batches to create (default=10)",
        "batch-megabytes": "max batch file size in megabytes (default=10)"
    }
});

const megabytes = 1048576;
const max_batches = parseInt(args["batch-limit"]) || 100;
const max_pending_rows = parseInt(args.pending) || 100000;
const max_batch_size = args["batch-megabytes"] ? parseInt(args["batch-megabytes"]) * megabytes : 10 * megabytes;
const t0 = new Date();

console.log(chalk.gray("Query pending products..."));
const t1 = new Date();
const all_product_ids = await queryPendingProductCategoryProductIds({ limit: max_pending_rows });
console.log(chalk.gray(`${all_product_ids.length} pending products returned in ${diffDate(t1)}`));
if (all_product_ids.length === 0 || args.check)
    process.exit();

console.log();
console.log(chalk.gray("Buffering data..."));
const t2 = new Date();
const buffer_file = await bufferPendingProducts(all_product_ids);
console.log(chalk.gray(`${all_product_ids.length} rows buffered in ${diffDate(t2)}`));

console.log();
console.log(chalk.gray("Downloading product categories..."));
await autocatInitialize();

console.log();
console.log(chalk.gray("Generating batch files..."));
const t3 = new Date();
const batch_files = await generateBatchFiles(buffer_file, all_product_ids, true, max_batches, max_batch_size);
fs.rmSync(buffer_file);
console.log(chalk.gray(`${batch_files.length} files generated in ${diffDate(t3)}`));
for (const { output_file, target_ids } of batch_files)
    console.log(chalk.gray(`${output_file} (${target_ids.length} lines)`));

console.log();
console.log(chalk.gray("Submitting batch files..."));
const batch_ids = await submitBatches(batch_files);

console.log(chalk.gray(batch_ids.join("\n")));
console.log(chalk.gray(`${batch_files.length} batches submitted in ${diffDate(t0)}`));
process.exit();
