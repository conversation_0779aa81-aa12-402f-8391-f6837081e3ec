
SELECT * FROM omega.product_match_analyze_pending WHERE account_key='moen' AND brand_name='House of Rohl'






CREATE OR REPLACE VIEW lookerstudio.product_match_analyze
AS
SELECT * REPLACE(DATETIME(timestamp, 'America/Los_Angeles') AS timestamp)
FROM omega.product_match_analyze_view;



-- view number of skus by account
SELECT account_key, COUNT(*) AS sku_count
FROM omega.product_match_analyze_snapshot
WHERE app_name='prowl'
GROUP BY 1
ORDER BY 2 DESC;

-- view latest analyze results
SELECT * FROM omega.product_match_analyze_view;

-- view cost
SELECT SUM(cost), SUM(diffbot_tokens), SUM(ARRAY_LENGTH(product_matches)), COUNT(*) FROM omega.product_match_analyze WHERE account_key='moen' AND timestamp>='2024-11-15';
