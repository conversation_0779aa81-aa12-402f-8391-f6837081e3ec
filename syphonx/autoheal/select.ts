import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";
import { flattenTemplateSelect, renderJQuery } from "syphonx-lib";

import {
    online,
    parseArgs,
    queryTemplatePathFromUrl,
    tryLoadTemplate
}
from "./lib/index.js";

const params = parseArgs({
    required: {
        0: "brand_monitor, wtb, prowl",
        1: "url"
    },
    optional: {
        pause: "pause for user input after each page load",
        snooze: "amount of time in seconds to snooze after loading a live page (default=2) (ignored if pause option is used)",
        headless: "hide browser window (default=false)",
        waitfor: "specify load, domcontentloaded, or networkidle (default=none)",
        unwrap: "simplified unwrapped output"
    },
    validate: params => {
        if (!["brand_monitor", "wtb", "prowl"].includes(params[0]))
            return "specify brand_monitor, wtb, or prowl only";
        if (!/^https?:\/\//.test(params[1]))
            return "invalid url"
    }
});

const app_name = params[0];
const url = params[1];
const headless = !!params.headless;
const pause = !!params.pause;
const snooze = parseInt(params.snooze) || undefined;
const unwrap = !!params.unwrap;

const template_path = await queryTemplatePathFromUrl(url, app_name);
if (!template_path) {
    console.log(chalk.red(`no template found`));
    process.exit(0);
}

console.log(chalk.gray(`loading template ${template_path}...`));
const { template, ok: template_ok, error: template_error } = await tryLoadTemplate(template_path);
if (!template || !template_ok) {
    console.log(chalk.red(`unable to load template ${template_path}${template_error ? ` ${JSON.stringify(template_error)}`: ""}`));
    process.exit(0);
}

if (!template.params)
    template.params = {};

if (params.timeout)
    Object.assign(template.params, { timeout: parseInt(params.timeout) });

if (params.waitfor)
    Object.assign(template.params, { waitfor: params.waitfor });

console.log();
const selects = flattenTemplateSelect(template.actions);
for (const select of selects) {
    console.log(chalk.gray.bold(`${select.name}: (type=${select.type || "default"}${select.repeated ? ", repeated)": ")"}`));
    if (select.query)
        for (const query of select.query)
            console.log(chalk.gray(`  ${renderJQuery(query)}`));
}
console.log();

await online({ template, url, headless, pause, snooze, unwrap });
process.exit(0);
