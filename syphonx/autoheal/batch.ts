import * as dotenv from "dotenv";
dotenv.config();

import * as fs from "fs";
import chalk from "chalk";
import OpenAI from "openai";
import { Batch } from "openai/src/resources";
import * as stream from "stream";
import * as util from "util";
import { parseArgs, diffDate } from "./lib/index.js";

const openai = new OpenAI();
const pipeline = util.promisify(stream.pipeline);

const args = parseArgs({
    optional: {
        0: "command: submit, status, fetch, cancel",
        1: "file for submit, or batch-id for status and fetch",
        2: "output file for fetch",
        all: "output all results",
        limit: "limit number of output results (default=10, ignored if all specified)",
        verbose: "verbose log output"
    },
    validate: args => {
        if (args[0] && !["submit", "status", "fetch", "cancel"].includes(args[0]))
            return "specify command: submit, status, fetch";
        if (args[0] === "submit" && !args[1])
            return "specify file to submit";
        if (["fetch", "cancel"].includes(args[0]) && !args[1])
            return "specify batch-id or part of a batch-id, use list command to view batches";
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

const command = args[0] || "status";
const target = args[1];
const limit = args.all ? undefined : parseInt(args.limit) || 20;

try {
    if (command === "submit") {
        const batch = await submitBatch(target);
        console.log(chalk.gray(`Batch ${batch.id} submitted`));
        if (process.env.VERBOSE)
            console.log(chalk.gray(JSON.stringify(batch, null, 2)));
    }

    if (command === "status" && !target) {
        const batches = await listBatches(limit);
        for (const batch of batches)
            console.log(formatBatchInfo(batch));
        if (process.env.VERBOSE)
            console.log(chalk.gray(JSON.stringify(batches, null, 2)));
    }

    if (target) {
        const batch = await findBatch(target);
        console.log(chalk.gray(`batch: ${batch.id}`));
        console.log(chalk.gray(`completion window: ${batch.completion_window}`));
    
        if (command === "status") {
            if (batch.status === "completed") {
                console.log(chalk.gray(`status: ${chalk.green(batch.status)}`));
                console.log(chalk.gray(`created: ${formatTimestamp(batch.created_at)}`));
                if (batch.completed_at) {
                    console.log(chalk.gray(`completed: ${formatTimestamp(batch.completed_at)}`));
                    console.log(chalk.gray(`elapsed: ${diffDate(toDate(batch.completed_at), toDate(batch.created_at))}`));
                }
                if (batch.request_counts)
                    console.log(chalk.gray(`${batch.request_counts.completed}/${batch.request_counts.total} completed, ${batch.request_counts.failed} failed`));
            }
            else if (batch.status === "validating") {
                console.log(chalk.gray(`status: ${chalk.blue(batch.status)}`));
                console.log(chalk.gray(`created: ${formatTimestamp(batch.created_at)}`));
            }
            else if (batch.status === "in_progress") {
                console.log(chalk.gray(`status: ${chalk.magenta(batch.status)}`));
                console.log(chalk.gray(`created: ${formatTimestamp(batch.created_at)}`));
                if (batch.in_progress_at) {
                    console.log(chalk.gray(`started: ${formatTimestamp(batch.in_progress_at)}`));
                    console.log(chalk.gray(`elapsed: ${diffDate(toDate(batch.in_progress_at), toDate(batch.created_at))}`));
                }
                if (batch.request_counts)
                    console.log(chalk.gray(`${batch.request_counts.completed}/${batch.request_counts.total}, ${batch.request_counts.failed} failed`));
            }
            else if (batch.status === "failed") {
                console.log(chalk.gray(`status: ${chalk.red(batch.status)}`));
                console.log(chalk.gray(`created: ${formatTimestamp(batch.created_at)}`));
                if (batch.failed_at) {
                    console.log(chalk.gray(`failed: ${formatTimestamp(batch.failed_at)}`));
                    console.log(chalk.gray(`elapsed: ${diffDate(toDate(batch.failed_at), toDate(batch.created_at))}`));
                }
                if (batch.errors?.data) {
                    console.log(chalk.gray(`${batch.errors.data.length} errors`));
                    for (const { message, code, line } of batch.errors.data)
                        console.log(chalk.gray.italic(`${message} [${code}] @${line}`));
                }
            }
            else if (["cancelling", "cancelled"].includes(batch.status)) {
                console.log(chalk.gray(`status: ${chalk.yellow(batch.status)}`));
                console.log(chalk.gray(`created: ${formatTimestamp(batch.created_at)}`));
                if (batch.cancelled_at) {
                    console.log(chalk.gray(`cancelled: ${formatTimestamp(batch.cancelled_at)}`));
                    console.log(chalk.gray(`elapsed: ${diffDate(toDate(batch.cancelled_at), toDate(batch.created_at))}`));
                }
            }
            else if (batch.status === "finalizing") {
                console.log(chalk.gray(`status: ${chalk.blue(batch.status)}`));
                console.log(chalk.gray(`created: ${formatTimestamp(batch.created_at)}`));
                if (batch.finalizing_at) {
                    console.log(chalk.gray(`finalizing: ${formatTimestamp(batch.finalizing_at)}`));
                    console.log(chalk.gray(`elapsed: ${diffDate(toDate(batch.finalizing_at), toDate(batch.created_at))}`));
                }
            }
            else {
                console.log(chalk.gray(`status: ${chalk.gray(batch.status)}`));
                console.log(chalk.gray(`created: ${formatTimestamp(batch.created_at)}`));
                if (batch.expires_at)
                    console.log(chalk.gray(`expires: ${formatTimestamp(batch.expires_at)}`));
            }
        }
        
        if (command === "cancel") {
            if (["validating", "in_progress"].includes(batch.status)) {
                await openai.batches.cancel(batch.id);
                console.log(chalk.gray(`Batch ${batch.id} cancelled`));
            }
            else {
                console.log(chalk.yellow(`Cannot cancel, status=${batch.status}`));
            }
        }
        
        if (command === "fetch") {
            if (batch.output_file_id) {
                console.log(chalk.gray(`Downloading output file ${batch.output_file_id}...`));
                await fetchFile(batch.output_file_id);
            }
            if (batch.error_file_id) {
                console.log(chalk.gray(`Downloading error file ${batch.error_file_id}...`));
                await fetchFile(batch.error_file_id);
            }
        }
        if (process.env.VERBOSE)
            console.log(chalk.gray(JSON.stringify(batch, null, 2)));
    }
}
catch (err) {
    console.log(chalk.red(err instanceof Error ? err.message : JSON.stringify(err)));
}

async function fetchFile(file_id: string, output_file = file_id): Promise<string> {
    const response = await openai.files.content(file_id);
    if (!response.ok || !response.body)
        throw new Error(`Failed to fetch file ${file_id}: ${response.statusText}`);
    const writer = fs.createWriteStream(output_file, { encoding: "utf-8" });
    await pipeline(response.body, writer);
    return output_file;
}

async function findBatch(key: string): Promise<Batch> {
    if (key.startsWith("batch_") && key.length >= 32) {
        const batch = await openai.batches.retrieve(key);
        return batch;
    }
    else {
        const batches = await listBatches();
        const hits = batches.filter(batch => batch.id.includes(key));
        if (hits.length === 0)
            throw new Error(`Batch like "${key}" not found`);
        if (hits.length > 1)
            throw new Error(`Batch like "${key}" has ${hits.length} hits, be more specific`);
        const [hit] = hits;
        return hit;
    }
}

function formatBatchInfo(batch: Batch) {
    const error_count = batch.errors?.data ? batch.errors?.data.length : 0;
    const error_message = error_count > 0 ? batch.errors!.data![0].message : undefined;
    if (batch.status === "completed")
        return chalk.gray(`${chalk.bold(batch.id)} ${chalk.green(batch.status.padEnd(12))} ${chalk.gray(`${diffDate(toDate(batch.completed_at! ))} ago, ${batch.request_counts?.completed}/${batch.request_counts?.total} in ${diffDate(toDate(batch.completed_at!), toDate(batch.created_at))}, ${batch.request_counts?.failed} failed`)}`);
    else if (batch.status === "in_progress")
        return chalk.gray(`${chalk.bold(batch.id)} ${chalk.magenta(batch.status.padEnd(12))} ${chalk.gray(`${diffDate(toDate(batch.in_progress_at!))} ago`)}`);
    else if (batch.status === "finalizing")
        return chalk.gray(`${chalk.bold(batch.id)} ${chalk.magenta(batch.status.padEnd(12))} ${chalk.gray(`${diffDate(toDate(batch.finalizing_at!))} ago`)}`);
    else if (batch.status === "failed")
        return chalk.gray(`${chalk.bold(batch.id)} ${chalk.red(batch.status.padEnd(12))} ${chalk.gray(`${diffDate(toDate(batch.failed_at!))} ago${error_message ? ` ${chalk.italic(error_message)}` : ""}${error_count > 1 ? chalk.italic(` (+${error_count - 1} more errors)`) : ""}`)}`);
    else
        return chalk.gray(`${chalk.bold(batch.id)} ${chalk.gray(batch.status.padEnd(12))} ${chalk.gray(`${diffDate(toDate(batch.created_at))} ago`)}`);
}

function formatTimestamp(timestamp: number) {
    const date = toDate(timestamp);
    const options: Intl.DateTimeFormatOptions = { year: "numeric", month: "numeric", day: "numeric", hour: "numeric", minute: "numeric", hour12: true };
    return `${new Intl.DateTimeFormat(undefined, options).format(date)} ${chalk.italic(`(${diffDate(date)} ago)`)}`;
}

async function listBatches(limit?: number): Promise<Batch[]> {
    const result: Batch[] = [];
    const batches = await openai.batches.list({ limit });
    let i = 0;
    for await (const batch of batches)
        if (!limit || i++ < limit) // workaround for list not honoring limit
            result.push(batch as Batch);
    return result;
}

async function submitBatch(file: string) {
    const batch_file = await openai.files.create({
        file: fs.createReadStream(file),
        purpose: "batch"
    });
    
    const batch = await openai.batches.create({
        input_file_id: batch_file.id,
        endpoint: "/v1/chat/completions",
        completion_window: "24h"
    });

    return batch;
}

function toDate(timestamp: number): Date {
    return new Date(timestamp * 1000);
}
