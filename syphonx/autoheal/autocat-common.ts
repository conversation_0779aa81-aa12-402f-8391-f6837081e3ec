import * as fs from "fs";
import * as path from "path";
import chalk from "chalk";
import { SingleBar, Presets } from "cli-progress";

import {
    closeStream,
    diffDate,
    generateTimestamp,
    insertProductCategoryCompletions,
    loadProductCategories,
    openai,
    queryProductCategoryEmbeddings,
    readFromFileStream,
    readFromFileStreamAsync,
    regexpExtract,
    renderAutocatPrompt,
    sleep,
    snowflake,
    ProductCategory,
    ProductCategoryCompletion,
    ProductEmbed
} from "./lib/index.js";

import { findProductCategoryCandidates } from "./autocat-candidates.js";

export interface CreateAutocatPromptOptions {
    product_text: string;
    brand_summary: string;
    product_vector: number[];
    subcategory_ids: number[];
}

interface BatchFile {
    output_file: string;
    target_ids: number[];
}

const table_name = "WTB.PRODUCT_CATEGORY.product_category_completions";
const stage_name = table_name;

const temp_dir = process.env.TEMP_DIR || "tmp";
const output_dir = path.resolve(temp_dir, "autocat");
const bulk_insert_count = 100;
const max_candidates = 25;
const append_ancestor_depth = 2;
const chunk_size = 10000;
const max_errors = 100;
const max_consecutive_errors = 0;

const categories: ProductCategory[] = [];
const category_map: Record<string, number> = {};

export async function autocatInitialize() {
    if (categories.length === 0) {
        const data = await loadProductCategories();
        categories.push(...data);
        for (const { category_name, category_id } of categories)
            category_map[category_name] = category_id;
    }
}

export async function bufferPendingProducts(all_product_ids: number[]): Promise<string> {
    fs.mkdirSync(output_dir, { recursive: true });
    const output_file = path.resolve(output_dir, generateTimestamp("seconds"));
    const output_stream = fs.createWriteStream(output_file, { flags: "w" });
    const progress = new SingleBar({}, Presets.shades_classic);
    progress.start(all_product_ids.length, 0);
    let i = 0;
    while (i < all_product_ids.length) {
        const product_ids = all_product_ids.slice(i, i + chunk_size);
        const products = await queryProductCategoryEmbeddings({ product_ids, pending: false });
        for (const product of products) {
            output_stream.write(JSON.stringify(product) + "\n");
            progress.increment();
        }
        i += chunk_size;
    }
    progress.stop();
    await closeStream(output_stream);
    return output_file;
}

export function createAutocatPrompt({ product_text, brand_summary, product_vector, subcategory_ids }: CreateAutocatPromptOptions): string {    
    if (categories.length === 0)
        throw new Error("autocat not initialized");
    if (!product_text)
        throw new Error("product_text is required");
    if (!brand_summary)
        throw new Error("brand_summary is required");
    if (!product_vector || product_vector.length === 0)
        throw new Error("product_vector is required");
    if (!product_vector || product_vector.length !== categories[0].category_vector.length)
        throw new Error(`product_vector incorrect vector length ${product_vector.length}, ${categories[0].category_vector.length} expected`);
    const candidates = findProductCategoryCandidates(categories, product_vector, subcategory_ids, max_candidates, append_ancestor_depth);
    const prompt = renderAutocatPrompt({ product_text, brand_summary, candidates });
    return prompt;
}

export interface CreateCompletionOptions {
    prompt: string;
    content: string;
    model: string;
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    product_id: number;
    batch_id?: string;
}

export interface CompleteBatchResult {
    ok: boolean;
    message?: string;
    file?: string;
}

export type BatchInsertMode = "DIRECT" | "STAGE";

export async function completeBatch(batch_id: string, output_file_id: string | undefined, insert_mode: BatchInsertMode, batch_count = 0): Promise<CompleteBatchResult> {
    if (categories.length === 0)
        throw new Error("autocat not initialized");

    if (!output_file_id) {
        console.log(chalk.yellow(`No batch output file`));
        await openai.batch.snowflake.updateBatchStatus({ batch_id, completion_count: 0, completion_cost: 0 });
        return { ok: false };
    }

    const prompts_file = path.resolve(output_dir, `${batch_id}_prompts`);
    const output_file = path.resolve(output_dir, `${batch_id}.gz`);
    const completions_file = path.resolve(output_dir, `${batch_id}_completions`);

    if (!fs.existsSync(prompts_file)) {
        console.log(chalk.yellow(`Regenerate missing file ${prompts_file}...`));
        if (categories.length === 0) {
            console.log(chalk.gray("Downloading product categories..."));
            await autocatInitialize();
        }
        if (!await regenerateBatchFile(batch_id))
            return { ok: false, message: "Missing prompts file, cannot regenerate." };
    }

    console.log(chalk.gray(`Reading prompts from ${prompts_file}...`));
    const prompts: Record<string, string> = {};
    await readFromFileStream(prompts_file, line => {
        const obj = JSON.parse(line);
        const key = obj.custom_id;
        prompts[key] = obj.body.messages[0].content;;
    });
    console.log(chalk.gray(`${Object.keys(prompts).length} prompts read`));

    console.log(chalk.gray(`Downloading completions to ${completions_file}...`));
    await openai.batch.downloadBatchCompletions(batch_id, output_file_id, completions_file);
    let completion_count = 0;
    let completion_cost = 0;
    let error_count = 0;

    if (insert_mode === "STAGE") {
        // TODO: Getting the following error when calling snowflake.loadFromStage() below
        // 000603 (XX000): SQL execution internal error: Processing aborted due to error 300010:2991190807; incident 9134554.
        console.log(chalk.gray(`Loading data to table ${table_name} from stage ${stage_name}...`));
        const output_stream = fs.createWriteStream(output_file, { flags: "w" });
        await readFromFileStream(completions_file, line => {
            const obj = openai.batch.parseBatchCompletion(line);
            const prompt = prompts[obj.id];
            if (prompt) {
                const completion = createCompletion({ ...obj, prompt, product_id: parseInt(obj.id), batch_id });
                if (completion.cost)
                    completion_cost += completion.cost;
        
                output_stream.write(JSON.stringify(completion) + "\n");
                completion_count += 1;

                if (completion.line_num && !completion.category_id) {
                    console.log(chalk.red(`Failed to extract category_id for custom_id=${obj.id}`));
                    error_count += 1;
                }
            }
            else {
                console.log(chalk.red(`No prompt for completion with custom_id=${obj.id}`));
                error_count += 1;
            }
        });
        await closeStream(output_stream);
        console.log(chalk.gray(`Uploading data to stage ${stage_name}...`));
        await snowflake.stage(stage_name, output_file);
    }
    else if (insert_mode === "DIRECT") {
        console.log();
        console.log(chalk.gray(`Inserting data to table ${table_name}...`));
        const progress = new SingleBar({}, Presets.shades_classic);
        progress.start(batch_count, 0);
    
        let completion_buffer: ProductCategoryCompletion[] = [];
        await readFromFileStreamAsync(completions_file, async line => {
            const obj = openai.batch.parseBatchCompletion(line);
            const prompt = prompts[obj.id];
            if (prompt) {
                //const completion = createBatchCompletion(obj, prompt, batch_id);
                // todo: debug new completion refactor
                const completion = createCompletion({ ...obj, prompt, product_id: parseInt(obj.id), batch_id });
                if (completion.cost)
                    completion_cost += completion.cost;

                
                completion_buffer.push(completion);
                if (completion_buffer.length >= bulk_insert_count) {
                    await insertProductCategoryCompletions(completion_buffer);
                    completion_buffer = [];
                }

                if (completion.line_num && !completion.category_id) {
                    console.log(chalk.red(`Failed to extract category_id for custom_id=${obj.id}`));
                    error_count += 1;
                }

                completion_count += 1;
                progress.increment();
            }
            else {
                console.log(chalk.red(`No prompt for completion with custom_id=${obj.id}`));
                error_count += 1;
            }
        });

        progress.stop();
        if (completion_buffer.length > 0)
            await insertProductCategoryCompletions(completion_buffer);
    }

    const prompt_count = Object.keys(prompts).length;
    console.log(chalk.gray(`${completion_count}/${prompt_count} completions written`));
    if (completion_count < prompt_count)
        console.log(chalk.yellow(`${prompt_count - completion_count} dropped`));

    if (error_count > 0)
        console.log(chalk.yellow(`${error_count} read errors`));
    
    await openai.batch.snowflake.updateBatchStatus({ batch_id, completion_count, completion_cost });

    //fs.rmSync(prompts_file);
    //fs.rmSync(completions_file);
    //fs.rmSync(output_file);
    return { ok: true, file: path.basename(output_file) };
}

export function createCompletion({ prompt, content, model, prompt_tokens, completion_tokens, total_tokens, product_id, batch_id }: CreateCompletionOptions): any {
    const line_num = parseInt(regexpExtract(/^(\d{1,2})/, content)!) || undefined;
    return {
        product_id,
        category_id: line_num ? resolveCategoryId(prompt, line_num) : undefined,
        product_text: regexpExtract(/\nPRODUCT: ([^\n]+)/, prompt),
        prompt,
        line_num,
        error_code: regexpExtract(/^([A-Z_]+)/, content), // first all-caps keyword like NO_MATCH or INSUFFICIENT_CONTEXT
        error_message: regexpExtract(/\s([A-Z].+)/, content), // first capital letter after a space, for example "The product is..." from  "NO_MATCH: The product is..."
        model,
        prompt_tokens,
        completion_tokens,
        total_tokens,
        cost: openai.batch.calculateCompletionCost(model, prompt_tokens, completion_tokens, !!batch_id),
        batch_id,
        timestamp: new Date()
    };
}

export async function generateBatchFile(buffer_file: string, product_ids: number[]): Promise<BatchFile> {
    const [batch_file] = await generateBatchFiles(buffer_file, product_ids, false);
    return batch_file;
}

export async function generateBatchFiles(buffer_file: string, product_ids: number[], autosplit = true, max_batches = 10, max_batch_size = 104857600): Promise<BatchFile[]> {
    const batch_files: BatchFile[] = [];

    // todo: refactor to use new OutputFileStream helper
    let output_file = "";
    let output_stream: fs.WriteStream | undefined = undefined;
    let output_size = 0;
    let target_ids: number[] = [];
    const all_output_streams: fs.WriteStream[] = [];

    let errors = 0;
    let consecutive_errors = 0;
    let skipped = 0;

    const progress = new SingleBar({}, Presets.shades_classic);
    progress.start(product_ids.length, 0);
    await readFromFileStream(buffer_file, line => {
        if (all_output_streams.length >= max_batches) {
            skipped += 1;
            return;
        }

        const { product_id, product_text, brand_summary, product_vector, category_ids: subcategory_ids } = JSON.parse(line) as ProductEmbed;

        let prompt = "";
        try {
            prompt = createAutocatPrompt({ product_text, brand_summary, product_vector, subcategory_ids });
        }
        catch (err) {
            console.log(chalk.red(`Failed to create prompt for product_id=${product_id}... ${err instanceof Error ? err.message : JSON.stringify(err)}`));
            errors += 1;
            consecutive_errors += 1;
            if (errors >= max_errors || consecutive_errors >= max_consecutive_errors) {
                console.log(chalk.red(`Too many errors, aborting...`));
                process.exit();
            }
        }
        consecutive_errors = 0;
        
        const output_line = openai.batch.formatBatchPrompt({ custom_id: product_id.toString(), text: prompt }) + "\n";

        if (!output_file || !output_stream) {
            output_file = path.resolve(output_dir, generateTimestamp("seconds"));
            output_stream = fs.createWriteStream(output_file, { flags: "w" });
        }

        output_stream.write(output_line);
        output_size += output_line.length;
        target_ids.push(product_id);
        progress.increment();

        if (autosplit && output_size > 0.99 * max_batch_size) {
            batch_files.push({ output_file, target_ids });
            all_output_streams.push(output_stream);
            output_file = "";
            output_stream = undefined;
            output_size = 0;
            target_ids = [];
        }
    });
    progress.stop();

    if (skipped > 0)
        console.log(chalk.yellow(`${skipped} skipped, ${all_output_streams.length} batches created, ${max_batches} max`))

    if (output_file && output_stream) {
        batch_files.push({ output_file, target_ids });
        all_output_streams.push(output_stream);
    }
    await Promise.all(all_output_streams.map(stream => closeStream(stream))); // close all output streams

    return batch_files;
}

export function getBatchCompletionFiles(): string[] {
    const files = fs.readdirSync(output_dir);
    return files.filter(file => /^batch_[0-9a-f]+$/.test(file)).map(file => path.resolve(output_dir, file));
}


export async function loadBatchCompletionsToTable(files: string[]): Promise<{ ok: boolean }> {
    if (files.length === 0)
        return { ok: false };

    // LOAD FROM STAGE MODE
    console.log(chalk.gray(`Loading stage data to table ${table_name}...`));
    await sleep(2000); // wait for snowflake changes to propegate
    const t0 = new Date();
    const result = await snowflake.loadFromStage({ table_name, stage_name, files, force: true });
    if (result.rows_loaded) {
        await sleep(2000); // wait for snowflake changes to propegate
        await snowflake.execute("CALL WTB.PRODUCT_CATEGORY.update_product_category()");
        console.log(chalk.gray(`${result.rows_loaded} rows loaded in ${diffDate(t0)}`));
        return { ok: true };
    }
    else {
        console.log(chalk.yellow(result.status));
        console.log(chalk.gray.italic(`hint: enter the command below into snowflake to try again later`));
        console.log(chalk.gray.italic(result.command));
    }

    if (result.errors_seen)
        console.log(chalk.yellow(`${result.errors_seen} load errors`));

    return { ok: false };
}

export async function regenerateBatchFile(batch_id: string): Promise<string | undefined> {
    console.log(chalk.gray(`Query batch ${batch_id}...`));
    const batch = await openai.batch.snowflake.queryBatch(batch_id);
    if (!batch) {
        console.log(chalk.gray("Cannot regenerate, batch record not found"));
        return;
    }

    console.log();
    console.log(chalk.gray("Buffering data..."));
    const t0 = new Date();
    const buffer_file = await bufferPendingProducts(batch.target_ids);
    console.log(chalk.gray(`${batch.target_ids.length} rows buffered in ${diffDate(t0)}`));

    const prompts_file = path.resolve(output_dir, `${batch_id}_prompts`);
    console.log();
    console.log(chalk.gray(`Regenerating batch file ${prompts_file}...`));
    const { output_file } = await generateBatchFile(buffer_file, batch.target_ids);
    fs.rmSync(buffer_file);
    fs.renameSync(output_file, prompts_file);
    console.log(chalk.gray(`${prompts_file} regenerated in ${diffDate(t0)}`));

    return prompts_file;
}

export async function submitBatches(batches: BatchFile[]): Promise<string[]> {
    const batch_ids = [];
    const progress = new SingleBar({}, Presets.shades_classic);
    progress.start(batches.length, 0);
    for (const { output_file, target_ids } of batches) {
        const batch = await openai.batch.submitBatch(output_file);
        await openai.batch.snowflake.insertBatch({
            batch_id: batch.id,
            batch_target: "product_category_completions",
            batch_size: target_ids.length,
            target_ids
        });
        batch_ids.push(batch.id);
        const output_dir = path.dirname(output_file);
        const prompt_file = path.resolve(output_dir, `${batch.id}_prompts`);
        fs.renameSync(output_file, prompt_file);
        progress.increment();
    }
    progress.stop();
    return batch_ids;
}

export async function updateProductCategories() {
    await snowflake.execute("CALL WTB.PRODUCT_CATEGORY.update_product_categories()");
}

function resolveCategoryId(prompt: string, line_num: number): number | undefined {
    if (Object.keys(category_map).length === 0)
        throw new Error("autocat not initialized");
    const category_name = regexpExtract(new RegExp(`\n${line_num}. ([^\n]+)`), prompt);
    if (category_name) {
        const category_id = category_map[category_name];
        return category_id;
    }
}
