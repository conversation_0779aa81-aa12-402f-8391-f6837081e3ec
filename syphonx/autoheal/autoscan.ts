import * as async from "async-parallel";
import * as cheerio from "cheerio";
import chalk from "chalk";

import {
    bigquery,
    classifyPage,
    closeLogStream,
    collapseWhitespace,
    createRemoteLogStream,
    escapeHtmlString,
    generateUniqueId,
    downloadFile,
    parseArgs,
    regexpExtract,
    renderFingerprint,
    renderHtml,
    rebaseHtml,
    truncate,
    AnalyzeResult,
    ClassifyPageResult,
    LLMModel
}
    from "./lib/index.js";

const args = parseArgs({
    required: {
        app: "app name",
        account: "account name"
    },
    optional: {
        brand: "brand filter",
        country: "country filter",
        seller: "seller-id filter",
        sku: "sku filter",
        n: "number of rows per seller (default=3)",
        limit: "max # of rows (default=100)",
        preview: "preview only",
        model: "model to use for page classify (gpt-4o-mini, gemini-2.0-flash)",
        browser: "browser to use (playwright, chrome)",
        force: "force override recent scans",
        random: "random order",
        concurrency: "number of analyze instances to run concurrently (default=1)",
        verbose: "verbose log output"
    }
});

const concurrency = parseInt(args.concurrency) || 10;
const random = !!args.random;
const force = !!args.force;
const model = args.model as LLMModel || "gemini-2.0-flash";
const limit = parseInt(args.limit) || 100;

process.env.BROWSER = args.browser || "playwright";
if (args.verbose)
    process.env.VERBOSE = "1";

const screenshotSize = model.startsWith("gpt4") ? 512 : 768; // optimize for openai discrete tile size of 512, for others use a little higher resolution
const maxTextContentLength = 4096;
const maxConsecutiveErrors = 10;
const maxErrors = 100;

const rows = await query({
    app_name: args.app,
    account_key: args.account,
    brand_name: args.brand,
    country_code: args.country,
    sku: args.sku,
    seller_id: parseInt(args.seller),
    n: parseInt(args.n),
    limit,
    random,
    force
});
console.log(chalk.gray(`${rows.length} rows returned`));

if (rows.length === 0)
    process.exit(0);

if (args.preview) {
    let seller_id = 0;
    for (const row of rows) {
        if (seller_id !== row.seller_id) {
            console.log();
            console.log(chalk.underline(`${row.seller_name} ${row.seller_id}`));
            seller_id = row.seller_id;
        }
        console.log(`${row.page_id} ${chalk.gray(row.capture_url)}`);
    }
    process.exit(0);
}

const counters = {
    inserted: 0,
    errors: 0,
    consecutive_errors: 0
};

let i = 0;
let aborting = false;
await async.each(rows, async row => {
    if (aborting)
        return;
    try {
        const analyze_id = generateUniqueId();
        const html = await downloadFile(row.content_url);
        const result = await render({ ...row, html, analyze_id });
        const { page_class: page_class, explain, analyze, ...page_metadata } = result.classify_result;
        await insert({
            analyze_id,
            domain_name: row.domain_name,
            page_id: row.page_id,
            page_class,
            page_title: result.title,
            page_text: truncate(result.text, maxTextContentLength),
            page_metadata,
            capture_id: row.capture_id,
            capture_date: row.capture_date,
            capture_url: row.capture_url,
            report_url: result.report_url,
            fingerprint: result.fingerprint,
            explain,
            analyze
        });
        counters.inserted += 1;
        counters.consecutive_errors = 0;
        console.log(`[${++i}/${rows.length}] ${row.page_id} ${chalk.gray(row.capture_url)} ${chalk.cyan(analyze_id)}`);
    }
    catch (err) {
        console.log(`[${++i}/${rows.length}] ${row.page_id} ${chalk.gray(row.capture_url)} ${chalk.red("FAILED")}`);
        console.log(chalk.red(err instanceof Error ? err.message : JSON.stringify(err)));
        counters.errors += 1;
        counters.consecutive_errors += 1;
        if ((counters.consecutive_errors > maxConsecutiveErrors || counters.errors > maxErrors) && !aborting) {
            aborting = true;
            console.log(chalk.red(`Aborting, too many errors...`));
        }
    }
}, concurrency);


console.log(`${counters.inserted} inserted, ${counters.errors} errors`);
process.exit();

interface RenderOptions {
    html: string;
    capture_url: string;
    seller_name: string;
    domain_name: string;
    page_id: string;
    analyze_id: string;
}

async function render({ html, capture_url, domain_name, seller_name, page_id, analyze_id }: RenderOptions) {
    const origin = new URL(capture_url).origin;
    html = rebaseHtml(html, origin);
    const { page, datauri, html: rendered_html } = await renderHtml({
        html,
        visibleOnly: true,
        maxWidth: screenshotSize,
        maxHeight: screenshotSize,
        fullPage: false,
        autoclose: false,
        shared: false,
        //show: true
    });
    if (!page || !rendered_html || !datauri)
        throw new Error("Render HTML failed");

    const title = regexpExtract(/<title>([^<]+)<\/title>/gi, html) || undefined;
    const $ = cheerio.load(rendered_html);
    const text = collapseWhitespace($.text(), { singleSpaced: true, unindent: true }) || undefined;
    const [fingerprint, debug_image] = await renderFingerprint(page);
    await page.close();

    const { stream, url: report_url } = createRemoteLogStream(`autoscan/${analyze_id}.html`, { title: "AUTOSCAN", subtitle: `${page_id} at ${seller_name}` });
    stream.write(`<img src="${debug_image}" style="max-width: 900px;">\n`);

    const classify_result = await classifyPage({
        datauri,
        model,
        context: {
            domain_name,
            capture_url
        },
        stream
    }) as Required<ClassifyPageResult>;

    stream.write(`<details>\n<summary>CONTENT</summary>\n<p>TITLE: ${title ? escapeHtmlString(title) : "<i>(none)</i>"}</p>\n<pre>\n${text ? escapeHtmlString(text) : "<i>(empty)</i>"}\n</pre>\n</details>\n`);
    await closeLogStream(stream);
    if (!classify_result.ok)
        throw new Error(classify_result.message);

    return {
        classify_result,
        title,
        text,
        fingerprint,
        report_url
    };
}

interface Row {
    page_id: string;
    app_name: string;
    account_key: string;
    country_code: string;
    sku: string;
    group_name: string;
    group_id: string;
    seller_name: string;
    seller_id: number;
    domain_name: string;
    capture_status: string;
    capture_id: string;
    capture_date: Date;
    capture_url: string;
    content_url: string;
    last_scanned_at: Date;
}

interface QueryOptions {
    app_name: string;
    account_key: string;
    brand_name: string;
    country_code: string;
    sku?: string;
    seller_id: number;
    n: number;
    limit: number;
    random?: boolean;
    force: boolean;
}

async function query({ app_name, account_key, brand_name, country_code, sku, seller_id, n, random, force, limit }: Partial<QueryOptions>) {
    const params: Record<string, unknown> = {};
    const where = [
        "capture_status IN ('latest-invalid', 'never-valid')",
        "content_url IS NOT NULL"
    ];

    if (app_name)
        where.push(`app_name=${bigquery.safeValue(app_name)}`);
    if (account_key)
        where.push(`account_key=${bigquery.safeValue(account_key)}`);
    if (brand_name) {
        params.brand = brand_name;
        where.push(`group_name=@brand`);
    }
    if (country_code)
        where.push(`country_code=${bigquery.safeValue(country_code.toUpperCase())}`);
    if (sku) {
        params.sku = sku;
        where.push(`sku=@sku`);
    }
    if (seller_id)
        where.push(`seller_id=${bigquery.safeValue(seller_id)}`);

    if (!force)
        where.push("(last_scanned_at IS NULL OR last_scanned_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP, INTERVAL 14 DAY))");

    let query = `SELECT *\nFROM omega.page_scans_input\nWHERE ${where.join("\nAND ")}`;
    if (n)
        query += `\nQUALIFY ROW_NUMBER() OVER (PARTITION BY account_key, seller_id ORDER BY capture_date DESC) <= ${bigquery.safeValue(n)}`;

    if (random)
        query += "\nORDER BY RAND()";
    else
        query += "\nORDER BY app_name, seller_name, account_key, country_code, sku";

    if (limit)
        query += `\nLIMIT ${bigquery.safeValue(limit)}`;

    const rows = await bigquery.query(query, params);
    return rows as Row[];
}

interface PageScan {
    analyze_id: string;
    page_id: string;
    page_class: string;
    page_title?: string;
    page_text?: string;
    page_metadata: Record<string, unknown>;
    capture_id: string;
    capture_date: Date;
    capture_url: string;
    domain_name: string;
    explain: string;
    report_url: string;
    fingerprint: string;
    analyze: AnalyzeResult[];
}

async function insert(data: PageScan): Promise<void> {
    await bigquery.insert("omega.page_scans", {
        ...data,
        page_metadata: JSON.stringify(data.page_metadata),
        timestamp: new Date()
    });
}
