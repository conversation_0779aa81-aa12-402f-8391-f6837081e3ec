
# Update playwright
```
yarn remove playwright
npx playwright install
```


# Commit Process

```mermaid
flowchart LR
    candidates --> verify --> commit --> pending --> confirmed
    pending --> unconfirmed
    verify --> rejected
```



# Roadmap

## Generation 1 (Rules Based)
* Staged Workflow
    - anomaly detection feeds into commit workflow
* Basic selectors
    - CSS selectors
    - No regexp extraction
    - No traversals
* Manual review

## Generation 2 (AI/LLM/Generative Based)

* Integrated
* More complex selectors
    - Advanced jQuery selectors
    - Regexp extraction
    - Clicks
* Fully automated

```mermaid
mindmap
    ((AUTOHEAL))
        LLM/GPT
            Generative AI
            Evaluate&rarr;Fix&rarr;Test
        SyphonX
            Templates
            Revision History
            Reselect/Recapture
        Microservices
            Page Classifiers
                Visual/Screenshot driven
                DOM&minus;driven
        Data
            Current Data
            Stored Content
            Historical Analytics
                Completeness
                Degredation
        Knowledge Base
            How To's
            Discussion Groups            
```






### Add jQuery to any page in browser
```js
(() => {
    const script = document.createElement('script');
    script.src = 'https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.slim.min.js';
    document.getElementsByTagName('head')[0].appendChild(script);
    //jQuery.noConflict();
})()
```
