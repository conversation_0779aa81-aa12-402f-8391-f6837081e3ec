CREATE OR R<PERSON>LACE TABLE omega.product_match_analyze
(
  analyze_id STRING,
  timestamp TIMESTAMP,
  app_name STRING,
  account_key STRING,
  country_code STRING,
  sku STRING,
  product_id INT64,
  product_matches ARRAY<STRUCT<
    product_match_url STRING,
    product_match_status STRING,
    product_match_explain STRING,
    product_match_id INT64,
    product_match_active BOOLEAN,
    page_id STRING,
    n1 INT64,
    n2 INT64
  >>,
  model STRING,
  input_tokens INT64,
  output_tokens INT64,
  cached_tokens INT64,
  total_tokens INT64,
  cost FLOAT64,
  run_id STRING,
  report_url STRING,
  data JSON,
  analyze ARRAY<STRUCT<
    name STRING NOT NULL,
    model STRING,
    input_tokens INT64,
    output_tokens INT64,
    tokens INT64,
    cost FLOAT64,
    elapsed INT64,
    message STRING
  >>
)
PARTITION BY DATE(timestamp)
CLUSTER BY app_name, account_key, country_code;

CREATE OR REPLACE VIEW omega.product_match_analyze_view
AS
WITH
user_log AS (
  SELECT
    LAX_STRING(data.analyze_id) AS analyze_id,
    LAX_STRING(data.page_id) AS page_id,
    COUNTIF(LAX_STRING(data.action)='accept') AS accept_count,
    COUNTIF(LAX_STRING(data.action)='reject') AS reject_count,
    NULLIF(ARRAY_TO_STRING(ARRAY_AGG(
      CASE
        WHEN LAX_STRING(data.user) IS NOT NULL AND LAX_STRING(data.comment) IS NOT NULL THEN CONCAT(LAX_STRING(data.user), ': ', IF(LAX_STRING(data.action)='accept', 'YES, ', 'NO, '), LAX_STRING(data.comment))
        WHEN LAX_STRING(data.comment) IS NOT NULL THEN CONCAT(IF(LAX_STRING(data.action)='accept', 'YES, ', 'NO, '), LAX_STRING(data.comment))
        WHEN LAX_STRING(data.user) IS NOT NULL THEN CONCAT(LAX_STRING(data.user), ': ', IF(LAX_STRING(data.action)='accept', 'YES', 'NO'))
        ELSE NULL
      END
      ORDER BY timestamp DESC), '\n'), ''
    ) AS comments,
    NULLIF(ARRAY_TO_STRING(ARRAY_AGG(DISTINCT LAX_STRING(data.user) ORDER BY LAX_STRING(data.user)), ', '), '') AS audited_by,
    MAX(timestamp) AS audited_at
  FROM omega.user_log
  WHERE key='product-match-analyze'
  GROUP BY 1, 2
),
page_audits AS (
  SELECT
    analyze_id,
    ARRAY_AGG(STRUCT(page_id, accept_count, reject_count, comments, audited_by, audited_at)) AS audits
  FROM user_log
  GROUP BY 1
),
latest_analyze AS (
  SELECT * EXCEPT(product_matches, data),
    ARRAY(
      SELECT AS STRUCT *, NET.REG_DOMAIN(product_match_url) AS domain_name,
      FROM UNNEST(product_matches)
      LEFT JOIN a.audits USING (page_id)
    ) AS product_matches
  FROM omega.product_match_analyze
  LEFT JOIN page_audits AS a USING (analyze_id)
  WHERE report_url IS NOT NULL
  QUALIFY ROW_NUMBER() OVER (PARTITION BY app_name, account_key, country_code, sku ORDER BY timestamp DESC) = 1
)
SELECT *,
  TRIM(
    CONCAT(
      '+',
      (SELECT SUM(accept_count) FROM UNNEST(product_matches)),
      ', -',
      (SELECT SUM(reject_count) FROM UNNEST(product_matches)),
      '\n',
      ARRAY_TO_STRING((ARRAY(SELECT comments FROM UNNEST(product_matches))), '\n')
    )
  ) AS audit_summary,
  ARRAY_LENGTH(audits) AS audit_count
FROM latest_analyze
ORDER BY timestamp DESC;

CREATE OR REPLACE VIEW lookerstudio.product_match_analyze
AS
SELECT * REPLACE(DATETIME(timestamp, 'America/Los_Angeles') AS timestamp)
FROM omega.product_match_analyze_view;


/*
CREATE OR REPLACE TABLE omega.product_match_analyze
PARTITION BY DATE(timestamp)
CLUSTER BY app_name, account_key, country_code
AS
SELECT *
REPLACE(ARRAY(
  SELECT AS STRUCT * EXCEPT(page_id),
    page_id,
    n AS n1,
    CAST(NULL AS INT64) AS n2
  FROM UNNEST(product_matches)
) AS product_matches)
FROM omega.product_match_analyze;
*/





-- determine which pages should be included in the analyze
SELECT
  product_id,
  COUNTIF(html_url IS NOT NULL) AS daily_count,
  COUNTIF(active IS TRUE) AS active_count,
  COUNTIF(active IS FALSE) AS inactive_count,
  COUNTIF(active IS TRUE AND html_url IS NULL) AS active_disgard_count,
  COUNTIF(html_url IS NOT NULL OR active IS FALSE) AS analyze_count,
  COUNT(*) AS total_count,
  ROUND(SAFE_DIVIDE(COUNTIF(html_url IS NOT NULL OR active IS FALSE), COUNT(*)), 3) AS analyze_rate
FROM (
  SELECT *
  REPLACE(ARRAY(SELECT AS STRUCT * FROM UNNEST(pages) WITH OFFSET i WHERE domain_name NOT IN ('amazon.com','ebay.com') QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name ORDER BY i) <= 3) AS pages)
  FROM omega.product_match_analyze_pending
  WHERE app_name='prowl'
  AND account_key IN ('companapet')
), UNNEST(pages)
GROUP BY 1
ORDER BY 1;

-- identify accounts to analyze
SELECT
  account_key,
  COUNT(DISTINCT product_id) AS product_count,
  COUNT(DISTINCT domain_name) AS domain_count,
  ARRAY_TO_STRING(ARRAY_AGG(DISTINCT a.country_code ORDER BY a.country_code), ', ') AS countries,
  COUNTIF(active IS TRUE) AS active_count,
  COUNTIF(active IS FALSE) AS inactive_count,
  COUNTIF(html_url IS NOT NULL) AS content_count,
  COUNT(*) AS total_count
FROM omega.product_match_analyze_snapshot AS a, UNNEST(pages) AS p
WHERE app_name='prowl'
GROUP BY 1
ORDER BY 1;

CREATE OR REPLACE VIEW omega.product_match_audits
AS
SELECT
  a.app_name,
  a.account_key,
  a.country_code,
  a.sku,
  b.domain_name,
  b.product_match_status AS analyze_status,
  CASE
    WHEN b.accept_count > b.reject_count THEN 'CONFIRMED'
    WHEN b.accept_count < b.reject_count THEN 'REJECTED'
    WHEN b.accept_count = b.reject_count THEN 'INDETERMINATE'
    ELSE NULL
  END AS audit_status,
  b.product_match_active AS analyze_active,
  c.page_id IS NOT NULL AS scheduled,
  b.accept_count,
  b.reject_count,
  b.comments,
  b.audited_by,
  b.audited_at,
  b.product_match_url,
  a.product_id,
  b.product_match_id,
  a.analyze_id,
  a.timestamp AS analyzed_at,
  b.page_id,
  a.report_url
FROM omega.product_match_analyze_view AS a, UNNEST(product_matches) AS b
LEFT JOIN conflux.product_page_daily AS c USING (page_id)
WHERE b.product_match_status NOT IN ('NONE', 'ERROR', 'INSUFFICIENT-DATA')
ORDER BY a.timestamp DESC;
