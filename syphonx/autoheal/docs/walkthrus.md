# Walkthru 1
- `node query --account=milwaukee --group=selector` analyze regressions for milwaukee by selector
- `node query --account=milwaukee --group=seller,selector` all of the name regressions are at acehardware
- `node check --account=milwaukee --seller=7057 --selector=name` check a few captures for the name selector at acehardware
- `node audit --account=milwaukee --seller=7057 --selector=name --regressions --priority=10 --ttl=3` audit all regressions for milwaukee with the name selector at acehwardware
- `node fix --account=milwaukee --seller=7057` fix template after audit


# Walkthru 2
Run `node query --selector=name --group=seller` to query name selectors grouped by seller.
Observe that **Ace Hardware (US)** id=7057 has 608 regressions which is 66% of 917 captures.

Run `node query --group=account --seller=7057 --selector=name` to see which accounts may be impacted by this issue.
Observe that **dupont**, **milwaukee**, and **ppg** are impacted 67%, 69%, and 48% respectively.

Run `node audit --account=dupont,milwaukee,ppg --seller=7057 --selector=name --regressions --priority=1` to submit an audit request for these regressions.


# Walkthru 3
Run `node query --group=selector --account=sony` to query regressions by selector for sony.
Observe that features selector has 401 regressions which is 42% of 953 captures.

Run `node query --group=seller --account=sony --selector=features` to see which sellers are impacted by this issue.
Observe that Best Buy id=187 and Target id=189 are 84% and 96% respectively.

Run `node check --seller=187 --account=sony --selector=features --regressions` to check Best Buy regressions.
Run `node check --seller=189 --account=sony --selector=features --regressions` to check Target regressions.

Run `node audit --account=sony --seller=187,189 --selector=features --regressions --priority=2` to submit an audit request for these sellers.
