
## Overview

```mermaid
flowchart LR

subgraph crawl["Crawl"]
  direction TB
  product_page_schedule
    --> product_page_captures
    --> product_page_analyze
    --> product_page_daily
end

subgraph audit["Audit"]
  direction TB
  audit_requests
  --> audit_request_queue
  --> audit_tool(("<PERSON><PERSON> Tool<br>(Human)"))
  --> audit_responses
end

subgraph correct["Correct"]
  direction TB
  generate(("Autoheal<br>(AI)"))
  --> audit_commits
end

crawl
--"regressions<br>gaps<br>alerts"--> audit
--"02620<br>results"--> correct
```


## product_page_daily

```mermaid
classDiagram
  direction TB
  product_page_schedule --> product_page_analyze: accounts -> skus -> sellers
  product_page_captures "1" --> "1" product_page_analyze: captures/urls
  product_page_analyze --> product_page_last_capture: last capture
  product_page_analyze --> product_page_last_selector: last selector
  product_page_schedule --> product_page_daily: account -> skus -> seller
  product_page_last_capture --> product_page_daily: capture
  product_page_last_selector --> product_page_daily: reference
  class product_page_daily {
    regressions
    gaps
    alerts
  }
  class product_page_last_capture {
    capture_id
    capture_date
  }
  class product_page_last_selector {
    capture_id
    capture_date
    selector_name
    selector_value_json
  }
```

## audit_requests

```mermaid
classDiagram
  direction LR
  class audit_request_log {
    audit_id
    audit_date
    selectors
    account_key [repeated]
    country_code [repeated]
    seller_id [repeated]
    page_id [repeated]
    audit_context
    audit_count
    priority
    ttl
    requested_at
    requested_by
  }
  class product_page_daily {
    account_key
    country_code
    sku
    seller_name
    seller_id
    capture_url
  }
  class audit_requests {
    account_key
    country_code
    seller_id
    page_id
    ...
  }
  audit_request_log "1" --|> "1..n" audit_requests : unnest
  product_page_daily "1" --> "1" audit_requests
```

## audit_request_queue

```mermaid
classDiagram
  direction LR
  audit_response_log --> audit_request
  product_page_daily --> audit_request
```

## audit_responses
```mermaid
classDiagram
  direction LR
  class audit_response_log {
    audit_id
    audit_date
    page_id
    audit_status
    audit_url
    audit_duration
    audit_comment
    audit_result
    html
    audited_by
    audited_at
  }
  class audit_result {
    selector_name
    selector_path
    selector_status
    selector_comment
    selector_html
    selector_text
    selector_metadata
  }
  audit_result "n" *-- "1" audit_response_log
  audit_response_log "1" --|> "1" audit_responses
  audit_request_log "1" --|> "1" audit_responses
  product_page_daily "1" --> "1" audit_responses
```

# audit_commits

```mermaid
classDiagram
  class audit_commit_log {
    commit_id
    commit_date
    audit_id [repeated]
    template_path
    commit
  }
  class commit {
    selector_name
    before
    after
  }
  commit "1" *-- "1..n" audit_commit_log
  audit_commit_log --> audit_commits
  audit_response_log --> audit_commits
  audit_request_log --> audit_commits
  product_page_daily --> audit_commits
```
