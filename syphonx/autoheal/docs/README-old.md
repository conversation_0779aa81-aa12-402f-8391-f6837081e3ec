# Overview

Below is an overview of the AUTOHEAL process which is responsible for identifying and repairing broken product page selectors within the CONFLUX  web scraping / data extraction pipeline.

1. **Analyze**: The process starts with an analysis phase, which analyzes the current state of product pages or the data extracted from them using Dbt.

2. **conflux.product_page_daily**: The analyzed data is then funneled into a daily process or report concerning product pages, perhaps identifying issues or changes that need addressing.

3. **Audit**: Following the analysis, an audit is conducted, possibly to verify the integrity of the data or to check the selectors used for scraping against the current structure of the product pages.

4. **syphonx.page_selector_audits**: The results of the audit are recorded or acted upon in a specific context, such as a database or a report titled 'page_selector_audits' within the 'syphonx' system. If a regression is confirmed or a selector is not found, the process moves to the next step.

5. **Autoheal**: This step involves an automatic correction or healing process for the issues identified in the audit, attempting to fix broken selectors or adapt to changes in the page structure without human intervention.

6. **syphonx.autoheal_result**: The outcomes of the autoheal process are recorded, detailing whether and how issues were resolved.

7. **syphonx.template_commits**: If the autoheal process successfully resolves the issues (successful commits), these changes are then committed to a template or a set of rules within 'syphonx', finalizing the update process.

This workflow is designed to ensure the continuous integrity and functionality of product page selectors, crucial for maintaining the accuracy of web scraping or data extraction operations.

```mermaid
graph TD
1((analyze))
-->
2[conflux.product_page_daily]
--regressions-->
3((audit<br>AI))
-->
4[syphonx.page_selector_audits]
--regression-confirmed, selector-not-found-->
5((autoheal<br>AI))
-->
6[syphonx.page_selector_generate]
--successful generated selectors-->
7[syphonx.template_commit_log]
--change trigger-->
8[syphonx.template_log]
```

# Local Installation Instructions
**Step 1** : Clone the repo to your preferred folder

**Step 2** : Navigate into your local `C:\YourReposFolder\mudder-of-all-repos\syphonx\autoheal` directory

**Step 3** : Open a new command line in this location using right click with 'Open in Terminal', or by using command 'CD' in a new CMD window and then path down to your folder

**Step 4** : Run the following commands:
             `yarn` | 
             `yarn build` | 
             `npx playwright install`

**Step 5** : Next create a new .env file inside the AutoHeal directory
> This can be done one of two ways, either create one in Notepad++ and save the file as '.env' or create a new text document, and then rename the file to '.env'  
> Please note, you will need to reach out to someone on the team to retrieve the key for BigData to put in the .env file

**Step 6** : Now create a new json file called 'ps-bigdata.json'
> This can be done same as above, Notepad++ recommended  
> Please note, you will need to reach out to a team member to retrieve the key for BigData to put in the .json file

**Step 7** : Paste retrieved keys from a fellow team member into each respective file and ensure they are saved in the AutoHeal folder

**To Test** : Run command `node audit brand_monitor name --summary`  
> This should return a queue with regressions, sellers, thresholds, etc. if everything was successful

# Audit
The `audit` tool takes primary arguments of `app-name` and `selector-name`, for example: `node audit brand_monitor name` to audit the name selector within the brand-monitor app.
Run `node audit --help` to see other options.

- `node audit brand_monitor name --summary` to view queued sellers
- `node audit brand_monitor name --preview` to run the audit tool in "preview" mode to spot check regressions within each seller for a specified target selector
- `node audit brand_monitor name --seller=167` to run the audit tool with a specific seller to perform an automated audit on the retailer
- `node audit brand_monitor name --seller=167 --preview --diagnose` to see more info before running an automated audit

# Autoheal
Use the `autoheal` tool to "autoheal" a specified target selector and seller...
```
node autoheal brand_monitor
```
> Workaround for out of memory error: `node --max-old-space-size=4096 repair --seller=235`

# Blacklist
Use the `blacklist` tool to view the blacklist and to add a seller to the blacklist.
```
node blacklist
node blacklist --add="Amazon (US)" --reason=blocked-captcha
```

# Classify
Use the classify tool to analyze any arbitrary page url...
```
node classify https://huellabeauty.com/products/the-form-file
node classify https://www.walgreens.com/search/results.jsp?Ntt=toothpaste
node classify https://kakaku.com/item/K0001455956/
```

# Locate
Use the locate tool to find product-name, price, etc. on any arbitrary product-detail-page url...
```
node locate --url=https://huellabeauty.com/products/the-form-file
node locate --url=https://www.otcsuperstore.com/product/frizz-ease-secert-weapn-finish-cream-4oz/
node locate --url=https://www.newark.com/texas-instruments/tlc555cp/ic-timer-cmos-dip8-555/dp/28AH5371?st=555%20timer
node locate --url=https://kakaku.com/item/K0001455956/
```

# Autocreate
```
node autocreate --url=https://huellabeauty.com/products/the-form-file
```

# Links
- [walkthrus](./docs/walkthrus.md)
- [table schema](./docs/table-schema.md)
- [brand-monitor accounts](https://pricespider.atlassian.net/wiki/spaces/BME/pages/***********/Demo+and+Live+Brand+Monitor+Accounts)
