import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";

import {
    renderBrandSummaryPrompt,
    snowflake,
    LLMChat,
    LLMModel
} from "./lib/index.js";

interface Row {
    manufacturer_name: string;
    manufacturer_id: number;
    group_name: string;
    group_id: number;
    example_products: string[];
}

const model: LLMModel = "gpt-4o-mini";

console.log(chalk.gray("Querying pending brand info..."));
const rows = await snowflake.query<Row>("SELECT * FROM WTB.PRODUCT_CATEGORY.brand_info_pending");
console.log(chalk.gray(`${rows.length} rows returned`));

for (const row of rows) {
    console.log(chalk.gray(`[${rows.indexOf(row) + 1}/${rows.length}] ${row.group_name}`));
    const chat = new LLMChat(model);
    const input = renderBrandSummaryPrompt(row);
    const output = await chat.prompt(input);
    await snowflake.insert("WTB.PRODUCT_CATEGORY.brand_info", {
        group_id: row.group_id,
        brand_summary: output,
        timestamp: new snowflake.SafeLiteral("CURRENT_TIMESTAMP")
    });
}

console.log(chalk.gray(`${rows.length} inserted`));