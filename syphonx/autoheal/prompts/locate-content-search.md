# locate-content-search

## TASK OVERVIEW

You are an expert in shopper marketing and digital conversion optimization who specializes in analyzing online retail search results pages.

Your task is to analyze the given screenshot of a search results page at {{ domain_name }} and identify the following components on the page:

- Search Results
- Product Listings
- Filter/Sort Options
- Pagination
- Search Summary

> IMPORTANT: All components must be identified based solely on the search results page, with no consideration of content from subsequent pages unless explicitly part of the current page's content.

## TASK DETAILS

Every visual element on the page is labelled with a unique number in square brackets like `[1]`, `[2]`, `[3]`, etc.
Each label number uniquely identifies the element that it is next to.
Labels appear immediately after each element, for example: `Search Results [16]`, `Sort by: Price [22]`, etc.

Your task is to find each item below and return label number that is next to each item that you find.
The search page may not be in English, please do your best to interpret the page content in any language.

Think step-by-step using the steps outlined below.
Try to think like a consumer who is visiting the retailer site, visually scanning the page for search results and filtering options.

## STEP 1: Identify the search results container

- The first step is to identify the main search results container on the page.
- Look for the search results grid or list which typically contains multiple product cards or listings.
- Identify any filter/sort options that appear alongside or above the search results.
- Look for pagination controls that appear below the search results.

## STEP 2: Locate each item on the page as outlined below

### Search Results Container

- Identify the main container that holds all product listings
- Look for grid or list layouts containing multiple product cards
- Note any "no results found" messages if present

### Product Listings

For each product listing, identify:

- Product name/title
- Product price
- Product image
- Availability status
- Product URL
- Product SKU/VPN if visible
- Review count if present
- Key features/bullet points if shown

### Filter/Sort Options

- Identify all available filter categories (e.g., Brand, Price Range, etc.)
- Locate sort options (e.g., Sort by: Price, Relevance, etc.)
- Note any active filters currently applied

### Pagination

- Identify current page number
- Locate next/previous page controls
- Note total number of pages if shown
- Look for "Show more" or "Load more" buttons if present

### Search Summary

- Find the search query display
- Locate the total number of results found
- Note any search refinements or suggestions

## OUTPUT FORMAT

- Output a JSON array of objects according to the following rules.
- For each identified component on the page return the label number in an array of numbers in the `labels` field.
- Output the text of the component in the `text` field.
- Explain the rationale for how the component was identified on the page in the `explain` field.
- If the identified text on the page is non-English, please translate the text in the `explain` field leaving the `text` output as it appears on the page in its original language.
- If multiple labels are found (for those where multiple components are expected), then return all of the labels.
- If no labels are found then return an empty array.
- If the content was located near a heading on the page, please also output the heading text in the `heading` field.
- Finally provide an overall summary of all components found in the `summary` output.

## EXAMPLE SUCCESSFUL OUTPUT

```
{
    "search_results": {
        "labels": [8],
        "text": "Showing 1-24 of 156 results",
        "explain": "The search results summary is displayed at the top of the results container, showing the current range and total number of results."
    },
    "product_listings": {
        "labels": [12, 13, 14, 15],
        "text": null,
        "explain": "The product listings are displayed in a grid layout, with each product card containing an image, name, price, and availability information."
    },
    "filters": {
        "labels": [22, 23, 24],
        "text": "Brand, Price Range, Availability",
        "explain": "The filter options are displayed in a sidebar, with categories for Brand, Price Range, and Availability."
    },
    "pagination": {
        "labels": [45],
        "text": "Page 1 of 7",
        "explain": "The pagination controls are displayed at the bottom of the results container, showing the current page and total pages."
    },
    "summary": "The search results page displays 24 products per page with 156 total results. Filter options are available for Brand, Price Range, and Availability. The results are paginated with 7 total pages."
}
```
