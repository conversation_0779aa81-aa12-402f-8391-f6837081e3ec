interface ClassifyPageContext {
    domain_name: string;
    title?: string | null;
}

interface ClassifyPageResponse {
    classification?: PageClassification;
    explain?: string;
    message?: string;
    out_of_stock?: boolean;
    discontinued?: boolean;
    local_only?: boolean;
    hidden_price?: boolean;
    no_price?: boolean;
    no_reviews?: boolean;
    popup_dialog?: boolean;
}

type PageClassification =
    "PRODUCT-PAGE" |
    "MULTI-PRODUCT-PAGE" |
    "CATEGORY-PAGE" |
    "BRAND-PAGE" |
    "SEARCH-PAGE" |
    "LANDING-PAGE" |
    "BLOCKED" |
    "MODAL" |
    "PNF" |
    "SIGNIN" |
    "EMPTY" |
    "BROKEN" |
    "OTHER";
