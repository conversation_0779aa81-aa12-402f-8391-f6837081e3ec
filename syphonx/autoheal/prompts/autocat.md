For the product below, which product category is the best match among the candidates?

Use the PRODUCT info given below as the primary signal for category matching, and use the BRAND SUMMARY to reinforce the category that best aligns with the general product space targeted by the brand.

Output only the line number (1-{{ candidates | length }}) of the best category match below without any additional text.

If there is no good category match, then output the code NO_MATCH followed by a brief explanation elaborating on why none of the candidates are a good match.

PRODUCT: {{ product_text }}

NOTE: If the above product information is too vauge or lacks sufficient context to match to the candidates below, then output the code INSUFFICIENT_CONTEXT followed by a brief explanation of why the input context was found to be insufficient.

BRAND SUMMARY: {{ brand_summary }}

PRODUCT CATEGORY CANDIDATES:
{% for candidate in candidates %}
{{ loop.index }}. {{ candidate.category_name }}
DESCRIPTION: {{ candidate.category_description }}
EXAMPLES: {{ candidate.category_examples }}

{% endfor %}