Below is an excerpt of HTML code from a product-detail page at {{ domain_name }} that indicates targeted data to be extracted from the HTML.
Your task is to generate a generic CSS selector or jQuery selector that targets the "{{ selector_name }}" information within the given HTML.

The selector should be generalized such that it has a reasonable chance of extracting the correct data from any product-detail page on this site.
Focus on classes or identifiers that appear to be contextual to the data being extracted.
Look for patterns in the HTML that indicate a stable naming convention across all pages of this type, avoiding anything that may be product or page specific.
The main goal is to generate a selector that is minimal yet robust, targeting identifiers with semantic meaning generally associated with "{{ selector_name }}".

# STEP PROCESS
Think step-by-step using the following process:
- Generate a selector that matches the targeted data.
- Evaluate the quality of the selector according to the requirements above.
- If the selector doesn't reflect any semantic meaning related to "{{ selector_name }}", then go back to the first step and try again.
- If the resulting text needs to be filtered (for example to pull "Blue" out of "Color: Blue"), then generate a regular expression that further filters the text.
{% if generate_info %}
- {{ generate_info | safe }}
{% endif %}
{% if (content_value) %}
- The generated selector should produce the following output:
"""
{{ content_value }}
"""
{% endif %}
- If any of the above checks are not met then go back to the first step and try again until all outcomes within reason using the existing information have been considered.

# INDIRECT TARGETING
If you're finding that there are no directly meaningful classes or identifiers to target,
then look for information that may be indirectly contextual such as the name of a button or some other label that's not page or product-specific.

{% if content_heading %}
First consider if the target can be connected to the "{{ content_heading }}" heading text on the page using a traversal, for example something like: `$('div:contains("{{ content_heading }}")').closest('section').find('p')`.
{% endif %}

Look for indirect context that may be able to be used:
- Look for other user visible content on the page that can be targeted with :contains
- Look for text in title or aria-label attributes
- Look for hyper-links targeting a sub-string within the href attribute that might be constant among different pages
- Look for any other class or id attributes that may be indirectly contextual

Examples of indirect content might be:
- Label content around a review widget like "Read Reviews"
- Label content around a price like "savings", "shipping", "ships from", etc.
- Label of an "Add to Cart" or "Buy" button
- Test hook attributes like `data-test`

Avoid targeting any indirect content that may be page specific or product specific, such as a specific price or review score, or anything that might be part of the information for an individual product that won't work on other product pages (such as words or phrases in a product name or product description).

If something that's indirectly contextual is found, consider if it can be connected to the desired target using a traversal of some sort.
Use `:contains` within either CSS selectors or jQuery selectors to target text content on the page.

## Example CSS Selector Traversals
- `a[title*='See more'] ~ span:contains('$')`
- `p:contains('Ships from:') + div + div > span`
- `div:has(button[aria-label*='Rating']) ~ div > span`

## Use jQuery if necessary
If you're unable to make the traversal with a CSS selector, then output a jQuery selector instead using jQuery's advanced traversal capabilities such as `parents`, `closest`, `find`, `next`, `prev`, etc.

IMPORTANT: The generated jQuery selector must only use a single chained jQuery method expression like `$('h1').parents('section').find('span')`.
- DO NOT generate any additional code outside of the single jQuery method chain, for example `$('h1').text() + '.' + $('h2').text()` is UNACCEPTABLE.
- DO NOT generate code within any jQuery functions such as filter or map, for example `$('h1').filter(function () { this.nodeType === 3; })` is UNACCEPTABLE.

## Example jQuery Traversals
- `$('h2:contains("Languages")').parents('.panel').find('p')`
- `$('.overview').closest('.panel').next('p:has(strong)')`
- `$('#ratings-block:has(h2)').closest('.col-lg-6').prev('.container').find('div:first-child')`

# IF UNABLE TO GENERATE A SELECTOR
If you are ultimately unable to generate a selector that reasonbly meets the requirements above (perhaps due to the HTML having no text content that can be targeted, and no meaningful classes or identifiers that can be targeted) then output the selector as `null` and provide an explanation of why one couldn't be generated.

# SELECTING NUMERIC VALUES
When selecting a numeric value such as a `price`, `review_count`, `review_score`, etc. it is not necessary to generate a regular expression to extract the number from any surrounding text. So don't bother generating extract or replace regexp's that look like `.replace('/\$(\d+).+(\d{2})/', '$1.$2')`. or `.extract('/\$(\d+).+(\d{2})/')`.

# OUTPUT SPECIFICATION
Output a JSON object that includes the following:
- `selector` the generated CSS selector or jQuery selector, or null if no selector could be generated
- `explain` a brief explanation of how the selector targets the content, or if no selector was generated an explaination of why one couldn't be generated
- `regex` a regular-expression text filter (use to extract a substring witin the text of an element if there are no sub-elements that directly contain the target text result, shouldn't be needed most of the time)

## EXAMPLE #1: A generated CSS selector with an explaination
```json
{
    "selector": "h1",
    "explain": "..."
}
```

## EXAMPLE #2: A generated CSS selector with a regular-expression text filter and an explaination
```json
{
    "selector": "#buybox div:contains('Sold by:')",
    "regex": "/Sold by: (.+)/",
    "explain": "..."
}
```

## EXAMPLE #3: A generated jQuery selector with an explanation
```json
{
    "selector": "$('section:contains(From the manufacturer)').closest('.overview').find('ul > li')",
    "explain": "..."
}
```

## EXAMPLE #4: A null selector output with an explaination of why a selector couldn't be generated
```json
{
    "selector": null,
    "explain": "A selector couldn't be generated because..."
}
```

# INPUT HTML
Here is the input HTML code to be evaluated.
Note the code has line-numbers with one or more arrows `>` indicating the line number(s) at or near where the content is to be targeted by the generated selector.
If relevant content is not found directly on the lines indicated by the arrows then look around within 5-10 lines before and after the indicated lines for relevant content.

{{ prompt_html | safe }}
