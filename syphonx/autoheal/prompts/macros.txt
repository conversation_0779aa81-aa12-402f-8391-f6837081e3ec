{% macro highlight_words(text, search_terms) %}
    {% for term in search_terms %}
        {% set text = text | replace(term, '<em>' ~ term ~ '</em>') %}
    {% endfor %}
    {{ text | safe }}
{% endmacro %}

{% macro substr_match(a, b) %}
    {{ (a|default('')|lower in b|default('')|lower) or (b|default('')|lower in a|default('')|lower) }}
{% endmacro %}

{% macro find_by_name(list, name) %}
  {% for item in list %}
    {% if item.name == name %}
      {{ item.value }}
    {% endif %}
  {% endfor %}
{% endmacro %}

{% macro find_reference_value(list, name) %}
  {% for item in list %}
    {% if item.selector_name == name and item.reference_value %}
The previous value for `{{ name }}` was:
"""
{{ item.reference_value }}
"""
Look for a value may be the same or slightly different on this page.
    {% endif %}
  {% endfor %}
{% endmacro %}
