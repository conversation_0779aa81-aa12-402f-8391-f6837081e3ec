Hints:
- If you're finding that there are no directly meaningful classes or identifiers to target, then look for information that may be indirectly contextual such as the name of a button or some other label that's not page or product-specific.
{% if content_heading %}
- Consider if the target can be connected to the "{{ content_heading }}" heading text on the page using a traversal.
{% endif %}
- Look higher up the parent tree for an id or a class that may be more contextual.
- Look for text content that's not product specific using :contains from which to traverse.
- Don't forget to use jQuery if the situation requires it!
