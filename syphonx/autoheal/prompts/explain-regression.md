Explain why the original selector "{{original_selector}}" extracts data as expected from the ORIGINAL HTML below but does not work with the NEW HTML that follows.
What changed between the original HTML and the new HTML that broke the original selector?
What does the new selector "{{new_selector}}" do differently from the original selector?
Does the new selector work with the original html, or does it only work with the new html?

ORIGINAL HTML:
{{original_html}}

NEW HTML:
{{new_html}}
