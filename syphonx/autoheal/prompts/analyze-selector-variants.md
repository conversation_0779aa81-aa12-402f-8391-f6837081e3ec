# analyze-selector-variants

## TASK OVERVIEW
You are an expert in web scraping, HTML, advanced CSS selectors, and regular expressions.
Your task is to select the single best CSS selector from a list of candidate selectors for each extraction target provided.

## TASK OVERVIEW
Below you will be provided a set of **extraction targets**, each with a list of candidate CSS selectors.

All of the candidates associated with a given target are valid variants (i.e. they all target the same element).
Your task is to choose the single best candidate selector for each target.

The optimal candidate selector is one that best meets the following criteria:
- Has the strongest semantic meaning relative to the target (i.e. uses classes or IDs that indicate the element's role, such as `.product-name` or `[itemprop="price"]`)
- Will be the most stable selector across multiple products and page (i.e. less likely to break due to design or structural changes)


## GENERAL RULES
Use the following rules to choose the best selector for each target.

### Positive Rules
Guidelines that describe more stable, semantic, and maintainable selectors.

1. **Prefer Semantic, Domain-Specific Classes/IDs**  
   - Example: `.product-name`, `.price-block`, `#productImageContainer`.  
   - Such labels typically reflect the element’s purpose and are less likely to change.

2. **Leverage Microdata and Structured Data Attributes**  
   - Attributes like `[itemprop="price"]`, `[itemtype="http://schema.org/Offer"]`, `[data-product-name]` are often used for SEO or JS logic and remain stable over time.

3. **Keep Selectors Short and Specific**  
   - Target exactly the element you need without over-nesting; e.g., `[itemtype='http://schema.org/Product'] .price`.

4. **Consider HTML Attributes such as Accessibility Attributes, ARIA Attributes, or other attributes that appear to contain meaningful hooks**
   - If consistently used, attributes like `[aria-label="Price"]` can be meaningful hooks.
   - However, be careful to avoid choosing a selector that has something like `[alt='Moen 69000']` containing information that appears to be page/product specific.

5. **Test Selectors in a Real Environment**  
   - Verify they match **only** your intended element and remain accurate across pages.

### Negative Rules
Guidelines that describe fragile or unreliable selectors that should be avoided.

1. **Don't Rely on Purely Presentational or Layout Classes**  
   - Examples: `.mt-2`, `.row`, `.col-6`.
   - These can break if the site’s design framework (Bootstrap, Material UI) changes.

2. **Avoid `nth-of-type` or Overly Nested Selectors**  
   - Example: `div > div:nth-of-type(3) > .price`.  
   - Small DOM changes (e.g., adding a `<div>`) can break these selectors.
   - Sometimes these are unavoidable, but look to minimize choosing selectors that look like this

3. **Avoid Selectors with Auto-Generated or Hashed Class Names**  
   - React or Vue build processes may generate class names like `.sc-bdVaJa` or `.xyz-12345`, which can change unpredictably.

4. **Avoid Overly Generic Class Names**  
   - Generic names like `.container`, `.content`, `.header` can appear in multiple places or be removed in a redesign.

### Tiebreaker Rules
Guidlines for how to choose the best selector when two or more candidate selectors seem equally viable.

1. If multiple selectors appear equally valid, choose the selector with the strongest semantic meaning or least complexity (i.e., fewer classes and tags).

2. If all selectors have drawbacks (e.g., each has dynamic classes), choose the one that appears least prone to change (e.g., the class that seems least auto-generated or the ID that seems least dynamic).

## OUTPUT SPECIFICATION
Output an array of JSON objects with the following fields:
- `name` (string): the contextual name of the extraction target
- `selector` (string): the single best selector chosen from the list of candidates
- `semantic` (string[]): list of keywords with strong semantic meaning that were found in the selector
- `hash` (string[]): list of hash codes that were found to be in the selector, for example `sc-bdVaJa` or `xyz-12345` or similar
- `explain` (string): an explanation for the rationale of why the selector was selected among the list of candidates

### JSON OUTPUT FORMAT
```
[
   {
      "name": "target #1",
      "selector": "target #1 selector",
      "semantic": [],
      "hash": [],
      "explain": "explanation for target #1 selector..."
   },
   {
      "name": "target #2",
      "selector": "target #2 selector",
      "semantic": [],
      "hash": [],
      "explain": "explanation for target #2 selector..."
   },
   ...
]
```

## EXAMPLE
The input example consists of a set of input targets each with an associated list of candidate selectors.
The example output is a JSON array of objects with the chosen optimal selector for each target.

### Example Input
```
brand_name
- .product-info__block > span:first-of-type > a:first-of-type
- .product-vendor > a:first-of-type
- .product-info__sticky > div:first-of-type > span:first-of-type > a:first-of-type

product_name
- h1
- div > .mt-1
- .product-name

price
- [itemscope] > .price
- div > div.sc-bdVaJa > .price

product_image
- figure img:first-of-type
- section > div.ra-r59fz > img.xyz-12345
- [data-main-image]
- .productView-image-main > .lazyloaded

product_description
- section > div.af0tq2 > p
- div.af0tq2 > p

sku
- div > ul > li:first-of-type

gtin
- .q03fwa > div.mt-2 > .xcr0fl > ul > li

availability
- .productView-info-left > .stock-message
– .productView-product > div:first-of-type > div:nth-of-type(3)
– .productView-details > div:first-of-type > div:first-of-type > div:nth-of-type(3)

review_score
– .bv8e0 .buy-box > .r0akk7 > .reviewNumContainer
– a div:nth-of-type(6)
```

### Example Output
```
[
   {
      "name": "brand_name",
      "selector": ".product-vendor > a:first-of-type",
      "semantic": ["product-vendor"],
      "hash": [],
      "explain": "This selector uses the semantically meaningful class product-vendor and maintains a simple structure, ensuring stability across different products and pages."
   },
   {
      "name": "product_name",
      "selector": ".product-name",
      "semantic": ["product-name"],
      "hash": [],
      "explain": "The selector directly reflects the product name through a semantic class, ensuring meaningfulness and stability across different pages and products."
   },
   {
      "name": "price",
      "selector": "[itemscope] > .price",
      "semantic": ["itemscope", "price"],
      "hash": [],
      "explain": "This selector leverages semantic HTML by targeting elements with the itemscope attribute, ensuring stability and meaningful context across different pages and products."
   },
   {
      "name": "product_image",
      "selector": "[data-main-image]",
      "semantic": ["data-main-image"],
      "hash": [],
      "explain": "This selector leverages a semantic data attribute explicitly designated for the main product image, ensuring both meaningfulness and stability across different products and page structures."
   },
   {
      "name": "product_description",
      "selector": "div.af0tq2 > p",
      "semantic": [],
      "hash": ["af0tq2"],
      "explain": "This selector is chosen because it is less dependent on the parent <section> element, making it most likely to be stable across different products and pages among the available candidates. However, the selector uses a hash identifier that lacks any semantic meaning and may be unstable across different products and page structures."
   },
   {
      "name": "sku",
      "selector": "div > ul > li:first-of-type",
      "semantic": [],
      "hash": [],
      "explain": "This selector targets the first list item within a <ul> inside a <div>, but the selector is totally generic and lacks any semantic meaning."
   },
   {
      "name": "gtin",
      "selector": ".q03fwa > div.mt-2 > .xcr0fl > ul > li",
      "semantic": [],
      "hash": ["q03fwa", "xcr0fl"],
      "explain": "The selector has several hash identifiers and lacks any semantic meaning, however it is the only choice available."
   },
   {
      "name": "availability",
      "selector": ".productView-info-left > .product-info",
      "semantic": ["productView-info-left", "product-info"],
      "hash": [],
      "explain": "While not semantically ideal, this is the least nested and most directly targets a likely container for availability information. Other selectors are much more deeply nested and more likely to break with changes to the site's structure."
   },
   {
      "name": "review_score",
      "selector": ".bv8e0 .buy-box > .r0akk7 > .reviewNumContainer",
      "semantic": ["buy-box", "reviewNumContainer"],
      "hash": ["bv8e0", "r0akk7"],
      "explain": "Among the given options, it is the most semantically meaningful, however it also contains hash identifiers which may be unstable across different products and pages or over time."
   }
]
```


## STEP-BY-STEP INSTRUCTIONS
1. **Review Each Target:** For every extraction target, evaluate all the candidate selectors provided.
2. **Select the Optimal Selector:** Choose the one candidate that best meets the semantic relevance and stability criteria.
3. **Prepare Your Output:** For each target, create a JSON object with the fields as specified above.
4. **Output the JSON Array:** Return the complete JSON array containing all selected targets.


## EXECUTE
Now apply this process to the actual input extraction targets below.

{% for target in targets %}
{{ target.name }}
{%- for candidate in target.candidates %}
- {{ candidate }}
{%- endfor %}
{% endfor %}
