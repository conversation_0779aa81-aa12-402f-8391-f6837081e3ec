# classify-page

## TASK OVERVIEW
Attached is a screenshot of a page at `{{ domain_name }}`.

{%- if title %}
PAGE TITLE: {{ title }}
{%- endif %}

Your task is to analyze the screenshot and do the following:
- **Classify the page** into one and only one PAGE CLASSIFICATION code listed below.
- **Determine additional boolean flags** if certain conditions are met, as described in the “Additional Output Flags” section.  
- **Provide a brief explanation** rationalizing your classification and flag outupts.
- **Output your results as a strict JSON object** as described in the OUTPUT FORMAT section below.


## PAGE CLASSIFICATION
Assign **exactly one** of the following classification codes based on the **visible content** of the page:

### PRODUCT-PAGE
   - A page featuring a **single primary product** “above the fold” that is more prominent than any other product on the page. This page will typically have:  
     - A product name or title.  
     - A main product image.  
     - Some purchase-related content (e.g., price, availability, “Add to Cart” button).  
   - The **first product panel visible above the fold** is considered the primary product, even if it is marked as discontinued or unavailable. Subsequent panels (e.g., "similar items" or alternative products) do not override the primary product classification unless they are of equal prominence and lack any indication of discontinued status for the first panel.
   - If the first product panel is marked as discontinued (e.g., "No Longer Available"), the page remains a PRODUCT-PAGE, and the `discontinued` flag should reflect this status for the primary product, ignoring other product panels unless they are the sole focus of the page.
   - If multiple products are shown but there is a single main/dominant product, still classify as PRODUCT-PAGE.


### MULTI-PRODUCT-PAGE
   - A page that shows multiple products with roughly **equal prominence**, such as a product-list page where no single item stands out as “the main” product.

### CATEGORY-PAGE
   - A page that lists products belonging to a **specific category** (e.g., “Kitchen Appliances,” “Light Bulbs,” etc.).
   - Typically includes:  
     - A category selector or filter (e.g., a sidebar).  
     - A list or grid of products in that category.  

### BRAND-PAGE
   - A page that lists multiple products from a single **brand**.  
   - Often looks like a category page but is brand-focused, for example “All Acme products.”

### SEARCH-PAGE
   - A page showing **search results** for a query, often with a search box that displays the query or says “results for…”.  
   - May resemble a category list, but typically references keywords or a “search results” heading.

### LANDING-PAGE
   - A page designed to **orient** or **promote** something (e.g., a big hero or lifestyle image, large headline, or brand/campaign focus).
   - A typical homepage is considered a landing-page for the entire site.
   - If the page is mostly blank or has minimal content, it does *not* qualify as a landing-page (see EMPTY instead).

### BLOCKED
   - The page shows a **captcha** or other blocking measures, suggesting access is restricted or blocked for bots.
   - Output the **exact** text of the message indicating the BLOCKED status in the `message` field (i.e. the text giving instructions for the user to solve a CAPTCHA challenge or other text indicating why the page is blocked).
   - If PAGE TITLE indicates BLOCKED status then output the substring of the PAGE TITLE indicating the BLOCKED status in the `title` field.
   - If the `message` or `title` outputs contain any words that might vary from page-to-page (i.e. a product-name, brand-name, sku, part-number, etc.) then output those words in the `keywords` field, space separated.

### MODAL
   - The page is showing **only** a modal dialog or popup that obscures everything else.  
   - Use this classification if the core page is overshadowed by a blocking modal, such as a “sign in” requirement that prevents any view of the actual page.

### PNF
   - “Page Not Found” or “404” scenario, or an explicit message that the page does not exist.  
   - On some sites (like Amazon), a “Dogs of Amazon” page indicates a PNF status.
   - Output the **exact** text of the message indicating the PNF status in the `message` field (i.e. the text of the message indicating the product cannot be found, or a general message like “Dogs of Amazon” in the case of Amazon).
   - If PAGE TITLE indicates PNF status then output the substring of the PAGE TITLE indicating the PNF status in the `title` field.
   - If the `message` or `title` outputs contain any words that might vary from page-to-page (i.e. a product-name, brand-name, sku, part-number, etc.) then output those words in the `keywords` field, space separated.

### SIGNIN
   - A sign-in page prompting for credentials (username, password, etc.).
   - Output the **exact** text of the message indicating the SIGNIN status in the `message` field (i.e. the text of the message asking the user to sign-in).
   - If PAGE TITLE indicates SIGNIN status then output the substring of the PAGE TITLE indicating the SIGNIN status in the `title` field.
   - If the `message` or `title` outputs contain any words that might vary from page-to-page (i.e. a product-name, brand-name, sku, part-number, etc.) then output those words in the `keywords` field, space separated.

### EMPTY
   - The page is essentially **blank** or has no meaningful information beyond perhaps a header/footer.

### BROKEN
   - The page only displays an **error message** or some indication of a server or technical problem.  
   - This is different from PNF; here it might be a 500 server error or a site break.
   - Output the **exact** text of the message indicating the BROKEN status in the `message` field (i.e. the text of the error message).
   - If PAGE TITLE indicates BROKEN status then output the substring of the PAGE TITLE indicating the BROKEN status in the `title` field (i.e. the text of the error message).

### OTHER
   - The page is a valid retailer page but does **not** meet any of the above classifications.

> **Important Distinction**: If a product page lacks a clear product name and availability info, consider that it might be something else (e.g., a LANDING-PAGE or MULTI-PRODUCT-PAGE) unless there is strong evidence it is intended as a single product page.


## ADDITIONAL OUTPUT FLAGS
If you identify the page as a PRODUCT-PAGE (or sometimes, other pages with product references), set any of the flags below to `true` if you see corresponding indicators. If there is **any** uncertainty, set `false` and elaborate in the `explain` field if needed.

### `discontinued`
   - The **primary product** (i.e., the first product panel above the fold) is no longer carried by this retailer, indicated by a message such as “discontinued”, “sold out”, “no longer available”, “no longer carried”, or a conspicuously missing price/availability section that suggests it is **permanently** unavailable.
   - This flag applies specifically to the main product identified in the first panel, regardless of the availability of other products or panels (e.g., "similar items") on the page.
   - The product is no longer carried by this retailer, indicated by a message such as “discontinued”, “sold out”, “no longer available”, “no longer carried”, or a conspicuously missing price/availability section that suggests it is **permanently** unavailable.
   - IMPORTANT: If uncertain whether `discontinued` or `out_of_stock`, default to `out_of_stock`.
   - Output the **exact** text of the message (if found) indicating the product is discontinued in the `message` field (i.e. the text of the message stating the product is no longer available).
   - If PAGE TITLE indicates DISCONTINUED status then output the substring of the PAGE TITLE indicating the DISCONTINUED status in the `title` field (i.e. the text of the message stating the product is no longer available).
   - If the `message` or `title` outputs contain any words that might vary from page-to-page (i.e. a product-name, brand-name, sku, part-number, etc.) then output those words in the `keywords` field, space separated.

### `out_of_stock`
   - The product is **unavailable but expected to return**. Typically indicated by “temporarily out of stock,” “backordered,” or “preorder.”  
   - Also set this if the add-to-cart button is disabled or if the text explicitly states the item will be restocked.

### `local_only`
   - The product's status indicates “not available online,” “in-store only,” or something implying you **cannot purchase online**.

### `hidden_price`
   - A numeric price is **deliberately hidden**, with text such as “see price in cart,” “sign-in to see price,” etc.

### `no_price`
   - There is **no visible numeric price** on the page (and no message about hidden price).  
   - This is different from `hidden_price` because there is no textual clue that the retailer is hiding it. It's simply absent.

### `no_reviews`
   - No user-generated product reviews or ratings are visible (e.g., star ratings, review counts). If you see “0 reviews” or a “Be the first to review” message, then set `true`.

### `popup_dialog`
   - A dismissible popup or dialog is present. Examples include:  
     - Cookie policy acceptance.  
     - Location request.  
     - Sales or promotional overlay with a close/“x” button.  
     - Error or alert with a dismiss button.  
   - **Does NOT** include chat windows or help widgets that sit unobtrusively in corners (unless they block the page content forcibly).  
   - **Does NOT** include popups that require performing an action that changes the page context (e.g., a forced sign-in overlay → classify as `modal` if it blocks all content).


## HANDLING NON-ENGLISH PAGES
- Pages may be in languages other than English. Please do your best to interpret them.  
- When you quote text in your explanation, provide **both** the original text **and** an English translation if it helps clarify your reasoning.  
  - e.g., “Text says: ‘Fuera de stock’ (Spanish for ‘Out of stock’).”


## EXPLANATION REQUIREMENTS
In the `explain` field of your JSON output, briefly justify your classification and any flags set to `true`.
Use the following guidelines:

1. **Classification Reasoning**  
   - State why you chose that classification (e.g., “This page features a single prominent product with a title and an add-to-cart button.”).
2. **Flag Reasoning**  
   - If you set a flag to `true`, explain the text or visual indicator that led to that conclusion (e.g., “The text ‘currently backordered’ confirms out_of_stock.”).
3. **Concise Yet Complete**  
   - Keep it short but sufficiently detailed to understand the logic.  
   - Provide relevant quotes from the page if needed (include translations for non-English).


## OUTPUT FORMAT
**Return a strictly valid JSON object** with the following structure **every time**. Include **all** fields, defaulting to `false` for boolean flags that do not apply. No additional keys or text outside this format:

```
{
  "classification": "PRODUCT-PAGE" | "MULTI-PRODUCT-PAGE" | "CATEGORY-PAGE" | "BRAND-PAGE" | "SEARCH-PAGE" | "LANDING-PAGE" | "BLOCKED" | "MODAL" | "PNF" | "SIGNIN" | "EMPTY" | "BROKEN" | "OTHER",
  "discontinued": false,
  "out_of_stock": false,
  "local_only": false,
  "hidden_price": false,
  "no_price": false,
  "no_reviews": false,
  "popup_dialog": false,
  "message": "..." | null,
  "title": "..." | null,
  "explain": "..."
}
```

## “DO NOT TRIGGER” CASES AND EDGE CONDITIONS
1. **Ignore** chat widgets or help popups unless they block core content.  
2. **Ignore** disclaimers in footers or user comments that mention “discontinued” or “out of stock” if they are not referring to the main product.  
3. If the screenshot is **incomplete** or the content is unclear, make your **best guess** based on visible elements.  
4. For a partially loaded page or minimal content that does not qualify for any classification other than possibly EMPTY or BROKEN, apply the logic carefully:  
   - EMPTY if it's truly blank.  
   - BROKEN if you see an explicit error or technical message.
5. When multiple product panels are present, always prioritize the **first product panel above the fold** as the main product for classification and flag determination. If this panel is marked as "No Longer Available" or similar, set `discontinued=true` and do not let the presence of active products in subsequent panels override this status unless the page clearly shifts focus to those alternatives (e.g., a dedicated "suggested products" section with no reference to the first panel).
6. Output the `message`, and `title`, `keywords` fields as described above for BLOCKED, BROKEN, PNF, and SIGNIN classifications, otherwise output `null`.


## EXAMPLE SCENARIOS
Below are a few brief examples to illustrate page classification and flags:

### Example: A page shows a single product with a prominent image, a price, an “Add to Cart” button, and a note “Out of stock.”  
   ```
   {
     "classification": "PRODUCT-PAGE",
     "discontinued": false,
     "out_of_stock": true,
     "local_only": false,
     "hidden_price": false,
     "no_price": false,
     "no_reviews": false,
     "popup_dialog": false,
     "message": null,
     "keywords": null,
     "title": null,
     "explain": "One main product with a title and add-to-cart button, labeled 'Out of stock'."
   }
   ```

### Example: A page listing multiple products with no single main product standing out.
   ```
   {
     "classification": "BRAND-PAGE",
     "discontinued": false,
     "out_of_stock": false,
     "local_only": false,
     "hidden_price": false,
     "no_price": false,
     "no_reviews": false,
     "popup_dialog": false,
     "message": null,
     "keywords": null,
     "title": null,
     "explain": "A grid of products under one brand heading 'AcmeCo'. No single item is prominent."
   }
   ```

### Example: A page indicating a non-existant page
```
{
   "classification": "PNF",
   "discontinued": false,
   "out_of_stock": false,
   "local_only": false,
   "hidden_price": false,
   "no_price": false,
   "no_reviews": false,
   "popup_dialog": false,
   "message": "The page you've requested can't be found.",
   "keywords": null,
   "title": "Page not found",
   "explain": "The page displays a 404 error indicating that the page does not exist."
}
```

### Example: A page indicating a non-existant page with a product-specific message
```
{
   "classification": "PNF",
   "discontinued": false,
   "out_of_stock": false,
   "local_only": false,
   "hidden_price": false,
   "no_price": false,
   "no_reviews": false,
   "popup_dialog": false,
   "message": "Sorry, the Acme Super Toaster is no longer available.",
   "keywords": "Acme Super Toaster",
   "title": "Page not found",
   "explain": "The page displays a 404 error indicating that the page does not exist."
}
```


### Example: A page displaying a critical error message without any product or other non-error information
```
{
   "classification": "BROKEN",
   "discontinued": false,
   "out_of_stock": false,
   "local_only": false,
   "hidden_price": false,
   "no_price": false,
   "no_reviews": false,
   "popup_dialog": false,
   "message": "500 Server Error – Something Went Wrong.",
   "keywords": null,
   "title": "500 Server Error",
   "explain": "Only an error message is displayed, indicating the page is broken."
}
```

### Example: A blocked page
```
{
   "classification": "BLOCKED",
   "discontinued": false,
   "out_of_stock": false,
   "local_only": false,
   "hidden_price": false,
   "no_price": false,
   "no_reviews": false,
   "popup_dialog": false,
   "message": "Press & Hold to confirm you are a human.",
   "keywords": null,
   "title": null,
   "explain": "The page is blocked and requires the user to 'Press & Hold' to confirm they are human."
}
```
