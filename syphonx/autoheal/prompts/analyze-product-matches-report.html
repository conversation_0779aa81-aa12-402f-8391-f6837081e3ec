{% from "macros.txt" import highlight_words, substr_match %}
{% extends "base.html" %}
{% block title %}Ω {{ title }} | PRODUCT MATCH ANALYZE{% endblock %}
{% block content %}
    <header>
        <div style="position: absolute; top: 10px; right: 140px;">
            <i class="fa fa-user"></i>
            <input id="user" type="text" placeholder="Enter user name" style="width: 120px;">
        </div>
        <h1>Ω PRODUCT MATCH ANALYZE</h1>
        <h2>{{ title }}</h2>
        <div>{{ timestamp }}</div>
        <table style="border-collapse: collapse; border: none;">
            <tr style="border-collapse: collapse; border: none;">
                <td style="border-collapse: collapse; border: none;">
                    <div data-tile="1"></div>
                </td>
                <td style="border-collapse: collapse; border: none;">
                    <ul>
                        <li>PRODUCT NAME: {{ product_name }}</li>
                        {%- if brand_name %}
                        <li>BRAND: {{ brand_name }}</li>
                        {%- endif %}
                        <li>PRICE: {{ price }} ({{ currency }})</li>
                        {%- if mpn %}
                        <li>MPN: {{ mpn }}</li>
                        {%- endif %}
                        {%- if gtin %}
                        <li>GTIN: {{ gtin }}</li>
                        {%- endif %}
                        <li>PRODUCT ID: {{ product_id }} <i>({{ app_name|upper }})</i></li>
                    </ul>
                </td>
            </tr>
        </table>
        <hr>
    </header>

    {% macro render_page_info(page, index) %}
    <tr id="{{ page.name }}">
        <td>
            <a href="{{ page.url }}" target="_blank" style="display: inline-block; margin-bottom: 8px;">{{ page.name | replace(' ', '&nbsp;') | safe }}</a>
            {% if page.image_num %}<div data-tile="{{ page.image_num }}"></div>{% endif %}
        </td>
        <td>
            <b>{{ page.status }}</b>
            <div style="margin-top: 16px; font-style: italic;">{{ page.explain }}</div>

            <div style="font-size: smaller; margin-top: 48px;">
                {%- if not page.active %}
                <span>Should this match be reactivated?</span>
                {%- elif page.status == 'OK' %}
                <span>Is this a good match?</span>
                {%- elif page.status == 'MISMATCH' %}
                <span>Is this a bad match?</span>
                {%- elif starts_with(page.status, 'QUESTIONABLE') %}
                <span>Is this a bad match?</span>
                {%- elif page.status == 'DISCONTINUED' %}
                <span>Should this match be deactivated?</span>
                {%- else %}
                <span>Is this a good match?</span>
                {%- endif %}
                <a href="#" data-params="?key=product-match-analyze&action=accept&analyze_id={{ analyze_id }}&page_id={{ page.name }}&n={{ index }}" style="margin-left: 8px; border: 1px solid gray; border-radius: 8px; padding: 4px;">
                    <i class="fa fa-thumbs-o-up"></i>
                    {%- if not page.active %}
                    <span>Yes, reactivate it</span>    
                    {%- elif page.status == 'OK' %}
                    <span>Yes, good match</span>
                    {%- elif page.status == 'MISMATCH' %}
                    <span>Yes, bad match</span>
                    {%- elif starts_with(page.status, 'QUESTIONABLE') %}
                    <span>Yes, bad match</span>
                    {%- elif page.status == 'DISCONTINUED' %}
                    <span>Yes, deactivate it</span>
                    {%- else %}
                    <span>Yes</span>
                    {%- endif %}
                    </a>
                <a href="#" data-params="?key=product-match-analyze&action=reject&analyze_id={{ analyze_id }}&page_id={{ page.name }}&n={{ index }}" style="margin-left: 8px; border: 1px solid gray; border-radius: 8px; padding: 4px;">
                    <i class="fa fa-thumbs-o-down"></i>
                    {%- if not page.active %}
                    <span>No</span>
                    {%- elif page.status == 'OK' %}
                    <span>No, bad match</span>
                    {%- elif page.status == 'MISMATCH' %}
                    <span>No, good match</span>
                    {%- elif starts_with(page.status, 'QUESTIONABLE') %}
                    <span>No, good match</span>
                    {%- elif page.status == 'DISCONTINUED' %}
                    <span>No, product is not discontinued</span>
                    {%- else %}
                    <span>No</span>
                    {%- endif %}
                </a>
            </div>
            <input type="text" placeholder="Optional comment" style="flex: 1; width: 95%; margin-top: 12px;">
        </td>
        <td>
            <ul>
                <li>PRODUCT NAME: {% if page.product_name %}{{ highlight_words(page.product_name, [mpn, gtin]) | safe }}{% else %}<i>(none)</i>{% endif %}</li>

                {%- if page.price == price %}
                <li>PRICE: {{ page.price }} <i class="fa fa-check-circle" style="color: green;"></i></li>
                {%- elif page.price_diff > 200 %}
                <li>PRICE: <i>(out of range)</i></li>
                {%- elif page.price %}
                <li>PRICE: {{ page.price }}
                    {%- if page.currency %}
                        <span>({{ page.currency }})</span>
                        {%- if page.currency != currency %}
                            <i>currency mismatch</i>
                        {%- elif page.price_diff %}
                            <span>vs {{ price }} <i>({{ page.price_diff }}% difference)</i></span>
                        {%- endif %}
                    {%- elif page.price_diff %}
                        <span>vs {{ price }} <i>({{ page.price_diff }}% difference)</i></span>
                    {%- endif %}
                </li>
                {%- else %}
                <li>PRICE: <i>(none)</i></li>
                {%- endif %}

                {%- if page.gtin_diff %}
                <li>GTIN: {{ page.gtin }} vs {{ gtin }}, DIFF: <small>{{ page.gtin_diff }}</small> <i>(off by {{ page.gtin_distance }} digits)</i></li>
                {%- elif page.gtin == gtin %}
                <li>GTIN: {{ page.gtin }} <i class="fa fa-check-circle" style="color: green;"></i></li>
                {%- elif page.gtin %}
                <li>GTIN: {{ page.gtin }}</li>
                {%- else %}
                <li>GTIN: <i>(none)</i></li>
                {%- endif %}

                {%- if page.mpn_diff %}
                <li>MPN: {{ page.mpn }} vs {{ mpn }}, DIFF: <small>{{ page.mpn_diff }}</small>  <i>(off by {{ page.mpn_distance }} digits)</i></li>
                {%- elif page.mpn == mpn %}
                <li>MPN: {{ page.mpn }} <i class="fa fa-check-circle" style="color: green;"></i></li>
                {%- elif page.mpn %}
                <li>MPN: {{ page.mpn }}</li>
                {%- else %}
                <li>MPN: <i>(none)</i></li>
                {%- endif %}

                {%- if page.model_diff %}
                <li>MODEL: {{ model }}, DIFF: <small>{{ page.model_diff }}</small></li>
                {%- elif page.model == mpn or page.model == gtin %}
                <li>MODEL: {{ page.model }} <i class="fa fa-check-circle" style="color: green;"></i> <i>(off by {{ page.model_distance }} digits)</i></li>
                {%- elif page.model %}
                <li>MODEL: {{ page.model }}</li>
                {%- else %}
                <li>MODEL: <i>(none)</i></li>
                {%- endif %}

                {%- if page.sku %}
                <li>SKU: {{ page.sku }}</li>
                {%- endif %}
            </ul>

            {%- if page.cross_checks|length > 0 %}
            <ul>
                {%- for cross_check in page.cross_checks %}
                <li>{{ cross_check }}</li>
                {%- endfor %}
            </ul>
            {%- endif %}

            <details>
                <summary style="margin-left: 24px;">MORE</summary>
                {%- if page.product_name %}
                <ul>
                    {%- if page.product_name_diff %}
                    <li>PRODUCT NAME: <small>{{ page.product_name_diff | safe }}</small></li>
                    {%- endif %}

                    {%- if page.brand_name_diff %}
                    <li>BRAND: {{ page.brand_name }} vs {{ brand_name }}, DIFF: <small>{{page.brand_name_diff}}</small></li>
                    {%- elif page.brand_name and substr_match(page.brand_name, brand_name) %}
                    <li>BRAND: {{ page.brand_name }} <i class="fa fa-check-circle" style="color: green;"></i></li>
                    {%- elif page.brand_name %}
                    <li>BRAND: {{ page.brand_name }}</li>
                    {%- else %}
                    <li>BRAND: <i>(none)</i></li>
                    {%- endif %}

                    {%- if page.availability %}
                    <li>AVAILABILITY: {{ page.availability }}</li>
                    {%- endif %}

                    {%- if page.condition %}
                    <li>CONDITION: {{ page.condition }}</li>
                    {%- endif %}

                    {%- if page.color %}
                    <li>COLOR: {{ page.color }}</li>
                    {%- endif %}

                    {%- if page.category %}
                    <li>CATEGORY: {{ page.category }}</li>
                    {%- endif %}

                    {%- if page.variants %}
                    <li>VARIANTS: {{ page.variants }}</li>
                    {%- endif %}

                    {%- if page.page_title %}
                    <li>TITLE: {{ page.page_title }}</li>
                    {%- endif %}

                    {%- if page.image_key %}
                    <li>IMAGE: {{ page.image_key }}</li>
                    {%- else %}
                    <li>IMAGE: <i>(none)</i></li>
                    {%- endif %}

                    {%- if page.description %}
                    <li>DESCRIPTION: {{ highlight_words(page.description, [mpn, gtin]) | safe }}</li>
                    {%- endif %}
                </ul>
                {%- endif %}

                <ul>
                    <li>Product Match ID: {{ page.product_match_id }} <i>({{ app_name|upper }})</i></li>
                    <li>SELLER ID: {{ page.seller_id }} <i>({{ app_name|upper }})</i></li>
                    {%- if page.page_id %}
                    <li>PAGE ID: {{ page.page_id }}</li>
                    {%- endif %}

                    <li>URL: <small><a href="{{ page.url }}" target="_blank">{{ page.url }}</a></small>
                    {%- if page.url_diff %}
                    <br>DIFF: <small>{{ page.url_diff }}</small></li>
                    {%- endif %}
                    </li>

                    <li><a href="{{ page.html_url }}" target="_blank">stored content <i class="fa fa-external-link" style="font-size: xx-small"></i></a></li>

                    {%- if page.error %}
                    <li class="error">ERROR: {{ page.error }}</li>
                    {%- endif %}
                </ul>
            </details>
            {% if pretty_json(page.page_data) %}
            <details>
                <summary style="margin-left: 24px;">JSON</summary>
                <pre style="max-width: 650px; overflow: scroll;">{{ pretty_json(page.page_data) }}</pre>
            </details>
            {% endif %}
        </td>
    </tr>
    {% endmacro %}

    <main>
    <details{%- if active_matches|length > 0 %} open{%- endif %}>
        <summary>Active Matches ({{ active_matches|length }})</summary>
        {% if active_matches|length > 0 %}
        <table style="margin-top: 8px;">
            <thead>
                <tr>
                    <td>PAGE</td>
                    <td style="min-width: 500px;">STATUS</td>
                    <td>PRODUCT INFO</td>
                </tr>
            </thead>
            <tbody>
                {% for page in active_matches %}
                    {{ render_page_info(page, loop.index) }}
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <i>(None)</i>
        {% endif %}
    </details>

    <details{%- if deactivated_matches|length > 0 %} open{%- endif %}>
        <summary>Deactivated Matches ({{ deactivated_matches|length }})</summary>
        {% if deactivated_matches|length > 0 %}
        <table style="margin-top: 8px;">
            <thead>
                <tr>
                    <td>PAGE</td>
                    <td style="min-width: 500px;">STATUS</td>
                    <td>PRODUCT INFO</td>
                </tr>
            </thead>
            <tbody>
                {% for page in deactivated_matches %}
                    {{ render_page_info(page, loop.index) }}
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <i>(None)</i>
        {% endif %}
    </details>

    {% if unanalyzed_matches|length > 0 %}
    <details>
        <summary>Unanalyzed ({{ unanalyzed_matches|length }})</summary>
        <table style="margin-top: 8px;">
            <thead>
                <tr>
                    <td style="min-width: 350px;">PAGE</td>
                    <td>STATUS</td>
                    <td>PMID</td>
                    <td>ACTIVE</td>
                </tr>
            </thead>
            <tbody>
                {% for page in unanalyzed_matches %}
                <tr>
                    <td><a href="{{ page.url }}" target="_blank">{{ page.name | replace(' ', '&nbsp;') | safe }}</a></td>
                    <td>{{ page.error }}</td>
                    <td>{{ page.product_match_id }}</td>
                    <td>{{ page.active }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </details>
    {% endif %}

    <details>
        <summary>PROMPT</summary>
        <pre>{{ prompt }}</pre>
        <hr>
        <pre>{{ completion }}</pre>
    </details>

    <details>
        <summary>JSON</summary>
        <pre>{{ pretty_json(obj) }}</pre>
    </details>
    <section>
        <img id="image-strip" src="{{ datauri }}">
    </section>
    <div>
        {% for item in distinct_image_counts %}
        {{ item[0] }}={{ item[1] }}{% if not loop.last %}, {% endif %}
        {% endfor %}
    </div>
    <p>USAGE: {{ model }}, {{ prompt_tokens }} prompt tokens, {{ completion_tokens }} completion tokens, {{ cached_tokens }} cached tokens, (${{ cost }})</p>
    </main>

    <style>
        header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: #fff;
            z-index: 1000;
            padding: 24px;
        }
        main {
            padding: 0px;
        }
    </style>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>
    <script src="../scripts/voting.js"></script>
    <script>
        $(document).ready(function() {
            // dynamically adjust page header height
            function adjustHeaderHeight() {
                const height = $("header").outerHeight();
                $("main").css("padding-top", height);
            }
            adjustHeaderHeight();
            $(window).resize(adjustHeaderHeight);

            // 5 column layout, each tile is 150px square with 8px padding
            const columns = 5;
            const padding = 8;
            const square_size = 150;

            // Get the full source for the sprite (the same as the <img> at the bottom)
            const src = $("#image-strip").attr("src");

            // Apply image to each data-tile...
            $("[data-tile]").each(function() {
                // Which tile do we want?
                const i = parseInt($(this).attr("data-tile")) - 1;

                // Compute row & column in a 5-column sprite layout
                const col = i % columns;
                const row = Math.floor(i / columns);

                // Calculate background offsets (negative to shift the correct tile into view)
                const x = -(col * (square_size + padding));
                const y = -(row * (square_size + padding));

                // Assign the tile as a background image
                $(this).css({
                    "width": `${square_size + 2 * padding}px`,
                    "height": `${square_size + 2 * padding}px`,
                    "background-repeat": "no-repeat",
                    "background-position": `${x}px ${y}px`,
                    "background-image": `url(${src})`
                });
            });
        });
    </script>
{% endblock %}
