The output of the price selector you just generated resulted in a whole number.
This being the case, please double check the selector is targeting **both** the dollars and cents parts of the price.

Some sites split the dollars and cents (or equivalent for non-US sites) parts of the price into seperate tags for display formatting like this:
```html
<div>
  <span class="price-first">$129</span>
  <sup class="price-second">99</sup>
</div>
```

In this example we want to select the whole price of $129.99, not just $129.
The solution is to generate a jQuery selector like so: `$('.price-first').parent().replace('/\\$(\\d+).+(\\d{2})/', '$1.$2')`
Note "\\" is used because the regexp is defined in a string so we need to escape the "\" in this case.

This jQuery selector targets the `price-first` element, traverses to the parent element, and then performs a text replacement to combine the dollars and cents figures into a number that looks like "129.99" in this case. The text that remains to the left and right of the 129.99 number is not a concern, we just need the number to appear like "129.99" within the string.

If the original HTML excerpt has a price that has been split like this, then please generate a jQuery selector accordingly.
Otherwise, if the price really is a whole number or has a zero number fractional component then please simply re-output the last generated selector once again.

You can assume jQuery in this environment has an extension method named `replace` that works just like the built-in Javascript `String.replace` function except that the first parameter is a string containing a regexp that starts and ends with a "/" as shown in the example above.

IMPORTANT: The generated jQuery selector must only use a single chained jQuery method expression like `$('aaa').parents('bbb').find('ccc')`.
It MUST NOT have any additional code outside of the single jQuery method chain, for example `$('aaa').text() + '.' + $('bbb').text()` is NOT acceptable.
