TASK: Evaluate the following selector (which may be a plain CSS selector or a jQuery selector) and determine whether it has any issues that would make it inherently unsuitable in web-scraping for generally extracting product data from a site.

SELECTOR: `{{ selector }}`
CONTEXT: `{{ selector_name }}`
SELECTOR OUTPUT JSON: `{{ selector_json_value }}`

Goal is to evaluate the SELECTOR given above against the examples that follow and identify any selectors that may be weak or brittle at extracting data within the above given CONTEXT.
{% if qualify_info %}
{{ qualify_info | safe }}
{% endif %}

# ACCEPTABLE EXAMPLES
The following examples are **ACCEPTABLE** and should not be flagged as too generic...
- `h1` acceptable at face value because an `h1` tag tends to have strong inherit contextual by itself on a page
- `.price`, `p.price` acceptable because the word "price" is contextual
- `.product-brand > a`, acceptable because the word "product-brand" is contextual
- `.our-price > div > span:first-child > p`, acceptable because the word "our-price" is contextual
- `.review-score > :first-child` acceptable because the word "review-score" is contextual
- `.product-title span` acceptable because the word "product-title" is contextual even though it is followed by span which is generic
- `div .review-score span` acceptable because the word "review-score" is contextual between generic targets of div and span
- `section .feature-bullets > div > span:first-child` acceptable because the word "feature-bullets" is contextual even though it is preceded and succeded by generic targets
- `p:contains('Product Features:')` acceptable because targeting "Product Features" in the page content is contextual and because `:contains` on CSS selectors is supported in this context
- `.title:contains('Description')` acceptable because `.title` is contextual and targeting "Description" in the page content is contextual
- `[data-test='price']` acceptable because the word "price" is contextual
- `$('img.product-image').attr('src')` jQuery selectors are generally acceptable
- `$('strong:contains("Product Features")').parent().next('ul').find('li')` acceptable because traversing from targeting "Product Features" in the page content is contextual
- `$('.price').extract("/^(€)/")` acceptable because `extract` is a special jQuery function extension that is valid in this context
- `$('.availability').filter("/(ships in \\d+ days)/i")` acceptable because `filter` is a special jQuery function extension that is valid in this context

# UNACCEPTABLE EXAMPLE: `TOO GENERIC`
Examples of selectors that may be too generic and lacking semantic meaning relative to the data being extracted...
  - `div` unacceptable because a `div` by itself does not contain any contextual meaning
  - `a` unacceptable because an `a` by itself does not contain any contextual meaning
  - `div > span > span` unacceptable because none of the targeted elements contain any contextual meaning
  - `div > div > div > div > p` unacceptable because none of the targeted elements contain any contextual meaning
  - `section > div > h2` unacceptable because none of the targeted elements contain any contextual meaning
  - `.item ul > li` unacceptable because `.item` contains very weak contextual meaning
  - `div.col-lg-6 > ul > li` unacceptable because `.col-lg-6` is generic layout information and doesn't contain any contextual meaning
  - `span > span > span` unacceptable because none of the targeted elements contain any contextual meaning
  - `.tabcontent.active .inner ul:first-of-type li` unacceptable because `.tabcontent` doesn't contain any contextual meaning
  - `div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div > div` unacceptable because none of the targeted elements contain any contextual meaning
  
> IMPORTANT: As discussed above, selectors paths containing plain elements on the path such as div or span or ul > li  IS acceptable as long as the overall path has a reasonable contextual meaning from an id, class or content on other elements.

# UNACCEPTABLE EXAMPLE: `INVALID CONTEXT`
Examples of selectors targeting product-specific or layout information or other content with invalid or weak contextual meaning...
- `div:contains('$9.99')` unacceptable because selector is targeting a price which is product specific
- `ul > li:contains('color: white')`unacceptable because selector information which may be product specific
- `[aria-label*='45 reviews']` unacceptable because selector is targeting a number of reviews which is product specific
- `[style*='width: 200px']` unacceptable because selector is targeting page layout information which doesn't relate to the context and may change from page-to-page

# UNACCEPTABLE EXAMPLE: `INVALID JQUERY SELECTOR`
If a jQuery selector is generated all of the following must be true:
- Generated selector must only use a single chained jQuery method expression like `$('h1').parents('section').find('span')`.
- Generated selector must not contain any additional code outside of the single jQuery method chain, for example `$('h1').text() + '.' + $('h2').text()` is UNACCEPTABLE.
- Generated selector must not define any Javascript code within the jQuery functions such as filter or map, for example `$('h1').filter(function () { this.nodeType === 3; })` is UNACCEPTABLE.

# OUTPUT
If one or more issues are found then output them in a markdown list as shown below with a brief description of the issue.
Otherwise if no issues are found then just output "OK" only.
DO NOT output an explanation outside of the markdown list output itself, or anything other than the markdown list or simply "OK".

## EXAMPLE #1
<input>`div > span > span`</input>
<output>
- TOO GENERIC: `div > span > span` by itself without any contextual meaning is too fragile as the DOM structure can easily change to break this selector.
</output>

## EXAMPLE #2
<input>`.our-price > div > span:first-child > p`</input>
<output>OK</output>
