interface ProductMatchAnalyzeContext extends ProductMatchAnalyzeItem {
    distinct_image_count: number;
    last_keycode?: string;
}

interface ProductMatchAnalyzeItem extends Record<string, unknown> {
    app_name: string;
    account_key: string;
    country_code: string;
    sku: string;
    product_id: number;
    client_id: number;
    group_id: number;
    product_name: string;
    brand_name: string;
    currency: string;
    gtin: string;
    msrp: number;
    target_price: number;
    regular_map: number;
    ref_price?: number; // one of target_price, regular_map, msrp
    product_match_count: number;
    pages: ProductMatchAnalyzeSubitem[];
    snapshot_id: string;
}

interface SEOProduct {
    brand_name: string;
    availability: string;
    category: string;
    color: string;
    condition: string;
    currency: string;
    description: string;
    gtin: string;
    images: string[];
    in_stock: boolean;
    model: string;
    mpn: string;
    name: string;
    price: string | number;
    variants: string;
    product_id: string;
    review_count: number;
    review_score: number;
    seller_name: string;
    sku: string;
    url: string;
}

interface ProductMatchAnalyzeSubitem extends SEOProduct {
    name: string;
    product_name: string;
    seller_name: string;
    seller_id: number;
    domain_name: string;
    product_match_id: number;
    page_id: string;
    page_title: string;
    url: string;
    url_id: number; // trinity
    capture_id: string; // trinity
    match_status: string; // trinity
    resolution_type: string; // trinity
    resolution_timestamp: Date; // trinity
    confidence: number;
    image_key?: string;
    image_num?: number;
    image_refs?: number;
    capture_date: Date;
    html_url: string;
    source: string;
    status: string;
    explain: string;
    cross_checks: string[];
    error: string;
    product_name_diff: string;
    price_diff: number;
    brand_name_diff: string;
    gtin_diff: string;
    gtin_distance: number;
    mpn_diff: string;
    mpn_distance: number;
    model_diff: string;
    model_distance: number;
    url_diff: string;
    active: boolean;
    page_data: unknown;
    n1: number;
    n2: number;
}
