import { renderJinjaTemplateFile } from "../lib/render-view.js";

export function renderAnalyzeSelectorVariantsPrompt(context: AnalyzeSelectorVariantsContext): string {
    return renderJinjaTemplateFile("analyze-selector-variants.md", context);
}

export function renderAutocloseDialogPrompt(context: AutoCloseDialogContext): string {
    return renderJinjaTemplateFile("auto-close-dialog.md", context);
}

export function renderAutocatPrompt(context: AutocatContext): string {
    return renderJinjaTemplateFile("autocat.md", context);
}

export function renderBrandSummaryPrompt(context: BrandSummaryContext): string {
    return renderJinjaTemplateFile("brand-summary.md", context);
}

export function renderClassifyPagePrompt(context: ClassifyPageContext): string {
    return renderJinjaTemplateFile("classify-page.md", context);
}

export function renderLocateContentPrompt(context: LocateContentContext): string {
    return renderJinjaTemplateFile("locate-content.md", context);
}

export function renderGenerateSelectorPrompt(context: GenerateSelectorContext): string {
    return renderJinjaTemplateFile("generate-selector.md", context);
}

export function renderGenerateSelectorHintPrompt(context: GenerateSelectorHintContext): string {
    return renderJinjaTemplateFile("generate-selector-hint.md", context);
}

export function renderGenerateSelectorHintPricePrompt(context: GenerateSelectorHintPriceContext): string {
    return renderJinjaTemplateFile("generate-selector-hint-price.md", context);
}

export function renderProductMatchAnalyzePrompt(context: ProductMatchAnalyzeContext): string {
    return renderJinjaTemplateFile("analyze-product-matches.md", context);
}

export function renderProductMatchAnalyzeReport(context: AnalyzeProductMatchesReportContext): string {
    return renderJinjaTemplateFile("analyze-product-matches-report.html", context);
}

export function renderQualifySelectorPrompt(context: QualifySelectorContext): string {
    return renderJinjaTemplateFile("qualify-selector.md", context);
}

export function renderResolveSelectorProblemsPrompt(context: ResolveSelectorProblemsContext): string {
    return renderJinjaTemplateFile("resolve-selector-problems.md", context);
}
