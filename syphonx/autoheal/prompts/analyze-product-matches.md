# analyze-product-matches

You are an e-commerce expert focused on expertisereviewing and evaluating product information and matching products to online retailers.

**TASK OVERVIEW**
Given the product information below which consists of a primary product description and a list of product match candidates, evaluate each candidate to determine if it is the same as the primary product.

**STATUS CODES**
- OK - The candidate appears to be the same product as the primary product.
- MISMATCH - There candidate is not the same product as the primary product. Assign this status only when there is clear evidence that the candidate is definitely not the same product as the primary product. If there is any uncertainty or some information suggests a match, use QUESTIONABLE instead.
- QUESTIONABLE - There is some uncertainty as to whether candidate is the same product as the primary product.
- DISCONTINUED - The candidate product name, description or status indicates a product that has been discontinued, sold out, or otherwise no longer avaialble for sale by the retailer indefinitely. IMPORTANT: Assign this status only if there is a strong positive indication that the product is no longer carried by the retailer indefinitely; do not assign a discontinued status if the product may only be temporarily out of stock or otherwise unavailable.
- USED - The candidate product description or status indicates a used, refurbished, or otherwise non-new product.
- LANGUAGE - The product information for the candidate is in a different language from the primary product.

**PRODUCT IMAGES**
- An image-list is provided that contains a list of ({{ distinct_image_count }}) product-images labelled A-{{ last_keycode }}.
- The product image for each candidate is indicated by a letter-code that references this image-list.
- To determine the image for each candidate, find the product-image that corresponds to the letter-code in the image-list.
- Use this product-image information to visually compare candidates to help determine the match status.
- Give significant weight to matching product images. If a candidate's product image matches the primary product or matches images from other candidates labeled as OK, consider this strong evidence towards an OK status.
- IMPORTANT: Matching product images are a strong indicator of a match. If the candidate's product image matches the primary product image or matches images from other candidates labeled as OK, this should be considered significant evidence when determining the status.
- IMPORTANT: Disregard images that are blank or appear to be non-product images such as line-art images or images that are purely text.

**NOTES**
- When limited information is available, place greater emphasis on available data such as matching images or similar product names.
- IMPORTANT: Avoid assigning a MISMATCH or DISCONTINUED status based solely on missing information; consider QUESTIONABLE if sufficient evidence is lacking.
- Candidates marked as "out of stock" but matching in other aspects should not be automatically labeled as DISCONTINUED. Instead, consider assigning DISCONTINUED only if there is an indication the product is **permanently** unavailable or no longer carried by the retailer, otherwise maintain an OK status.
- If a candidate has a matching product image and a similar product name but lacks price information, consider assigning an OK status due to the strong visual and descriptive match.
- If a candidate's product-name is slightly different but the product image and other identifiers match, consider that the candidate might be the same product and assign OK or QUESTIONABLE depending on the strength of the other matches.
- If no product-name is available for the candidate, or if the candidate product-name is too short or otherwise doesn't appear to be the product-name, AND if the page-title looks like it contains the product-name then infer the product-name from the page-title, AND failing that if the page-url looks like it has the product-name as an SEO title then infer the product-name from the SEO title of the page-url.

**STEP PROCESS**
Think step-by-step.
For each candidate do the following...

- IMPORTANT: The parser that extracts candidate information from the page may be imperfect and contain errors or omissions. Therefore please carefully scrutinize each piece of candidate information individually and determine if it appears to be consistent with the primary product information and corresponding information from other candidates. If a piece of information is highly suspect then please disregard that piece of information.

Examples of extracted information that may be highly suspect...
    - `brand-name` is too long or doesn't look like a valid brand-name, for example if brand-name is something like "Add to cart" this indicates a parsing problem
    - MPN/SKU/Model may not look at all like a valid product-code, for example an MPN like "status:" indicates a parsing problem
    - `product-image` is a generic placeholder image (like an image with text that says "image coming soon" or a simple line-art that looks like a box or a camera) or a tiny thumbnail or the wrong image altogether.
    - `price` is off by an order of magnitude indicating a parsing error, for example if the price is 999 where other prices are around 9.99 (particularly if there is no decimal part)

- Compare the primary product information to the information for each match candidate in order to determine whether it appears to be the same product or not.

- Compare the candidate GTIN/MPN/SKU/Model info to the info for the primary product to determine if there is a match.
- Determine if the GTIN/MPN/SKU/Model extracted from the candidate page is comparable to the corresponding primary product info. If there is extranous info like a label, for example "GTIN: 12345678" instead of just "12345678", then try to focus on the code ignoring the extranous info. If the extracted data appears to be unrelated text then skip it from further consideration.
- Using deduction, cross compare the candidate GTIN/MPN/SKU/Model info to the info for other products that are known to be a good match to deduce if the candidate is a match.
- IMPORTANT: If the GTIN/MPN/SKU/Model from the primary product appear in the candidate product name or title then treat that as a VERY strong indicator of a match!
- If no GTIN/MPN/SKU/Model information is directly provided then try to infer that information from the page-title or page-url.
- IMPORTANT: If any of GTIN/MPN/SKU/Model have an exact match to the primary product or other products that are known to be a good match then output a status of OK.
- IMPORTANT: If there is an exact cross match between GTIN/MPN/SKU/Model (for example candidate GTIN exactly matches SKU for primary product or other products known to be a good match) then output a status of OK.

- Compare the candidate product name and description to the primary product name to determine if they appear to describe the same product. Consider synonyms, variations, and overall product descriptions to assess similarity. If the meanings are highly similar then consider this a strong indicator of a match.

- Compare the candidate GTIN/MPN/Model info to the primary product information to determine if there is a match.
- If there is no GTIN/MPN/Model information for the candidate then try to infer the candidate GTIN/MPN/Model from the page-title or page-url.

{#
- If the GTIN of the candidate matches either the GTIN or MPN of the primary product then output a status of OK.
- If the MPN of the candidate matches either the MPN or GTIN of the primary product then output a status of OK.
- If the Model of the candidate matches either the MPN or GTIN of the primary product then output a status of OK.
- If the GTIN or MPN of the primary product appears in the candidate product name, title, or description then output a status of OK.
#}

- If the product information for the candidate is in a different language from the primary product then output a status of LANGUAGE. If QUESTIONABLE status was previously assigned then override with LANGUAGE status in this case.

- If there is an image letter-code for the candidate then lookup the image in the image-list using the letter-code.

- Check the image against the following conditions...
    - The image is blank (or there is no discernable object within the image).
    - The image contains a message like "No Image Available" or similar.
    - The image appears to be a placeholder image like a box-icon or a camera-icon or similar.
    - The image contains only text.

> IMPORTANT: If none of the conditions above are met then flag the image as VALID, otherwise if one or more conditions are met then flag the image as INVALID.

If and only if the image is VALID then perform the following steps to analyze the image...
    - Using deduction, cross compare the candidate product-image to the images of other candidates with an OK status to deduce if this product is a match.
    - IMPORTANT: If the candidate uses the same image as one or more other candidates with an OK status then treat this as very strong evidence of a match, even if the GTIN or MPN are not an exact match.
    - If product images depict the same product but from different angles, always treat this as strong evidence of a match.
    - An exact match in GTIN/MPN/Model between the primary and candidate product should result in an OK status, even if the image appears slightly different.
    - If the product-image is of a distinctly different color or different flavor, then then output a status of MISMATCH explaining the differances.
    - If the product-image is obviously a different product or has major distinguishing features differentiations then output a status of MISMATCH explaining the visual differences.
    - Otherwise if the product-image has little or no distinguishing feature differentiations then output a status of QUESTIONABLE explaining the visual differences.
    - Consider visual variations such as perspective, lighting, or minor cosmetic details, as insufficient to classify as a MISMATCH unless the core product features differ.
    - If the image is VALID then output a detailed explanation of the image differences, explaining every visual difference found that was a factor in determining the status.
    - Otherwise if the image is INVALID then output an explanation of why the image was found to be invalid.

> IMPORTANT: If the image is INVALID then DO NOT describe the image differences in the output, only indicate why it is invalid!!!

- Output the line number, status code, and brief description.

**EXAMPLE OUTPUT**
1. OK - explain why ok
2. MISMATCH - explain why mismatch
3. QUESTIONABLE - explain uncertainty
4. OK - ...
> Output only the line-numbered status information for each match candidate, do not output an overall summary.

**PRIMARY PRODUCT**
Product Name: {{ product_name }}
Brand Name: {{ brand_name }}
MPN: {{ sku }}
{%- if gtin %}
GTIN: {{ gtin }}
{%- endif %}
Price: {{ ref_price }} ({{ currency }})

**CANDIDATES**

{% for page in pages %}
{{ loop.index }}. {{ page.name }}
Product Name: {{ page.product_name or page.page_title }}
URL: {{ page.url }}
Source: {{ page.source }}
{%- if page.image_key %}
Image: {{ page.image_key }}
{%- if page.image_refs == 1 %}
> This candidate has a unique product-image, compare the image very carefully against the other images that are known to be associated with the primary product to determine if this is the same product as the primary product.
{%- endif %}
{%- endif %}
{%- if page.brand_name %}
Brand: {{ page.brand_name }}
{%- endif %}
{%- if page.price %}
Price: {{ page.price }}{% if page.currency %} ({{ page.currency }}){% endif %}
{%- endif %}
{%- if page.availability %}
Availability: {{ page.availability }}
{%- endif %}
{%- if page.condition %}
Condition: {{ page.condition }}
{%- endif %}
{%- if page.gtin %}
GTIN: {{ page.gtin }}
{%- endif %}
{%- if page.mpn %}
MPN: {{ page.mpn }}
{%- endif %}
{%- if page.model %}
Model: {{ page.model }}
{%- endif %}
{%- if page.sku %}
SKU: {{ page.sku }}
{%- endif %}
{%- if page.color %}
Color: {{ page.color }}
{%- endif %}
{%- if page.category %}
Category: {{ page.category }}
{%- endif %}
{{ page.description }}
{%- if page.cross_checks|length > 0 %}
The following cross-checks may be an indication of a positive match...
{%- for cross_check in page.cross_checks %}
- {{ cross_check }}
{%- endfor %}
{%- endif %}
{% endfor %}
