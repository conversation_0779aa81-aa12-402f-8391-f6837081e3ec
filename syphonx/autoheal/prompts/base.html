<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>{% block title %}{% endblock %}</title>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<style>
    body { font-family: sans-serif; }
    h1 { font-size: 24px; }
    h2 { font-size: 16px; }
    h3 { font-size: 16px; }
    a { text-decoration: none; }
    a:hover { text-decoration: underline; }
    section { margin-top: 1em; }
    table, th, td { border: 1px solid #aaa; border-collapse: collapse; }
    thead, th { background-color: #eee; font-weight: bold; }
    td { padding: 4px; vertical-align: top; word-wrap: break-word; }
    section { margin-top: 1em; }
    summary { cursor: pointer; }
    summary > small { font-weight: normal; }
    details { margin-top: 1em; }
    details > summary { font-weight: bold; }
    details > :not(:first-child) { margin-left: 1em; }
    code { font-family: monospace; font-size: 14px; white-space: pre; background-color: #eee; }
    pre del { color: red; }
    ins { color: green; background-color: #e6ffe6; }
    del { color: red; background-color: #ffebe6; text-decoration: line-through; }
    em { color: green; background-color: #ccffcc; }
    .diff { font-size: x-small; padding: 0; }
    .d-block { display: block; }
    .title { font-weight: bold; }
    .success { color: green; background-color: #e6ffe6; }
    .error { color: red; background-color: #ffebe6; }
    .warning { color: black; background-color: #FFFF99; }
    .info { color: gray; }
    .stat { color: gray; }
</style>
</head>
<body>
{% block content %}
{% endblock %}
</body>
</html>
