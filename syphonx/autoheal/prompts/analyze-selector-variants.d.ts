interface AnalyzeSelectorVariantsContext {
    targets: AnalyzeSelectorVariantsTarget[];
}

interface AnalyzeSelectorVariantsTarget {
    name: string;
    label: number;
    selector?: string;
    selector_output?: string | null;
    selector_output_filtered?: string | null;
    selector_output_unfiltered?: string | null;
    attr?: string;
    filter?: string;
    status?: string;
    candidates: string[];
    explain?: string;
    semantic?: string[];
    nth_child?: number;
    nth_of_type?: number;
    hash?: string[];
}

interface AnalyzeSelectorVariantsResponse {
    name: string;
    selector: string;
    semantic: string[];
    hash: string[];
    explain: string;
}