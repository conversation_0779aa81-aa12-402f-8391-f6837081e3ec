interface AnalyzeProductMatchesReportContext {
    analyze_id: string;
    product_id: number;
    app_name: string;
    title: string;
    prompt: string;
    completion: string;
    model: string;
    prompt_tokens: number;
    completion_tokens: number;
    cached_tokens: number;
    total_tokens: number;
    cost: number;
    product_name: string;
    brand_name: string;
    mpn: string;
    price: number;
    currency: string;
    gtin: string;
    active_matches: ProductMatchAnalyzeSubitem[];
    deactivated_matches: ProductMatchAnalyzeSubitem[];
    unanalyzed_matches: ProductMatchAnalyzeSubitem[];
    distinct_image_counts: Array<[string, number]>;
    summary: Array<[string, number]>;
    datauri?: string;
    timestamp: string;
    obj: unknown;
}
