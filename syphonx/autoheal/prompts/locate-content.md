# locate-content

## TASK OVERVIEW
You are an expert in shopper marketing and digital conversion optimization who specializes in analyzing online retail product-pages.

Your task is to analyze the given screenshot of a product-page at {{ domain_name }} and identify the following components on the page:
{%- for target in targets %}
- {{ target.name | safe }}
{%- endfor %}

> IMPORTANT: All components must be identified based solely on the main product defined in STEP 1, with no consideration of content from subsequent panels unless explicitly part of the main product’s details.

## TASK DETAILS
Every visual element on the page is labelled with a unique number in square brackets like `[1]`, `[2]`, `[3]`, etc.
Each label number uniquely identifies the element that it is next to.
Labels appear immediately after each element, for example: `Apple AirTag 4 Pack [16]`, `$79.98 [22]`, etc.

Your task is to find each item below and return label number that is next to each item that you find.
The product page may not be in English, please do your best to interpret the page content in any language.

Think step-by-step using the steps outlined below.
Try to think like a consumer who is visiting the retailer site, visually scanning the page for product information.

## STEP 1: Identify the main product
- The first step is to identify the main product on the page.
- If the page is a multi-product page where no single product is more prominent than any other then focus on the first product closest to the top-left of the page.
- If there are multiple product panels on the page with the **first panel above the fold** showing a discontinued product (e.g., marked as "No Longer Available"), this indicates the discontinued product is the main product, and all subsequent panels (e.g., alternative or similar products) should be ignored for the purpose of identifying the main product's components. The main product is strictly the first panel, regardless of the availability or prominence of other panels.
- Start by looking for the main product-image which is typically the largest image "above the fold".
- Look for the other product information (e.g., product-name, stock-status) **within the same first panel** as the main product image, and disregard any information from subsequent panels unless explicitly indicated as part of the main product's details.
- Disregard any additional products on the page that may appear to be similar products, or suggested products but not the main product.

> IMPORTANT: The main product’s identity and all associated components (e.g., name, price, features) must be derived exclusively from the first panel, even if the panel indicates the product is discontinued. If the first panel lacks components due to its discontinued status, do not seek them in subsequent panels; instead, note their absence.

## STEP 2: Locate each item on the page as outlined below
Locate each item on the page in accordance with the requirements outlined below.

{#

************************************************************************
NOTE: Targets are defined in source file ./lib/analyze/selector-profiles
************************************************************************

#}
{% for target in targets %}
### {{ target.name | safe }}
{{ target.locate | safe}}

{% endfor %}

> IMPORTANT: All item locations must be confined to the first panel identified as the main product in STEP 1. If an item (e.g., product-name, price) is not present in the first panel, return an empty labels array and note the absence in the explain field, rather than seeking it in subsequent panels.

- **Clarification:** All item locations must be strictly limited to the first panel identified as the main product in STEP 1. If an item (e.g., product-name, price) is absent from the first panel—especially due to a discontinued status (e.g., 'No Longer Available')—return an empty `labels` array and explain the absence in the `explain` field, without considering subsequent panels.

## “DO NOT TRIGGER” CASES AND EDGE CONDITIONS
1. **Ignore Subsequent Panels:** If the first panel is marked with 'No Longer Available,' 'Discontinued,' or similar text, it is the main product, and all product information (e.g., product-name, price) must be sourced exclusively from this panel. Do not use data from subsequent panels, even if they contain active products.
2. **Partial Content:** If the screenshot is incomplete and the first panel is only partially visible, base your analysis on the visible portion of the first panel and assume it represents the main product.
3. **Ambiguity:** If multiple panels appear equally prominent but the first panel indicates a discontinued status, prioritize the first panel as the main product and ignore others.
4. If the first panel lacks a component (e.g., price, features) because it is marked as 'No Longer Available' or similar, return an empty `labels` array for that component and explicitly state its absence in the `explain` field, without referencing subsequent panels.

## PRICE OUTPUT
Below are some special instructions for identying and outputting the price on the page.

> IMPORTANT: Watch out for prices formatted as **superscript pricing** aka **raised fractional pricing** aka **retail pricing formatting**, for example $9⁹⁹ for $9.99.

Due to labelling, prices formated in this way may (or may not) be split among two adjacent labelled elements, for example `9[101].99[102]`. This typically happens when the fractional part of the price is rendered in a smaller type resulting in the price being contained in two elements instead of one as is typically the case. In this situation please set the text output equal to the full price including the fractional part and output both label numbers. See below for some examples that illustrate the visual input with corresponding expected `text` and `labels` outputs.

### Example: Price with a single label
- VISUAL INPUT: $9.99[76]
- STRING OUTPUT `text`: $9.99
- JSON OUTPUT `labels`: `[76]`

### Example: Price with special **retailer pricing formatting**
- VISUAL INPUT: $9⁹⁹[76]
- STRING OUTPUT `text`: $9.99
- JSON OUTPUT `labels`: `[76]`

### Example: Price with seperate labels for whole and fractional parts
- VISUAL INPUT: $19[101]99[102]
- STRING OUTPUT `text`: $19.99
- JSON OUTPUT `labels`: `[101,102]`

### Example: Price with seperate labels for whole and fractional parts
- Visual INPUT: $1,024[16]42[17]
- STRING OUTPUT `text`: $1,024.42
- JSON OUTPUT `labels`: `[16,17]`

### Example: Euro price with seperate labels for whole and fractional parts
- Visual INPUT: €2.489,01[312]
- STRING OUTPUT `text`: €2.489,01
- JSON OUTPUT `labels`: `[312]`


## OUTPUT FORMAT
- Output a JSON array of objects according to the following rules.
- For each identified component on the page return the label number in an array of numbers in the `labels` field.
- Output the text of the component in the `text` field.
- Explain the rationale for how the component was identified on the page in the `explain` field.
- If the identified text on the page is non-English, please translate the text in the `explain` field leaving the `text` output as it appears on the page in its original language.
- If multiple labels are found (for those where multiple components are expected), then return all of the labels.
- If no labels are found then return an empty array.
- If the content was located near a heading on the page, please also output the heading text in the `heading` field.
- Finally provide an overall summary of all components found in the `summary` output as shown below.
- Field names should always appear as text e.g. "name", "price", "features", "description", "brand_name" and so forth.

## NEVER RETURN THIS OUTPUT
```
{
  "0": {
    "name": "brand_name",
    "labels": [],
    "text": null,
    "explain": "The brand name was not found near the product name, price, or in the product details area in the main product panel. No explicit 'Brand:' or 'Manufacturer:' label was present, nor was the brand name clearly part of the product name."
  },
  "1": {
    "name": "price",
    "labels": [283, 284],
    "text": "$0.68",
    "explain": "The primary selling price is located near the quantity selector and add-to-cart button. The price is displayed as '$[283]0.68[284]', with the currency symbol and value split into two labelled elements."
  },
  "2": {
    "name": "product_name",
    "labels": [249],
    "text": "14 AWG Solid Grounding Pigtail, Green, 8\" Long, 50/Box",
    "explain": "The product name is the main heading for the product, prominently displayed at the top of the main product panel."
  },
  "3": {
    "name": "product_description",
    "labels": [576],
    "text": "Grounding Pigtail, Green, 14 AWG Solid, 8\", with Loop & Ground Screw & Stripped End, 50 per Box",
    "heading": "Overview",
    "explain": "The product description is found under the 'Overview' heading [574] and describes the product details."
  },
  "4": {
    "name": "product_image",
    "labels": [236],
    "text": null,
    "explain": "The main product image is located in the top left of the main product panel, showing the grounding pigtail."
  },
  "5": {
    "name": "product_variants",
    "labels": [],
    "text": null,
    "explain": "No options for product variants such as color, size, etc., were found in the main product panel."
  },
  "6": {
    "name": "product_condition",
    "labels": [],
    "text": null,
    "explain": "No indication of the product condition (e.g., NEW, USED, REFURBISHED) was found on the page."
  }
}
```

## EXAMPLE SUCCESSFUL OUTPUT
```
{
    "name": {
        "labels": [8],
        "text": "...",
        "explain": "..."
    },
    "price": {
        "labels": [22],
        "text": "...",
        "explain": "..."
    },
    "features": {
        "labels": [76, 77, 78],
        "text": "...",
        "explain": "..."
    },
    "description": {
        "labels": [53],
        "text": "...",
        "heading": "Descrizione del prodotto",
        "explain": "The product description is under the heading 'Descrizione del prodotto' and consists of a series of sentences describing the product."
    },
    "brand_name": {
        "labels": [],
        "text": "...",
        "explain": "No brand name found on the page."
    },
    "summary": "..."
}
```
