<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{name}}</title>
    <link rel="stylesheet" href="main.css" />
</head>
<body>

<h3 class="mb1">{{name}}</h3>
<div>({{regression_count}} regressions)</div>

<h4>Status</h4>
<table id="status-table" class="mt1">
    <tbody>
        <tr>
            <td>status</td>
            <td>{{status}}</td>
        </tr>
        <tr>
            <td>capture engine</td>
            <td>{{capture_engine}}</td>
        </tr>
    </tbody>
</table>

<h4>Template</h4>
<table id="template-table" class="mt1">
    <tbody>
        <tr>
            <td>template path</td>
            <td>{{template_path}}</td>
        </tr>
        <tr>
            <td>created_at</td>
            <td>{{template.created_at}}</td>
        </tr>
        <tr>
            <td>created_by</td>
            <td>{{template.created_by}}</td>
        </tr>
        <tr>
            <td>modified_at</td>
            <td>{{template.modified_at}}</td>
        </tr>
        <tr>
            <td>modified_by</td>
            <td>{{template.modified_by}}</td>
        </tr>
        <tr>
            <td>revisions</td>
            <td>{{template.revision_count}}</td>
        </tr>
        <tr>
            <td>selectors changed</td>
            <td>{{template.selectors_changed}}</td>
        </tr>
        <tr>
            <td>capture id</td>
            <td>{{capture_id}}</td>
        </tr>
        <tr>
            <td>capture timestamp</td>
            <td>{{capture_date}}</td>
        </tr>
    </tbody>
</table>

<h4>Selectors</h4>
<table id="selectors-table" class="mt1">
    <thead>
        <td>selector</td>
        <td>original output</td>
        <td>updated output</td>
        <td>query</td>
    </thead>
    <tbody>
    {% for selector in selectors %}
        <tr>
            <td>{{selector.selector_name}}</td>
            {% if selector.regression %}
            <td class="regression-color">
                <span target="_blank" title="{{selector.reference.selector_json_value}}">{{selector.reference.selector_json_value|truncate(100)|replace('\n','<br>')|safe}}</span>
                <a href="{{selector.reference.screenshot_url}}" title="View screenshot from {{selector.reference.selector_age_days}} days ago" target="_blank"><i>({{selector.reference.selector_age_days}}d ago)</i></a>
            </td>
            {% elseif selector.selector_json_value %}
            <td title="{{selector.selector_json_value}}">{{selector.selector_json_value|truncate(100)|replace('\n','<br>')|safe}}</td>
            {% else %}
            <td>Ø</td>
            {% endif %}
            {% if selector.query_json_value %}
            <td>
                <div title="{{selector.query_json_value}}">{{selector.query_json_value|truncate(100)|replace('\n','<br>')|safe}}</div>
                {% if selector.query_diff_count > 0 %}
                <div class="diff mt1">{{selector.query_diff | safe}}</div>
                <i>({{selector.query_diff_count}}&nbsp;diffs)</i>
                {% elseif selector.query_json_value %}
                <i>(no&nbsp;diffs)</i>
                {% endif %}
            </td>
            {% else %}
            <td>Ø</td>
            {% endif %}
            <td>{{(selector.query or [])|join('<br>')|safe}}</td>
        </tr>
    {% endfor %}
    </tbody>
</table>

<div class="mt1">
    <div>
        <a href="{{capture_id}}.html" target="_blank" title="View rebased HTML">html</a>
        <a href="{{screenshot_url}}" class="ml1" target="_blank" title="View captured screenshot">screenshot</a>
        <a href="{{capture_url}}" class="ml1" target="_blank" title="View live page at {{seller_name}}">live page</a>
        <a href="{{html_url}}" class="ml1" target="_blank" title="View the HTML as stored in PS datastores">stored html</a>
    </div>
    <img src="{{screenshot_url}}" class="screenshot-inline">
</div>

</body>
</html>
