.screenshot-thumbnail { position: relative; float: left; margin: 8px; border: 2px solid #ccc; padding: 4px; text-decoration: none; }
.screenshot-thumbnail:hover { outline: 4px solid #aaa; }
.screenshot-thumbnail .label { position: absolute; }
.screenshot-thumbnail.blocked { border-color: red; }
.screenshot-thumbnail.invalid { border-color: coral; }
.screenshot-thumbnail.discontinued { border-color: orange; }
.screenshot-thumbnail.modal { border-color: skyblue; }
.screenshot-thumbnail.pnf { border-color: fuchsia; }
.screenshot-inline { border: 4px solid #aaa; padding: 4px; max-width: 1100px; }
.screenshot-selector-link { margin-top: 4px; margin-left: 4px; }

.ribbon .blocked { color: red; }
.ribbon .invalid { color: coral; }
.ribbon .discontinued { color: orange }
.ribbon .modal { color: skyblue; }
.ribbon .pnf { color: fuchsia; }

.blocked .label { color: red; }
.invalid .label { color: coral; }
.discontinued .label { color: orange }
.modal .label { color: skyblue; }
.pnf .label { color: fuchsia; }
.predicted { border-style: dashed; }

.diff { display: inline; }
.diff-added { color: green; background-color: #dfd; }
.diff-removed { color: red; background-color: #fdd; text-decoration: line-through;  }
.diff-unchanged { color: gray; background-color: #eee; }

.flex { display: flex; justify-content: space-between; }
.flex > :first-child { flex-grow: 1; flex-shrink: 1; flex-basis: auto; }
.flex > :not(:first-child) { flex-grow: 0; flex-shrink: 0; flex-basis: auto; }

.regression-color { color: red; }
.regression-color > * { color: red; }

.clip { display: block; width: 400px; height: 300px; overflow: hidden; }
.clip > img { width: 400px; }

#selectors-table td:nth-child(1) { max-width: 400px; }
#selectors-table td:nth-child(2) { max-width: 600px; }
#selectors-table td:nth-child(3) { max-width: 600px; }
#selectors-table td:nth-child(4) { max-width: 600px; }

table, th, td { border: 1px solid #aaa; border-collapse: collapse; }
thead { background-color: #eee; font-weight: bold; }
td { padding: 4px; vertical-align: top; word-wrap: break-word; }

.mb1 { margin-bottom: 1em; }
.mt1 { margin-top: 1em; }
.ml1 { margin-left: 4px; }
.mr1 { margin-right: 4px; }

.top-right { right: 2px; top: 2px; }
.top-left { right: 2px; top: 2px; }
.bottom-right { right: 2px; bottom: 2px; }
.bottom-left { left: 2px; bottom: 2px; }
