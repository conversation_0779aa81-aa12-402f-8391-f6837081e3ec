<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{title}}</title>
    <link rel="stylesheet" href="main.css" />
</head>
<body>

<div class="ribbon">
    <span>{{capture_count}} captures</span>
    <span class="regressions ml1">{{regression_count}} regressions</span>
    <span class="blocked ml1">{{blocked_count}} blocked</span>
    <span class="invalid ml1">{{invalid_count}} invalid</span>
    <span class="discontinued ml1">{{discontinued_count}} discontinued</span>
    <span class="modal ml1">{{modal_count}} modal</span>
    <span class="pnf ml1">{{pnf_count}} pnf</span>
</div>

<div>
{% for screenshot in screenshots %}
    <a href="{{screenshot.href}}" target="_blank" class="screenshot-thumbnail{% if screenshot.predicted %} predicted{% endif %}{% if screenshot.status != 'ok' %} {{screenshot.status}}{% endif %}">
        <div class="flex">
            <span>{{screenshot.name}}</span>
            <span title="{{screenshot.regression_selector_names}}">({{screenshot.regression_count}})</span>
        </div>
        <div class="clip"><img src="{{screenshot.screenshot_url}}"></div>
        {% if screenshot.status != 'ok' %}
        <small class="label bottom-right">{{screenshot.status}}</small>
        {% endif %}
    </a>
{% endfor %}
</div>

</body>
</html>
