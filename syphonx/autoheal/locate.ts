import * as dotenv from "dotenv";
dotenv.config();

import * as fs from "fs";
import chalk from "chalk";
import open from "open";

import {
    closeLogStream,
    createLocalLogStream,
    generateTimestamp,
    hookConsoleLog,
    jsdom,
    llm_models,
    locateContent,
    openPageForVision,
    parseArgs,
    parseDomainName,
    selectorProfiles,
    summarize,
    unhook<PERSON>onsoleLog,
    WaitFor
}
from "./lib/index.js";

const args = parseArgs({
    required: {
        url: "page url",
        model: llm_models.join(", "),
    },
    optional: {
        select: `specify selector profile: ${selectorProfiles().join(", ")}`,
        headless: "hide browser window (default=false)",
        pause: "pause for user input after each page load (ignored if cached option is used)",
        waitfor: "specify load, domcontentloaded, or networkidle (default=none)"
    }
});

const url = args.url;
const domain_name = parseDomainName(url);
const selector_profile = args.select || selectorProfiles()[0];

const options = {
    headless: !!args.headless,
    timeout: 10000,
    waitfor: (args.waitfor as WaitFor | undefined) || (!args.snooze && !args.pause ? "networkidle" : undefined)
};

console.log(chalk.gray("opening page..."));
const { page, message: page_message } = await openPageForVision(url, options);
if (!page) {
    console.log(chalk.red(page_message));
    process.exit(0);
}

const { stream, url: report_url, path: report_path } = createLocalLogStream(`locate/${generateTimestamp("seconds")}/index.html`, { title: "LOCATE", subtitle: url });
hookConsoleLog();
const result = await locateContent({ page, selector_profile, domain_name, stream });
await closeLogStream(stream);
unhookConsoleLog();

if (!result.ok || !result.targets || !result.labelled_html) {
    console.log(chalk.red(result.message));
    process.exit(0);
}

const keys = Object.keys(result.targets);

fs.writeFileSync(`${report_path}/labelled.html`, result.labelled_html);
const html = result.labelled_html.replace(/\s+__label__="\d+"/g, "").replace(/\s+__show__/g, "");
jsdom(html);
fs.writeFileSync(`${report_path}/unlabelled.html`, html);

console.log();
for (const key of keys) {
    const { text, explain } = result.targets[key];
    console.log(chalk.gray(`${key}: ${text ? text : chalk.italic(explain)}`));
}

console.log();
console.log(chalk.gray(`${report_path}/labelled.html`));
console.log(chalk.gray(`${report_path}/unlabelled.html`));
console.log(chalk.gray(`report: ${report_url}`));

const { summary } = summarize(result.analyze);
console.log(chalk.gray(summary));

open(report_url);
process.exit(0);
