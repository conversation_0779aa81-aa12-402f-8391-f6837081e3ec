import * as dotenv from "dotenv";
dotenv.config();

import * as fs from "fs";
import * as syphonx from "syphonx-lib";

import {
    commit,
    parseArgs,
    tryParseJson
}
from "./lib/index.js";

export interface CommitOptions {
    template_path: string;
    updates: SelectorUpdate[];
    analyze_url: string;
}

export interface SelectorUpdate {
    selector_name: string;
    selector: string;
    query?: syphonx.SelectQuery;
}

const params = parseArgs({
    optional: {
        verbose: "verbose log output"
    }
});

if (params.verbose)
    process.env.VERBOSE = "1";

const config = tryParseJson(fs.readFileSync("commit.jsonc", "utf8")) as CommitOptions;
await commit(config);
process.exit(0);
