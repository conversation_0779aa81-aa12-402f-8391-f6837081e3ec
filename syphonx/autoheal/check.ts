import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";
import * as syphonx from "syphonx-lib";
import { flattenTemplateSelect } from "syphonx-lib";
import { diffWords } from "diff";

import {
    diffDays,
    downloadFile,
    formatJson,
    loadTemplate,
    parseArgs,
    queryAuditCaptures
}
from "./lib/index.js";

const params = parseArgs({
    optional: {
        app: "app-name filter (brand_monitor, wtb, prowl)",
        account: "account key filter",
        country: "country code filter",
        seller: "seller id filter",
        selector: "selector name filter",
        regressions: "minimum regression count",
        gaps: "minimum gap count",
        engine: "capture engine (syphonx, puppeteer)",
        sku: "a regex filter reducing the result set to a set of skus",
        limit: "maximum number of captures",
        verbose: "verbose log output"
    },
    validate: ({ app, account, country, seller, selector }) => {
        if (!app) 
            return "Specify a PriceSpider app (brand_monitor, wtb, prowl)"
        if (!account && !country && !seller)
            return "Specify least one of --account, --country, or --seller";
        if (selector?.includes(","))
            return "Specifying multiple selectors is not supported";
        if (selector && !/^[A-Za-z0-9_]+$/.test(selector))
            return "Invalid selector name";
    }
});

if (params.verbose)
    process.env.VERBOSE = "1";

const rows = await queryAuditCaptures({
    app: params.app,
    account_key: params.account,
    country_code: params.country,
    seller_id: parseInt(params.seller),
    selector_name: params.selector,
    regressions: parseInt(params.regressions) || 1,
    gaps: parseInt(params.gaps) || 0,
    engine: params.engine !== "all" ? params.engine || "syphonx" : undefined,
    sku: params.sku,
    limit: parseInt(params.limit) || 3
});
console.log(chalk.gray(`(${rows.length} urls)`));

for (const row of rows) {
    const template_path = row.template_path;
    const { template } = await loadTemplate(template_path);

    console.log();
    console.log();
    console.log(chalk.cyan.underline(`[${rows.indexOf(row) + 1}/${rows.length}] ${row.page_id} (${row.seller_name})`));
    if (!params.selector) {
        console.log(chalk.gray(`${row.selectors.length} selectors`));
        console.log(chalk.gray(`${row.regression_count} regressions`));
        console.log(chalk.gray(`${row.gap_count} gaps`));
        console.log(chalk.gray(`${row.alert_count} alerts`));
    }

    console.log();
    console.log(chalk.gray(row.capture_url));
    console.log(chalk.gray(`${row.last_capture.html_url} (${diffDays(row.last_capture.capture_date)}d)`));
    console.log(chalk.gray(template_path));
    console.log();

    const html = await downloadFile(row.last_capture.html_url);
    if (html) {
        const select = flattenTemplateSelect(template.actions, row.selectors.map(selector => selector.selector_name));
        const result = syphonx.select(select, html);
        const selectors = params.selector ? row.selectors.filter(({ selector_name }) => params.selector.split(",").includes(selector_name)) : row.selectors;
        for (const {
            selector_name,
            selector_json_value,
            selector_alerts,
            regression,
            gap,
            hit_rate,
            reference
        } of selectors) {
            if (regression)
                console.log(chalk.white(`SELECTOR: ${selector_name}`) + chalk.yellow.bold(` (regression ${reference?.selector_age_days}d)`));
            else if (gap)
                console.log(chalk.white(`SELECTOR: ${selector_name}`) + chalk.yellow.bold(` (gap ${(100 * hit_rate).toFixed(1)}%)`));
            else if (selector_alerts)
                console.log(chalk.white(`SELECTOR: ${selector_name}`) + chalk.yellow.bold(` (alerts: ${selector_alerts})`));
            else
                console.log(chalk.white(`SELECTOR: ${selector_name}`));

            const { query } = select.find(select => select.name === selector_name) || {};
            if (query) {
                console.log(chalk.gray(`query (${query.length})`));
                for (const q of query)
                    console.log(chalk.gray(syphonx.renderJQuery(q)));

                const obj = result.data[selector_name];
                const reference_json_value = (regression ? reference?.selector_json_value : selector_json_value) || null;
                const old_json = formatJson(reference_json_value !== null ? reference_json_value : "null");
                const new_json = formatJson(obj?.value !== undefined ? JSON.stringify(obj.value) : "null");

                const diffs = diffWords(old_json, new_json);
                console.log();
                console.log(chalk.gray(`diffs (${diffs.filter(diff => diff.added || diff.removed).length})`));
                diffs.forEach(diff => {
                    if (diff.added)
                        process.stdout.write(chalk.green(diff.value));
                    else if (diff.removed)
                        process.stdout.write(chalk.red(diff.value));
                    else
                        process.stdout.write(chalk.grey(diff.value));
                });
                console.log();

                if (obj?.nodes) {
                    console.log();
                    console.log(chalk.gray(`nodes (${obj.nodes.length})`));
                    for (const node of obj.nodes)
                        console.log(chalk.gray(node));
                }
                console.log();

                if (reference?.capture_id) {
                    console.log(chalk.gray(`reference: ${reference.html_url} [${reference.capture_date}]`));
                    const reference_html = await downloadFile(reference.html_url);
                    if (reference_html) {
                        const reference_result = syphonx.select(select, reference_html);
                        if (reference_result?.data[selector_name])
                            console.log(chalk.gray(reference_result.data[selector_name].value));
                        else
                            console.log(chalk.gray("no reference result"));
                    }
                    else {
                        console.log(chalk.gray("no reference content"));
                    }
                }
            }
            else if (regression || selector_alerts) {
                console.log(chalk.red("query not defined"));
            }
            else {
                console.log(chalk.gray("query not defined"));
            }
            console.log();
        }
    }
}

process.exit();
