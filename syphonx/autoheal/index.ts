import * as dotenv from "dotenv";
dotenv.config();

import * as fs from "fs";
import open from "open";

import {
    formatPercent,
    parseArgs,
    queryMetrics
}
from "./lib/index.js";

const params = parseArgs({
    optional: {
        app: "brand_monitor, wtb, prowl (default=brand_monitor)",
        group: "group by account, country, seller, or selector",
        account: "account key filter",
        country: "country code filter",
        offset: "rows to skip (works with max to provide pagination of larger result sets)",
        seller: "seller id filter",
        selector: "selector name filter",
        sku: "a regex filter reducing the result set to a set of skus",
        max: "max number of rows to return",
        verbose: "verbose log output"
    }
});

if (!params.app)
    params.app = "brand_monitor";

if (params.verbose)
    process.env.VERBOSE = "1";

if (params.seller && !params.group)
    params.group = "selector";
else if (!params.group)
    params.group = "seller";

const rows = await queryMetrics({
    app: params.app,
    group_by: new Set(params.group.split(",")),
    account_key: params.account,
    country_code: params.country,
    seller_id: parseInt(params.seller),
    selector_name: params.selector,
    sku: params.sku,
    max: parseInt(params.max) || undefined,
    offset: parseInt(params.offset) || 0
});

if (rows.length > 0) {
    const lines = `
<!doctype html>
<html>
<head>
<style>
table, th, td { border: 1px solid #aaa; border-collapse: collapse; }
thead, th { background-color: #eee; font-weight: bold; }
td { padding: 4px; vertical-align: top; word-wrap: break-word; }
sup { margin-left: 4px; font-size: 0.7em; color: #888; }
</style>
</head>
<body>
    `.trim().split("\n");
    lines.push("<table>");

    lines.push("<tr>");
    const [row] = rows;
    for (const key of Object.keys(row as {}))
        lines.push(`<th>${key}</th>`);
    lines.push("</tr>");

    for (const row of rows) {
        lines.push("<tr>");
        row.regressions += `<sup>${formatPercent(row.regressions, row.pages)}</sup>`;
        row.gaps += `<sup>${formatPercent(row.gaps, row.pages)}</sup>`;
        row.blocked += `<sup>${formatPercent(row.blocked, row.pages)}</sup>`;
        row.modal += `<sup>${formatPercent(row.modal, row.pages)}</sup>`;
        row.regression_selectors = row.regression_selectors ? `${row.regression_selectors.replace(/,/g, ", ")} (${row.regression_selectors.split(",").length})` : "-";
        for (const key of Object.keys(row as {}))
            lines.push(`<td>${row[key]}</td>`);
        lines.push("</tr>");
    }
    lines.push("</table>");

    lines.push("</body>");
    lines.push("</html>");
    const html = lines.join("\n");

    const file = "tmp/_a0.html";
    fs.writeFileSync(file, html);
    console.log(file);
    open(file);
}
console.log(`${rows.length} rows`);
