ERROR: '#4419_4424_addToCart' is not a valid selector https://storage.googleapis.com/ps-syphonx/autogen/20240725160039.html
ERROR: Cannot read properties of undefined (reading 'obj') https://storage.googleapis.com/ps-syphonx/autogen/20240725160039.html
ERROR: Cannot read properties of undefined (reading 'obj') https://storage.googleapis.com/ps-syphonx/autogen/20240725101040.html

Implement update mode
Shorten truncation in slicer
Click to reveal
Add click action for auto-close

Fix screenshot smearing problem
Look in url for vpn, sku, etc.
Review how selectors with regex are validated, see currency in https://storage.googleapis.com/ps-syphonx/autogen/20240724114947.html
Remove screenshot from top level in report output
Put the autogen_id in the report output
add formatElapsedTime() function and apply to cost summary breakdown

Add cost and elapsed time to steamlit app
Auth

DONE:
Multiple retries for auto closing dialogs
Qualification needs to know about regex extract otherwise it rejects custom syphonx extract syntax
