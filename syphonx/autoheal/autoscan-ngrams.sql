WITH
page_scans AS (
  SELECT
    domain_name,
    page_class,
    page_id,
    page_text
  FROM omega.page_scans
),
count_by_page_class AS (
  SELECT
    domain_name,
    page_class,
    COUNT(*) AS page_class_count
  FROM page_scans
  GROUP BY ALL
),
page_text_ngrams AS (
  SELECT
    domain_name,
    page_class,
    page_id,
    ngram
  FROM page_scans, UNNEST(ML.NGRAMS(udf.split_words(LOWER(page_text), TRUE), [2,6])) AS ngram
),
page_text_ngrams_grouped AS (
  SELECT
    domain_name,
    ngram,
    COUNT(DISTINCT page_id) AS page_count,
    ANY_VALUE(page_class_count) AS rollup_count,
    ROUND(SAFE_DIVIDE(COUNT(DISTINCT page_id), ANY_VALUE(page_class_count)), 3) AS page_coverage,
    ARRAY_TO_STRING(ARRAY_AGG(DISTINCT page_class),', ') AS page_class,
    COUNT(DISTINCT page_class) AS page_class_hits,
    COUNT(*) AS ngram_count
  FROM page_text_ngrams
  JOIN count_by_page_class USING (domain_name, page_class)
  GROUP BY ALL
)
SELECT * EXCEPT(page_class_hits)
FROM page_text_ngrams_grouped
WHERE page_class_hits=1
--AND page_coverage >= 0.6
--AND LENGTH(ngram) >= 10
QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name, page_class ORDER BY page_coverage DESC, LENGTH(ngram) DESC, ngram) <= 10;