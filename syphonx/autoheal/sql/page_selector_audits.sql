CREATE OR <PERSON><PERSON>LACE TABLE syphonx.page_selector_audits
(
  audit_id STRING NOT NULL,
  audited_at TIMESTAMP NOT NULL,
  audit_code STRING NOT NULL,
  validation_code STRING,
  app_name STRING NOT NULL,
  page_type STRING NOT NULL,
  page_id STRING NOT NULL,
  selector_name STRING,
  seller_name STRING NOT NULL,
  seller_id INT64 NOT NULL,
  account_key STRING NOT NULL,
  country_code STRING NOT NULL,
  sku STRING NOT NULL,
  audit_url STRING,
  audit_message STRING,
  screenshot_url STRING,
  duration INT64,
  cost FLOAT64,
  analyze ARRAY<STRUCT<
    name STRING NOT NULL,
    duration INT64,
    model STRING,
    usage INT64,
    cost FLOAT64,
    message STRING,
    report_url STRING
  >>
)
PARTITION BY DATE(audited_at)
CLUSTER BY country_code, seller_name, selector_name, audit_code;