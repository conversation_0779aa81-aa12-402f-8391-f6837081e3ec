CREATE OR REPLACE VIEW syphonx.audit_requests
AS
SELECT DISTINCT
  a.audit_id,
  a.audit_date,
  b.page_id,
  a.selectors,
  b.account_key,
  b.country_code,
  b.sku,
  b.seller_name,
  b.seller_id,
  b.capture_url,
  a.priority,
  a.ttl,
  a.requested_at,
  a.requested_by
FROM syphonx.audit_request_log AS a,
UNNEST(SPLIT(IFNULL(account_key, ''))) AS account_key,
UNNEST(SPLIT(IFNULL(country_code, ''))) AS country_code,
UNNEST(SPLIT(IFNULL(seller_id, ''))) AS seller_id,
UNNEST(SPLIT(IFNULL(page_id, ''))) AS page_id
JOIN syphonx.brand_monitor_product_page_daily AS b ON b.date=a.audit_date AND b.page_id=page_id;
