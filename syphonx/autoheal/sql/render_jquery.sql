CREATE OR REPLACE FUNCTION syphonx.render_jquery(query JSON)
RETURNS STRING
DETERMINISTIC
LANGUAGE js
AS
r"""
  if (!is_valid_query(query))
    return null;

  const selector = query[0];
  const ops = query.slice(1);
  return [`$("${selector}")`, ...ops.map(op => `${op[0]}(${op.slice(1).map(param => JSON.stringify(param)).join(", ")})`)].join(".");

  function is_valid_query(query) {
    return query instanceof Array && query.length > 0 && typeof query[0] === 'string' && query.slice(1).every(op => op instanceof Array);
  }
""";

-- SELECT syphonx.render_jquery(JSON '[]') --> NULL
-- SELECT syphonx.render_jquery(JSON '{}') --> NULL
-- SELECT syphonx.render_jquery(JSON '0') --> NULL
-- SELECT syphonx.render_jquery(JSON '["h1"]') --> $("h1")
