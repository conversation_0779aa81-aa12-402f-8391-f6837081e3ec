CREATE OR REPLACE TABLE omega.user_log
(
  timestamp TIMESTAMP,
  key STRING,
  ip_address STRING,
  data JSON
)
PARTITION BY DATE(timestamp)
CLUSTER BY key;

/*
G<PERSON> CLOUD RUN FUNCTION
https://us-central1-ps-bigdata.cloudfunctions.net/omega?key=test&a=b

# index.js
```
const functions = require('@google-cloud/functions-framework');
const { BigQuery } = require("@google-cloud/bigquery");
const bigquery = new BigQuery();

functions.http("log", async (req, res) => {
    res.set("Access-Control-Allow-Origin", "*"); // allow all origins
    res.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS"); // allowed HTTP methods
    res.set("Access-Control-Allow-Headers", "Content-Type"); // allowed headers

    const { key, ...data } = req.query;
    if (!key) {
        res.status(400).send("INVALID");
        return;
    }

    try {
        await bigquery
            .dataset("omega")
            .table("user_log")
            .insert({
            key,
            timestamp: new Date(),
            ip_address: req.headers["x-forwarded-for"]?.split(",")[0] || req.ip,
            data: JSON.stringify(data)
        });
    }
    catch (err) {
        console.error(err);
        res.status(500).send("ERROR");
        return;
    }

    res.status(200).send("OK");
});
```

# package.json
```
{
    "dependencies": {
        "@google-cloud/functions-framework": "^3.0.0",
        "@google-cloud/bigquery": "^7.9.1"
    }
}
```
*/