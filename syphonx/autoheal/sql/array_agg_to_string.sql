-- Use to workaround limitation of ARRAY_CONCAT_AGG not allowed in UNNEST
-- more info: https://medium.com/@kayrnt/14-bigquery-shortfalls-that-are-driving-me-crazy-and-how-to-workaround-them-b00b3a1bdf3f#131b
CREATE OR REPLACE FUNCTION udf.array_agg_to_string(keys ARRAY<STRING>, delimiter STRING)
RETURNS STRING
AS
(IF(ARRAY_LENGTH(keys)>0, ARRAY_TO_STRING(ARRAY(SELECT DISTINCT key FROM UNNEST(keys) AS key ORDER BY key), delimiter), NULL));
