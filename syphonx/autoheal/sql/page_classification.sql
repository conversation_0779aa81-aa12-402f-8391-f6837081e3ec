WITH screenshots AS (
  SELECT
    capture_date,
    ARRAY_TO_STRING(ARRAY(SELECT label FROM UNNEST(labels) AS label WHERE label<>'product-page'), ', ') AS label,
    CONCAT('http://storage.googleapis.com/ps-colossus/', capture_id, '/1.png') AS screenshot_url
  FROM brand_monitor_data_quality.capture_classifications_ex
  WHERE DATE(capture_date)=CURRENT_DATE()
  AND captured_as='product_page'
  AND 'nominal' NOT IN UNNEST(labels)
  AND SAFE_CAST(probability AS FLOAT64)>=0.9
  ORDER BY capture_date DESC
)
SELECT ARRAY_AGG(screenshot_url)
FROM screenshots
WHERE label='lightbox';

/*
https://storage.googleapis.com/pricespider/thumbnail-viewer/index.html

blank 56
broken 1861
challenge 50
homepage 177
lightbox 3546
multiple-products 93
*/
