CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION conflux.parse_error_message(message STRING)
RETURNS STRUCT<
  error_class STRING,
  error_code STRING,
  error_value STRING
>
AS
(  
  CASE
    WHEN STARTS_WITH(message, '[DataError] No product data found') THEN
      STRUCT(
        'DataError',
        'no-product-data',
        NULL
      )
    WHEN STARTS_WITH(message, '[DataError] HTTP Server Bad Request Response') THEN
      STRUCT(
        'DataError',
        'bad-http-request',
        REGEXP_EXTRACT(message, r'(\d+)$')
      )
    WHEN STARTS_WITH(message, '[Hound') THEN
      STRUCT(
        'CaptchaError',
        REGEXP_EXTRACT(message, r'\] (.+)$'),
        null
      )
    WHEN LOWER(message) LIKE '%timeout%' THEN
      STRUCT(
        'TimeoutError',
        CASE
          WHEN message='[Error] Timeout was reached' THEN 'general-timeout'
          WHEN message LIKE '%page.%' OR <PERSON>OWER(message) LIKE '%browser%' THEN 'browser-timeout'
          WHEN STARTS_WITH(message, '[ConnectionError]') AND LOWER(message) LIKE '%proxy%' THEN 'proxy-connection-timeout'
          WHEN STARTS_WITH(message, '[ConnectionError]') THEN 'connection-timeout'
          WHEN STARTS_WITH(message, '[SyphonXError]') AND message LIKE '%LOCATOR%' THEN 'syphonx-locator-timeout'
          WHEN message LIKE '%CrawlTimeoutError%' THEN 'crawl-timeout'
        END,
        REGEXP_EXTRACT(message, r'"name":"([a-z_]+)"')
      )
    WHEN STARTS_WITH(message, '[SyphonXError]') AND message LIKE '%LOCATOR%' THEN
      STRUCT(
        'SyphonXError',
        CASE
          WHEN message LIKE '%Timeout%' THEN 'locator-timeout' --superceded by general timeout case above
          WHEN message LIKE '%not a valid selector%' THEN 'locator-invalid-selector'
          WHEN message LIKE '%Failed to find frame for selector%' THEN 'locator-frame-not-found'
          WHEN message LIKE '%strict mode violation%' THEN 'locator-strict-mode-violation'
          ELSE 'locator-other'
        END,
        REGEXP_EXTRACT(message, r'"name":"([a-z_]+)"')
      )
    WHEN STARTS_WITH(message, '[SyphonXError]') THEN
      STRUCT(
        'SyphonXError',
        REGEXP_EXTRACT(message, r'code: ([a-z-]+)'),
        COALESCE(
          REGEXP_EXTRACT(message, r'Object requires property "([^"]+)"'),
          REGEXP_EXTRACT(message, r'Object.([^ ]+)')
        )
      )
    WHEN LOWER(message) LIKE '%proxy%' THEN
      STRUCT(
        'ProxyError',
        NULL,
        NULL
      )
    WHEN
      LOWER(message) LIKE '%execution context was destroyed%'
      OR LOWER(message) LIKE '%navigat%'
      OR LOWER(message) LIKE '%target closed%'
      OR LOWER(message) LIKE '%page closed%' THEN
      STRUCT(
        'RenavigationError',
        NULL,
        NULL
      )
    WHEN REGEXP_CONTAINS(message, r'^\[[^]]+\]') THEN
      STRUCT(
        REGEXP_EXTRACT(message, r'^\[([^]]+)\]'),
        NULL,
        NULL
      )
    ELSE
      STRUCT(
        'UnknownError',
        NULL,
        NULL
      )
  END
);