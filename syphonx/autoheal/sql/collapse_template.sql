CREATE OR REPLACE FUNCTION syphonx.collapse_template(template JSON)
RETURNS ARRAY<
  STRUCT<
    selector_name STRING,
    selector_query_json JSON
  >
>
DETERMINISTIC
LANGUAGE js
AS
r"""
if (!isValidTemplate(template))
  return null;

const a0 = collapse(template.actions);
const a1 = a0.map(obj => ({
  selector_name: obj.name || "",
  selector_query_json: obj.query || [[""]]
}));
return a1;

function collapse(actions, selectors) {
  const selectActions = findAction(actions, 'select').map(action => action.select);
  const result = [];
  for (const action of selectActions)
    for (const select of action)
      if (!selectors || (select.name && selectors.includes(select.name))) {
        const existing_select = result.find(obj => obj.name === select.name);
        if (!existing_select)
          result.push(select);
        else
          existing_select.query = mergeQueries(existing_select.query, select.query);
      }
  return result;
}

function findAction(actions, action_type) {
  return flatten(actions)
    .map(obj => obj.action)
    .filter(action => action.hasOwnProperty(action_type));
}

function flatten(actions, result = [], level = 0, n) {
  for (const action of actions) {
    result.push(!n ? { action, level } : { action, level, case: n });
    if (action.hasOwnProperty('each'))
      flatten(action.each.actions, result, level + 1);
    else if (action.hasOwnProperty('repeat'))
      flatten(action.repeat.actions, result, level + 1);
    else if (action.hasOwnProperty('switch'))
      for (const obj of action.switch)
        flatten(obj.actions, result, level + 1, action.switch.indexOf(obj) + 1);
  }
  return result;
}

function mergeQueries(q1, q2) {
  if (q1 && q2) {
    const a0 = q1.map(obj => JSON.stringify(obj));
    const a1 = q2.filter(obj => a0.find(json => json === JSON.stringify(obj)));
    q1.push(...a1);
    return q1;
  }
  else if (q1 && !q2)
    return q1;
  else if (!q1 && q2)
    return q2;
  else
    return [];
}

function isValidTemplate(template) {
  return template !== null
    && typeof template === 'object'
    && template.hasOwnProperty('actions')
    && Array.isArray(template.actions)
    && template.actions.every(obj => typeof obj === 'object' && obj !== null);
}
""";

--> SELECT syphonx.collapse_template(NULL) --> NULL
--> SELECT syphonx.collapse_template(JSON '{}') --> NULL
--> SELECT syphonx.collapse_template(JSON '0') --> NULL
--> SELECT syphonx.collapse_template(JSON '{ "actions": [ { "select": [{ "name": "title", "query": [["h1"]] }]} ] }') --> ["title", [["h1"]]]
--> SELECT syphonx.collapse_template(JSON '{ "actions": [ { "select": [{ "name": "title" }]} ] }') --> ["title", ""]
--> SELECT syphonx.collapse_template(JSON '{ "actions": [ { "select": [{ "query": [["h1"]] }]} ] }') --> ["", ""]
