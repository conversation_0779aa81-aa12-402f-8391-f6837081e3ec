CREATE OR REPLACE VIEW syphonx.audit_request_queue
AS
SELECT
  a.audit_id,
  a.audit_date,
  b.page_id,
  b.account_key,
  b.country_code,
  b.sku,
  b.seller_name,
  b.seller_id,
  a.selectors,
  a.priority,
  a.ttl,
  b.capture_url,
  a.requested_at,
  a.requested_by,
  c.audited_at,
  c.audited_by
FROM syphonx.audit_request AS a
JOIN syphonx.brand_monitor_product_page_daily AS b ON b.date=a.audit_date AND b.page_id=a.page_id
LEFT JOIN syphonx.audit_response_log AS c ON c.audit_id=a.audit_id AND c.page_id=a.page_id
WHERE a.audit_date >= DATE_SUB(CURRENT_DATE(), INTERVAL a.ttl DAY)
QUALIFY ROW_NUMBER() OVER (PARTITION BY audit_id, page_id ORDER BY audited_at DESC, requested_at DESC) = 1
ORDER BY audited_at IS NOT NULL, priority, requested_at DESC, country_code, seller_name, capture_url;
