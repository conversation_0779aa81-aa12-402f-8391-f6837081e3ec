CREATE OR REPLACE TABLE syphonx.autogen_requests
(
  autogen_id STRING,
  country_code STRING,
  domain_name STRING,
  url STRING,
  template_path STRING,
  selector_profile STRING,
  autogen_profile STRING,
  overwrite BOOLEAN,
  priority INT64,
  requested_at TIMESTAMP,
  requested_by STRING
)
PARTITION BY DATE(requested_at)
CLUSTER BY country_code, domain_name;


CREATE OR REPLACE VIEW syphonx.autogen_view
AS
SELECT *
FROM syphonx.autogen_requests
LEFT JOIN syphonx.autogen_status USING (autogen_id)
ORDER BY COALESCE(updated_at, requested_at) DESC;


CREATE OR REPLACE VIEW syphonx.autogen_status
AS
WITH latest AS (
  SELECT
    autogen_id,
    timestamp AS updated_at,
    status,
    LAX_STRING(data.report_url) AS report_url,
    JSON_EXTRACT_ARRAY(data, '$.analyze') AS analyze,
    JSON_EXTRACT_ARRAY(data, '$.generated_selectors') AS generated_selectors
  FROM syphonx.autogen_log
  QUALIFY ROW_NUMBER() OVER (PARTITION BY autogen_id ORDER BY timestamp DESC) = 1  
)
SELECT * EXCEPT(analyze, generated_selectors),
  (
    SELECT AS STRUCT
      ROUND((SUM(LAX_FLOAT64(a.cost))), 5) AS cost,
      (SUM(LAX_INT64(a.elapsed))) AS elapsed,
      ARRAY_TO_STRING(ARRAY_AGG(DISTINCT LAX_STRING(a.model)), ', ') AS models,

    FROM UNNEST(analyze) AS a
  ).*,
  (
    SELECT AS STRUCT
      COUNTIF(LAX_BOOL(g.ok) IS TRUE) AS generated_selector_count,
      COUNTIF(LAX_BOOL(g.ok) IS FALSE) AS ungenerated_selector_count,
      COUNT(*) AS target_selector_count
    FROM UNNEST(generated_selectors) AS g
  ).*,
  ARRAY_TO_STRING(ARRAY(SELECT LAX_STRING(g.selector_name) FROM UNNEST(generated_selectors) AS g WHERE LAX_BOOL(g.ok) IS TRUE), ', ') AS generated_selectors,
  ARRAY_TO_STRING(ARRAY(SELECT LAX_STRING(g.selector_name) FROM UNNEST(generated_selectors) AS g WHERE LAX_BOOL(g.ok) IS FALSE), ', ') AS ungenerated_selectors
FROM latest;
