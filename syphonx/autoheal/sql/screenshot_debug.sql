WITH screenshot_analyze AS (
  SELECT
    capture_date,
    ARRAY_TO_STRING(ARRAY(SELECT label FROM UNNEST(labels) AS label WHERE label<>'product-page'), ', ') AS label,
    SAFE_CAST(probability AS FLOAT64) AS confidence,
    CONCAT('http://storage.googleapis.com/ps-colossus/', capture_id, '/1.png') AS screenshot_url
  FROM brand_monitor_data_quality.capture_classifications_ex
  WHERE DATE(capture_date)=CURRENT_DATE()
  AND captured_as='product_page'
  AND 'nominal' NOT IN UNNEST(labels)
  ORDER BY capture_date DESC
)
SELECT ARRAY_AGG(screenshot_url)
FROM screenshot_analyze
WHERE label='challenge' AND confidence>=0.9;

SELECT label, COUNT(*)
FROM screenshot_analyze
GROUP BY 1
ORDER BY 2 DESC;
--> lightbox, broken, multiple-products, homepage, challenge, other, blank
