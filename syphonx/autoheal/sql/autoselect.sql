CREATE OR REPLACE TABLE syphonx.autoselect_log
(
  domain_name STRING NOT NULL,
  selector_name STRING NOT NULL,
  selector STRING NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  disabled BOOLEAN
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name;

CREATE OR REPLACE VIEW syphonx.autoselect
AS
SELECT * EXCEPT(disabled) FROM (
  SELECT *
  FROM syphonx.autoselect_log
  QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name, selector_name, selector ORDER BY timestamp DESC) = 1
)
WHERE disabled IS NOT TRUE
ORDER BY 1, 2, 3;

INSERT INTO syphonx.autoselect_log (domain_name, selector_name, selector, timestamp) VALUES
("", "autoclose", "", CURRENT_TIMESTAMP());

INSERT INTO syphonx.autoselect_log (domain_name, selector_name, selector, timestamp) VALUES
("gnkitchenandbath.com", "autoclose", ".Vtl-Popup__CloseButton", CURRENT_TIMESTAMP());

