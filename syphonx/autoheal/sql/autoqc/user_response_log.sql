CREATE OR REPLACE TABLE autoqc.user_response_log
(
  timestamp TIMESTAMP,
  key STRING,
  data JSON
)
PARTITION BY DATE(timestamp)
CLUSTER BY key;

/*
GCP CLOUD RUN FUNCTION
https://console.cloud.google.com/functions/details/us-central1/autoqc-user-response


const functions = require("@google-cloud/functions-framework");
const { BigQuery } = require("@google-cloud/bigquery");

const bigquery = new BigQuery();

exports.insert = async (req, res) => {
    try {
        const timestamp = new Date();
        const { key, ...data } = req.query;
        if (key) {
            await bigquery
                .dataset("autoqc")
                .table("user_response_log")
                .insert({ key, timestamp, data: JSON.stringify(data) });
            res.status(200).send("OK");
        }
        else {
            res.status(400).send("INVALID");
        }
    } catch(err) {
        console.error(err);
        res.status(500).send("ERROR");
    }
};

{
    "name": "generic-insert",
    "version": "0.0.1",
    "dependencies": {
        "@google-cloud/functions-framework": "^3.0.0",
        "@google-cloud/bigquery": "^7.7.0"
    }
}
*/