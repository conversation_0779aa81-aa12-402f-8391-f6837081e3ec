CREATE OR <PERSON><PERSON>LACE TABLE FUNCTION autoqc.create_brand_monitor_product_page_data(account_keys ARRAY<STRING>, seller_ids ARRAY<INT64>, interval_days INT64)
AS
SELECT
  CONCAT(ARRAY_TO_STRING([a.account_key, a.country_code, a.sku, NET.REG_DOMAIN(a.last_capture.capture_url)], '__')) AS page_id,
  a.account_key,
  a.country_code,
  a.sku,
  a.seller_name,
  a.seller_id,
  a.product_name,
  a.last_capture.metascore,
  a.msrp,
  JSON_EXTRACT_SCALAR(a.last_capture.price_data, '$.was_price') AS was_price,
  JSON_EXTRACT_SCALAR(a.last_capture.price_data, '$.price') AS price,
  JSON_EXTRACT_SCALAR(a.last_capture.availability_data, '$.in_stock') AS in_stock,
  a.last_capture.discontinued,
  JSON_EXTRACT_SCALAR(a.last_capture.review_data, '$.review_score') AS review_score,
  JSON_EXTRACT_SCALAR(a.last_capture.review_data, '$.review_count') AS review_count,
  JSON_EXTRACT_SCALAR(a.last_capture.image_data, '$.image_count') AS image_count,
  JSON_EXTRACT_SCALAR(a.last_capture.content_data, '$.enhanced_content') AS enhanced_content,
  JSON_EXTRACT_SCALAR(a.last_capture.content_data, '$.title_compare.target_text') AS name,
  JSON_EXTRACT_SCALAR(a.last_capture.content_data, '$.short_description_compare.target_text') AS description,
  JSON_EXTRACT(a.last_capture.content_data, '$.feature_bullets_compare.target_text') AS feature_bullets,
  a.last_capture.capture_id,
  a.last_capture.capture_date,
  a.last_capture.capture_url,
  STRUCT(
    b.capture_status,
    b.last_capture.screenshot_url,
    --workaround for 24h propegation delay to copy screenshots from temporary conflux storage to long-term brand-monitor storage
    CONCAT(
      'https://azed-05.s3.us-east-1.amazonaws.com/scout/micro/',
      EXTRACT(YEAR FROM CURRENT_TIMESTAMP),
      '/',
      EXTRACT(DAYOFYEAR FROM CURRENT_TIMESTAMP),
      '/',
      b.last_capture.capture_id,
      '.png'
    ) AS alt_screenshot_url,
    ARRAY(SELECT AS STRUCT selector_name, selector_json_value, regression, reference.selector_json_value AS ref_json_value FROM UNNEST(b.selectors) WHERE selector_name IS NOT NULL) AS selectors
  ) AS conflux_data
FROM brand_monitor.product_page AS a
LEFT JOIN conflux.product_page_daily AS b
  ON SPLIT(b.page_id, '::')[OFFSET(0)]=CONCAT(ARRAY_TO_STRING([a.account_key, a.country_code, a.sku, NET.REG_DOMAIN(a.last_capture.capture_url)], '__'))
  AND b.app_name='brand_monitor'
  AND b.account_key IN UNNEST(account_keys)
WHERE
  (NOT a.last_capture.discontinued OR a.last_capture.discontinued IS NULL)
  AND a.timestamp
    BETWEEN
      TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, 'America/Los_Angeles'), INTERVAL interval_days DAY)
      AND TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY, 'America/Los_Angeles')
  AND a.account_key IN UNNEST(account_keys)
  AND IF(ARRAY_LENGTH(IFNULL(seller_ids,[]))>0, a.seller_id IN UNNEST(seller_ids), TRUE)
QUALIFY ROW_NUMBER() OVER (PARTITION BY page_id ORDER BY capture_date DESC) = 1;