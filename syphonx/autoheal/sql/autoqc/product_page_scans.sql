CREATE OR R<PERSON>LACE TABLE autoqc.product_page_scans
(
  scan_id STRING,
  scan_date TIMESTAMP,
  account_key STRING,
  country_code STRING,
  sku STRING,
  seller_name STRING,
  seller_id INT64,
  page_id STRING,
  scan_url STRING,
  domain_name STRING,
  model STRING,
  usage INT64,
  cost FLOAT64,
  duration INT64,
  screenshot_url STRING,
  summary STRING,
  status STRING,
  scan_result ARRAY<STRUCT<
    name STRING,
    value JSON,
    labels STRING,
    explain STRING
  >>,
  conflux_data STRUCT<
    capture_status STRING,
    screenshot_url STRING,
    selectors ARRAY<STRUCT<
      selector_name STRING,
      selector_json_value JSON,
      ref_json_value JSON,
      regression BOOLEAN
    >>
  >,
  product_data JSON
)
PARTITION BY DATE(scan_date)
CLUSTER BY domain_name;
