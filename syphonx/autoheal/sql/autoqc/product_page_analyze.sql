CREATE OR REPLACE VIEW autoqc.product_page_analyze
AS
SELECT
  account_key,
  country_code,
  sku,
  seller_name,
  seller_id,

  page_id,
  scan_id,
  scan_date,
  LAX_STRING(product_data.capture_id) AS capture_id,
  SAFE_CAST(LAX_STRING(product_data.capture_date) AS TIMESTAMP) AS capture_date,
  conflux_data.capture_status,
  scan_url AS capture_url,
  screenshot_url,

  status,
  summary,

  flagged_fields,
  flagged_by,

  usage,
  cost,
  duration,

  LAX_STRING(product_data.price) AS price,
  (SELECT LAX_STRING(value) FROM UNNEST(scan_result) WHERE name='price' LIMIT 1) AS price_ref,
  (SELECT regression FROM UNNEST(conflux_data.selectors) WHERE selector_name='price' LIMIT 1) AS price_regression,
  (SELECT LAX_STRING(selector_json_value) FROM UNNEST(conflux_data.selectors) WHERE selector_name='price' LIMIT 1) AS price_crawler_dq,
  (SELECT LAX_STRING(ref_json_value) FROM UNNEST(conflux_data.selectors) WHERE selector_name='price' LIMIT 1) AS price_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(scan_result) WHERE name='price' LIMIT 1) AS price_explain,

  LAX_BOOL(product_data.in_stock) AS in_stock,
  (SELECT LAX_BOOL(value) FROM UNNEST(scan_result) WHERE name='in_stock' LIMIT 1) AS in_stock_ref,
  (SELECT regression FROM UNNEST(conflux_data.selectors) WHERE selector_name='in_stock' LIMIT 1) AS in_stock_regression,
  (SELECT LAX_BOOL(selector_json_value) FROM UNNEST(conflux_data.selectors) WHERE selector_name='in_stock' LIMIT 1) AS in_stock_crawler_dq,
  (SELECT LAX_BOOL(ref_json_value) FROM UNNEST(conflux_data.selectors) WHERE selector_name='in_stock' LIMIT 1) AS in_stock_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(scan_result) WHERE name='in_stock' LIMIT 1) AS in_stock_explain,

  LAX_INT64(product_data.image_count) AS image_count,
  (SELECT LAX_INT64(value) FROM UNNEST(scan_result) WHERE name='image_count' LIMIT 1) AS image_count_ref,
  (SELECT regression FROM UNNEST(conflux_data.selectors) WHERE selector_name='images' LIMIT 1) AS image_count_regression,
  (SELECT ARRAY_LENGTH(JSON_QUERY_ARRAY(selector_json_value)) FROM UNNEST(conflux_data.selectors) WHERE selector_name='images' LIMIT 1) AS image_count_crawler_dq,
  (SELECT ARRAY_LENGTH(JSON_QUERY_ARRAY(ref_json_value)) FROM UNNEST(conflux_data.selectors) WHERE selector_name='images' LIMIT 1) AS image_count_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(scan_result) WHERE name='image_count' LIMIT 1) AS image_count_explain,

  LAX_FLOAT64(product_data.review_score) AS review_score,
  (SELECT LAX_FLOAT64(value) FROM UNNEST(scan_result) WHERE name='review_score' LIMIT 1) AS review_score_ref,
  (SELECT regression FROM UNNEST(conflux_data.selectors) WHERE selector_name='review_score' LIMIT 1) AS review_score_regression,
  (SELECT LAX_FLOAT64(selector_json_value) FROM UNNEST(conflux_data.selectors) WHERE selector_name='review_score' LIMIT 1) AS review_score_crawler_dq,
  (SELECT LAX_FLOAT64(ref_json_value) FROM UNNEST(conflux_data.selectors) WHERE selector_name='review_score' LIMIT 1) AS review_score_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(scan_result) WHERE name='review_score' LIMIT 1) AS review_score_explain,

  LAX_INT64(product_data.review_count) AS review_count,
  (SELECT LAX_INT64(value) FROM UNNEST(scan_result) WHERE name='review_count' LIMIT 1) AS review_count_ref,
  (SELECT regression FROM UNNEST(conflux_data.selectors) WHERE selector_name='review_count' LIMIT 1) AS review_count_regression,
  (SELECT LAX_INT64(selector_json_value) FROM UNNEST(conflux_data.selectors) WHERE selector_name='review_count' LIMIT 1) AS review_count_crawler_dq,
  (SELECT LAX_INT64(ref_json_value) FROM UNNEST(conflux_data.selectors) WHERE selector_name='review_count' LIMIT 1) AS review_count_crawler_dq_ref,
  (SELECT IF(labels IS NOT NULL, CONCAT(explain, ' [', labels, ']'), explain) FROM UNNEST(scan_result) WHERE name='review_count' LIMIT 1) AS review_count_explain,

  LAX_STRING(product_data.capture_id) AS production_capture_id, --DEPRECATED
  SAFE_CAST(LAX_STRING(product_data.capture_date) AS TIMESTAMP) AS production_capture_date, --DEPRECATED
  scan_id AS ai_capture_id, --DEPRECATED
  scan_date AS ai_capture_date --DEPRECATED

FROM autoqc.product_page_scans
LEFT JOIN autoqc.user_flagged_fields USING(scan_id)
WHERE scan_date >= '2024-05-16 17:00:00 UTC'
QUALIFY ROW_NUMBER() OVER (PARTITION BY page_id ORDER BY scan_date DESC) = 1;