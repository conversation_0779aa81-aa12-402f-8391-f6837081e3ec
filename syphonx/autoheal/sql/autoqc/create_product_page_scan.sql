CREATE OR <PERSON><PERSON>LACE TABLE FUNCTION autoqc.create_product_page_scan(account_keys ARRAY<STRING>, seller_ids ARRAY<INT64>, limit_by_seller INT64, recent_days INT64)
AS
WITH recent_captures AS (
  SELECT DISTINCT page_id, scan_date AS last_scan_date
  FROM autoqc.product_page_scans
  WHERE DATE(scan_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL recent_days DAY)
)
SELECT *
FROM autoqc.create_brand_monitor_product_page_data(account_keys, seller_ids, 1)
LEFT JOIN recent_captures USING (page_id)
QUALIFY ROW_NUMBER() OVER (
  PARTITION BY seller_name
  ORDER BY
    last_scan_date IS NULL DESC, -- deprioritize pages not recently analyzed
    conflux_data.screenshot_url IS NOT NULL DESC, -- prioritize pages with screenshots
    price IS NOT NULL DESC, -- prioritize pages without price
    image_count IS NOT NULL DESC, -- prioritize pages with no images
    SAFE_CAST(image_count AS INT64)<2 DESC, -- prioritize pages with less than 2 images
    ARRAY_LENGTH(conflux_data.selectors)>0 DESC -- prioritize pages with conflux crawler results
) <= limit_by_seller;