CREATE OR R<PERSON>LACE TABLE temp.autoscan
(
  domain_name STRING,
  page_url STRING,
  page_class STRING,
  explain STRING,
  report_url STRING,
  screenshot_url STRING,
  elapsed INT64,
  model STRING,
  tokens INT64,
  input_tokens INT64, --new
  output_tokens INT64, --new
  cost FLOAT64,
  url_hash INT64,
  ok BOOLEAN,
  message STRING,
  analyze ARRAY<STRUCT<
    name STRING NOT NULL,
    message STRING,
    model STRING,
    tokens INT64,
    input_tokens INT64, --new
    output_tokens INT64, --new
    cost FLOAT64,
    elapsed INT64
  >>,
  data JSON,
  timestamp TIMESTAMP
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name;

/*
CREATE OR REPLACE TABLE temp.autoscan
(
  domain_name STRING,
  page_url STRING,
  page_class STRING,
  explain STRING,
  report_url STRING,
  screenshot_url STRING,
  elapsed INT64,
  model STRING,
  tokens INT64,
  cost FLOAT64,
  url_hash INT64,
  ok BOOLEAN,
  message STRING,
  analyze ARRAY<STRUCT<
    name STRING NOT NULL,
    message STRING,
    model STRING,
    tokens INT64,
    cost FLOAT64,
    elapsed INT64
  >>,
  data JSON,
  timestamp TIMESTAMP
)
PARTITION BY DATE(timestamp)
CLUSTER BY domain_name
AS
SELECT
  domain_name,
  page_url,
  page_class,
  explain,
  report_url,
  screenshot_url,
  elapsed,
  model,
  tokens,
  cost,
  url_hash,
  SAFE_CAST(JSON_EXTRACT_SCALAR(data, '$.ok') AS BOOLEAN) AS ok,
  SAFE_CAST(JSON_EXTRACT_SCALAR(data, '$.message') AS STRING) AS message,
  ARRAY(
    SELECT AS STRUCT 
      SAFE_CAST(JSON_EXTRACT_SCALAR(entry, '$.name') AS STRING) AS name,
      SAFE_CAST(JSON_EXTRACT_SCALAR(entry, '$.message') AS STRING) AS message,
      SAFE_CAST(JSON_EXTRACT_SCALAR(entry, '$.model') AS STRING) AS model,
      SAFE_CAST(JSON_EXTRACT_SCALAR(entry, '$.tokens') AS INT64) AS tokens,
      SAFE_CAST(JSON_EXTRACT_SCALAR(entry, '$.cost') AS FLOAT64) AS cost,
      SAFE_CAST(JSON_EXTRACT_SCALAR(entry, '$.elapsed') AS INT64) AS elapsed
    FROM UNNEST(JSON_EXTRACT_ARRAY(data, '$.analyze')) AS entry
  ) AS analyze,
  data,
  timestamp
FROM temp.autoscan;
*/
