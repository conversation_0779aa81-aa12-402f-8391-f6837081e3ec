CREATE OR REPLACE FUNCTION syphonx.selector_diff(
  a ARRAY<STRUCT<selector_name STRING, selector_query_json JSON>>,
  b ARRAY<STRUCT<selector_name STRING, selector_query_json JSON>>
)
RETURNS ARRAY<STRUCT<
  selector_name STRING,
  changed BO<PERSON>,
  before JSON,
  after JSON,
  added ARRAY<JSON>,
  removed ARRAY<JSON>,
  unchanged ARRAY<JSON>
>>
DETERMINISTIC
LANGUAGE js
AS
r"""
if (!is_valid_arg(a) || !is_valid_arg(b))
  return null;

const keys = Array.from(
  new Set([
    ...(a || []).map(obj => obj.selector_name),
    ...(b || []).map(obj => obj.selector_name)
  ]))
  .sort((a, b) => a.localeCompare(b));

const result = [];
for (const key of keys) {
  const ax = find(a, key);
  const bx = find(b, key);
  result.push({
    selector_name: key,
    changed: JSON.stringify(ax) !== JSON.stringify(bx),
    before: ax,
    after: bx,
    ...diff(ax, bx)
  });
}
return result;

function find(a, key) {
  return a?.find(obj => obj.selector_name === key)?.selector_query_json;
}

function to_string_array(a) {
  return a?.map(obj => JSON.stringify(obj)) || [];
}

function diff(a, b) {
  const ax = to_string_array(a);
  const bx = to_string_array(b);
  const keys = Array.from(new Set([...ax, ...bx]));
  return {
    added: keys.filter(key => !ax.includes(key) && bx.includes(key)).map(json => JSON.parse(json)),
    removed: keys.filter(key => ax.includes(key) && !bx.includes(key)).map(json => JSON.parse(json)),
    unchanged: keys.filter(key => ax.includes(key) && bx.includes(key)).map(json => JSON.parse(json))
  };
}

function is_valid_arg(a) {
  return a?.length > 0 ? a.every(obj => obj && typeof obj === 'object' && obj.selector_query_json instanceof Array && obj.selector_query_json.every(is_valid_query)) : true;
}

function is_valid_query(query) {
  return query instanceof Array && query.length > 0 && typeof query[0] === 'string' && query.slice(1).every(op => op instanceof Array);
}
""";

--SELECT syphonx.selector_diff(NULL, NULL) --> NULL
--SELECT syphonx.selector_diff([],[]) --> NULL
--SELECT syphonx.selector_diff([STRUCT('title' AS selector_name, JSON '[["h1"]]' AS selector_query_json)], [STRUCT('title' AS selector_name, JSON '[["h1"],["h2"]]' AS selector_query_json)]) --> before: [["h1"]], after: [["h1"],["h2"]], added: ["h2"], removed: null, unchanged: ["h1"]
