# pmanalyze

node pmanalyze run --app=prowl --account=belkin --verbose --domain=bhphotovideo.com,belkin.com,cdw.com,compsource.com,arsenalpc.com,newegg.com,beachaudio.com,bestbuy.com,abt.com,cablestogo.com,centralcomputer.com,camcor.com,crutchfield.com

## Run Mode
- `node pmanalyze run --app=prowl --account=belkin --country=us`
- `node pmanalyze run --app=prowl --account=belkin --country=us --preview`
- `node pmanalyze run --app=prowl --brand="House of Rohl" --limit=10`

## Batch Mode
- `node pmanalyze batch create --app=prowl --account=moen` to create a batch
- `node pmanalyze batch complete` to complete pending batches, no options necessary

## Other
- `node pmanalyze prompt --app=prowl --random` to generate and run a single prompt for a random product
- `node pmanalyze prompt --app=prowl --account=acme --sku=123 --model=gpt-4o-mini` to generate and run a single prompt and run model
- `node pmanalyze prompt --app=prowl --account=acme --sku=123 --preview` to view generate a single prompt without running
- `node pmanalyze view models`


- Confirmed Candidates
- Unreviewed Candidates


SELECT *
FROM omega.product_match_analyze_pending
WHERE app_name='prowl'
AND account_key IN ('shure')
AND (last_analyzed_at IS NULL OR last_analyzed_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY))
AND (SELECT COUNT(*) FROM UNNEST(pages) WHERE active) > 0
ORDER BY app_name, account_key, country_code, brand_name, sku
