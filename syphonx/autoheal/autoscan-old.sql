CREATE OR R<PERSON>LACE VIEW temp.autoscan_input
AS
WITH last_scanned AS (
  SELECT DISTINCT page_url AS url, timestamp AS last_scanned_at
  FROM temp.autoscan
  QUALIFY ROW_NUMBER() OVER (PARTITION BY page_url ORDER BY timestamp DESC) = 1
)
SELECT url, SAFE_CAST(url_hash AS STRING) AS url_hash, domain_name, last_scanned_at
FROM temp.prowl_moen_deactivated
LEFT JOIN last_scanned USING (url)
ORDER BY last_scanned_at IS NOT NULL, last_scanned_at; -- order by never scanned first, then by latest capture date, then by oldest scanned date

CREATE OR REPLACE VIEW temp.prowl_moen_deactivated
AS
SELECT
  url, FARM_FINGERPRINT(TRIM(url)) AS url_hash, NET.REG_DOMAIN(url) AS domain_name
  --client_name, sku, domain, reason, brand_name, url, url_id,  is_scheduled, is_crawled
FROM conflux.client_product_page_captures_scheduler
WHERE client_name = 'Moen' 
AND sku IN ("900-006","900-001","9000","900-002","5923SRS","S3104","87233BRB","7864BL","2570","7565SRS","87886SRS","TS6925BL","7594SRS","7565","7864EWBL","5923EWBLS","S5530SRS","TS42108BG","S112WR","EX75C","S6320EPBN","TS3302TB","S53004BG","S112","T2232EPORB","TS42108BN","S3946SRS","6192BG","T4943BN","TS6984BG","6402ORB","EX50C","TV6805BN","T4520BN","S5510ORB","TS42114BL","S122BN","S3945ORB","S72003SRS","6221","S5530","TS2711BN","UTS2411BN","FP62380PF","T2193EPBL","7840","T3691BN","T908","S11705EP","7565BLS","U130XS","UT3611","6172BN","T2262EPBL","TS2912EPBG","GT33C","S6981BG","S1004EP","S6360EPBN","T933BN","UTS2411BG","7260EWSRS","6803BN","T2182ORB","7882","UT2473NHBL","UT2921BN","T2253EP","U361X","S176EPBL","7545ORB","UT3692BN","T4520","TV6173BN","7245C","S73104EWSRS","TS6984NL","6192","S3946BG","S5520ORB","7825","S931BG","S122","T2283EPBL","S55005BL","UT3322","7622EVBL","9126EWSRS","TS984","7255SRS","T2282EPBL","T2693NHBN","7255ORB","6802BN","TS42112BG","TS3661NHORB","S1002","WMV130CXS","UTS33102BG","T2693EPORB")
-- AND TRIM(reason) NOT IN ('url is archived,','url is not monitored,','url is not monitored, url is archived,')
--AND domain IN ('homedepot.com','lowes.com','menards.com','build.com','ferguson.com','wayfairsupply.com','wayfair.com','grainger.com','zoro.com','amazon.com','walmart.com')
AND domain IN ('homedepot.com','walmart.com','zoro.com')
AND STARTS_WITH(url, 'https://')
AND is_scheduled IS FALSE
QUALIFY ROW_NUMBER() OVER (PARTITION BY url ORDER BY capture_date DESC) = 1;


SELECT *
FROM temp.autoscan
WHERE timestamp >= '2024-11-22'
AND page_class='product-page'
AND NOT LAX_BOOL(data.discontinued)
ORDER BY timestamp DESC;
