COPY INTO 'gcs://ps-tmp/moen.csv.gz'
FROM (
SELECT
    LOWER(clients.clientName) AS account_key,
    pwl_brand.name AS brand_name,
    pwl_product.name AS product_name,
    'US' AS country_code,
    pwl_product.sku,
    pwl_product.upc,
    pwl_product.target_price,
    pwl_brand.currency,
    pwl_voyager_candidate_url.candidate_url,
    pwl_voyager_candidate_url.confidence,
    pwl_voyager_candidate_url.created_at
FROM PROWL.PROWL_MYSQL.PWL_VOYAGER_CANDIDATE_URL AS pwl_voyager_candidate_url
INNER JOIN PROWL.PROWL_MYSQL.PWL_VOYAGER_PRODUCT AS pwl_voyager_product ON (pwl_voyager_candidate_url."VOYAGER_PRODUCT_ID") = (pwl_voyager_product."VOYAGER_PRODUCT_ID")
LEFT JOIN PROWL.PROWL_MYSQL.PWL_PRODUCT AS pwl_product ON (pwl_voyager_product."PRODUCT_ID") = pwl_product.product_id
INNER JOIN PROWL.PROWL_MYSQL.PWL_BRAND AS pwl_brand ON pwl_product.brand_id = pwl_brand.brand_id
INNER JOIN PROWL.PROWL_MYSQL.BRANDSBYCLIENT AS brandsbyclient ON brandsbyclient.brandId = pwl_brand.brand_id
INNER JOIN PROWL.PROWL_MYSQL.CLIENTS AS clients ON brandsbyclient.clientId = clients.clientId
WHERE clients.clientName IN ('Moen')
AND pwl_voyager_candidate_url.confidence >= 0.4
AND ((( pwl_voyager_candidate_url."CREATED_AT"  ) >= ((CONVERT_TIMEZONE('Universal', 'UTC', CAST(DATE_TRUNC('year', DATE_TRUNC('day', CONVERT_TIMEZONE('UTC', 'Universal', CAST(CURRENT_TIMESTAMP() AS TIMESTAMP_NTZ)))) AS TIMESTAMP_NTZ)))) AND ( pwl_voyager_candidate_url."CREATED_AT"  ) < ((CONVERT_TIMEZONE('Universal', 'UTC', CAST(DATEADD('year', 1, DATE_TRUNC('year', DATE_TRUNC('day', CONVERT_TIMEZONE('UTC', 'Universal', CAST(CURRENT_TIMESTAMP() AS TIMESTAMP_NTZ))))) AS TIMESTAMP_NTZ))))))
AND (clients.clientStatusId ) = 1 AND ((clients.clientTypeId ) <> 4 OR (clients.clientTypeId ) IS NULL) AND ((pwl_voyager_candidate_url."CONFIDENCE") ) IS NOT NULL
QUALIFY ROW_NUMBER() OVER (PARTITION BY account_key, country_code, sku, candidate_url ORDER BY pwl_voyager_candidate_url.created_at DESC) = 1
)
FILE_FORMAT = (
  TYPE = 'CSV',
  COMPRESSION = 'GZIP',
  FIELD_OPTIONALLY_ENCLOSED_BY = '"'
)
OVERWRITE = TRUE
SINGLE = TRUE;

LOAD DATA OVERWRITE temp.moen_voyager_candidates (
  account_key STRING,
  brand_name STRING,
  product_name STRING,
  country_code STRING,
  sku STRING,
  upc STRING,
  target_price FLOAT64,
  currency STRING,
  candidate_url STRING,
  confidence FLOAT64,
  created_at TIMESTAMP
)
FROM FILES (
  format = 'CSV',
  uris = ['gs://ps-tmp/moen.csv.gz'],
  compression = 'GZIP'
);

CREATE OR REPLACE VIEW temp.moen_voyager_candidates_view
AS
SELECT
  'voyager' AS app_name,
  account_key,
  country_code,
  sku,
  ANY_VALUE(brand_name) AS brand_name,
  ANY_VALUE(product_name) AS product_name,
  ANY_VALUE(upc) AS upc,
  ANY_VALUE(target_price) AS target_price,
  ANY_VALUE(currency) AS currency,
  ARRAY_AGG(
    STRUCT(
      NET.REG_DOMAIN(candidate_url) AS domain_name,
      candidate_url AS url,
      confidence
    )
    ORDER BY NET.REG_DOMAIN(candidate_url), confidence DESC, LENGTH(candidate_url), candidate_url
  ) AS product_matches,
  ANY_VALUE(created_at) AS created_at,
  FARM_FINGERPRINT(ARRAY_TO_STRING(['prowl', account_key, country_code, sku], '__')) AS snapshot_id
FROM temp.moen_voyager_candidates
GROUP BY 1, 2, 3, 4
ORDER BY created_at DESC;