<PERSON><PERSON><PERSON><PERSON> (BE) 3855317 [2/6]

opening page... url=https://www.krefel.be/nl/p/21008857-haardroger-supersonic-cadeau-editie-2023, target=price
snoozing for 4 seconds
classifying live page...
page classification: (product-page)
checking selectors on live page...
✓ verified selectors for "price" not returning a value
rendering screenshot...
locating content... (price)
  name: The product name 'Dyson Haardroger Supersonic Cadeau-editie 2023' is located at the top of the page, in a large, bold font, and immediately followed by the label [21].
  description: The product description is under the heading 'Beschrijving' and consists of a series of sentences describing the product.
  brand_name: The brand name '<PERSON><PERSON><PERSON>' appears within the product name 'Dyson Haardroger Supersonic Cadeau-editie 2023'. Thus, the product-name is also selected as the brand-name.
> price: The price is shown prominently to the right of the main product image and is the same as the previous value provided.
  images: The main product image and additional thumbnails for the product can be found in the image gallery, with the first image labelled [25].
  videos: There does not appear to be a video or thumbnail with a play button icon within the image gallery.
  stock_status: The button label 'Op voorraad' which translates to 'In stock' indicates that the product is available for purchase.
  in_stock: The button 'In winkelmandje' which translates to 'Add to Cart' provides an indication that the product is in-stock and available for purchase.
  features: Product features are listed as individual items under the heading 'Specificaties', indicating power, ionic function, number of speeds, and maximum temperature.
  review_score: The user-generated review score for the product is 9,8 out of 10, which is reflected next to the star ratings.
  review_count: The number of user-generated reviews for the product is 5, indicated next to the text 'Beoordelingen'.
generating selector for "price"...
retry #1: [class*='Price-styled__StyledPriceWrapper-sc-'] [class*='Typography-styled__StyledTypography-sc-'][class*='current-price'] -> over-selected
retry #2: null -> empty result, making additional suggestions
unexpected error: Expected comma at character 155
report: http://storage.googleapis.com/ps-syphonx/autoheal/20240401104654587.html
analyze failed [error]
"Expected comma at character 155"
no valid selectors generated




HiFi International (BE) 23817917 [3/6]

opening page... url=https://www.hifi.lu/fr/p/21008857-seche-cheveux-supersonic-bleu-edition-cadeau-2023, target=price
snoozing for 4 seconds
classifying live page...
page classification: (product-page)
checking selectors on live page...
✓ verified selectors for "price" not returning a value
rendering screenshot...
locating content... (price)
  name: The product name is in a large font at the top of the page, adjacent to the main product image and price.
  description: The product description is under the 'Description [49]' heading and it consists of a short paragraph describing the product.
  brand_name: The brand name 'Dyson' is found within the product name and is also adjacent to the product name.
> price: The price is in a prominent location near the product name and image. It is the main selling price on the page.
  images: These are the images in the product gallery.
  videos: There are no images with a 'play button' indicating the presence of videos.
  stock_status: The product page has a label indicating that the product is 'En stock' which means 'in-stock'.
  in_stock: The 'Acheter maintenant' button, which means 'Buy Now', indicates that the product is available for purchase.
  features: Under the 'Specificaitons [65]' heading, a list of features is provided including 'Regulation Intelligente de la temperature' and others, without bullet points but appears as a list.
  review_score: There is no review score visible on the product page.
  review_count: The review count is indicated as '0 avis,' which means '0 reviews.'
generating selector for "price"...
retry #1: [class*='Price-styled__StyledPrice-sc-'] > [class*='Typography-styled__StyledTypography-sc-'][class*='current-price'] -> over-selected
retry #2: #product-add-to-cart [class*='Price-styled__StyledPriceWrapper-sc-'] > [class*='Price-styled__StyledPrice-sc-'] > [class*='Typography-styled__StyledTypography-sc-'][class*='current-price'] -> whole number price detected, double checking for formatted price
retry #3: null -> empty result, making additional suggestions
unexpected error: Expected comma at character 155
report: http://storage.googleapis.com/ps-syphonx/autoheal/20240401105005692.html
analyze failed [error]
"Expected comma at character 155"
no valid selectors generated





node --max-old-space-size=4096 analyze --target=description --seller=3868505 --sku=00087692011675 --commit
https://storage.googleapis.com/ps-syphonx/analyze/20240118-1508/boston-beer-00087692011675-at-Albertsons-US.html
Generated selector `.product-details-wrapper .body-text` is too broad


