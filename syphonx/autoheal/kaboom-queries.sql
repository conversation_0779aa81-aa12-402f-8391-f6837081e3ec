SELECT timestamp, account_key, country_code, sku, page_id, accept_count, reject_count, comments
FROM omega.product_match_analyze_view, UNNEST(audits)
WHERE timestamp>='2025-01-10 17:00:00 UTC'
AND audit_summary IS NOT NULL
ORDER BY 1 DESC;


SELECT timestamp, account_key, country_code, sku, audit_summary
FROM omega.product_match_analyze_view
WHERE timestamp>='2025-01-10 17:00:00 UTC'
ORDER BY 1 DESC;

SELECT
  DATETIME(timestamp, 'America/Los_Angeles') AS timestamp,
  ip_address,
  LAX_STRING(data.action) AS action,
  LAX_INT64(data.n) AS n,
  NULLIF(LAX_STRING(data.page_id),'') AS page_id,
  LAX_STRING(data.analyze_id) AS analyze_id,
FROM omega.user_log
WHERE LAX_STRING(data.analyze_id)='52De0h3kqTq1r2e'
ORDER BY 1;

SELECT ip_address, COUNT(*)
FROM omega.user_log
WHERE key='product-match-analyze'
GROUP BY 1
ORDER BY 2 DESC, 1;

SELECT *
FROM omega.user_log
WHERE key='product-match-analyze'
AND ip_address='************'
ORDER BY 1 DESC;

SELECT timestamp, account_key, country_code, sku, audit_summary
FROM omega.product_match_analyze_view
WHERE timestamp>='2025-01-10 17:00:00 UTC'
AND audit_summary IS NOT NULL
ORDER BY 1 DESC;


SELECT COUNTIF(audit_summary IS NOT NULL), COUNT(*)
FROM omega.product_match_analyze_view, UNNEST(audits)
WHERE timestamp>='2025-01-10 17:00:00 UTC';


SELECT
  DATETIME(timestamp, 'America/Los_Angeles') AS timestamp,
  --account_key,
  --country_code,
  sku,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE product_match_status='MISMATCH') AS mismatch_count,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE product_match_status='MISMATCH' AND accept_count>0) AS true_mismatch,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE product_match_status='MISMATCH' AND reject_count>0) AS false_mismatch,
  ROUND(SAFE_DIVIDE((SELECT COUNT(*) FROM UNNEST(product_matches) WHERE product_match_status='MISMATCH' AND reject_count>0), (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE product_match_status='MISMATCH')), 3) AS false_mismatch_rate,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE STARTS_WITH(product_match_status, 'QUESTIONABLE')) AS questionable_count,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE STARTS_WITH(product_match_status, 'QUESTIONABLE') AND accept_count>0) AS true_questionable,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE STARTS_WITH(product_match_status, 'QUESTIONABLE') AND reject_count>0) AS false_questionable,
  ROUND(SAFE_DIVIDE((SELECT COUNT(*) FROM UNNEST(product_matches) WHERE STARTS_WITH(product_match_status, 'QUESTIONABLE') AND accept_count>0), (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE STARTS_WITH(product_match_status, 'QUESTIONABLE'))), 3) AS questionable_mismatch_rate,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE product_match_status='OK') AS ok_count,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE product_match_status='OK' AND accept_count>0) AS true_ok,
  (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE product_match_status='OK' AND reject_count>0) AS false_ok,

  /*
  ARRAY(
    SELECT AS STRUCT
      product_match_status,
      accept_count,
      reject_count,
      comments,
      product_match_id,
      product_match_url
    FROM UNNEST(product_matches)
    WHERE accept_count IS NOT NULL
    AND product_match_status='MISMATCH'
  ) AS audit
  */
FROM omega.product_match_analyze_view
WHERE timestamp>='2025-01-10 17:00:00 UTC'
AND (SELECT COUNT(*) FROM UNNEST(product_matches) WHERE accept_count IS NOT NULL)>0
ORDER BY 1 DESC;

