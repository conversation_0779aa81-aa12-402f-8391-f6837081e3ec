import * as dotenv from "dotenv";
dotenv.config();

import chalk from "chalk";

import {
    bigquery,
    diffbot,
    parseDomain<PERSON>ame,
    Timer
} from "./lib/index.js";

const rows = await bigquery.query("SELECT page_id, capture_url FROM autoqc.product_page_diffbot_queue");
for (const row of rows) {
    console.log(chalk.gray(`[${rows.indexOf(row) + 1}/${rows.length}] analyzing ${row.capture_url}`));
    const timer = new Timer();
    const [response] = await diffbot.product(row.capture_url, { fields: ["breadcrumb", "content", /*"dom",*/ "meta"] });
    const data = {
        page_id: row.page_id,
        timestamp: new Date(),
        domain_name: parseDomainName(row.capture_url),
        url: row.capture_url,
        duration: timer.elapsed(),
        data: JSON.stringify(response)
    };
    bigquery.insert("autoqc.schneider_product_page_diffbot", data);
}

console.log(chalk.gray(`${rows.length} rows inserted`));
process.exit(0);
