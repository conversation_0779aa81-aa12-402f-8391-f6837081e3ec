import * as dotenv from "dotenv";
dotenv.config();

import * as playwright from "playwright";
//import * as fs from "fs";

import { sleep } from "./lib/index.js";

import {
    addClickEvent,
    addMouseMoveEvent,
    //addPanel
}
from "./functions/index.js";

const url = process.argv[2];
if (!url) {
    console.error("Please specify a url.");
    process.exit(1);
}

//const panelHtml = fs.readFileSync(new URL("./functions/panel.html", import.meta.url), "utf8");
//const panelCss = fs.readFileSync(new URL("./functions/panel.css", import.meta.url), "utf8");

const browser = await playwright.chromium.launch({ headless: false });
const context = await browser.newContext();

let audit_code = undefined;
let selector = undefined;

const page = await context.newPage();
await page.goto(url);

//const html = `<style>${panelCss}</style>${panelHtml}`;
//await page.evaluate(addPanel, { html });
await page.evaluate(addClickEvent);
await page.evaluate(addMouseMoveEvent);
await page.addStyleTag({ content: ".x-hover { outline: 15px solid rgba(255, 165, 0, 0.4) !important; }" });

await page.exposeFunction("hook", (obj: Record<string, unknown>) => {
    if (obj.selector) {
        console.log(`selector clicked: ${obj.selector}`);
        audit_code = "found";
        selector = obj.selector;
    }
    /*
    else if (obj.button) {
        console.log(`button clicked: ${obj.button}`);
        audit_code = obj.button;
    }
    */
});

/*
const selectors = ["features"];
for (const selector of selectors) {
    audit_code = undefined;
    await page.evaluate(html => window.__panel.querySelector("#prompt")!.innerHTML = html, `Where is <b>${selector}</b>?`);
    while (!audit_code)
        await sleep(500);
    console.log(audit_code, selector);
}
*/

while (!audit_code)
    await sleep(500);

await page.close();
process.exit(0);
