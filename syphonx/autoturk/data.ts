import { BigQuery } from "@google-cloud/bigquery";

const bigquery = new BigQuery();

export interface AuditRow {
    date: Date;
    account_key: string;
    country_code: string;
    sku: string;
    seller_name: string;
    seller_id: number;
    capture_id: string;
    capture_date: Date;
    capture_url: string;
    capture_engine: string;
    page_id: string;
    alert_count: number;
    regression_count: number;
    miss_count: number;
    selectors: Selector[];
}

export interface Selector {
    selector_name: string;
    selector_value: string;
    selector_alerts: string;
    hits: number;
    hit_rate: number;
    regression: boolean;
    miss: boolean;
    reference: CaptureReference;
}

export interface CaptureReference {
    selector_value: string;
    diff_days: number;
    capture_id: string;
    capture_date: Date;
}

export async function query(): Promise<AuditRow[]> {
    const [rows] = await bigquery.query(`SELECT * FROM syphonx.audit_queue`) as [AuditRow[]];
    return rows;
}

export interface PostData {
    audit_code: string;
    seller_id: number;
    selector_name: string;
    selector?: string;
    url: string;
    duration: number;
}

export async function post(data: PostData): Promise<void> {
    await bigquery.dataset("syphonx").table("audit_log").insert([{
        ...data,
        username: "test1",
        timestamp: new Date()
    }]);
}
