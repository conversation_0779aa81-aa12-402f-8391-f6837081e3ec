declare global {
    interface Window {
        __panel: ShadowRoot;
    }
}

export function addPanel({ html }: { html: string }): void {
    const panel = document.createElement("div");
    panel.id = "__panel";
    panel.style.position = "sticky";
    panel.style.bottom = "0";
    panel.style.left = "0";
    panel.style.background = "#ddffdd";
    panel.style.height = "50px";
    panel.style.zIndex = "10000";

    const shadow = panel.attachShadow({ mode: "closed" });
    shadow.innerHTML = html;
    document.body.appendChild(panel);
    window.__panel = shadow;
}
