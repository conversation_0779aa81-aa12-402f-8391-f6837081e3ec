export function addMouseMoveEvent(): void {
    document.body.addEventListener("mousemove", event => {
        document.querySelectorAll(".x-hover").forEach(element => {
            element.classList.remove("x-hover");
            if (element.classList.length === 0)
                element.removeAttribute("class");
        });

        if (event.target instanceof HTMLElement && !hasAncestorWithId(event.target, "__panel") && !window.shift)
            event.target.classList.add("x-hover");

        function hasAncestorWithId(element: HTMLElement | null, id: string): boolean {
            while (element) {
                if (element.id === id)
                    return true;
                element = element.parentElement;
            }
            return false;
        }
    }, true);
}
