SELECT *, ROW_NUMBER() OVER (ORDER BY any_count DESC, total_count DESC) AS n
FROM (
  SELECT
    country_code,
    seller_name,
    seller_id,
    COUNTIF(
      flagged.price[OFFSET(0)]=0 OR
      flagged.review_count[OFFSET(0)]=0 OR
      flagged.review_score[OFFSET(0)]=0 OR
      flagged.brand_name[OFFSET(0)]=0 OR
      flagged.name[OFFSET(0)]=0 OR
      flagged.description[OFFSET(0)]=0 OR
      flagged.in_stock[OFFSET(0)]=0 OR
      flagged.features[OFFSET(0)]=0 OR
      flagged.images[OFFSET(0)]=0 OR
      flagged.videos[OFFSET(0)]=0
    ) AS any_count,
    COUNT(*) AS total_count,
    COUNTIF(flagged.price[OFFSET(0)]=0) AS price,
    COUNTIF(flagged.review_count[OFFSET(0)]=0) AS review_count,
    COUNTIF(flagged.review_score[OFFSET(0)]=0) AS review_score,
    COUNTIF(flagged.brand_name[OFFSET(0)]=0) AS brand_name,
    COUNTIF(flagged.name[OFFSET(0)]=0) AS name,
    COUNTIF(flagged.description[OFFSET(0)]=0) AS description,
    COUNTIF(flagged.in_stock[OFFSET(0)]=0) AS in_stock,
    COUNTIF(flagged.features[OFFSET(0)]=0) AS features,
    COUNTIF(flagged.images[OFFSET(0)]=0) AS images,
    COUNTIF(flagged.videos[OFFSET(0)]=0) AS videos
  FROM brand_monitor_data_quality.product_page_history_view
  WHERE product_library='syphonx'
  GROUP BY 1, 2, 3
)
ORDER BY n;