<html>
    <head>
      <title>Nice trinket</title>
    </head>
    <body>
      <div typeof="schema:Product">
        <div property="schema:sku" content="trinket-12345"></div>
        <div property="schema:gtin14" content="12345678901234"></div>
        <div property="schema:name" content="Nice trinket"></div>
        <div rel="schema:image" resource="https://example.com/photos/16x9/trinket.jpg"></div>
        <div rel="schema:image" resource="https://example.com/photos/4x3/trinket.jpg"></div>
        <div rel="schema:image" resource="https://example.com/photos/1x1/trinket.jpg"></div>
        <div property="schema:description" content="Trinket with clean lines"></div>
        <div rel="schema:brand">
          <div typeof="schema:Brand">
            <div property="schema:name" content="MyBrand"></div>
          </div>
        </div>
        <div rel="schema:offers">
          <div typeof="schema:Offer">
            <div rel="schema:url" resource="https://example.com/trinket_offer"></div>
            <div property="schema:itemCondition" content="https://schema.org/NewCondition"></div>
            <div property="schema:availability" content="https://schema.org/InStock"></div>
            <div property="schema:price" content="39.99"></div>
            <div property="schema:priceCurrency" content="USD"></div>
            <div property="schema:priceValidUntil" datatype="xsd:date" content="2020-11-20"></div>
            <div rel="schema:shippingDetails">
              <div typeof="schema:OfferShippingDetails">
                <div rel="schema:shippingRate">
                  <div typeof="schema:MonetaryAmount">
                    <div property="schema:value" content="3.49"></div>
                    <div property="schema:currency" content="USD"></div>
                  </div>
                </div>
                <div rel="schema:shippingDestination">
                  <div typeof="schema:DefinedRegion">
                    <div property="schema:addressCountry" content="US"></div>
                  </div>
                </div>
                <div rel="schema:deliveryTime">
                  <div typeof="schema:ShippingDeliveryTime">
                    <div rel="schema:handlingTime">
                      <div typeof="schema:QuantitativeValue">
                        <div property="schema:minValue" content="0"></div>
                        <div property="schema:maxValue" content="1"></div>
                        <div property="schema:unitCode" content="DAY"></div>
                      </div>
                    </div>
                    <div rel="schema:transitTime">
                      <div typeof="schema:QuantitativeValue">
                        <div property="schema:minValue" content="1"></div>
                        <div property="schema:maxValue" content="5"></div>
                        <div property="schema:unitCode" content="DAY"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div rel="schema:review">
          <div typeof="schema:Review">
            <div rel="schema:reviewRating">
              <div typeof="schema:Rating">
                <div property="schema:ratingValue" content="4"></div>
                <div property="schema:bestRating" content="5"></div>
              </div>
            </div>
            <div rel="schema:author">
              <div typeof="schema:Person">
                <div property="schema:name" content="Fred Benson"></div>
              </div>
            </div>
          </div>
        </div>
        <div rel="schema:aggregateRating">
          <div typeof="schema:AggregateRating">
            <div property="schema:reviewCount" content="89"></div>
            <div property="schema:ratingValue" content="4.4"></div>
          </div>
        </div>
      </div>
    </body>
  </html>