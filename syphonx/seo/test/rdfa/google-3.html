<html>
    <head>
      <title>Cheese Knife Pro review</title>
    </head>
    <body>
      <div typeof="schema:Product">
        <div property="schema:name" content="Cheese Knife Pro review"></div>
          <div rel="schema:review">
            <div typeof="schema:Review">
              <div rel="schema:positiveNotes">
                <div typeof="schema:ItemList">
                  <div rel="schema:itemListElement">
                    <div typeof="schema:ListItem">
                      <div property="schema:position" content="1"></div>
                      <div property="schema:name" content="Consistent results"></div>
                    </div>
                    <div typeof="schema:ListItem">
                      <div property="schema:position" content="2"></div>
                      <div property="schema:name" content="Still sharp after many uses"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div rel="schema:negativeNotes">
                <div typeof="schema:ItemList">
                  <div rel="schema:itemListElement">
                    <div typeof="schema:ListItem">
                      <div property="schema:position" content="1"></div>
                      <div property="schema:name" content="No child protection"></div>
                    </div>
                    <div typeof="schema:ListItem">
                      <div property="schema:position" content="2"></div>
                      <div property="schema:name" content="Lacking advanced features"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div rel="schema:author">
                <div typeof="schema:Person">
                  <div property="schema:name" content="Pascal Van Cleeff"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </body>
  </html>