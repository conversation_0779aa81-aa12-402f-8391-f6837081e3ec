import * as fs from "fs";
import JSON5 from "json5";
import * as syphonx from "syphonx-lib";
import { findLastSelectGroup, Template } from "syphonx-lib";

const template = loadJson(new URL("../templates/product_page.json", import.meta.url).pathname) as Template;
const select_group = findLastSelectGroup(template.actions)!;

export type SelectResult = syphonx.ExtractState;

export function select(file: string, url?: string): SelectResult {
    const html = fs.readFileSync(file, "utf-8");    
    const result = syphonx.select(select_group, html, { url, unwrap: true, debug: true });
    return result;
}

export function loadJson(file: string): {} {
    const json = fs.readFileSync(file, "utf-8");
    const obj = JSON5.parse(json);
    return obj;
}

export function removeNulls<T extends object, K extends keyof T>(obj: T): Partial<T> {
    if (obj === null)
        return obj;
    const result: Partial<T> = {};
    const keys = Object.keys(obj) as K[];
    for (const key of keys)
        if (obj[key] !== null)
            result[key] = obj[key];
    return result;
}
