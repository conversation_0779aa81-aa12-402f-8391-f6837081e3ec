<html>
    <head>
      <title>Nice trinket</title>
      <script type="application/ld+json">
      {
        "@context": "https://schema.org/",
        "@type": "Product",
        "sku": "trinket-12345",
        "gtin14": "12345678901234",
        "image": [
          "https://example.com/photos/16x9/trinket.jpg",
          "https://example.com/photos/4x3/trinket.jpg",
          "https://example.com/photos/1x1/trinket.jpg"
        ],
        "name": "Nice trinket",
        "description": "Trinket with clean lines",
        "brand": {
          "@type": "Brand",
          "name": "MyBrand"
        },
        "offers": {
          "@type": "Offer",
          "url": "https://www.example.com/trinket_offer",
          "itemCondition": "https://schema.org/NewCondition",
          "availability": "https://schema.org/InStock",
          "priceSpecification": {
            "@type": "PriceSpecification",
            "price": 39.99,
            "priceCurrency": "CHF"
          },
          "hasMerchantReturnPolicy": {
            "@type": "MerchantReturnPolicy",
            "applicableCountry": "CH",
            "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
            "merchantReturnDays": 60,
            "returnMethod": "https://schema.org/ReturnByMail",
            "returnFees": "https://schema.org/FreeReturn"
          }
        }
      }
      </script>
    </head>
    <body>
    </body>
  </html>