import { expect } from "chai";
import { loadJson, removeNulls, select } from "../common.js";
import * as path from "path";

describe("json-ld/euro", function () {
    const result = select(new URL(`${path.basename(this.title)}.html`, import.meta.url).pathname);
    const expected = loadJson(new URL(`${path.basename(this.title)}.json`, import.meta.url).pathname);
    const actual = removeNulls(result.data.seo);
    it("has expected result", () => expect(actual).eql(expected));
    it("no errors", () => expect(result.errors).to.be.empty);
});
