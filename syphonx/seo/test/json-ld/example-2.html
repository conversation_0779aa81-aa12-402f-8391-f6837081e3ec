<script type="application/ld+json">
{
    "@context": "http://schema.org/",
    "@type": "Product",
    "name": "Smartphone X1000",
    "image": "http://example.com/x1000.jpg",
    "description": "The latest Smartphone X1000 with high-resolution camera and long-lasting battery life.",
    "brand": {
        "@type": "Brand",
        "name": "SmartTech"
    },
    "offers": [
        {
            "@type": "Offer",
            "url": "http://example.com/store/refurbished-x1000",
            "priceCurrency": "USD",
            "price": "399.99",
            "priceValidUntil": "2025-11-05",
            "itemCondition": "http://schema.org/RefurbishedCondition",
            "availability": "http://schema.org/InStock",
            "seller": {
                "@type": "Organization",
                "name": "Example Refurbished Store"
            }
        },
        {
            "@type": "Offer",
            "url": "http://example.com/store/new-x1000",
            "priceCurrency": "USD",
            "price": "499.99",
            "priceValidUntil": "2025-11-05",
            "itemCondition": "http://schema.org/NewCondition",
            "availability": "http://schema.org/InStock",
            "seller": {
                "@type": "Organization",
                "name": "Example Online Store"
            }
        }
    ]
}
</script>