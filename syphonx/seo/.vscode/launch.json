{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "outFiles": [
                "${workspaceFolder}/**/*.js"
            ],
            //"console": "externalTerminal", // WORKAROUND https://stackoverflow.com/questions/41100916/node-js-readline-in-debug-console-in-visual-studio-code
            //"outputCapture": "std", // WORKAROUND https://github.com/microsoft/vscode/issues/19750            
            "program": "${file}",
            "args": ["./test/cvs.html", "--template=test"]
            //"args": ["https://www.bestbuy.com/site/sony-playstation-portal-remote-player-white/6562576.p?skuId=6562576"]
        },
        {
            "name": "Run All Tests",
            "type": "node",
            "request": "launch",
            "skipFiles": ["<node_internals>/**"],
            "outFiles": ["${workspaceFolder}/**/*.js"],
            "sourceMaps": true,
            "internalConsoleOptions": "openOnSessionStart",
            "outputCapture": "std", // WORKAROUND https://github.com/microsoft/vscode/issues/19750
            "cwd": "${workspaceFolder}",
            "program": "${workspaceFolder}/node_modules/mocha/lib/cli/cli.js",
            "args": [
                "--config", "${workspaceFolder}/.mocharc.yml"
            ],
            "runtimeArgs": ["--experimental-specifier-resolution=node"] // WORKAROUND (see devnotes)
        },
        {
            "name": "Run Selected Test",
            "type": "node",
            "request": "launch",
            "skipFiles": ["<node_internals>/**"],
            "outFiles": ["${workspaceFolder}/**/*.js"],
            "sourceMaps": true,
            "internalConsoleOptions": "openOnSessionStart",
            "outputCapture": "std", // WORKAROUND https://github.com/microsoft/vscode/issues/19750
            "cwd": "${workspaceFolder}",
            "program": "${workspaceFolder}/node_modules/mocha/lib/cli/cli.js",
            "args": [
                "--config", "${workspaceFolder}/.mocharc.yml",
                "--grep", "${selectedText}"
            ],
            "runtimeArgs": ["--experimental-specifier-resolution", "node"]
        }
    ]
}