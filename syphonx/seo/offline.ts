import * as fs from "fs";
import * as path from "path";
import * as syphonx from "syphonx-lib";
import JSON5 from "json5";
import { findLastSelectGroup, Template } from "syphonx-lib";
import { parseArgs } from "./lib/index.js";

const params = parseArgs({
    required: {
        0: "url"
    },
    optional: {
        template: "template name to use (default=generic)",
        debug: "show debug output"
    }
});

const debug = !!params.debug;

const htmlPath = path.resolve(params[0]);
if (!fs.existsSync(htmlPath)) {
    console.error(`${htmlPath} not found`);
    process.exit(1);
}
const html = fs.readFileSync(htmlPath, "utf-8");

const templatePath = path.resolve(`./templates/${params.template || "product_page"}.json`);
if (!fs.existsSync(templatePath)) {
    console.error(`${templatePath} not found`);
    process.exit(1);
}
const template = JSON5.parse(fs.readFileSync(templatePath, "utf-8")) as Template;

const select = findLastSelectGroup(template.actions);
if (!select) {
    console.error("No select group found in template");
    process.exit(1);
}

const result = syphonx.select(select, html, { unwrap: true, debug });
console.log(JSON.stringify(result.data, null, 2));

if (debug) {
    console.log("");
    console.log(result.log);
    console.log(JSON.stringify(result.vars));
}

if (result.errors.length > 0) {
    console.log("");
    console.error(result.errors.map(error => error.message).join("\n"));
    console.log(result.log);
}

process.exit(0);
