{"actions": [{"select": [{"name": "_product", "type": "object", "query": [["script[type='application/ld+json']", ["json"], ["filter", "{(a => a.some(obj => typeof obj === 'object' && obj !== null && obj['@type'] === 'Product'))(Array.isArray(value) ? value.flat(Infinity) : [value])}"]]], "value": "{Array.isArray(value) ? value.flat(Infinity).find(obj => obj['@type'] === 'Product') : value}", "all": true}, {"name": "_offer", "value": "{Array.isArray(_product?.offers) ? _product.offers.flat(Infinity).sort((a, b) => (b.itemCondition?.includes('New')?1:0)-(a.itemCondition?.includes('New')?1:0)).find(obj => obj['@type'] === 'Offer') : _product?.offers}"}, {"name": "_images", "query": [["script[type='application/ld+json']", ["json"], ["filter", "{Array.isArray(value) && value.flat(Infinity).some(obj => typeof obj === 'object' && obj !== null && obj['@type'] === 'ImageObject')}"]]], "value": "{Array.isArray(value) ? value.flat(Infinity).filter(obj => typeof obj === 'object' && obj !== null && obj['@type'] === 'ImageObject') : []}", "all": true}, {"name": "_rdfa", "type": "boolean", "query": [["[typeof='schema:Product']"]]}, {"name": "_microdata", "type": "boolean", "query": [["[itemtype*='schema.org/Product']"]]}, {"name": "seo", "type": "object", "when": "{_product || _rdfa || _microdata}", "query": [["html"]], "select": [{"name": "name", "type": "string", "query": [["[typeof='schema:Product'] > [property='schema:name']", ["attr", "content"]], ["[typeof='schema:Product'] [property='schema:name']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] > [itemprop='name'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='name'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='name']"]], "value": "{_product?.name || value}"}, {"name": "price", "type": "number", "query": [["[typeof='schema:Product'] [property='schema:price'], [property='schema:lowPrice'], [property='schema:highPrice']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='price'][content], [itemtype*='schema.org/Product'] [itemprop='lowPrice'][content], [itemtype*='schema.org/Product'] [itemprop='highPrice'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='price'], [itemtype*='schema.org/Product'] [itemprop='lowPrice'], [itemtype*='schema.org/Product'] [itemprop='highPrice']"]], "value": "{_offer?.price || _offer?.lowPrice || _offer?.highPrice || _offer?.priceSpecification?.price || value}"}, {"name": "currency", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:priceCurrency']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='priceCurrency'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='priceCurrency']"]], "value": "{_offer?.priceCurrency || _offer?.priceSpecification?.priceCurrency || value}"}, {"name": "availability", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:availability']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='availability'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='availability']"]], "value": "{(_offer?.availability || value)?.replace(/.*\\//, '')}"}, {"name": "condition", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:itemCondition']", ["attr", "content"], ["extract", "/([^/]+)$/"]], ["[itemtype*='schema.org/Product'] [itemprop='itemCondition'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='itemCondition']"]], "value": "{(_offer?.itemCondition || value)?.replace(/.*\\//, '')}"}, {"name": "seller_name", "type": "string", "query": [["[typeof='schema:Product'] [rel='schema:seller'] [property='schema:name']", ["attr", "content"]]], "value": "{_offer?.seller?.name || value}"}, {"name": "in_stock", "type": "boolean", "value": "{data.availability ? ['InStock', 'LimitedAvailability', 'InStoreOnly', 'OnlineOnly'].includes(data.availability) : null}"}, {"name": "url", "type": "string", "format": "href", "query": [["[typeof='schema:Product'] [rel='schema:url']", ["attr", "resource"]], ["[itemtype*='schema.org/Product'] [itemprop='url'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='url'][href]", ["attr", "href"]], ["[itemtype*='schema.org/Product'] [itemprop='url']"]], "value": "{_product?.url || value}"}, {"name": "description", "type": "string", "query": [["[typeof='schema:Product'] > [property='schema:description']", ["attr", "content"]], ["[typeof='schema:Product'] [property='schema:description']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] > [itemprop='description'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] > [itemprop='description']"], ["[itemtype*='schema.org/Product'] [itemprop='description'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='description']"]], "value": "{_product?.description || value}"}, {"name": "sku", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:sku']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='sku'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='sku']"]], "value": "{_product?.sku || value}"}, {"name": "gtin", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:gtin14'], [typeof='schema:Product'] [property='schema:gtin13'], [typeof='schema:Product'] [property='schema:gtin12'], [typeof='schema:Product'] [property='schema:gtin8'], [typeof='schema:Product'] [property='schema:gtin']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='gtin14'][content], [itemtype*='schema.org/Product'] [itemprop='gtin13'][content], [itemtype*='schema.org/Product'] [itemprop='gtin12'][content], [itemtype*='schema.org/Product'] [itemprop='gtin8'][content], [itemtype*='schema.org/Product'] [itemprop='gtin'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='gtin14'], [itemtype*='schema.org/Product'] [itemprop='gtin13'], [itemtype*='schema.org/Product'] [itemprop='gtin12'], [itemtype*='schema.org/Product'] [itemprop='gtin8'], [itemtype*='schema.org/Product'] [itemprop='gtin']"]], "value": "{_product?.gtin14 || _product?.gtin13 || _product?.gtin12 || _product?.gtin8 || _product?.gtin || value}"}, {"name": "model", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:model']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='model'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='model']"]], "value": "{_product?.model || value}"}, {"name": "mpn", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:mpn']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='mpn'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='mpn']"]], "value": "{_product?.mpn || value}"}, {"name": "product_id", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:productID']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='productID'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='productID']"]], "value": "{_product?.productID || value}"}, {"name": "color", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:color']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='color'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='color']"]], "value": "{_product?.color || value}"}, {"name": "category", "type": "string", "query": [["[typeof='schema:Product'] [property='schema:category']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='category'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='category']"]], "value": "{_product?.category || value}"}, {"name": "brand_name", "type": "string", "query": [["[typeof='schema:Product'] [typeof='schema:Brand'] [property='schema:name']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='brand'] [itemprop='name']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='brand'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='brand']"]], "value": "{_product?.brand?.name || _product?.brand || _product?.manufacturer?.name || _product?.manufacturer || value}"}, {"name": "review_score", "type": "number", "query": [["[typeof='schema:Product'] [typeof='schema:AggregateRating'] [property='schema:ratingValue']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='aggregateRating'] [itemprop='ratingValue'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='aggregateRating'] [itemprop='ratingValue']"]], "value": "{_product?.aggregateRating?.ratingValue || value}"}, {"name": "review_count", "type": "number", "query": [["[typeof='schema:Product'] [typeof='schema:AggregateRating'] [property='schema:reviewCount']", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='aggregateRating'] [itemprop='reviewCount'][content]", ["attr", "content"]], ["[itemtype*='schema.org/Product'] [itemprop='aggregateRating'] [itemprop='reviewCount']"]], "value": "{_product?.aggregateRating?.reviewCount || _product?.aggregateRating?.ratingCount || value}"}, {"name": "images", "type": "string", "repeated": true, "distinct": true, "removeNulls": true, "format": "href", "query": [["[typeof='schema:Product'] [rel='schema:image']", ["attr", "resource"]], ["[itemtype*='schema.org/Product'] [itemprop='image'][href]", ["attr", "href"]], ["[itemtype*='schema.org/Product'] img[itemprop='image']", ["attr", "src"]]], "value": "{_images.length > 0 ? _images.map(obj => obj.thumbnailUrl) : Array.isArray(_product?.image) ? _product.image.map(obj => typeof obj === 'object' ? obj.contentUrl || obj.thumbnailUrl : obj) : typeof _product?.image === 'string' ? [_product.image] : Array.isArray(value) ? value : [] }"}]}]}]}