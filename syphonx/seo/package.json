{"name": "seo", "version": "1.0.0", "description": "Structured data formats: * HTML Microdata * JSON-LD", "main": "index.js", "type": "module", "directories": {"example": "examples"}, "scripts": {"build": "npx tsc", "clean": "bash clean.sh", "test": "npx mocha --config .mocharc.yml"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/chai": "^4.3.11", "@types/chai-as-promised": "^7.1.8", "@types/mocha": "^10.0.6", "@types/node": "^20.11.16", "chai": "^5.0.3", "json5": "^2.2.3", "mocha": "^10.3.0", "playwright": "^1.41.2", "syphonx-lib": "^1.2.74", "typescript": "^5.3.3", "word-wrap": "^1.2.5"}}