import * as fs from "fs";
import * as path from "path";
import JSON5 from "json5";
import { findLastSelectGroup, Template } from "syphonx-lib";
import { parseArgs, selectPage } from "./lib/index.js";

const params = parseArgs({
    required: {
        0: "url"
    },
    optional: {
        template: "template name to use (default=generic)",
        show: "show browser window",
        debug: "show debug output"
    }
});

const url = params[0];
const debug = !!params.debug;
const headless = false; //!params.show;

const templatePath = path.resolve(`./templates/${params.template || "product_page"}.json`);
if (!fs.existsSync(templatePath)) {
    console.error(`${templatePath} not found`);
    process.exit(1);
}
const template = JSON5.parse(fs.readFileSync(templatePath, "utf-8")) as Template;

const select = findLastSelectGroup(template.actions);
if (!select) {
    console.error("No select group found in template");
    process.exit(1);
}

const result = await selectPage(url, select, { headless /*, timeout: 30000, waitfor: "load"*/ });
console.log(JSON.stringify(result.data, null, 2));

if (debug) {
    console.log("");
    console.log(result.log);
    console.log(JSON.stringify(result.vars));
}

if (result.errors.length > 0) {
    console.log("");
    console.error(result.errors.map(error => error.message).join("\n"));
    console.log(result.log);
}

process.exit(0);
