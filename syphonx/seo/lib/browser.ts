import * as playwright from "playwright";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";

export type WaitFor = "load" | "domcontentloaded" | "networkidle";
export { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";

export interface OpenPageOptions {
    headless?: boolean;
    timeout?: number;
    waitfor?: WaitFor;
}

export interface OpenPageResult {
    browser: Browser;
    page: Page;
    timeout: boolean;
    error?: string;
    ok: boolean;
}

export async function openPage(url: string, { waitfor, ...options }: OpenPageOptions = {}): Promise<OpenPageResult> {
    const browser = await playwright.chromium.launch(options);
    const page = await browser.newPage();
    const result: OpenPageResult = {
        browser,
        page,
        timeout: false,
        ok: false
    };
    try {
        await page.goto(url, { timeout: options.timeout });
        if (waitfor)
            await page.waitForLoadState(waitfor, { timeout: options.timeout });
    }
    catch (err: any) {
        if (err.name === "TimeoutError")
            result.timeout = true;
        else
            return { ...result, ok: false, error: err };
    }
    result.ok = true;
    return result as OpenPageResult;
}
