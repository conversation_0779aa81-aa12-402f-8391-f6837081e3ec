import * as syphonx from "syphonx-lib";
import { ExtractState, Select } from "syphonx-lib";
import { openPage, OpenPageOptions } from "./browser.js";

interface EvaluateArg {
    select: syphonx.Select[];
    unwrap?: boolean;
    debug?: boolean
}

type EvaluateResult = syphonx.ExtractState;
type PageFunction = (arg: EvaluateArg) => Promise<EvaluateResult>;
const script = new Function("obj", `return ${syphonx.script}(obj)`) as PageFunction;

export async function selectPage(url: string, select: Select[], options?: OpenPageOptions): Promise<ExtractState> {
    const { page, browser } = await openPage(url, options);
    const result = await page.evaluate<EvaluateResult, EvaluateArg>(script, { select, unwrap: true, debug: true });
    await page.close();
    await browser.close();
    return result;
}
