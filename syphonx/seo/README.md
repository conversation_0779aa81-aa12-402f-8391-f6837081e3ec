# Overview
[Structured data](https://webdatacommons.org/structureddata/) enables web crawlers to uniformly extract product details from retailer websites by embedding standardized, machine-readable information about products, such as price, availability, and specifications, directly within the HTML content, thereby providing a way to extract product data without having to resort to site specific crawling instructions.

This project implements a generic [SyphonX template](templates/product_page.json) for extracting the following structured data formats to a simplified output specification:
- JSON-LD
- RDFa 
- Microdata

## Output Specification

field          | type     | description
-------------- | -------- | -----------
`name`         | string   | Name of the product.
`price`        | number   | Price of the product.
`currency`     | string   | Currency code of the product price (e.g., USD, EUR).
`availability` | string   | Availability status of the product (e.g., InStock, OutOfStock).
`condition`    | string   | Condition of the product (e.g., New, Used, Refurbished).
`seller_name`  | string   | Name of the seller within the marketplace for the product.
`in_stock`     | boolean  | Indicates whether or not the product is in-stock.
`url`          | string   | Canonical URL of the product page.
`review_count` | number   | Total number of reviews for the product.
`review_score` | number   | Average score of the product reviews.
`sku`          | string   | A unique identifier assigned by merchants for internal inventory and sales management.
`model`        | string   | A model number identifing a specific product model within a manufacturer's product line.
`mpn`          | string   | Manufacturer Part Number, a unique identifier set by the manufacturer to distinguish a product among all products.
`gtin`         | string   | Global Trade Item Number, a unique identifier for products, often represented by barcodes.
`product_id`   | string   | A system-generated identifier used within a specific platform or database to uniquely identify a product.
`brand_name`   | string   | The name of the brand or manufacturer of the product.
`color`        | string   | The color of the product (e.g. White, Black).
`description`  | string   | A detailed description of the product.
`images`       | string[] | A detailed description of the product.

## Example
```json
{
    "name": "Nice trinket",
    "price": 39.99,
    "currency": "USD",
    "availability": "InStock",
    "condition": "NewCondition",
    "seller_name": null,
    "in_stock": true,
    "url": null,
    "description": "Trinket with clean lines",
    "sku": "trinket-12345",
    "gtin": "12345678901234",
    "model": null,
    "mpn": null,
    "product_id": null,
    "brand_name": "MyBrand",
    "review_score": 4.4,
    "review_count": 89,
    "color": null,
    "images": [
        "https://example.com/photos/16x9/trinket.jpg",
        "https://example.com/photos/4x3/trinket.jpg",
        "https://example.com/photos/1x1/trinket.jpg"
    ]
}
```

## Verification Tests
Verification tests for the SyphonX template are based on example templates from Google located [here](https://developers.google.com/search/docs/appearance/structured-data/product), and a few other examples from various select retailers. Use the `yarn test` to run the verification tests.


# Prerequisites
- Node.js 18 or higher
- Yarn


# Setup
1. `<NAME_EMAIL>:PriceSpider-NeuIntel/mudder-of-all-repos.git` to clone repo
2. `cd mudder-of-all-repos/syphonx/seo` to change directory
3. `yarn` to install dependencies
4. `yarn build` to build
5. `yarn test` to run tests


## Commands
- `node offline test/json-ld/bestbuy-1.html`
- `node online https://www.bestbuy.com/site/sony-playstation-portal-remote-player-white/6562576.p?skuId=6562576`


# Maintenance
Follow the steps below to maintain the template...
1. Add a unit test under [test](./test) directory with the new data scenario
2. Update the [SyphonX template](templates/product_page.md)
3. Adjust the unit tests until all are passing
4. Deploy the template change to production


# Troubleshooting Tips
- Select name of a test in the unit teste code and use the **Run Selected Test** debug profile to run just that test
- Inspect the `result.log` variable returned from `select` within the test code to trace selector results
- To debug into the syphonx library, open the file `node_modules/syphonx-core/dist/esm/package/controller.js` and set a breakpoint in the select function on or around line #1376


# Discussion
The adoption of different structured data formats, namely JSON-LD, Microdata, and RDFa, varies significantly across the web, with each format offering its own set of advantages and challenges for implementation. JSON-LD has emerged as the most recommended format by Google, primarily due to its ease of implementation, scalability, and the fact that it doesn't interfere with the HTML of a page, making it simpler for developers to manage structured data without affecting the website's design​​.

Microdata, which has been around since 2011, integrates directly with HTML5, making it a stable and well-adopted choice, especially for those already deeply integrated into HTML-based website development. Its direct embedding into HTML allows for semantic richness and enhanced validation through HTML5, but it can be more rigid and complex to implement compared to JSON-LD​​.

RDFa, while offering high versatility and the ability to express complex data relationships, has a steeper learning curve and is not as widely supported as JSON-LD or Microdata, especially in terms of search engine optimization (SEO) applications. However, its interoperability and support for linked data principles make it a powerful tool for certain applications beyond simple SEO​​.

In terms of adoption rates, the HTTP Archive's 2022 Web Almanac provides insightful statistics: RDFa is used on 62% of pages (both desktop and mobile), indicating its wide usage, possibly due to its earlier introduction and broader applicability beyond just search engine optimization. JSON-LD has seen increasing adoption, used on 37% of pages, reflecting its growing popularity and recommendation by Google for structured data. Microdata, meanwhile, is used on about 23-25% of pages, showing significant but lesser usage compared to RDFa and JSON-LD​​.

Google has updated its structured data guidance to emphasize that while JSON-LD is recommended due to its ease of use and maintenance, Microdata and RDFa are still perfectly acceptable formats. This update underscores the importance of choosing a structured data format that best fits a website's specific needs and setup, highlighting that all three formats are supported as long as the markup is valid and properly implemented according to the documentation​​.

JSON-LD offers simplicity and strong SEO impact, making it a good choice for many users. Microdata provides a reliable option with deep integration into HTML5, and RDFa offers versatility for more complex data structures and applications beyond SEO.

# Links
- https://developers.google.com/search/docs/appearance/structured-data/product
- https://webdatacommons.org/structureddata/
- https://schemapros.com/json-vs-microdata-vs-rdfa/
- https://almanac.httparchive.org/en/2022/structured-data
- https://www.searchenginejournal.com/google-updates-structured-data-guidance-to-clarify-supported-formats/478544/
