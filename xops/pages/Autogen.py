import streamlit as st
import pandas as pd
from datetime import datetime
import validators
import utils.helpers as utils
import utils.bigquery as bigquery
import utils.pubsub as pubsub

st.set_page_config(layout="wide")
utils.set_page_config_popover_size(600, 650)

if 'data' not in st.session_state:
    st.session_state.data = {}

st.header("Autogen")

def run_query(query):
    return bigquery.run_query(query)

def query_autogen_requests():
    return run_query("""
        SELECT
            url,
            CONCAT(COALESCE(report_url, 'about:blank'), '#', status) AS status,
            COALESCE(updated_at, requested_at) AS last_updated,
            IF(status='succeeded', CONCAT(generated_selector_count, '/', target_selector_count), NULL) AS generated,
            ROUND(cost, 5) AS cost,
            ROUND(elapsed/60000, 1) AS elapsed,
            template_path,
            selector_profile,
            autogen_profile,
            priority,
            IF(status='succeeded', generated_selectors, NULL) AS generated_selectors,
            IF(status='succeeded', ungenerated_selectors, NULL) AS ungenerated_selectors,
            models,
            requested_by,
            autogen_id
        FROM syphonx.autogen_view
        WHERE requested_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
        ORDER BY last_updated DESC
        LIMIT 100
    """)

def insert_autogen_request(
        url: str,
        country_code: str,
        selector_profile: str,
        autogen_profile: str,
        overwrite: bool,
        priority: int
    ):
    data = {
        "autogen_id": utils.generate_random_id(),
        "url": url,
        "domain_name": utils.parse_domain_name(url),
        "country_code": country_code,
        "template_path": template_path,
        "selector_profile": selector_profile,
        "autogen_profile": autogen_profile,
        "overwrite": overwrite,
        "priority": priority,
        "requested_at": datetime.now().isoformat(),
        #"requested_by": requested_by  # TODO implement
    }
    bigquery.insert("syphonx.autogen_requests", data)
    pubsub.send_message('syphonx-autogen', data)

def create_template_path(url: str, country_code: str, selector_profile: str):
    if url and country_code:
        domain_name = utils.parse_domain_name(url)
        template_name = utils.remove_tld(domain_name)
        return f"pricespider/autogen/{selector_profile.replace('-', '_')}/{country_code.lower()}/{template_name}.json"
    else:
        return None

col1, col2 = st.columns(2)
with col1:
    with st.popover("Add"):
        if 'reset_form' not in st.session_state:
            st.session_state.reset_form = False
        elif st.session_state.reset_form:
            st.session_state.url = ""
            st.session_state.template_path = ""
            st.session_state.overwrite = False
            st.session_state.reset_form = False

        def update_template_path():
            if 'url' in st.session_state and 'country_code' in st.session_state and 'selector_profile' in st.session_state:
                st.session_state['template_path'] = create_template_path(st.session_state['url'], st.session_state['country_code'], st.session_state['selector_profile'])

        url = st.text_input("URL", key="url", max_chars=500, on_change=update_template_path)
        country_code = st.selectbox("Country", utils.country_codes(), key="country_code", on_change=update_template_path)

        selector_profile = st.selectbox("Selector profile", [
            "common-product-page",
            "brand-monitor-product-page",
            "matching-product-page"
        ], key="selector_profile", on_change=update_template_path)

        autogen_profile = st.selectbox("Autogen profile", [
            "default",
            "diffbot",
            "chatgpt-standard",
            "chatgpt-economy",
            "gemini-standard",
            "gemini-economy",
            "claude-premium",
            "claude-standard",
            "claude-economy"
        ], key="autogen_profile")
    
        priority = st.selectbox("Priority", [1, 2, 3], 2)
        overwrite = True #st.checkbox("Overwrite")
        template_path = st.text_input("Template path", disabled=True, key="template_path")
        submit_button = st.button("Submit", disabled=template_path is None or len(template_path) < 1)

        if submit_button:
            if validators.url(url):
                with st.spinner('Please wait...'):
                    insert_autogen_request(
                        url,
                        country_code,
                        selector_profile,
                        autogen_profile,
                        overwrite,
                        priority
                    )
                st.success('Request added successfully.')
                st.session_state.reset_form = True
            else:
                st.error("Please enter a valid URL like https://www.example.com/")
with col2:
    refresh_button = st.button("🔄", help="Refresh Data")

if 'autogen' not in st.session_state.data or refresh_button:
    with st.spinner('Refreshing data...'):
        rows = query_autogen_requests()
        st.session_state.data['autogen'] = pd.DataFrame(rows)

st.data_editor(
    st.session_state.data['autogen'],
    hide_index=True,
    disabled=True,
    column_config={
        "url": st.column_config.LinkColumn("page", width="medium", display_text="https://(?:www\.|m\.)?([^/]+)"),
        "status": st.column_config.LinkColumn("status", width="small", display_text="#(.+)"),
        "cost": st.column_config.NumberColumn(format="$ %.5f", width="small"),
        "elapsed": st.column_config.NumberColumn(format="%fm", width="small"),
        "last_updated": st.column_config.DatetimeColumn("last updated", format="M/D h:mm a", timezone="America/Los_Angeles")
    }
)
