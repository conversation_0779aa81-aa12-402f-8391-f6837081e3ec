import streamlit as st
import pandas as pd
from datetime import datetime
import utils.bigquery as bigquery
from utils.helpers import checklist_table

if 'data' not in st.session_state:
    st.session_state.data = {}

st.header("Blocked Seller Domains")

app_names = ["wtb", "prowl", "brand_monitor"]

@st.dialog("Add blocked domain")
def add_dialog():
    domain_name = st.text_input("Domain", placeholder="example.com")
    app_name = st.radio("App", options=[""]+app_names, horizontal=True)
    comments = st.text_area("Comments")
    date = st.date_input("Date")
    submit = st.button("Add domain")        
    if submit:
        if not domain_name:
            st.warning("Please enter a valid domain name.")
        elif domain_name.startswith("www."):
            st.warning("Please remove www. from domain name.")
        elif not app_name:
            st.warning("Please specify wtb, prowl, or brand_monitor.")
        else:
            data = {
                "key": "blocked",
                "domain_name": domain_name,
                "app_name": app_name,
                "status": "blocked",
                "event_date": date.isoformat(),
                "comments": comments if comments else None,
                "timestamp": datetime.now().isoformat()
            }
            bigquery.insert("conflux.seller_domain_log", data)
            st.session_state.refresh_count += 1
            st.session_state.refresh_now = True
            st.rerun()

@st.dialog("Update status")
def update_dialog(row: dict):
    domain_name = st.text_input("Domain", row['domain_name'], disabled=True)
    status = st.selectbox("Status", ["blocked", "unblocked"], index=0 if row['status'] == 'blocked' else 1)
    app_name = st.text_input("App", row['app_name'], disabled=True)
    comments = st.text_area("Comments", row['comments'])
    date = st.date_input("Date", row['event_date'] if status == row['status'] else datetime.now().date()) # set date to today if status is changed
    submit = st.button("Update")
    if submit:
        data = {
            "key": "blocked",
            "domain_name": domain_name,
            "app_name": app_name,
            "status": status,
            "event_date": date.isoformat(),
            "comments": comments,
            "timestamp": datetime.now().isoformat()
        }
        bigquery.insert("conflux.seller_domain_log", data)
        st.session_state.refresh_count += 1
        st.session_state.refresh_now = True
        st.rerun()

#def format_rollup(sellers):
    #d = defaultdict(int)
    #for seller in sellers:
        #key = seller['app_name']
        #value = seller['page_count']
        #d[key] += value
    #return ", ".join([f"{value} {key}" for key, value in d.items()])

def format_app_names(sellers):
    app_names = {seller['app_name'] for seller in sellers}
    if not app_names:
        return "(invalid domain)"
    return ', '.join(app_names) + ' only'

def format_filtered_sum(sellers, app_names):
    unfiltered = sum(seller['page_count'] for seller in sellers if 'page_count' in seller)
    if pd.notnull(app_names):
        app_names_set = set(app_names.split(', '))
        filtered_sellers = [seller for seller in sellers if seller['app_name'] in app_names_set]
        filtered = sum(seller['page_count'] for seller in filtered_sellers if 'page_count' in seller)
        percentage = (filtered / unfiltered * 100) if unfiltered > 0 else 0
        return "{:,} ({:.0f}%)".format(filtered, percentage)
    else:
        return "{:,}".format(unfiltered)

def query_blocked_seller_domains(_cache_key=None):
    rows = bigquery.run_query("SELECT * FROM conflux.blocked_seller_domains")
    return pd.DataFrame(rows)
    #df['page_count'] = df.apply(lambda row: format_filtered_sum(row['sellers'], row['app_name']), axis=1)
    #df['apps'] = df.apply(lambda row: format_app_names(row['sellers']) if pd.notna(row['app_name']) else format_app_names(row['sellers']), axis=1)
    #return pd.DataFrame(df)

def group_by_domain(df):
    # Group by 'domain_name' and aggregate 'app_name' as a comma-separated list and event_date for min/max
    grouped_df = df.groupby('domain_name').agg({
        'app_name': lambda x: ', '.join(x),
        'event_date': ['min', 'max'],
        'page_count': 'sum'
    }).reset_index()
    # Flatten the multi-level column index
    grouped_df.columns = ['domain', 'apps', 'from', 'to', 'pages']
    # Set max_event_date to None if it is equal to min_event_date
    grouped_df['to'] = grouped_df.apply(lambda row: '-' if row['from'] == row['to'] else row['to'], axis=1)
    return grouped_df

if 'refresh_count' not in st.session_state:
    st.session_state.refresh_count = 0

if 'refresh_now' not in st.session_state:
    st.session_state.refresh_now = False

if 'blocked_seller_domains' not in st.session_state.data or st.session_state.refresh_now:
    with st.spinner('Refreshing data...'):
        df = query_blocked_seller_domains(_cache_key=st.session_state.refresh_count)
        st.session_state.data['blocked_seller_domains'] = df
        st.session_state.refresh_now = False

if st.button("🔄", help="Refresh data"):
    st.session_state.refresh_count += 1
    st.session_state.refresh_now = True
    st.rerun()

df = st.session_state.data['blocked_seller_domains']
blocked_selection = checklist_table(
    df[df['status'] == 'blocked'],
    column_order=["domain_name", "app_name", "page_count", "event_date", "comments"]
)

col1, col2, col3 = st.columns(3)
with col1:
    if st.button("Add blocked domain..."):
        add_dialog()
with col2:
    if st.button("Update status...", key='update_blocked_status', disabled=len(blocked_selection) != 1):
        update_dialog(blocked_selection.iloc[0])

blocked_df = df[df['status'] == 'blocked']
if not blocked_df.empty:
    blocked_df = group_by_domain(blocked_df)
    st.subheader("Blocked domains")
    st.dataframe(blocked_df, hide_index=True)

unblocked_df = df[df['status'] == 'unblocked']
if not unblocked_df.empty:
    unblocked_df = group_by_domain(unblocked_df)
    st.subheader("Unblocked domains")
    st.dataframe(unblocked_df, hide_index=True)
