import streamlit as st
import pandas as pd
import utils.bigquery as bigquery
from utils.helpers import first

st.header("Seller Finder")

@st.cache_data
def run_query(query):
    return bigquery.run_query(query)

country_codes = run_query("SELECT DISTINCT country_code FROM pricespider.sellers WHERE country_code IS NOT NULL ORDER BY country_code")
country_codes = first(country_codes)
country_codes.insert(0, '(all)')

search_input = st.text_input("Enter seller names comma seperated", "")
include_deactivated = st.checkbox("Include deactivated sellers")
include_nonimmutable = st.checkbox("Include non-immutable sellers")
include_3p = st.checkbox("Include third-party sellers")
country_code = st.selectbox("Country:", country_codes)

if st.button('Search'):
    select_clause = [
        "IF(NOT ENDS_WITH(name, CONCAT('(', country_code, ')')), CONCAT(name, ' (', country_code, ')'), name) AS name",
        "CAST(id AS STRING) AS id",
        "IFNULL(url,'-') AS url"
    ]
    
    keys = [key.strip().lower().replace('"', '').replace(" ", "%")[:20] for key in search_input.split(',')]
    like_statements = ['LOWER(name) LIKE "%{0}%"'.format(key) for key in keys]
    where_clause = [f"({' OR '.join(like_statements)})"]
    default_query = (not include_deactivated) and (not include_nonimmutable) and (not include_3p)

    if include_deactivated:
        select_clause.append("active")
    else:
        where_clause.append("active")

    if include_nonimmutable:
        select_clause.append("CAST(account_id AS STRING) AS account_id")
    else:
        where_clause.append("account_id IS NULL")

    if not include_3p:
        where_clause.append("parent_seller_id IS NULL")

    if default_query:
        where_clause.append("partition_key=1")

    if country_code != '(all)':
        where_clause.append(f"country_code = '{country_code}'")

    query = f"""
        SELECT {', '.join(select_clause)}
        FROM pricespider.sellers 
        WHERE {' AND '.join(where_clause)}
        ORDER BY
        url IS NOT NULL DESC,
        affiliate_name IS NOT NULL DESC,
        is_crawlable DESC,
        logo DESC,
        country_code DESC,
        id DESC
        LIMIT 500
    """

    rows = run_query(query)
    if rows:
        df = pd.DataFrame(rows)
        st.dataframe(df, hide_index=True)
        st.write(f"{len(df)} rows returned")
    else:
        st.write("No results found")
