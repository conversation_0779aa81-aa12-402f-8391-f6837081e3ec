import streamlit as st
import pandas as pd
from datetime import datetime
import utils.bigquery as bigquery
import json
from utils.helpers import country_codes, checklist_table, generate_random_id

st.header("Workstreams")

if 'data' not in st.session_state:
    st.session_state.data = {}

app_names = ["wtb","prowl","brand_monitor"]
country_codes = country_codes()

@st.cache_data
def query_workstreams():
    rows = bigquery.run_query("SELECT * FROM syphonx.autorun_workstreams WHERE active IS TRUE ORDER BY workstream_name<>'default', workstream_name")
    df = pd.DataFrame(rows)

    def workstream_info(row):
        a = []
        if pd.notna(row['app_name']):
            a.append(row['app_name'])
        if pd.notna(row['account_key']):
            a.append('accounts:' + row['account_key'])
        else:
            a.append('accounts: all')
        if pd.notna(row['country_code']):
            a.append('countries: ' + row['country_code'])
        else:
            a.append('countries: all')
        if pd.notna(row['seller_id']):
            a.append('sellers: ' + str(row['seller_id']))
        else:
            a.append('sellers: all')
        return ', '.join(a) if a else None

    df['workstream_info'] = df.apply(workstream_info, axis=1)
    return df

@st.cache_data
def query_account_keys():
    return bigquery.run_query("SELECT DISTINCT account_key FROM conflux.product_page_daily ORDER BY 1")

account_keys = [row['account_key'] for row in query_account_keys()]

@st.dialog("Add workstream")
def add_dialog():
    name = st.text_input("Name")
    app_name = st.selectbox("App", app_names)
    account_key = st.multiselect("Account", account_keys, placeholder="(All)")
    country_code = st.multiselect("Country", country_codes, placeholder="(All)")
    if st.button("OK"):
        data = {
            "key": "workstream",
            "timestamp": datetime.now().isoformat(),
            "user": None,
            "data": json.dumps({
                "workstream_id": generate_random_id(),
                "workstream_name": name,
                "app_name": app_name if app_name else None,
                "account_key": ",".join(account_key) if account_key else None,
                "country_code": ",".join(country_code) if country_code else None,
                #"seller_id": "",
                "active": True
            })
        }
        bigquery.insert("syphonx.autorun_log", data)
        st.cache_data.clear()
        st.session_state.refresh_now = True
        st.rerun()

@st.dialog("Edit workstream")
def edit_dialog(df):
    workstream_id = df.iloc[0]['workstream_id']
    workstream_name = df.iloc[0]['workstream_name']
    app_name = df.iloc[0]['app_name']
    account_key = df.iloc[0]['account_key']
    country_code = df.iloc[0]['country_code']
    new_name = st.text_input("Name", workstream_name)
    active = st.checkbox("Active", True)
    if st.button("OK"):
        data = {
            "key": "workstream",
            "timestamp": datetime.now().isoformat(),
            "user": None,
            "data": json.dumps({
                "workstream_id": workstream_id,
                "workstream_name": new_name,
                "app_name": app_name,
                "account_key": account_key,
                "country_code": country_code,
                # "seller_id": "",
                "active": active
            })
        }
        bigquery.insert("syphonx.autorun_log", data)

        st.session_state.refresh_now = True
        st.rerun()

refresh_button = st.button("🔄", help="Refresh Data")

if 'workstreams' not in st.session_state.data or refresh_button or st.session_state.refresh_now:
    with st.spinner('Refreshing data...'):
        st.session_state.data['workstreams'] = query_workstreams()
        st.session_state.refresh_now = False

if 'refresh_now' not in st.session_state:
    st.session_state.refresh_now = False

if 'selected_row' not in st.session_state:
    st.session_state.selected_row = None

selection = checklist_table(
    st.session_state.data['workstreams'],
    column_order=['workstream_name', 'workstream_info']
)

col1, col2 = st.columns(2)
with col1:
    if st.button("Add workstream..."):
        add_dialog()

with col2:
    edit_button = st.button("Update workstream...", disabled=selection.empty or len(selection) != 1)
    if edit_button:
        if not selection.empty:
            edit_dialog(selection)
