
## How to debug streamlit app with vscode
add the following to launch.json
```
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug streamlit",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/.venv/bin/streamlit",
            "args": ["run", "Main.py"]
        }
    ]
}
```

https://discuss.streamlit.io/t/vs-code-debug/520/5#post_7

