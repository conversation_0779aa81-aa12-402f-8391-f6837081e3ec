import streamlit as st
import pandas as pd
from utils.helpers import checklist_table

df = pd.DataFrame(
    {
        "Animal": ["Lion", "Elephant", "Giraffe", "Monkey", "Zebra"],
        "Habitat": ["Savanna", "Forest", "Savanna", "Forest", "Savanna"],
        "Lifespan (years)": [15, 60, 25, 20, 25],
        "Average weight (kg)": [190, 5000, 800, 10, 350],
    }
)

@st.dialog("Edit")
def edit_dialog(name):
    name = st.text_input("Name", name)
    if st.button("OK"):
        st.write(f"{name}")

selection = checklist_table(df)
edit_button = st.button("Edit...", disabled=selection.empty or len(selection) != 1)
if edit_button:
    if not selection.empty:
        first_animal = selection.iloc[0]['Animal']
        edit_dialog(first_animal)
