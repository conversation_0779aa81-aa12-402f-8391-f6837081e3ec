# Blocked Seller Domains
Compute from/to dates for unblocked rows
Ability to tag rows


TODO
DONE: isolate cached data between pages
reconsider usage of @st.cache_data over custom session_state data caching
consider returning a pd.DataFrame from bigquery.run_query
add type decorators to bigquery.run_query

implement overwrite option
add missing selectors
add diffbot
show cost
overall timeout abort
add status message to table display
format table to fit screen


DONE: pubsub trigger start, and requery
DONE: sort by updated_at desc
DONE: reset pending, retry or fail

DISCUSS
sites that cause screenshot timeouts
multiple dialog popups
not always running




FROM python:3.8-slim
EXPOSE 8080
WORKDIR /app
COPY . ./
RUN python -m pip install --upgrade pip && \
    python -m pip install virtualenv && \
    virtualenv venv && \
    . venv/bin/activate && \
    pip install -r requirements.txt
ENTRYPOINT ["streamlit", "run", "Main.py", "--server.port", "8080"]
