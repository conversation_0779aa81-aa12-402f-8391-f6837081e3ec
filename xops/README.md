# XOPS

- [link](https://streamlit-hvthjpewsa-wl.a.run.app)

## Setup
- `git clone`
- `cd xops`
- copy ps-bigdata.json into directory
- `pip install virtualenv`
- `virtualenv .venv`

## Run
- linux: `source .venv/bin/activate`
- windows: `virtualenv .venv`
- `pip install -r requirements.txt`
- `streamlit run Main.py`

## Deploy
- `gcloud builds submit --tag=gcr.io/ps-bigdata/streamlit --project=ps-bigdata`
- `gcloud run deploy streamlit --image=gcr.io/ps-bigdata/streamlit --platform=managed --region=us-west2 --allow-unauthenticated --project=ps-bigdata`
- [app](https://streamlit-hvthjpewsa-wl.a.run.app)

## Links
- [Python Client for Google BigQuery](https://cloud.google.com/python/docs/reference/bigquery/latest)
- [Cloud Run with IAP](https://www.youtube.com/watch?v=MO8T9jTMSXs)
- [Using Python on Google Cloud with Cloud Run](https://www.youtube.com/watch?v=s2TIWIzCftM)
- [Cloud Run user auth for internal apps](https://www.youtube.com/watch?v=ayTGOuCaxuc)
- [How to deploy Streamlit apps to Google App Engine](https://dev.to/whitphx/how-to-deploy-streamlit-apps-to-google-app-engine-407o)
- [How to Deploy and Secure your Streamlit App on GCP?](https://www.artefact.com/blog/how-to-deploy-and-secure-your-streamlit-app-on-gcp/)
- [Streamlit Dataframes](https://docs.streamlit.io/develop/concepts/design/dataframes#additional-formatting-options)

## Helpful Commands
- `pip install <package-name>` to add a new dependency (admin)
- `pip install --upgrade streamlit` to upgrade streamlit version (admin)
- `pip freeze` to see dependency versions
- `pip list` to list installed packages
- `streamlit version` to check installed streamlit version
- `curl -X POST http://localhost:8081/autogen -H "Content-Type: application/json" -d '{"url":"..."}'`
- `curl -X POST https://us-central1-ps-bigdata.cloudfunctions.net/syphonx-service/autogen -H "Content-Type: application/json" -d '{"url":"https://www.dickssportinggoods.com/p/nike-mens-dunk-low-retro-shoes-24nikmdnklwfggryxmns/24nikmdnklwfggryxmns","selector_profile":"matching-product-page","autogen-profile":"default","country_code":"US"}'`

# Other Info
- username is in http header `x-goog-iap-jwt-assertion`
