import streamlit as st
import pandas as pd
import secrets
import string
from urllib.parse import urlparse

def first(rows):
    return [next(iter(row.values())) for row in rows]

def parse_domain_name(url):
    domain_name = urlparse(url).netloc
    if domain_name.startswith('www.'):
        domain_name = domain_name[4:]
    return domain_name

def remove_tld(domain_name):
    return domain_name.split('.')[0]

def generate_random_id(length=10):
    alphabet = string.ascii_letters + string.digits  # uppercase, lowercase and digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def country_codes():
    return ["US", "CA", "CN", "JP", "DE", "GB", "IN", "FR", "IT", "BR", "RU", "KR", "AU", "ES", "MX", "ID", "NL", "SA", "TR", "CH", "TW", "PL", "BE", "TH", "SE"]

def set_page_config_popover_size(width: int, height: int):
    st.markdown(f"<style>[data-testid='stPopoverBody'] {{ min-width: {width}px !important; min-height: {height}px !important }}</style>", unsafe_allow_html=True)

from typing import Callable, Dict, Iterable, Optional
def checklist_table(
        df: pd.DataFrame,
        width: Optional[int] = 0,
        height: Optional[int] = 0,
        use_container_width: Optional[bool] = True,
        column_order: Optional[Iterable[str]] = None,
        column_config: Optional[Dict[str, st.column_config.Column]] = None,
        key: Optional[str] = None,
        on_change: Optional[Callable] = None,
        args: Optional[tuple] = None,
        kwargs: Optional[Dict] = None
    ) -> pd.DataFrame:

    editor_df = df.copy()
    editor_df.insert(0, "select", False)

    editor_df = st.data_editor(
        editor_df,
        width=width,
        height=height,
        use_container_width=use_container_width,
        hide_index=True,
        column_order=['select', *column_order] if column_order else None,
        column_config={**{"select": st.column_config.CheckboxColumn(label="", width=28, required=True)}, **(column_config or {})},
        num_rows="fixed",
        disabled=df.columns,
        key=key,
        on_change=on_change,
        args=args,
        kwargs=kwargs
    )

    selected_rows = editor_df[editor_df.select]
    editor_df['select'] = False
    return selected_rows.drop('select', axis=1)
