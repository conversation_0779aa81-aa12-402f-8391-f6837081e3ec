import pandas as pd
from google.cloud import bigquery

client = bigquery.Client()

def run_query(query):
    query_job = client.query(query)
    rows = query_job.result()
    return [dict(row.items()) for row in rows]

def insert(table_name, data):
    dataset, table = table_name.split('.')
    table_ref = client.dataset(dataset).table(table)
    table_obj = client.get_table(table_ref)

    if isinstance(data, dict):
        data = [data] # Single JSON object
    elif isinstance(data, list):
        data = data # List of JSON objects
    elif isinstance(data, pd.DataFrame):
        data = data.to_dict('records') # Pandas DataFrame
    else:
        raise ValueError(f"Unsupported data type: {type(data)}")

    client.insert_rows_json(table_obj, data)
