import streamlit as st
import pandas as pd
from dotenv import load_dotenv
import bigquery
from util import first

def seller_search():
    @st.cache_data
    def run_query(query):
        return bigquery.run_query(query)

    country_codes, _ = run_query("SELECT DISTINCT country_code FROM pricespider.sellers WHERE country_code IS NOT NULL ORDER BY country_code")
    country_codes = first(country_codes)
    country_codes.insert(0, '(all)')

    st.header("WTB-US Platform")
    st.subheader("Find seller")

    search_input = st.text_input("Enter seller names comma seperated", "")
    include_deactivated = st.checkbox("Include deactivated sellers")
    include_nonimmutable = st.checkbox("Include non-immutable sellers")
    include_3p = st.checkbox("Include third-party sellers")
    country_code = st.selectbox("Country:", country_codes)

    if st.button('Search'):
        select_clause = [
            "IF(NOT ENDS_WITH(name, CONCAT('(', country_code, ')')), CONCAT(name, ' (', country_code, ')'), name) AS name",
            "CAST(id AS STRING) AS id",
            "IFNULL(url,'-') AS url"
        ]
        
        keys = [key.strip().lower().replace('"', '').replace(" ", "%")[:20] for key in search_input.split(',')]
        like_statements = ['LOWER(name) LIKE "%{0}%"'.format(key) for key in keys]
        where_clause = [f"({" OR ".join(like_statements)})"]

        if include_deactivated:
            select_clause.append("active")
        else:
            where_clause.append("active")

        if include_nonimmutable:
            select_clause.append("CAST(account_id AS STRING) AS account_id")
        else:
            where_clause.append("account_id IS NULL")

        if not include_3p:
            where_clause.append("parent_seller_id IS NULL")

        if country_code != '(all)':
            where_clause.append(f"country_code = '{country_code}'")

        query = f"""
SELECT {select_clause.join(", ")}
FROM pricespider.sellers 
WHERE {where_clause.join("\nAND ")}
ORDER BY
url IS NOT NULL DESC,
affiliate_name IS NOT NULL DESC,
is_crawlable DESC,
logo DESC,
country_code DESC,
id DESC
LIMIT 500
        """

        rows = run_query(query)
        if rows:
            df = pd.DataFrame(rows)
            return df

    return None
