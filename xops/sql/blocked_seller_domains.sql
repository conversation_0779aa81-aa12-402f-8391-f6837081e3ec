CREATE OR REPLACE VIEW conflux.blocked_seller_domains
AS
WITH
latest AS (
  SELECT domain_name, app_name, status, event_date, comments
  FROM conflux.seller_domain_log
  WHERE key='blocked'
  QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name, app_name ORDER BY timestamp DESC)=1
)
SELECT
  domain_name,
  app_name,
  ANY_VALUE(status) AS status,
  ANY_VALUE(event_date) AS event_date,
  SUM(page_count) AS page_count,
  ARRAY_AGG(
    STRUCT(
      seller_name,
      seller_id,
      country_code,
      page_count
    )
    ORDER BY page_count DESC, seller_name
  ) AS seller_info,
  ANY_VALUE(comments) AS comments
FROM latest
JOIN conflux.seller_domains USING (domain_name, app_name)
GROUP BY 1, 2
ORDER BY 1, 2;