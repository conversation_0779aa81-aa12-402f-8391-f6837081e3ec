# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands
- Build: `npm run build` or `npx tsc` - Compiles TypeScript to JavaScript
- Run importers: `node import-feeds.js`, `node import-leads.js <date>`, `node import-sales.js <date>`
- Renew API tokens: `./renew.sh` - Refreshes Hatch API access and refresh tokens

## Architecture Overview

This is a Hatch data importer that downloads sales, leads, and feeds data from the Hatch API and loads it into Snowflake. The project consists of three main importers:

### Core Components
- **import-feeds.ts**: Downloads pending feeds from Hatch API and loads into Snowflake
- **import-leads.ts**: Downloads lead data for specified date ranges (with time divisions for large datasets)  
- **import-sales.ts**: Downloads sales data for specified date ranges
- **lib/**: Shared utilities for API calls, Snowflake operations, and argument parsing

### Data Flow
1. **Authentication**: Uses OAuth refresh tokens to maintain API access via `refreshHatchAPIToken()`
2. **Download**: Fetches data from Hatch APIs (feeds, leads, sales) and saves to local tmp/ files
3. **Upload**: Stages files to Snowflake using `snowflake.stage()` 
4. **Load**: Copies staged data into Snowflake tables using `COPY INTO` commands

### Key Libraries
- **lib/snowflake.ts**: Snowflake connection pool, query execution, and data loading utilities
- **lib/utilities.ts**: HTTP downloads, date utilities, and Hatch API token management
- **lib/args.ts**: Command-line argument parsing with help system

## Environment Setup
- Requires `.env` file with `HATCH_ACCESS_TOKEN`, `HATCH_REFRESH_TOKEN`, and `SNOWFLAKE_CREDENTIALS`
- Snowflake credentials format: `key:value,key:value` pairs
- API tokens can be renewed using the `renew.sh` script

## Usage Patterns
- Feeds: Run without arguments to process all pending feeds, or specify feed ID
- Leads/Sales: Require start date (YYYY-MM-DD), optional end date
- All scripts support `--curl` flag to generate curl commands instead of downloading
- Use `--verbose` flag for detailed logging output

## Error Handling
- Scripts track consecutive failures and abort after threshold (3-10 failures)
- API timeouts suggest VPN connection issues
- Comprehensive error logging with chalk color coding