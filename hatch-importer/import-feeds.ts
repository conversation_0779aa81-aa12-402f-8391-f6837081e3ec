import * as dotenv from "dotenv";
dotenv.config();

import * as path from "path";
import {
    createCurlCommand,
    downloadToFile,
    parseArgs,
    refreshHatchAPIToken,
    sleep,
    snowflake
} from "./lib/index.js";
import chalk from "chalk";

const api_url = "https://bo.aws.gethatch.com/iceleads_web/web/feed";
const snooze_seconds = 10;
const max_consecutive_failures = 10;

const args = parseArgs({
    optional: {
        0: "feed-id (optional, fetch all pending if not specified)",
        curl: "show curl command instead of downloading",
        verbose: "verbose log output"
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

const options = {
    method: "GET",
    headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.HATCH_ACCESS_TOKEN}`
    }
};

if (args.curl) {
    console.log(chalk.cyan(createCurlCommand({ ...options, url: `${api_url}/${args[0] || "1"}` })));
    process.exit(0);
}

// Get current date in YYYY-MM-DD format
const date = new Date().toISOString().split('T')[0];

const rows = await snowflake.query("SELECT feed_id FROM NEXUS.PROD.HATCH_FEEDS_PENDING ORDER BY 1");
console.log(chalk.gray(`${rows.length} rows returned`));

let succeeded = 0;
let failed = 0;
let consecutive_failures = 0;

await refreshHatchAPIToken();

for (const row of rows) {
    if (rows.indexOf(row) > 0)
        await sleep(snooze_seconds * 1000);
 
    const { feed_id } = row;
    console.log(chalk.gray(`Fetching feed_id=${feed_id} [${rows.indexOf(row) + 1}/${rows.length}]...`));
    const fullpath = path.resolve(`tmp/feeds/${feed_id}.json`);

    try {
        await downloadToFile({ ...options, url: `${api_url}/${feed_id}`, file: fullpath });
        console.log(chalk.gray(`${fullpath} fetched`));

        await snowflake.stage("NEXUS.PROD.HATCH_FEEDS", fullpath);
        const [{ file, status, rows_loaded, errors_seen }] = await snowflake.query(`COPY INTO NEXUS.PROD.HATCH_FEEDS_STAGE FROM @NEXUS.PROD.HATCH_FEEDS/${date}/${feed_id}.json.gz FILE_FORMAT=(TYPE='JSON')`);
        console.log(chalk.gray(`${file} ${status} (${rows_loaded} rows, ${errors_seen} errors)`));

        succeeded += 1;
        consecutive_failures = 0;
    }
    catch (err) {
        failed += 1;
        if (typeof err === "object" && err !== null && (err as any).cause?.message) {
            console.error(chalk.red(`ERROR ${feed_id}: ${(err as any).cause.message}`));
            if ((err as any).cause.name === "ConnectTimeoutError") {
                console.log("Are you connected to the VPN?");
                break;
            }
        }
        else {
            console.error(`ERROR ${feed_id}: ${err instanceof Error ? err.message : JSON.stringify(err)}`);
        }
        if (++consecutive_failures >= max_consecutive_failures) {
            console.error("Aborting - too many consecutive failures");
            break;
        }
    }
}

console.log();
console.log(`${succeeded} succeeded, ${failed} failed`);
process.exit(failed > 0 ? 1 : 0);