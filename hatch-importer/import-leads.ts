import * as dotenv from "dotenv";
dotenv.config();

import * as path from "path";
import chalk from "chalk";

import {
    createCurlCommand,
    downloadToFile,
    generateDateArray,
    parseArgs,
    refreshHatchAPIToken,
    snowflake,
    unixTimestampDayRange
}
from "./lib/index.js";

const args = parseArgs({
    required: {
        0: "start date (YYYY-MM-DD)",
    },
    optional: {
        1: "end date (YYYY-MM-DD)",
        curl: "show curl command instead of downloading",
        verbose: "verbose log output"
    }
});

if (args.verbose)
    process.env.VERBOSE = "1";

if (args.curl)
    process.env.CURL = "1";

const from = args[0];
const to = args[1];

if (!/^\d{4}-\d{2}-\d{2}$/.test(from) || (to && !/^\d{4}-\d{2}-\d{2}$/.test(to))) {
    console.error("specify a date like 2025-01-01");
    process.exit(1);
}

const divs = 8; // number of divisions per day
const date_range = generateDateArray(from, to ?? from);
let succeeded = 0;
let failed = 0;
let consecutive_failures = 0;

if (!args.curl)
    await refreshHatchAPIToken();

for (const date of date_range) {
    if (process.env.VERBOSE) {
        console.log();
        console.log(chalk.underline(date));
    }
    try {
        for (let i = 0; i < divs; ++i) {
            const name = `${date}-${String.fromCharCode(96 + i + 1)}.csv`;
            const file = path.resolve("tmp/leads", name);
            const options = {
                url: "https://papi.gethatch.com/api/pils/report/v2/export",
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${process.env.HATCH_ACCESS_TOKEN}`
                },
                body: JSON.stringify({
                    statType: "lead",
                    currency: "USD",
                    dataFormat: "csv",
                    priceFormat: "COMMA_AND_PERIOD",
                    projection: ["bot", "brandId", "brandName", "campaignId", "campaignName", "campaignProductId", "categoryId", "categoryName", "conversion.AUD", "conversion.EUR", "conversion.GBP", "conversion.MXN", "conversion.USD", "conversion.ZAR", "countryId", "currency", "currencyId", "countryName", "custom.var0", "custom.var1", "custom.var2", "custom.var3", "custom.var4", "custom.var5", "custom.var6", "custom.var7", "custom.var8", "custom.var9", "dateIns", "deviceType", "ean", "feedId", "huid", "leadDateIns", "leadId", "mpn", "operatingSystem", "price", "productId", "productName", "proprietaryId", "recognizeStatus", "retailerId", "retailerName", "searchableId", "trackingType", "type", "userAgent", "vendorId", "vendorName"],
                    filters: [{
                        columnName: "dateIns",
                        operation: "range",
                        value: unixTimestampDayRange(date, i + 1, divs)
                    }]
                }),
                file
            };

            if (args.curl) {
                console.log();
                console.log(chalk.cyan(createCurlCommand(options)));
                continue;
            }

            if (process.env.VERBOSE)
                console.log(chalk.gray(`Fetching ${date} [${i + 1}/${divs}]...`));
            await downloadToFile(options);

            await snowflake.stage("NEXUS.PROD.HATCH_LEADS", file);
            const [{ file: stage_file, status, rows_loaded, errors_seen }] = await snowflake.query(`COPY INTO NEXUS.PROD.HATCH_LEADS_STAGE FROM @NEXUS.PROD.HATCH_LEADS/${name}.gz FILE_FORMAT=NEXUS.PROD.EXCEL_CSV`);
            console.log(chalk.gray(`[${date_range.indexOf(date) + 1}/${date_range.length}] ${stage_file} ${status} (${rows_loaded} rows, ${errors_seen} errors)`));
        }
    
        succeeded += 1;
    }
    catch (err) {
        console.error(chalk.red(`ERROR ${date}: ${err instanceof Error ? err.message : JSON.stringify(err)}`));
        failed += 1;
        if (++consecutive_failures >= 3) {
            console.error(chalk.gray("Too many consecutive failures, aborting..."));
            break;
        }
    }
}

console.log();
console.log(`summary: ${succeeded} succeeded, ${failed} failed`);
process.exit(failed > 0 ? 1 : 0);
