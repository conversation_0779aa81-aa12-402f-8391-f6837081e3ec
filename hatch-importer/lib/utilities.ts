import * as fs from "fs";
import * as path from "path";


export interface FetchOptions {
    url: string;
    method: string;
    headers?: Record<string, string>;
    body?: string;
}

export interface DownloadToFileOptions extends FetchOptions {
    file: string;
}

export function createCurlCommand({ url, method, headers, body }: FetchOptions) {
    const lines = [`curl -X ${method} "${url}"`];
    if (headers)
        lines.push(...Object.entries(headers).map(([key, value]) => `-H "${key}: ${value}"`));
    if (body)
        lines.push(`-d '${body}'`);
    return lines.join(" \\\n");
}

export async function downloadToFile({ url, method, headers, body, file }: DownloadToFileOptions): Promise<void> {        
    const response = await fetch(url, { method, headers, body });
    if (!response.ok)
        throw new Error(`${response.status} - ${response.statusText}`);
    
    if (!response.body)
        throw new Error("HTTP response body is null");
    
    fs.mkdirSync(path.dirname(file), { recursive: true });
    const reader = response.body.getReader();
    const writer = fs.createWriteStream(file);
    
    await new Promise<void>((resolve, reject) => {
        writer.on("finish", () =>
            resolve());
        
        writer.on("error", err =>
            reject(err));
        
        async function pump() {
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        writer.end();
                        break;
                    }
                    writer.write(value);
                }
            } catch (error) {
                writer.destroy(error instanceof Error ? error : new Error(String(error)));
                reject(error);
            }
        }
        
        pump();
    });
}

export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export function unixTimestampDayRange(value: string, div = 1, divs = 1): [number, number] {
    const d1 = new Date(`${value}T00:00:00.000Z`);
    const d2 = new Date(d1.getTime() + 24 * 60 * 60 * 1000 - 1); // exact end of day as start of next day minus 1 millisecond    
    if (divs === 1)
        return [d1.getTime(), d2.getTime()];
    
    // Calculate the total milliseconds in the day
    const dayStart = d1.getTime();
    const dayEnd = d2.getTime();
    const totalMs = dayEnd - dayStart + 1; // +1 because we want inclusive range
    
    // Calculate milliseconds per division
    const msPerDiv = totalMs / divs;
    
    // Calculate start and end for the requested division
    const divStart = dayStart + Math.floor(msPerDiv * (div - 1));
    const divEnd = div === divs 
        ? dayEnd // For the last division, use the exact end of day
        : dayStart + Math.floor(msPerDiv * div) - 1; // -1 to avoid overlap
    
    return [divStart, divEnd];
}

export function generateDateArray(from: string, to: string): string[] {
    const result = [];
    const startDate = new Date(`${from}T00:00:00.000Z`);
    const endDate = new Date(`${to}T00:00:00.000Z`);
    
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
        const year = currentDate.getUTCFullYear();
        const month = String(currentDate.getUTCMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getUTCDate()).padStart(2, '0');
        
        result.push(`${year}-${month}-${day}`);
        
        currentDate.setUTCDate(currentDate.getUTCDate() + 1);
    }
    
    return result;
}

export async function refreshHatchAPIToken(): Promise<void> {
    const response = await fetch(
        "https://papi.gethatch.com/oauth/token", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Authorization": `Basic Z2V0aGF0Y2g6d2hhdGV2ZXI=`
        },
        body: `grant_type=refresh_token&refresh_token=${process.env.HATCH_REFRESH_TOKEN}`
    });
    if (!response.ok)
        throw new Error(`${response.status} - ${response.statusText}`);
    const obj = await await response.json();
    process.env.HATCH_ACCESS_TOKEN = obj.access_token;
    process.env.HATCH_REFRESH_TOKEN = obj.refresh_token;
}
