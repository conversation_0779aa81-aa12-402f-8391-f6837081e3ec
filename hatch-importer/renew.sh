curl -XPOST --max-time 30 'https://papi.gethatch.com/oauth/token' -H 'authorization: Basic Z2V0aGF0Y2g6d2hhdGV2ZXI=' --data-raw 'username=<EMAIL>&password=Fidelio-99&grant_type=password' --compressed \
| jq -r '{access: .access_token, refresh: .refresh_token} | "ACCESS_TOKEN=\(.access)\nREFRESH_TOKEN=\(.refresh)"' \
| (while IFS= read -r line; do key=$(echo "$line" | cut -d= -f1); value=$(echo "$line" | cut -d= -f2); sed -i "s|$key=.*|$key=$value|" .env; done)
