# Retry Logic Enhancement Plan

## Current State Analysis

**Existing Retry Mechanisms:**
- **import-feeds.ts**: Tracks consecutive failures (max 10), aborts on specific connection errors
- **import-leads.ts**: Tracks consecutive failures (max 3), aborts after threshold  
- **import-sales.ts**: Tracks consecutive failures (max 3), aborts after threshold
- **No individual item retry**: Current logic only fails entire batches, no single item retry
- **No exponential backoff**: Fixed 10-second snooze in feeds, no delays in leads/sales

**Pain Points:**
- API failures in the middle of large batches cause complete restart
- No differentiation between retryable vs permanent errors
- Limited recovery options when operations fail
- No progress persistence across restarts

## Phase 1: Create Retry Utility Library
**Location:** `lib/retry.ts`

### 1.1 Core Retry Function
- **withRetry()** - Generic retry wrapper with configurable options
- **RetryOptions interface** - Max attempts, delays, error filtering
- **RetryableError class** - Distinguish retryable vs permanent errors

```typescript
interface RetryOptions {
    maxAttempts: number;
    strategy: 'exponential' | 'fixed' | 'jittered';
    baseDelay: number;
    maxDelay: number;
    shouldRetry?: (error: Error) => boolean;
}

async function withRetry<T>(
    operation: () => Promise<T>,
    options: RetryOptions
): Promise<T>
```

### 1.2 Retry Strategies
- **Exponential backoff** - 1s, 2s, 4s, 8s delays
- **Fixed delay** - Consistent intervals
- **Jittered backoff** - Add randomness to prevent thundering herd

### 1.3 Error Classification
- **Retryable errors**: Network timeouts, 5xx HTTP, connection errors
- **Non-retryable errors**: 4xx HTTP (except 429), authentication failures
- **Special handling**: Rate limiting (429), token expiration

## Phase 2: Individual Operation Retries
**Target:** Individual API calls and Snowflake operations

### 2.1 API Download Retries
- **downloadToFile()** enhancement in `lib/utilities.ts`
- Retry on network failures, timeouts, server errors
- Preserve partial downloads where possible

```typescript
// Enhanced downloadToFile with retry support
async function downloadToFile(
    options: DownloadToFileOptions & { retryOptions?: RetryOptions }
): Promise<void>
```

### 2.2 Snowflake Operation Retries  
- **snowflake.stage()** retry wrapper
- **snowflake.query()** retry for transient failures
- Connection pool health checks

### 2.3 Token Refresh Retries
- **refreshHatchAPIToken()** enhancement
- Automatic token refresh on 401 errors
- Fallback to manual token renewal

## Phase 3: Import Program Integration
**Target:** Main import scripts

### 3.1 Individual Item Recovery
- Retry failed feeds/dates/divisions individually
- Skip/resume functionality for partially completed runs
- Progress persistence across restarts

```typescript
// Enhanced import with item-level retry
for (const item of items) {
    try {
        await withRetry(() => processItem(item), retryOptions);
        markSuccess(item);
    } catch (error) {
        markFailure(item, error);
        if (shouldAbort(error)) break;
    }
}
```

### 3.2 Enhanced Progress Tracking
- Track retry attempts per item
- Detailed failure reason logging
- Resume from last successful item

### 3.3 Configurable Retry Policies
- Command-line retry configuration
- Environment variable overrides
- Different policies per operation type

```bash
# Example usage with retry options
node import-leads.js 2025-01-01 --max-retries=5 --retry-strategy=exponential
```

## Phase 4: Monitoring & Recovery
**Target:** Operational improvements

### 4.1 Enhanced Logging
- Structured retry attempt logging
- Performance metrics (retry rates, success rates)
- Alert thresholds for excessive failures

### 4.2 Resume Capability
- Save progress to state files
- `--resume` flag to continue interrupted runs
- Automatic deduplication checks

```typescript
// Progress state management
interface ImportState {
    lastProcessedItem: string;
    failedItems: Array<{ item: string; error: string; attempts: number }>;
    startTime: Date;
    totalItems: number;
    processedItems: number;
}
```

### 4.3 Circuit Breaker Pattern
- Fail fast after excessive retries
- Service health monitoring
- Automatic recovery detection

## Implementation Priority

### Phase 1 (1-2 days): Core retry utilities
**Tasks:**
1. Create `lib/retry.ts` with core retry function
2. Implement retry strategies (exponential, fixed, jittered)
3. Add error classification helpers
4. Unit tests for retry logic

### Phase 2 (2-3 days): Individual operation retries  
**Tasks:**
1. Enhance `downloadToFile()` with retry support
2. Add retry wrappers for Snowflake operations
3. Implement automatic token refresh on failures
4. Integration tests for enhanced operations

### Phase 3 (2-3 days): Import script integration
**Tasks:**
1. Refactor import loops for item-level retry
2. Add progress tracking and state persistence
3. Implement command-line retry configuration
4. Add resume functionality

### Phase 4 (1-2 days): Advanced features
**Tasks:**
1. Enhanced structured logging
2. Circuit breaker implementation
3. Performance metrics collection
4. Operational documentation

**Total Effort:** ~1-2 weeks for full implementation

## Configuration Examples

### Environment Variables
```bash
# Default retry settings
HATCH_RETRY_MAX_ATTEMPTS=3
HATCH_RETRY_STRATEGY=exponential
HATCH_RETRY_BASE_DELAY=1000
HATCH_RETRY_MAX_DELAY=30000

# Per-operation overrides
HATCH_API_RETRY_MAX_ATTEMPTS=5
HATCH_SNOWFLAKE_RETRY_MAX_ATTEMPTS=2
```

### Command Line Options
```bash
# Basic retry configuration
node import-leads.js 2025-01-01 --max-retries=5

# Advanced configuration
node import-feeds.js --retry-strategy=exponential --max-delay=60000 --resume

# Dry run with retry simulation
node import-sales.js 2025-01-01 --simulate-failures=0.1 --max-retries=3
```

## Success Metrics

1. **Reduced manual intervention**: Automatic recovery from 80% of transient failures
2. **Improved completion rates**: 95% of import runs complete successfully
3. **Faster recovery**: Resume interrupted runs within minutes instead of hours
4. **Better visibility**: Detailed retry metrics and failure analysis

## Benefits

- **Individual item recovery** instead of failing entire batches
- **Smart retry policies** with exponential backoff to avoid overwhelming services
- **Resume capability** for interrupted runs minimizes data loss
- **Better error classification** to avoid retrying permanent failures
- **Enhanced monitoring** to track retry effectiveness and identify systemic issues

This phased approach allows for incremental implementation and testing, with Phase 1-2 providing immediate value while Phase 3-4 adds advanced features for production robustness.