## setup
```
git clone ...
cd hatch-importer
yarn
yarn build
mkdir tmp
```

## daily run
```
bash renew.sh
node import-sales 2025-06-09
node import-leads 2025-06-09
node import-feeds
```

- import-feeds requires AWS VPN
- import-leads breaks a day into several files, if one of them fails delete that day in snowflake and try again
- for extra credit: use claude code to finish the retry-logic plan to add retries to make it more robust for the previous point


## data check
```
SELECT DATE(TO_TIMESTAMP(CAST(dateins AS INTEGER)/1000)), COUNT(*) FROM NEXUS.PROD.HATCH_LEADS_STAGE GROUP BY ALL ORDER BY 1 DESC
SELECT DATE(TO_TIMESTAMP(CAST(dateins AS INTEGER)/1000)), COUNT(*) FROM NEXUS.PROD.HATCH_SALES_STAGE GROUP BY ALL ORDER BY 1 DESC
DELETE FROM NEXUS.PROD.HATCH_LEADS_STAGE WHERE DATE(TO_TIMESTAMP(CAST(dateins AS INTEGER)/1000))='2024-03-17'
```

```
CREATE OR REPLACE TABLE NEXUS.PROD.SELLER_METRICS
AS
WITH all_metrics AS (
    SELECT 1 AS platform_id, *
    FROM NEXUS.PROD.PRICESPIDER_METRICS
    WHERE date >= '2024-01-01'

    UNION ALL

    SELECT 3 AS platform_id, *
    FROM NEXUS.PROD.CC_METRICS
    WHERE date >= '2024-01-01'

    UNION ALL

    SELECT 4 AS platform_id, *
    FROM NEXUS.PROD.HATCH_METRICS
    WHERE date >= '2023-06-01'
)
SELECT
    date,
    brand_name, -- DEPRECATED
    account_name, -- NEW
    account_id, -- NEW
    group_name, -- NEW
    group_id, -- NEW
    platform_name,
    platform_id,
    NEXUS.PROD.FORMAT_SELLER_NAME(seller_name, country_code) AS seller_name,
    CONCAT(platform_id, '-', seller_id) AS seller_id,
    seller_key,
    seller_url,
    country_code,
    domain_name,
    affiliate_key,
    sales_usd,
    sales_count,
    leads_usd,
    click_count
FROM all_metrics
JOIN NEXUS.PROD.PLATFORM_SELLERS USING (platform_id, seller_id, country_code)

GRANT SELECT ON TABLE NEXUS.PROD.SELLER_METRICS TO ROLE ETL_ROLE;
```

## Links
- [Hatch Portal](https://my.gethatch.com/)
- [Hatch API DOcumentation](https://pricespider.atlassian.net/wiki/spaces/EKB/pages/***********/Hatch+WTB+Online+Reporting+API+new)
- [New Hatch "Projection" API](https://pricespider.atlassian.net/wiki/spaces/TEST/pages/***********/Hatch+WTB+Online+Reporting+API+Projection)
- [Looker report](https://pricespiderstaging.cloud.looker.com/dashboards/2351?Country%20Code=&Domain=&Seller=&Sales%20Data%20Source=&Platform=&Brand=&Date=30%20day)
