CREATE OR <PERSON><PERSON>LACE TABLE TEMP.PUBLIC.SELLER_METRICS
AS
WITH all_metrics AS (
    SELECT 1 AS platform_id, *
    FROM TEMP.PUBLIC.PRICESPIDER_METRICS
    WHERE date >= '2024-01-01'

    UNION ALL

    SELECT 3 AS platform_id, *
    FROM TEMP.PUBLIC.CC_METRICS
    WHERE date >= '2024-01-01'

    UNION ALL

    SELECT 4 AS platform_id, *
    FROM TEMP.PUBLIC.HATCH_METRICS
    WHERE date >= '2023-06-01'
)
SELECT
    date,
    brand_name, -- DEPRECATED
    account_name, -- NEW
    account_id, -- NEW
    group_name, -- NEW
    group_id, -- NEW
    platform_name,
    platform_id,
    TEMP.PUBLIC.FORMAT_SELLER_NAME(seller_name, country_code) AS seller_name,
    CONCAT(platform_id, '-', seller_id) AS seller_id,
    seller_key,
    seller_url,
    country_code,
    domain_name,
    affiliate_key,
    sales_usd,
    sales_count,
    leads_usd,
    click_count
FROM all_metrics
JOIN TEMP.PUBLIC.PLATFORM_SELLERS USING (platform_id, seller_id, country_code)

--> UNIQUE ???
--> affiliate_key IS NULL AND (sales_count>0 OR sales_usd>0) --> EMPTY

GRANT SELECT ON TABLE TEMP.PUBLIC.SELLER_METRICS TO ROLE ETL_ROLE



--TESTS

SELECT
    domain_name,
    country_code,
    affiliate_key,
    platform_id,
    SUM(sales_usd) AS sales_usd,
    SUM(sales_count) AS sales_count
FROM TEMP.PUBLIC.SELLER_METRICS
WHERE date >= '2025-01-01'
AND affiliate_key IS NOT NULL
GROUP BY ALL
ORDER BY 1, 2, 3, 4

SELECT
    affiliate_key,
    SUM(sales_usd) AS sales_usd,
    SUM(sales_count) AS sales_count
FROM TEMP.PUBLIC.SELLER_METRICS
WHERE date >= '2025-01-01'
AND affiliate_key IS NOT NULL
GROUP BY ALL
HAVING SUM(sales_count)>0
ORDER BY 1