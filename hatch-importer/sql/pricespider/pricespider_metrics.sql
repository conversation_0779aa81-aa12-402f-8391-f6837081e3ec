CREATE OR <PERSON><PERSON><PERSON>CE VIEW TEMP.PUBLIC.PRICESPIDER_METRICS
AS
SELECT
    date,
    group_name AS brand_name, -- DEPRECATED
    group_id::STRING AS brand_id, -- DEPRECATED

    account_name AS account_name, -- NEW
    account_id AS account_id, -- NEW
    group_name AS group_name, -- NEW
    group_id::STRING AS group_id, -- NEW

    s.value:seller_name::STRING AS seller_name,
    s.value:seller_id::STRING AS seller_id,
    s.value:country_code::STRING AS country_code,
    TEMP.PUBLIC.AFFILIATE_NAME_MAP(s.value:affiliate_name) AS affiliate_key,
    SUM(s.value:sales_usd::FLOAT) AS sales_usd,
    SUM(s.value:sales_count::INTEGER) AS sales_count,
    SUM(s.value:purchase_lead_value_usd::FLOAT) AS leads_usd,
    SUM(s.value:redirect_count::INTEGER) AS click_count
FROM WTB.PROD.TRAFFIC,
LATERAL FLATTEN (input => sellers) AS s
GROUP BY ALL

--> UNIQUE date, brand_name, seller_name, seller_id, country_code