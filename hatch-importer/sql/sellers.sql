CREATE OR REPLACE TABLE TEMP.PUBLIC.SELLERS
AS
SELECT
    IFF(REGEXP_LIKE(seller_name, '.+\\([A-Z]{2}\\)'), seller_name, CONCAT(seller_name, ' (', country_code, ')')) AS seller_name,
    seller_key,
    country_code,
    domain_name,
    TEMP.PUBLIC.FORMAT_URL(seller_url) AS seller_url,
    platforms,
    platform_count,
    platform_ids,
    --NULLIF(ARRAY_TO_STRING(ARRAY_SORT(affiliate_keys), ', '), '') AS affiliates,
    --affiliate_count,
    --affiliate_keys
FROM (
    SELECT
        COALESCE(
            ANY_VALUE(IFF(platform_id=1, seller_name, NULL)),
            ANY_VALUE(IFF(platform_id=4, seller_name, NULL)),
            ANY_VALUE(IFF(platform_id=3, seller_name, NULL)),
            ANY_VALUE(IFF(platform_id=2, seller_name, NULL))
        ) AS seller_name,
        seller_key,
        country_code,
        domain_name,
        ARRAY_AGG(seller_url) WITHIN GROUP (ORDER BY LENGTH(seller_url), seller_name)[0]::STRING AS seller_url, --shortest
        ARRAY_TO_STRING(
            ARRAY_AGG(
                CASE
                    WHEN platform_id = 1 THEN 'pricespider'
                    WHEN platform_id = 2 THEN 'prowl'
                    WHEN platform_id = 3 THEN 'cc'
                    WHEN platform_id = 4 THEN 'hatch'
                    ELSE NULL
                END
            ) WITHIN GROUP (ORDER BY platform_id),
            ', '
        ) AS platforms,
        --COUNT(DISTINCT affiliate_key) AS affiliate_count,
        COUNT(DISTINCT platform_id) AS platform_count,
        --ARRAY_AGG(DISTINCT affiliate_key) WITHIN GROUP (ORDER BY affiliate_key) AS affiliate_keys,
        ARRAY_AGG(DISTINCT platform_id) WITHIN GROUP (ORDER BY platform_id) AS platform_ids
    FROM TEMP.PUBLIC.PLATFORM_SELLERS
    GROUP BY ALL
)


-- show all sellers that are on pricespider, cc, and hatch
SELECT seller_name, seller_key, platforms
FROM TEMP.PUBLIC.SELLERS
WHERE ARRAY_CONTAINS(1, platform_ids)
AND ARRAY_CONTAINS(3, platform_ids)
AND ARRAY_CONTAINS(4, platform_ids)
ORDER BY 1
