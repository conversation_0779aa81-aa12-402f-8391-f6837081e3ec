CREATE OR <PERSON><PERSON><PERSON>CE VIEW TEMP.PUBLIC.H<PERSON>CH_FEEDS
AS
SELECT
    src:merchantName::STRING AS feed_name,
    src:merchantId::INTEGER AS feed_id,
    src:dateImp::TIMESTAMP_TZ AS timestamp,
    src:sourceTypeDTO:key::STRING AS source_type,
    src:countryDTO:key::STRING AS country_code,
    NU<PERSON>IF(src:trackingTypeDTO:name::STRING, 'None') AS affiliate_name,
    NULLIF(src:trackingTypeDTO:id::INTEGER, '1') AS affiliate_id,
    TEMP.PUBLIC.PARSE_DOMAIN(url) AS domain_name,
    TEMP.PUBLIC.FORMAT_URL(url) AS url,
    NULLIF(ARRAY_TO_STRING(src:affiliates, ', '), '') AS brands,
    ARRAY_SIZE(src:affiliates) AS brand_count
FROM TEMP.PUBLIC.HATCH_FEEDS_STAGE
LEFT JOIN TEMP.PUBLIC.<PERSON><PERSON><PERSON>_FEED_DOMAINS ON feed_id = src:merchantId::INTEGER

--> UNIQUE feed_id
