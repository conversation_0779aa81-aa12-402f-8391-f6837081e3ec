CREATE OR REPLACE VIEW TEMP.PUBLIC.HATCH_SALES
AS
SELECT
    TO_TIMESTAMP(CAST(dateins AS INTEGER)/1000) AS timestamp,
    TO_NUMBER(NULLIF(productid, '\\N')) AS product_id,
    brandname AS brand_name,
    brandid AS brand_id,
    retailername AS retailer_name,
    retailerid AS retailer_id,
    c.country_code,
    affiliate_name,
    affiliate_id,
    TO_NUMBER(feed_id) AS feed_id,
    NULLIF(mpn, '\\N') AS mpn,
    NULLIF(ean, '\\N') AS ean,
    NULLIF(description, '\\N') AS description,
    ROUND(TO_DECIMAL(REPLACE(NULLIF(conversion_usd, '\\N'), ',', ''), 32, 2), 2) AS price_usd,
    TO_NUMBER(qty) AS quantity,

    NULLIF(orderid, '\\N') AS order_id,
    NULLIF(orderstatus, '\\N') AS order_status,
    NULLIF(categoryname, '\\N') AS category_name,
    NULLIF(categoryid, '\\N') AS category_id,
    NULLIF(leadid, '\\N') AS lead_id,
    NULLIF(vendorname, '\\N') AS vendor_name,
    NULLIF(vendorid, '\\N') AS vendor_id,
    NULLIF(campaignname, '\\N') AS campaign_name,
    NULLIF(campaignid, '\\N') AS campaign_id,

    price AS original_price,
    currency AS original_currency,
    NULLIF(cid, '') AS cid,
    NULLIF(custom_var0, '\\N') AS var0,
    NULLIF(custom_var1, '\\N') AS var1,
    NULLIF(custom_var2, '\\N') AS var2,
    NULLIF(custom_var3, '\\N') AS var3,
    NULLIF(custom_var4, '\\N') AS var4,
    NULLIF(custom_var5, '\\N') AS var5,
    NULLIF(custom_var6, '\\N') AS var6,
    NULLIF(custom_var7, '\\N') AS var7,
    NULLIF(custom_var8, '\\N') AS var8,
    NULLIF(custom_var9, '\\N') AS var9

FROM TEMP.PUBLIC.HATCH_SALES_STAGE AS a
LEFT JOIN TEMP.PUBLIC.HATCH_FEEDS AS b ON b.feed_id=CAST(a.feedid AS INTEGER)
LEFT JOIN TEMP.PUBLIC.HATCH_COUNTRIES AS c ON c.country_id=a.countryid
