CREATE OR <PERSON><PERSON><PERSON>CE VIEW TEMP.PUBLIC.<PERSON><PERSON>CH_SELLERS
AS
WITH
sales_retailers AS (
    SELECT DISTINCT
        retailer_name,
        retailer_id,
        feed_id
    FROM TEMP.PUBLIC.HATCH_SALES
),
leads_retailers AS (
    SELECT DISTINCT
        retailer_name,
        retailer_id,
        feed_id
    FROM TEMP.PUBLIC.HATCH_LEADS
),
all_retailers AS (
    SELECT DISTINCT * FROM (
        SELECT * FROM sales_retailers
        UNION ALL
        SELECT * FROM leads_retailers
    )
)
SELECT
    retailer_name AS seller_name,
    retailer_id AS seller_id,
    country_code,
    ARRAY_AGG(domain_name) WITHIN GROUP (ORDER BY LENGTH(domain_name), domain_name)[0]::STRING AS domain_name, --shortest
    ARRAY_AGG(url) WITHIN GROUP (ORDER BY LENGTH(url), url)[0]::STRING AS seller_url, --shortest
    ARRAY_AGG(DISTINCT feed_id) WITHIN GROUP (ORDER BY feed_id) AS feed_ids,
    COUNT(DISTINCT feed_id) AS feed_count,
    NULLIF(ARRAY_TO_STRING(ARRAY_SLICE(ARRAY_AGG(DISTINCT url) WITHIN GROUP (ORDER BY LENGTH(url), url), 1, ARRAY_SIZE(ARRAY_AGG(DISTINCT url))), '\n'), '') AS alt_urls
FROM all_retailers
LEFT JOIN TEMP.PUBLIC.HATCH_FEEDS USING (feed_id)
GROUP BY ALL

-- UNIQUE seller_id