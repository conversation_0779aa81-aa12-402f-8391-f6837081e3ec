CREATE OR REPLACE VIEW TEMP.PUBLIC.<PERSON><PERSON><PERSON>_METRICS
AS
WITH
sales AS (
    SELECT
        DATE(timestamp) AS date,
        brand_name, -- DEPRECATED
        brand_id, -- DEPRECATED
        
        brand_name AS group_name, -- NEW
        brand_id AS group_id, -- NEW
        retailer_name AS seller_name,
        retailer_id AS seller_id,
        country_code,
        IFNULL(affiliate_name, '') AS affiliate_name,
        SUM(price_usd * quantity) AS sales_usd,
        COUNT(*) AS sales_count
    FROM TEMP.PUBLIC.HATCH_SALES
    GROUP BY ALL
    
),
leads AS (
    SELECT
        DATE(timestamp) AS date,
        brand_name, -- DEPRECATED
        brand_id, -- DEPRECATED
        
        brand_name AS group_name, -- NEW
        brand_id AS group_id, -- NEW
        retailer_name AS seller_name,
        retailer_id AS seller_id,
        country_code,
        IFNULL(affiliate_name, '') AS affiliate_name,
        SUM(price_usd) AS leads_usd,
        COUNT(*) AS click_count
    FROM TEMP.PUBLIC.<PERSON><PERSON><PERSON>_LEADS
    GROUP BY ALL
),
date_range AS (
    SELECT DISTINCT date FROM (
        SELECT DISTINCT date FROM sales
        UNION ALL SELECT DISTINCT date FROM leads
    )
),
dimensions AS (
    SELECT DISTINCT group_name, group_id, seller_name, seller_id, country_code, affiliate_name FROM (
        SELECT DISTINCT group_name, group_id, seller_name, seller_id, country_code, affiliate_name FROM sales
        UNION ALL SELECT DISTINCT group_name, group_id, seller_name, seller_id, country_code, affiliate_name FROM leads
    )
)
SELECT
    date,
    group_name AS brand_name, -- DEPRECATED
    group_id AS brand_id, -- DEPRECATED
    CAST(NULL AS STRING) AS account_name, -- NEW
    CAST(NULL AS NUMBER) AS account_id, -- NEW
    group_name, -- NEW
    group_id, -- NEW
    seller_name,
    seller_id,
    country_code,
    TEMP.PUBLIC.AFFILIATE_NAME_MAP(affiliate_name) AS affiliate_key,
    sales_usd,
    sales_count,
    leads_usd,
    click_count
FROM date_range
CROSS JOIN dimensions
LEFT JOIN sales USING (date, group_name, group_id, seller_name, seller_id, country_code, affiliate_name)
LEFT JOIN leads USING (date, group_name, group_id, seller_name, seller_id, country_code, affiliate_name)