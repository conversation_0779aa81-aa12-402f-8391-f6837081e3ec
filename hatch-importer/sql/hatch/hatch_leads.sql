CREATE OR REPLACE VIEW TEMP.PUBLIC.HATCH_LEADS
AS
SELECT
    TO_TIMESTAMP(CAST(dateins AS INTEGER)/1000) AS timestamp,
    leadid AS lead_id,
    brandname AS brand_name,
    brandid AS brand_id,
    retailername AS retailer_name,
    retailerid AS retailer_id,
    c.country_code,
    affiliate_name,
    affiliate_id,
    TO_NUMBER(feed_id) AS feed_id,
    TO_NUMBER(NULLIF(productid, '\\N')) AS product_id,
    NULLIF(mpn, '\\N') AS mpn,
    ROUND(TO_DECIMAL(REPLACE(NULLIF(conversion_usd, '\\N'), ',', ''), 32, 2), 2) AS price_usd,
    
    NULLIF(campaignname, '\\N') AS campaign_name,
    NULLIF(campaignid, '\\N') AS campaign_id,
    NULLIF(vendorname, '\\N') AS vendor_name,
    NULLIF(vendorid, '\\N') AS vendor_id,
    NULLIF(categoryname, '\\N') AS category_name,

    devicetype AS device_type,
    operatingsystem AS operating_system,

    price AS original_price,
    currency AS original_currency,
    NULLIF(custom_var0, '\\N') AS var0,
    NULLIF(custom_var1, '\\N') AS var1,
    NULLIF(custom_var2, '\\N') AS var2,
    NULLIF(custom_var3, '\\N') AS var3,
    NULLIF(custom_var4, '\\N') AS var4,
    NULLIF(custom_var5, '\\N') AS var5,
    NULLIF(custom_var6, '\\N') AS var6,
    NULLIF(custom_var7, '\\N') AS var7,
    NULLIF(custom_var8, '\\N') AS var8,
    NULLIF(custom_var9, '\\N') AS var9

FROM TEMP.PUBLIC.HATCH_LEADS_STAGE AS a
LEFT JOIN TEMP.PUBLIC.HATCH_FEEDS AS b ON b.feed_id=CAST(a.feedid AS INTEGER)
LEFT JOIN TEMP.PUBLIC.HATCH_COUNTRIES AS c ON c.country_id=a.countryid

-- --> UNIQUE: ???