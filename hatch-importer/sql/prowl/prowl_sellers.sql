CREATE OR R<PERSON>LACE VIEW TEMP.PUBLIC.PROWL_SELLERS
AS
SELECT
    name AS seller_name,
    seller_id::STRING AS seller_id,
    TEMP.PUBLIC.PARSE_DOMAIN(url) AS domain_name,
    TEMP.PUBLIC.FORMAT_URL(url) AS seller_url,
FROM PROWL.PROWL_MYSQL.PWL_SELLER
WHERE seller_type_id=1 AND name IS NOT NULL AND TEMP.PUBLIC.PARSE_DOMAIN(url) IS NOT NULL
QUALIFY ROW_NUMBER() OVER (PARTITION BY domain_name ORDER BY LENGTH(seller_url) DESC, seller_url) = 1
