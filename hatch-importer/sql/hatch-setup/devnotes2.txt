CREATE STAGE TEMP.PUBLIC.HATCH_SALES;
CREATE STAGE TEMP.PUBLIC.HATCH_LEADS;
CREATE STAGE TEMP.PUBLIC.HATCH_FEEDS;

LIST @TEMP.PUBLIC.HATCH_SALES;
LIST @TEMP.PUBLIC.HATCH_LEADS;
LIST @TEMP.PUBLIC.HATCH_FEEDS;

SELECT COUNT(*) FROM TEMP.PUBLIC.HATCH_SALES;
SELECT COUNT(*) FROM TEMP.PUBLIC.HATCH_LEADS;
SELECT COUNT(*) FROM TEMP.PUBLIC.HATCH_FEEDS;
SELECT * FROM TEMP.PUBLIC.HATCH_FEEDS;



SELECT DISTINCT a.feed_id FROM TEMP.PUBLIC.HATCH_LEADS AS a LEFT JOIN TEMP.PUBLIC.HATCH_FEEDS AS b ON a.feed_id=src:merchantId::INTEGER WHERE b.src IS NULL ORDER BY 1

SELECT
    src:merchantName::STRING AS merchant_name,
    src:merchantId::INTEGER AS merchant_id,
    src:dateImp::TIMESTAMP_TZ AS timestamp,
    src:affiliates[0]::STRING AS affiliate_1,
    src:affiliates[1]::STRING AS affiliate_2,
    src:affiliates[2]::STRING AS affiliate_3,
    src:affiliates[3]::STRING AS affiliate_4,
FROM TEMP.PUBLIC.HATCH_FEEDS;

TRUNCATE TABLE TEMP.PUBLIC.HATCH_SALES;
TRUNCATE TABLE TEMP.PUBLIC.HATCH_LEADS;


CREATE OR REPLACE TABLE TEMP.PUBLIC.HATCH_FEEDS (SRC VARIANT);

CREATE OR REPLACE FILE FORMAT TEMP.PUBLIC.EXCEL_CSV
    TYPE = CSV
    SKIP_HEADER = 1
    FIELD_DELIMITER = '\t'  -- Use tab if tabs separate fields; use ' ' for space
    FIELD_OPTIONALLY_ENCLOSED_BY = '"'
    NULL_IF = ('NULL', '\N')
    TRIM_SPACE = TRUE
    ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
    RECORD_DELIMITER = '\n'  -- Try '\r\n' if \n fails
    ESCAPE = NONE
    ESCAPE_UNENCLOSED_FIELD = NONE;

SELECT *
FROM TABLE(
    INFER_SCHEMA(
        LOCATION => '@temp.public.test_stage/Hatch_SaleReport_1745509917664_2025_01_01-2025_04_24.csv',
        FILE_FORMAT => 'temp.public.excel_csv'
    )
);



CREATE TABLE temp.public.hatch_sales (
    date DATE,
    time TIME,
    session_key VARCHAR,
    order_id VARCHAR,
    order_status VARCHAR,
    cookie VARCHAR,
    brand_name VARCHAR,
    country_name VARCHAR,
    retailer_name VARCHAR,
    category_name VARCHAR,
    lead_id VARCHAR,
    pixel_string VARCHAR,
    vendor_name VARCHAR,
    product_id VARCHAR,
    mpn VARCHAR,
    ean VARCHAR,
    product_tag VARCHAR,
    campaign_name VARCHAR,
    description VARCHAR,
    qty VARCHAR,
    original_price VARCHAR,  -- Load as VARCHAR to handle "69,99"
    original_total_amount VARCHAR,
    original_currency VARCHAR,
    converted_price VARCHAR,  -- Load as VARCHAR to handle "67,62"
    converted_total_amount VARCHAR,
    converted_currency VARCHAR,
    cid VARCHAR,
    var1 VARCHAR,
    var2 VARCHAR,
    var3 VARCHAR,
    var4 VARCHAR,
    var5 VARCHAR,
    var6 VARCHAR,
    var7 VARCHAR,
    var8 VARCHAR,
    var9 VARCHAR,
    my_sale VARCHAR
);

COPY INTO temp.public.hatch_sales
FROM @temp.public.test_stage/Hatch_SaleReport_1745509917664_2025_01_01-2025_04_24.csv
FILE_FORMAT = temp.public.excel_csv;

CREATE TABLE temp.public.hatch_leads (
    date DATE,
    time TIME,
    session_key VARCHAR,
    lead_id VARCHAR,
    country VARCHAR,
    brand_name VARCHAR,
    retailer_id VARCHAR,
    feed_id VARCHAR,
    retailer_name VARCHAR,
    product_id VARCHAR,
    mpn VARCHAR,
    product_tag VARCHAR,
    campaign_name VARCHAR,
    model_name VARCHAR,
    vendor_name VARCHAR,
    category_name VARCHAR,
    original_price VARCHAR, TRY_TO_DECIMAL(REPLACE(ORIGINAL_PRICE, ',', '.'), '999,999,999.99', 12, 2)
    original_currency VARCHAR,
    converted_price VARCHAR, TRY_TO_DECIMAL(REPLACE(ORIGINAL_PRICE, ',', '.'), '999,999,999.99', 12, 2)
    converted_currency VARCHAR,
    device_type VARCHAR,
    operating_system VARCHAR,
    cid VARCHAR,
    var1 VARCHAR,
    var2 VARCHAR,
    var3 VARCHAR,
    var4 VARCHAR,
    var5 VARCHAR,
    var6 VARCHAR,
    var7 VARCHAR,
    var8 VARCHAR,
    var9 VARCHAR
);

COPY INTO temp.public.hatch_leads
FROM @temp.public.test_stage/hatch_lead_4.csv
FILE_FORMAT = temp.public.excel_csv
ON_ERROR = 'CONTINUE';


SELECT * FROM temp.public.hatch_sales;
SELECT * FROM temp.public.hatch_leads;

-- NULLIF(NULLIF(category_name, ''), '\\N')
-- TRY_TO_DECIMAL(REPLACE(ORIGINAL_PRICE, ',', '.'), '999,999,999.99', 10, 2)


SELECT * EXCLUDE(ORIGINAL_PRICE), TRY_TO_DECIMAL(REPLACE(ORIGINAL_PRICE, ',', '.'), '999,999,999.99', 10, 2) AS ORIGINAL_PRICE
FROM temp.public.hatch_leads;




SELECT DISTINCT ORIGINAL_PRICE, TRY_TO_DECIMAL(REPLACE(ORIGINAL_PRICE, ',', '.'), '999,999,999.99', 12, 2) AS ORIGINAL_PRICE_NUM
FROM temp.public.hatch_leads
WHERE TRY_TO_DECIMAL(REPLACE(ORIGINAL_PRICE, ',', '.'), '999,999,999.99', 12, 2) IS NULL


SELECT NULLIF(TRIM(category_name), '') AS category_name, COUNT(*) FROM temp.public.hatch_leads GROUP BY 1 ORDER BY 2 DESC, 1;

SELECT category_name FROM temp.public.hatch_leads WHERE category_name='\\n'

NULLIF(TRIM(category_name), '')

SELECT 
    DATE("TIMESTAMP_UTC") AS "DATE",
    "COUNTRY_ID",
    "SHOP_ID",
    "REFERRAL_MANUFACTURER_ID",
    COUNT("CLICK_ID") AS "CLICKS"
FROM 
    "WTB_EU"."KPI"."CLICK"
WHERE 
    "TIMESTAMP_UTC" >= '2023-01-01' 
    AND "TIMESTAMP_UTC" < CURRENT_DATE
    AND "BOT_LEVEL" < 84
    AND "VISIBILITY" = 'Yes'
    AND "TYPE_ID" IN ('1', '2')
GROUP BY 
    DATE("TIMESTAMP_UTC"),
    "COUNTRY_ID",
    "SHOP_ID",
    "REFERRAL_MANUFACTURER_ID"
ORDER BY 
    "DATE" DESC

SELECT 
    DATE_TRUNC('DAY', "SALE_TIMESTAMP_UTC") AS "DATE",
    "COUNTRY_ID",
    "SHOP_ID",
    "REFERRAL_MANUFACTURER_ID",
    SUM("QUANTITY") AS "SALES QTY",
    SUM("REVENUE_USD") AS "SALES REVENUE (USD)"
FROM 
    "WTB_EU"."KPI"."SALE"
WHERE 
    "SALE_TIMESTAMP_UTC" >= '2023-01-01' 
    AND "SALE_TIMESTAMP_UTC" < CURRENT_DATE
    AND "VISIBILITY" = TRUE
GROUP BY 
    "DATE",
    "COUNTRY_ID",
    "SHOP_ID",
    "REFERRAL_MANUFACTURER_ID"
ORDER BY 
    "DATE" DESC


