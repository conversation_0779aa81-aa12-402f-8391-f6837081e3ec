CREATE OR REPLACE VIEW TEMP.PUBLIC.HATCH_FEEDS_PENDING
AS
WITH all_feeds AS (
    SELECT DISTINCT feed_id FROM (
        SELECT DISTINCT CAST(feedId AS INTEGER) AS feed_id FROM TEMP.PUBLIC.HATCH_SALES_STAGE
        UNION ALL
        SELECT DISTINCT CAST(feedId AS INTEGER) AS feed_id FROM TEMP.PUBLIC.HATCH_LEADS_STAGE
    )
)
SELECT feed_id
FROM all_feeds
LEFT JOIN TEMP.PUBLIC.HATCH_FEEDS_STAGE ON feed_id=src:merchantId::INTEGER
WHERE src IS NULL