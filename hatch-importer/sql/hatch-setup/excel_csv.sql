CREATE OR REPLACE FILE FORMAT TEMP.PUBLIC.EXCEL_CSV
    TYPE = CSV
    SKIP_HEADER = 1
    FIELD_DELIMITER = '\t'  -- Use tab if tabs separate fields; use ' ' for space
    FIELD_OPTIONALLY_ENCLOSED_BY = '"'
    NULL_IF = ('NULL', '\N')
    TRIM_SPACE = TRUE
    ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
    RECORD_DELIMITER = '\n'  -- Try '\r\n' if \n fails
    ESCAPE = NONE
    ESCAPE_UNENCLOSED_FIELD = NONE;
