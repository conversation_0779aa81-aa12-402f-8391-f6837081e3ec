CREATE STAGE TEMP.PUBLIC.HATCH_STAGE;
LIST @TEMP.PUBLIC.HATCH_STAGE;

CREATE PIPE TEMP.PUBLIC.HATCH_SALES_PIPE AS
COPY INTO TEMP.PUBLIC.HATCH_SALES
FROM @TEMP.PUBLIC.HATCH_SALES
FILE_FORMAT=(TYPE='CSV')
MATCH_BY_COLUMN_NAME=CASE_INSENSITIVE;

CREATE PIPE TEMP.PUBLIC.HATCH_FEEDS_PIPE AS
COPY INTO TEMP.PUBLIC.HATCH_SALES
FROM @TEMP.PUBLIC.HATCH_SALES
FILE_FORMAT=(TYPE='JSON')
MATCH_BY_COLUMN_NAME=CASE_INSENSITIVE;



CREATE STAGE temp.public.test_stage;


SELECT *
FROM TABLE(
    INFER_SCHEMA(
        LOCATION => '@temp.public.test_stage/Hatch_SaleReport_1745509917664_2025_01_01-2025_04_24.csv',
        FILE_FORMAT => 'TEMP.PUBLIC.EXCEL_CSV'
    )
);


COPY INTO temp.public.hatch_sales
FROM @temp.public.test_stage/Hatch_SaleReport_1745509917664_2025_01_01-2025_04_24.csv
FILE_FORMAT = temp.public.excel_csv;

COPY INTO temp.public.hatch_leads
FROM @temp.public.test_stage/hatch_lead_4.csv
FILE_FORMAT = temp.public.excel_csv
ON_ERROR = 'CONTINUE';
