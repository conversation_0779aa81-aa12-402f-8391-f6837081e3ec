
## platforms
- pricespider (1)
- prowl (2)
- cc (3)
- hatch (4)

## Slack
- conversation with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> on 4/22/25
- channel project-unified-retailer with <PERSON><PERSON> on 5/13/25


hatch: https://pricespidereur.sharepoint.com/sites/GlobalMissionControl/Shared%20Documents/Forms/A[…]e4&FolderCTID=0x0120004AA3684349BF8446BBE7A5822CADBFCB
ps :

## clicks  .Source (zga98119.us-east-1.snowflakecomputing.com)
```
 WITH EXPANDED_SELLERS AS (
    SELECT
        f.date,
        f.account_id,
        CAST(s.value:country_code AS VARCHAR) AS country_code,          -- Text type
        CAST(s.value:redirect_count AS INTEGER) AS redirect_count,       -- Whole number
        CAST(s.value:seller_id AS INTEGER) AS seller_id,                 -- Whole number
        CAST(s.value:seller_name AS VARCHAR) AS seller_name,             -- Text type
    FROM
        WTB.PROD.TRAFFIC f,
        LATERAL FLATTEN(input => f.SELLERS) AS s
    WHERE
        f.date > '2023-01-01'
)
SELECT *
FROM EXPANDED_SELLERS
WHERE
    seller_name IS NOT NULL
    AND redirect_count <> 0
```

## sales
```
WITH EXPANDED_SELLERS AS (
    SELECT
        f.date,
        f.account_id,
        CAST(s.value:country_code AS VARCHAR) AS country_code,          -- Text type
        CAST(s.value:sales_count AS INTEGER) AS sales_count,             -- Whole number
        CAST(s.value:sales_usd AS DECIMAL(18, 2)) AS sales_usd,          -- Decimal with 2 decimal places
        CAST(s.value:seller_id AS INTEGER) AS seller_id,                 -- Whole number
        CAST(s.value:seller_name AS VARCHAR) AS seller_name,             -- Text type
    FROM
        WTB.PROD.TRAFFIC f,
        LATERAL FLATTEN(input => f.SELLERS) AS s
    WHERE
        f.date > '2023-01-01'
)
SELECT *
FROM EXPANDED_SELLERS
WHERE
    seller_name IS NOT NULL
    AND sales_count <> 0
```

## cc clicks
```
SELECT 
    DATE("TIMESTAMP_UTC") AS "DATE",
    "COUNTRY_ID",
    "SHOP_ID",
    "REFERRAL_MANUFACTURER_ID",
    COUNT("CLICK_ID") AS "CLICKS"
FROM 
    "WTB_EU"."KPI"."CLICK"
WHERE 
    "TIMESTAMP_UTC" >= '2023-01-01' 
    AND "TIMESTAMP_UTC" < CURRENT_DATE
    AND "BOT_LEVEL" < 84
    AND "VISIBILITY" = 'Yes'
    AND "TYPE_ID" IN ('1', '2')
GROUP BY 
    DATE("TIMESTAMP_UTC"),
    "COUNTRY_ID",
    "SHOP_ID",
    "REFERRAL_MANUFACTURER_ID"
ORDER BY 
    "DATE" DESC
```

## cc sales
```
SELECT
    DATE_TRUNC('DAY', "SALE_TIMESTAMP_UTC") AS "DATE",
    "COUNTRY_ID",
    "SHOP_ID",
    "REFERRAL_MANUFACTURER_ID",
    SUM("QUANTITY") AS "SALES QTY",
    SUM("REVENUE_USD") AS "SALES REVENUE (USD)"
FROM 
    "WTB_EU"."KPI"."SALE"
WHERE 
    "SALE_TIMESTAMP_UTC" >= '2023-01-01' 
    AND "SALE_TIMESTAMP_UTC" < CURRENT_DATE
    AND "VISIBILITY" = TRUE
GROUP BY 
    "DATE",
    "COUNTRY_ID",
    "SHOP_ID",
    "REFERRAL_MANUFACTURER_ID"
ORDER BY 
    "DATE" DESC
```


## links from Ark
- https://app.powerbi.com/groups/me/reports/f5012ee3-3755-465b-9fa9-229619908632/ReportSection76fac3e73d09e70329de?ctid=a923015c-b511-46ed-8691-9be1371a8841&experience=power-bi