CREATE OR R<PERSON>LACE VIEW TEMP.PUBLIC.CC_METRICS
AS
WITH
sales AS (
    SELECT
        DATE(sale_timestamp_utc) AS date,
    
        c.name AS brand_name, -- DEPRECATED
        a.referral_manufacturer_id::STRING AS brand_id, -- DEPRECATED

        c.name AS group_name, -- NE<PERSON>, should this go into group_name instead?
        a.referral_manufacturer_id::STRING AS group_id, -- NEW, should this go into group_id instead?

        b.name AS seller_name,
        a.shop_id::STRING AS seller_id,
        b.country AS country_code,
        IFNULL(d.affiliate_network_name, '') AS affiliate_name,
        SUM(a.revenue_usd) AS sales_usd,
        COUNT(*) AS sales_count
    FROM WTB_EU.KPI.SALE AS a
    JOIN WTB_EU.DIM.ONLINE_SHOP AS b ON b.osid=a.shop_id
    JOIN WTB_EU.DIM.MANUFACTURER AS c ON c.id=a.referral_manufacturer_id
    LEFT JOIN WTB_EU.MONITORING.MONITORING_AFFILIATE_NETWORKS AS d ON d.shop_id_unique=a.shop_id
    WHERE a.visibility = TRUE
    AND sale_timestamp_utc >= DATEADD(YEAR, -1, CURRENT_DATE())
    GROUP BY ALL
),
leads AS (
    SELECT
        DATE(timestamp_utc) AS date,
        c.name AS brand_name, -- DEPRECATED
        a.referral_manufacturer_id::STRING AS brand_id, -- DEPRECATED
        c.name AS group_name,
        a.referral_manufacturer_id::STRING AS group_id,
        b.name AS seller_name,
        a.shop_id::STRING AS seller_id,
        b.country AS country_code,
        IFNULL(d.affiliate_network_name, '') AS affiliate_name,
        CAST(NULL AS FLOAT) AS leads_usd,
        COUNT(DISTINCT a.click_id) AS click_count
    FROM WTB_EU.KPI.CLICK AS a
    JOIN WTB_EU.DIM.ONLINE_SHOP AS b ON b.osid=a.shop_id
    JOIN WTB_EU.DIM.MANUFACTURER AS c ON c.id=a.referral_manufacturer_id
    LEFT JOIN WTB_EU.MONITORING.MONITORING_AFFILIATE_NETWORKS AS d ON d.shop_id_unique=a.shop_id
    WHERE a.bot_level < 84
    AND a.visibility = 'Yes'
    AND a.type_id IN ('1', '2')
    AND timestamp_utc >= DATEADD(YEAR, -1, CURRENT_DATE())
    GROUP BY ALL
),
date_range AS (
    SELECT DISTINCT date FROM (
        SELECT DISTINCT date FROM sales
        UNION ALL
        SELECT DISTINCT date FROM leads
    )
),no
distinct_sellers AS (
    SELECT DISTINCT group_name, group_id, seller_name, seller_id, country_code, affiliate_name FROM (
        SELECT DISTINCT group_name, group_id, seller_name, seller_id, country_code, affiliate_name FROM sales
        UNION ALL
        SELECT DISTINCT group_name, group_id, seller_name, seller_id, country_code, affiliate_name FROM leads
    )
)
SELECT
    date,
    group_name AS brand_name, -- DEPRECATED
    group_id AS brand_id, -- DEPRECATED
    CAST(NULL AS STRING) AS account_name, -- NEW
    CAST(NULL AS NUMBER) AS account_id, -- NEW
    group_name, -- NEW
    group_id, -- NEW
    seller_name,
    seller_id,
    country_code,
    TEMP.PUBLIC.AFFILIATE_NAME_MAP(affiliate_name) AS affiliate_key,
    sales_usd,
    sales_count,
    leads_usd,
    click_count
FROM date_range
CROSS JOIN distinct_sellers
LEFT JOIN sales USING (date, group_name, group_id, seller_name, seller_id, country_code, affiliate_name)
LEFT JOIN leads USING (date, group_name, group_id, seller_name, seller_id, country_code, affiliate_name)
WHERE sales_count IS NOT NULL OR click_count IS NOT NULL
