CREATE OR <PERSON><PERSON><PERSON>CE VIEW TEMP.PUBLIC.cc_sellers
AS
SELECT
    TRIM(name) AS seller_name,
    osid::STRING AS seller_id,
    country AS country_code,
    ARRAY_AGG(TEMP.PUBLIC.PARSE_DOMAIN(homepage)) WITHIN GROUP (ORDER BY LENGTH(TEMP.PUBLIC.PARSE_DOMAIN(homepage)), TEMP.PUBLIC.PARSE_DOMAIN(homepage))[0]::STRING AS domain_name, --shortest
    ARRAY_AGG(TEMP.PUBLIC.FORMAT_URL(homepage)) WITHIN GROUP (ORDER BY LENGTH(homepage), homepage)[0]::STRING AS seller_url --shortest
FROM WTB_EU.DIM.ONLINE_SHOP
WHERE active=1
GROUP BY ALL

-- UNIQUE: seller_id
