CREATE OR REPLACE FUNCTION TEMP.PUBLIC.AFFILIATE_NAME_MAP(affiliate_name STRING)
RETURNS STRING
LANGUAGE SQL
AS
$$
CASE LOWER(TRIM(affiliate_name))
    WHEN '2performant' THEN '2performant.com'
    WHEN 'accesstrade' THEN 'accesstrade.global'
    WHEN 'awin' THEN 'awin.com'
    WHEN 'admitad' THEN 'admitad.com'
    WHEN 'adtraction' THEN 'adtraction.com'
    WHEN 'affili.net' THEN 'awin.com' -- affili.net merged with <PERSON>win
    WHEN 'affiliate gateway' THEN 'affiliategateway.asia'
    WHEN 'affiliatebot' THEN NULL -- no clear primary domain found
    WHEN 'amazon' THEN 'amazon.com'
    WHEN 'amazon.au' THEN 'amazon.com.au'
    WHEN 'amazon.be' THEN 'amazon.com.be'
    WHEN 'amazon.br' THEN 'amazon.com.br'
    WHEN 'amazon.ca' THEN 'amazon.ca'
    WHEN 'amazon.cn' THEN 'amazon.cn'
    WHEN 'amazon.co.uk' THEN 'amazon.co.uk'
    WHEN 'amazon.com' THEN 'amazon.com'
    WHEN 'amazon.de' THEN 'amazon.de'
    WHEN 'amazon.es' THEN 'amazon.es'
    WHEN 'amazon.fr' THEN 'amazon.fr'
    WHEN 'amazon.in' THEN 'amazon.in'
    WHEN 'amazon.it' THEN 'amazon.it'
    WHEN 'amazon.jp' THEN 'amazon.co.jp'
    WHEN 'amazon.mx' THEN 'amazon.com.mx'
    WHEN 'amazon.nl' THEN 'amazon.nl'
    WHEN 'amazon.se' THEN 'amazon.se'
    WHEN 'avantlink' THEN 'avantlink.com'
    WHEN 'b&h photo' THEN 'bhphotovideo.com'
    WHEN 'bol' THEN 'bol.com'
    WHEN 'commission factory' THEN 'commissionfactory.com'
    WHEN 'cj' THEN 'cj.com'
    WHEN 'commission junction' THEN 'cj.com'
    WHEN 'cuelinks' THEN 'cuelinks.com'
    WHEN 'doordash' THEN 'doordash.com'
    WHEN 'ebay' THEN 'ebay.com'
    WHEN 'effiliation' THEN 'effinity.fr'
    WHEN 'flexoffers' THEN 'flexoffers.com'
    WHEN 'freeworld' THEN NULL -- no clear primary domain found
    WHEN 'impact radius' THEN 'impact.com'
    WHEN 'impact radius alt2' THEN 'impact.com'
    WHEN 'involve asia' THEN 'involve.asia'
    WHEN 'linkwise' THEN 'linkwise.gr'
    WHEN 'linkshare' THEN 'rakutenadvertising.com' -- LinkShare is part of Rakuten
    WHEN 'mediamarkt de' THEN 'mediamarkt.de'
    WHEN 'netaffiliation' THEN 'kwanko.com' -- NetAffiliation rebranded to Kwanko
    WHEN 'otto de' THEN 'otto.de'
    WHEN 'partnerize' THEN 'partnerize.com'
    WHEN 'partnerize alt 2' THEN 'partnerize.com'
    WHEN 'performance horizon' THEN 'partnerize.com' -- Performance Horizon is now Partnerize
    WHEN 'pepperjam' THEN 'pepperjam.com'
    WHEN 'radius' THEN 'impact.com' -- Radius is associated with Impact
    WHEN 'rakuten' THEN 'rakutenadvertising.com'
    WHEN 'rakuten/linkshare japan' THEN 'rakuten.co.jp'
    WHEN 'saturn de' THEN 'saturn.de'
    WHEN 'shareasale' THEN 'shareasale.com'
    WHEN 'soicos' THEN 'soicos.com'
    WHEN 'tradedoubler' THEN 'tradedoubler.com'
    WHEN 'tradetracker' THEN 'tradetracker.com'
    WHEN 'valorebooks' THEN 'valorebooks.com'
    WHEN 'webgains' THEN 'webgains.com'
    WHEN 'zanox' THEN 'awin.com' -- Zanox merged with Awin

    WHEN 'pricespider' THEN 'pricespider'
    WHEN 'hatch' THEN 'hatch'
    WHEN 'commerce connector' THEN 'cc'

    ELSE NULL
END
$$