CREATE OR REPLACE TABLE TEMP.PUBLIC.PLATFORM_SELLERS
AS
SELECT *,
    CONCAT(domain_name, '_', LOW<PERSON>(country_code)) AS seller_key
FROM (

    SELECT
        seller_name,
        seller_id,
        country_code,
        domain_name,
        seller_url,
        'pricespider' AS platform_name,
        1 AS platform_id
    FROM TEMP.PUBLIC.PRICESPIDER_SELLERS
    WHERE domain_name IS NOT NULL
    
    UNION ALL
    
    SELECT
        seller_name,
        seller_id,
        country_code,
        domain_name,
        seller_url,
        'prowl' AS platform_name,
        2 AS platform_id
    FROM TEMP.PUBLIC.PROWL_SELLERS
    WHERE domain_name IS NOT NULL
    
    UNION ALL
    
    SELECT
        seller_name,
        seller_id,
        country_code,
        domain_name,
        seller_url,
        'cc' AS platform_name,
        3 AS platform_id
    FROM TEMP.PUBLIC.CC_SELLERS
    WHERE domain_name IS NOT NULL

    UNION ALL

    SELECT
        seller_name,
        seller_id,
        country_code,
        domain_name,
        seller_url,
        'hatch' AS platform_name,
        4 AS platform_id
    FROM TEMP.PUBLIC.HATCH_SELLERS
    WHERE domain_name IS NOT NULL

)
QUALIFY ROW_NUMBER() OVER (PARTITION BY platform_id, domain_name, seller_id, country_code ORDER BY LENGTH(seller_name) DESC, seller_name) = 1