https://my.gethatch.com/
https://pricespider.atlassian.net/wiki/spaces/EKB/pages/35151216641/Hatch+WTB+Online+Reporting+API+new

JWT TOKEN
curl -XPOST 'https://papi.gethatch.com/oauth/token' -H 'authorization: Basic Z2V0aGF0Y2g6d2hhdGV2ZXI=' --data-raw 'username=<EMAIL>&password=Fidelio-99&grant_type=password' --compressed \
| jq -r '.access_token' \
| (read token; sed -i "s/ACCESS_TOKEN=.*/ACCESS_TOKEN=$token/" .env)


REFRESH TOKEN
curl 'https://papi.gethatch.com/oauth/token' -H 'authorization: Basic Z2V0aGF0Y2g6d2hhdGV2ZXI=' -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' --data-raw 'grant_type=refresh_token&refresh_token=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Sa-hE4ffLnIasAvJKL3904413bga3LED3vdzmCB3ywcpASTR727CUcfyxTFHWQ1kyIzh0Y2w3XKwJAHU9dSrAwkWoOKaSj62hFuxaKr3u4U6MSN4qnemzbsjhs4-LKaraI1bOPWZOM4xu7h2QPtTNvpaSdnJ9PHmdhrHYo4hcVAbHvGDHGhfm5587hL-FIv4kfy_OqKMMk3rhdSmHG1DM2w1clRu2qcQBxUuQl9yer1ipTXFSUjQ2SvDbgmNGhMgwG8jzpw-c71opowo2YKOssST1Is2f1-9h3V8mDnyGzbOjvwJSm4FkBFsmh0hIyWH1X2F46YLZFuuGTdcgd4t7g' --compressed -i


OVERVIEW
curl -X POST "https://papi.gethatch.com/api/pils/report/v2/overview" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S_-twmhKHuEaHrgLYMsbrI5v9RqJmND0yOzD3_-49Zl77SrmgANaQh1k7Al-BeskaD7QRwQP7xoDiXAV7kYYPJtmM3T9KBLte2ecO1OG0R4rzwDfQsN8ZplCYjWPGYxNXQx2RUYU2h0jzfApuUqy9r-H3kHaRidgjYE9KSSUps1RlRfiyDyHSfnQzCDiDCez0wa5c5svPvaHDNFuViAAaiSRGhMDG61SyVPFowwStgHFqz2jnh0__EVdMUsRqX3m1BtfONonYajwOO1cByll1HxWAFS_AwVDXVUA3CEbDHLvtnDHpUQw3ebpx4e-Uv-LpV6fo-PAK-CHqLPCkt9RyQ" \
-d '{"currency":"EUR","filters":[{"columnName":"dateIns","operation":"range","value":[1743465600000,1743552000999]}]}'
>>> WORKS!

EXPORT
curl -X POST "https://papi.gethatch.com/api/pils/report/v2/export" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nr8tneiUD0lhikwpGH5rb2Wfx8lBuShk3m0_QJ2Qws7FRuRDHZKWmehOVvIwE5IdDO1rK1bUQF5pEP0wSjluHU0dl_KpiMbOQX4ffL3hFkLmRl8cpxT2gGjgpc6zuMAiW9sbmICjtOYKQu3_vAQzrMIrx69nHhB1HVTHPVhzuivTX-Sp8FIxQ7eHaWivAt-ip0oaIJBeCmjK295JzojXcawq7Tpwgm-bHD-2b5GyMwCZm7z6_ocSn6141dAzV09UVEwhI7tP2tFeHZV60jepduo1PI2Xrw4BqN-ms1msKjYoPtJnR_XTi1-oNm4WaPqDsall2miYNyw4II3f1Yu1Pg" \
-d '{"statType":"sale","currency":"EUR","dataFormat":"csv","priceFormat":"PERIOD_AND_COMMA","filters":[{"columnName":"dateIns","operation":"range","value":[1743465600000,1743552000999]}]}'
>>> WORKS!


curl -X POST "https://papi.gethatch.com/api/pils/report/v2/export" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S_-twmhKHuEaHrgLYMsbrI5v9RqJmND0yOzD3_-49Zl77SrmgANaQh1k7Al-BeskaD7QRwQP7xoDiXAV7kYYPJtmM3T9KBLte2ecO1OG0R4rzwDfQsN8ZplCYjWPGYxNXQx2RUYU2h0jzfApuUqy9r-H3kHaRidgjYE9KSSUps1RlRfiyDyHSfnQzCDiDCez0wa5c5svPvaHDNFuViAAaiSRGhMDG61SyVPFowwStgHFqz2jnh0__EVdMUsRqX3m1BtfONonYajwOO1cByll1HxWAFS_AwVDXVUA3CEbDHLvtnDHpUQw3ebpx4e-Uv-LpV6fo-PAK-CHqLPCkt9RyQ" \
-d '{"statType":"sale","currency":"EUR","dataFormat":"csv","priceFormat":"PERIOD_AND_COMMA","filters":[{"columnName":"dateIns","operation":"range","value":[1747008000000,1747094399999]}]}'



FEEDS
curl -X GET "https://bo.aws.gethatch.com/iceleads_web/web/feed/100500" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.n9cO6-9xqC7-0ALkuGuPnh4_Tkqkn-cqiz0WgR3l5kvtnyN_1m-oXsj0PvlcDAVO96BEbsUYqKvJAls1I5-fY-9o139O2SNviMsP-bW1GQtwshx6TcKCihVYUn_B1mw6U-NsJ6DNfD2e6fFzU7bpwyiI6Q6THd1AWT5Pekxez7ryhX6HhtVWwDdwr8s7dx8nyf5Cr9QbGdIooUT4JJs39PvLGvIjbJsUiUcaj_ESvParT21OEjE1L_nhfeWermTKi_mK1CnHqUSRYFoqa-6wKkdgL9lEZEX5rP4obbVE2CuhGqIiDmygKFcNmiIq9ZVL1xYn5IgUPHBqlZ_GQhuUQA"

curl -X GET "https://bo.aws.gethatch.com/iceleads_web/web/feed/68188" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dA4xmOz0iR5fRlgyDXUMr9ZKZFX5U_5O9rC7CJsjv3mO5Md7Mu-99p0ASWnYVZZRX_m_EJgNiQLUGCThJAl55rhyqJjyGOkzO6RSh7yLUdL-mNWwewKWvGmK5gxZ4GYZkY_VoHymm99fFHTzAaFy_CafcsZ5HBcwS00KtmtH2OFrVl96Fw6Dc_1vHGUddaFBm3okYAEO54mTtkaFuqUU26qVWPdWiRFDQgs_NyS39dEpo6zIvGdX6HZKxLjCawEuB3FHORIvdXkfnYBHBT8HnXQTeGXmiv430ck6fY6h-GDtSECn6O_e7tA1ec_dn8lQclsR0aN7K5G0tRb8zJOH-Q"
